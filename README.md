# <img src="./libs/shared/assets/src/icons/svg/venio-logo.svg" width="30" alt="logo"> Venio Frontend 


This project was created using [Nx](https://nx.dev) with additional configurations such as Tailwind, ESLint rules, Prettier, and pre-commit hooks tailored to specific needs.

To ensure optimal usage and implementation of the aforementioned tools, It is highly recommended reviewing the [Nx](https://nx.dev) documentation for a better understanding of their general concepts and functionalities


# Nodejs installation and managing versions

If you encounter any issues related to Node.js, I would highly recommend using NVM (Node Version Manager) instead of relying on a single instance of Node.js installation. By utilizing NVM, you gain the ability to manage multiple versions of Node.js and select the one that suits your development needs.

To get started with NVM, follow these steps:

1. First, navigate to the following link: [NVM Windows Releases](https://github.com/coreybutler/nvm-windows/releases), and download the executable package.

2. Once the download is complete, run the executable to install NVM.

3. After the installation, open your terminal or command prompt.

4. Verify that the 'nvm' command is recognized by running it in your terminal. This confirms that NVM is correctly installed and accessible.

5. Now, it's a good practice to remove any previously installed versions of Node.js to avoid conflicts. You'll want to perform a fresh installation using NVM.

Here are some useful NVM commands:

- `nvm list`: This command displays a list of locally installed Node.js versions.

- `nvm list available`: Use this command to see the list of available Node.js versions from the online repository.

- `nvm install 16 --lts`: Installing the LTS (Long-Term Support) version of Node.js 16 can be done with this command.

- `nvm use <installed version>`: Replace `<installed version>` with the specific Node.js version you want to use, for example, '16.20.2'. This command sets the active Node.js version for your current session.

By following these steps and utilizing NVM, you'll have a powerful tool at your disposal to manage Node.js versions seamlessly during your development projects.

# Referral Links

Before beginning any new endeavor, it's always a good idea to peruse a few blog posts, official documentation, and guidelines to prepare oneself adequately.
🙂

The following links were used as references during the project's design phase.👇

* The [project guidelines](https://github.com/elsewhencode/project-guidelines) cover a comprehensive summary of almost everything one needs to know. However, it's essential to note that a few things may differ depending on the specific tool or JavaScript framework we use. Nevertheless, the general concept remains the same across the board.
* [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/) A specification for adding human and
  machine-readable meaning to commit messages
* [understanding monorepo](https://monorepo.tools/#understanding-monorepos) The content provided offers an exceptional description of monorepos and a thorough comparison between different build systems

# apps

This directory contains all of the representational apps that are served, built, bundled, and distributed. It's important to note that this directory has the capacity to contain as many apps as necessary.

# libs

This directory contains all the shareable libraries that ensure re-usability within both the libraries and applications. Examples include `feature`, `ui`, and `data-access`, which are grouped based on their respective functionalities.

`ui` primarily consists of dumb components related to UX/UI, `feature` is made up of smart components, and `data-access` contains modules and files related to the store and API. By organizing our libraries in this manner, we can maintain a higher level of modularity and reusability throughout our project.

NX has provided IDE plugin for making easy to creating libs, apps, components etc.,

For VS code extension [Nx Console
](https://marketplace.visualstudio.com/items?itemName=nrwl.angular-console)

For Jetbrains extension [Nx Console Idea
](https://plugins.jetbrains.com/plugin/15101-nx-console-idea) or [nx-webstorm
](https://plugins.jetbrains.com/plugin/15000-nx-webstorm)

# Library Guidelines

Use one of extension from above or CLI to generate library inside `libs` directory.

Navigate [nx applications and libraries](https://nx.dev/more-concepts/applications-and-libraries) documentation guide for detailed information.

During library creation, these things are must as an args of CLI:

example command to generate publishable library:

 ```powershell
 nx generate @nx/angular:library 
 --name=worker --directory=shared 
 --importPath=@venio/shared/worker 
 --prefix=venio 
 --buildable 
 --addTailwind
 ```

- `importPath`
  - it should always prefix with `@venio` namespace
  - for example, lets say we have shared folder inside `libs` where we have `card` & `grid` libraries grouped by shared
    folder. Now, the import paths should `@venio/shared/card` and `@venio/shared/grid` as you can see we
    have `organization/group-of-lib/name` similar to other lib like `@kendo/buttons` or `@angular/common`
  - specify lib name and path name clearly to indicate its use cases with meaningful namespace
- `prefix`
  - it should always be `venio` as describing the organization name
- `buildable` library should either buildable or publishable but not both (should choose buildable)
- `addTailwind` should always add tailwind config file if the library is UI related. Non-ui related libs doesn't need to
  add this.
  - once the tailwind config is generated in the lib, we should reuse workspace level shared config inside this
    example: inside `libs/some-linb/tailwind.config.js`, import shared config from root and use it as
   ```js
    // require from root
    const sharedTailwindConfig = require('../../tailwind.config.shared')
    module.exports = {
     // use shared file
      presets: [sharedTailwindConfig],
      ...
    }
   ```
- library folder structure _(when you create a library there will be`src`)_ must be organized as follows which will be exported from the public api `index.ts`
    ```html
    src
    └───lib <!-- default folder -->
    │ └───models <!-- group folder -->
    │ |    └───constants <!-- enum, const variable  group folder -->
    │ |    └───interfaces <!-- data model group folder -->
    │ └───worker <!-- web-worker group folder -->
    │ └───functions <!-- reusable function group folder -->
    │ └───components <!-- library components group folder -->
    │ └───services <!-- angular service group folder -->
    │ └───+state <!-- NGRX  group folder -->
    ```

It is not recommended to use random or too specific name/folder for libraries.

**Good**

```html
libs
└───data-access <!-- group folder -->
│   └───auth <!-- lib -->
│   └───login <!-- lib -->
|
└───shared <!-- group folder -->
│   └───styles <!-- lib -->
│   └───assets <!-- lib -->
|
└───feature <!-- group folder -->
│   └───login <!-- lib -->
│   └───registration <!-- lib -->
│   └───search <!-- lib -->
|
└───ui <!-- group folder -->
│   └───loader <!-- lib -->
│   └───expansion-panel <!-- lib -->
│   └───card <!-- lib -->
│   └───grid <!-- lib -->
|
└───util <!-- group folder -->
│   └───functions <!-- lib for pure functions / decorators -->
```

**BAD**

```html
libs
└───modules <!-- folder -->
│   └───card <!-- lib -->
│   └───grid <!-- lib -->
└───login <!-- lib -->
└───signup <!-- lib -->
└───loader <!-- lib -->
└───expansion-panel <!-- lib -->
└───assets-images <!-- lib -->
└───styles-css <!-- lib -->
└───api <!-- lib -->
└───login-store <!-- lib -->
```

To see the dependency, run `npm run affected:dep-graph` or `nx affected:dep-graph` for affected only or `npm run dep-graph` or `nx dep-graph` for full.

Here is the demo graph:

<img src="./libs/shared/assets/src/images/graph-demo.png" alt="graph demo">

for better explanation, please go to [Using Nx at Enterprises
](https://nx.dev/more-concepts/monorepo-nx-enterprise#using-nx-at-enterprises)

# Styles/Assets/Theme Guidelines

Primary css framework is [tailwind](https://tailwindcss.com/) including the `kendo` theme for their components.

Globally shared styling and themes are shared from

- `libs/shared/styles` for tailwind, scss files
  - `scss`is the default language for styles
  - the primary classes for html should always be the tailwind but in some scenarios, we might need to declare custom css which we need to follow naming convention having prefix to identify css selectors coming from tailwind, kendo or our own.
    for example, lets say we have to declare a css selector of our own and use it with other's selectors. We'd declare something like `.v-sub-header{}` and use it as
  ```html
   <div class="v-sub-header t-bg-white k-px-0">
  ```
  notice the `v`, `t` & `k` prefix on selectors as they are prefixed after first character of organization name. `v` -> venio, `k` -> kendo and `t` -> tailwind.
- `libs/shared/assets` for images, fonts, icons

# Component Creation Guidelines

- `changeDetection` it should always set to `ChangeDetectionStrategy.OnPush` to avoid unnecessary change detection. for detailed explanation check [ChangeDetectionStrategy OnPush in a nutshell](https://medium.com/@matsal.dev/angular-changedetectionstrategy-onpush-in-a-nutshell-b1bd8aaeea64) & [Change Detection Strategy: OnPush](https://angular-training-guide.rangle.io/change-detection/change_detection_strategy_onpush)
- avoid having more than 100 lines of code. we can create a child component or if it is about logic of code behind, we can extract it on a separate function or on a component level service class separately.
- avoid `html` template inline logic complexity. Many developers mix the components and business logic, resulting in a complex and untidy mess that is difficult to comprehend so always try to move business logic to the code-behind or on a separate components, functions, classes.
- for more detail
  - [angular-best-practices](https://massivepixel.io/blog/angular-best-practices/)
  - [best practices for angular](https://aglowiditsolutions.com/blog/angular-best-practices/)

# Web Worker

It is always recommended to use web worker for heavy lifting tasks in the background thread. Project have already configured
the setup and is ready to be used. We are going to use [comlink web worker](https://github.com/GoogleChromeLabs/comlink)
wrapper library by `GoogleChromeLabs` to reduce additional cost of implementing basic event handler. Please navigate to this [comlink examples](https://github.com/GoogleChromeLabs/comlink#examples)

it is quite simple to use `comlink` wrapper.

Basic Example:

```ts
  // in my-task.worker.ts
  import {groupBy} from 'lodash'
  import {expose} from 'comlink'

  const myBgTaskHandler = (somePrama: type): Result => {

  const dict = groupBy(somePrama, ['someProp'])
  //... do more stuff
  }
  expose<()=> Result>(myBgTaskHandler)
```

```ts
  // in main thread file where worker file is used

  public myFn(): void{

    const myBgTaskHandler = new Worker(new URL('/my-task.worker', import.meta.url), {type: 'module'})
    
    myBgTaskHandler(someParam).then(resultFromWorker => {
      // use final result
      console.log(resultFromWorker)
    })

  }

```

for more example, navigate [comlink advance examples](https://github.com/GoogleChromeLabs/comlink/tree/main/docs/examples)

# Web Worker Structure

The following pattern should be used for worker:

```html
  <!-- assuming you're doing it inside one of the library -->
  src
  └───lib <!-- default folder -->
  │ └───worker <!-- web-worker group folder -->
  |      └───bg-task1.worker.ts <!-- worker file -->
  |      └───bg-task2.worker.ts <!-- worker file -->

  <!-- assuming you're doing it somewhere inside apps -->
  src
  └───app <!-- default folder -->
  │ └───modules, components, shared <!-- group directory of representational pages in the app -->
  │       └───some feature page <!-- group by feature directory -->
  │             └───worker <!-- web-worker group folder -->
  |                  └───bg-feature-x.worker.ts <!-- worker file -->
  |                  └───bg-feature-y.worker.ts <!-- worker file -->

```

Notice the `.worker` or `worker` files/directories which are scanned and compiled as a worker file by compiler tool and extracted as a lazy chunk.

It is discourage to import bigger external libraries or from `node_moduels` like `importScripts(node_module/index)` inside the worker context as they'll be imported and bundled everything unnecessarily resulting bigger worker file size.

If we need to import external libraries in the worker context, please look for cdn links first and use them as [comlink examples](https://github.com/GoogleChromeLabs/comlink#examples) avoid bigger worker file size.

# Bundle Analyzer

Please check what is inside the bundle after adding/using modules inside the web worker file by running `npm run analyze:vod:bundle` as you'll see something like image below where you can inspect and optimize the bundled files as needed

<img src="./libs/shared/assets/src/images/bundle-analyzer-demo.png" alt="graph demo">

# TESTS (unit/e2e/integration)

- unit (`jest` is the default test runner)
  - all test must be written following BDD (behaviour derived scenario) pattern
    - what is [BDD](https://cucumber.io/blog/bdd/bdd-vs-tdd/) to get an idea
    - we write BDD scenario in the JEST by following this pattern.

    Example:
      ```ts
      it('should show success message when submit button is clicked', () => {
        // GIVEN a submit button
        const button = myBtnFn
        const messageSpy = syp(messageFN)

        // WHEN submit button is clicked
        button.click()
      
        // THEN should show a success message
        expect(messageSpy).haveBeenCalled()
      })
      ```
    We need to focus on **GIVEN**, **WHEN** and **THEN** steps to cover an end user scenario.
  - test description must be clear and meaningful to the scenario you're covering
  - each test should cover single scenario
  - test shouldn't be complicated to read and modify
    - example of GOOD & BAD test

    ```ts
      // GOOD test
      it('should load user info when user is authenticated successfully ', () => {
        // GIVEN user authentication credentials
        const cred = {username:x,pw:y}
        const fetchUserSpy = jest.spyOn(service, 'fetch').mock

        // WHEN user is authenticated
        component.ngOnInit()
        component.form.patchValue(cred)
        component.submit()

        // THEN should load user info
        expect(component.userInfo).toBeDefined()
        expect(fetchUserSpy).haveBeenCalledWith(cred)
        expect(component.userInfo.username).toBe(cred.username)
      })

      // BAD test
      it('load user detail', () => {
        const userForm = new Form({...})
        const cred = {username:x,pw:y}
        myservice.user = jest.fn().mock
        component.ngOnInit()
        userForm.patchValue(cred)
        component.form = userForm
        component.submit()
        expect(component.userInfo).toBeDefined()
        expect(component.userInfo.username).toBe(cred.username)
      })
    ```
- e2e / integration (`cypress` is the default test runner)
  - each feature you've worked on should have end-to-end tests implemented
  - detail explanation can be found at [angular end-to-end testing](https://testing-angular.com/end-to-end-testing/)

# Available Scripts

We have listed several `cmd` in the `package.json > scripts` section. For
better understanding, navigate to [nx cli commands](https://nx.dev/reference/commands#nx-cli-commands) link

for example:

- `npm start` or `nx serve` or `ng serve`
  - it'll serve the app http://localhost:4200
- `npm run build` or `nx build` or `ng build`
  - it'll build the app including its dependents of libs (check NX documentation)
- `npm test` or `nx test` or `ng test`
  - runs test to the default project (default app)
  - to run test on all including apps, libs `nx run-many --target=test`
  - to watch the changes of test, `nx test --watch` will watch the test file on default app. if we need to watch on the
    specific lib, app we need to do as `nx run data-access-auth:test --watch` for auth lib
    inside `libs/data-access/auth` same goes to other libs/apps.
- `nx affected`
  - commands are extended by **NX** and useful for many cases like run execution on affected files only etc. for more
    details, see [affected](https://nx.dev/nx/affected#affected)

# Starting/Contributing

- Start
  - after cloning, navigate to the directory where `package.json` lives. run `npm i --verbose` to installing npm packages.
  - once the packages are installed, run on of `npm start` or `nx serve` or `ng serve` which will serves app on http://localhost:4200
  - see [available script section](#available-scripts)👆
    - 💡**TIP:** if you're using `powershell` and facing `nx` not being recognized issue, open powershell as an admin and execute `
      Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`
  - to obtain data from backend, please check on backend repo
- contribute
  - checkout to active development branch something like `v10.0.0.0`
  - always pull the latest code from the active development branch before creating feature branch out of it
  - when creating a PR (pull/merge request), depending on the feature scope, it is recommended to create sub-branch out of your feature branch to have review of specific sub feature code which will merge into your feature branch to avoid complexity of reviewing a big PR. The flow should something like
  - all steps of CI must be passed to merge on the target branch

  ```html
      master
      └───v10.0.0 
      │   └───your-big-feature-branch <!-- entire feature code review (HARD) -->
      │       └───sub-feature-1 <!--sub feature code review (EASY) -->
      |       └───sub-feature-2 <!-- sub feature code review (EASY )-->
  ```
- commits
  - please follow 👉 [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/)
  - `feat: message`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-> The new feature you're adding to a particular application
  - `fix: message`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-> A bug fix
  - `style: message`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-> Feature and updates related to styling
  - `refactor: message`&nbsp;-> Refactoring a specific section of the codebase
  - `test: message`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-> Everything related to testing
  - `docs: message`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-> Everything related to documentation
  - `chore: message`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-> Regular code maintenance ((un)install, cleanup). [ You can also use emojis 💪 👽 to represent commit types]
