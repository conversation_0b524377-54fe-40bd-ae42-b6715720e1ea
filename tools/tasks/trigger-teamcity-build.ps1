param (
    [string]$TeamCityToken,
    [string]$TeamCityServerUrl,
    [string]$BranchName
)

$headers = @{
    'Authorization' = "Bearer $TeamCityToken"
    'Content-Type' = 'application/xml'
}

# Debug information
Write-Output "TeamCityToken: $TeamCityToken"
Write-Output "TeamCityServerUrl: $TeamCityServerUrl"
Write-Output "BranchName: $BranchName"

# Create XML payloads using proper here-strings
$bodyWebMain = @"
<build personal='false' branchName='$BranchName'>
    <buildType id='MainRepository_VenioWeb_WebMain_Default'/>
</build>
"@

$bodyVenioWebWixSetup = @"
<build personal='false' branchName='$BranchName'>
    <buildType id='MainRepository_VenioWeb_VenioWebWixSetup_Default'/>
</build>
"@

# Debug information
Write-Output "Body for WebMain: $bodyWebMain"
Write-Output "Body for VenioWebWixSetup: $bodyVenioWebWixSetup"

# Trigger the first build
$responseWebMain = Invoke-WebRequest -Uri "$TeamCityServerUrl/app/rest/buildQueue" -Method Post -Headers $headers -Body $bodyWebMain -UseBasicParsing
Write-Output "Response for WebMain: $($responseWebMain.StatusCode)"

# Wait for 10 seconds
Start-Sleep -Seconds 10

# Trigger the second build
$responseVenioWebWixSetup = Invoke-WebRequest -Uri "$TeamCityServerUrl/app/rest/buildQueue" -Method Post -Headers $headers -Body $bodyVenioWebWixSetup -UseBasicParsing
Write-Output "Response for VenioWebWixSetup: $($responseVenioWebWixSetup.StatusCode)"
