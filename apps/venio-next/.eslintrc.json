{"extends": ["plugin:cypress/recommended", "../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["apps/venio-next/tsconfig.*?.json"]}, "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "venio", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "venio", "style": "kebab-case"}]}}, {"files": ["*.html"], "extends": ["plugin:@nx/angular-template", "prettier"], "rules": {"@angular-eslint/template/cyclomatic-complexity": ["warn", {"maxComplexity": 50}], "@angular-eslint/template/conditional-complexity": ["error", {"maxComplexity": 50}], "@angular-eslint/template/no-duplicate-attributes": "error"}}, {"files": ["*.cy.{ts,js,tsx,jsx}", "cypress/**/*.{ts,js,tsx,jsx}"], "rules": {}}]}