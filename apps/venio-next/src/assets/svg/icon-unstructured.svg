<svg xmlns="http://www.w3.org/2000/svg" width="128" height="125" viewBox="0 0 128 125">
  <g id="Group_565" data-name="Group 565" transform="translate(-530 -83)">
    <g id="Group_564" data-name="Group 564" transform="translate(530.322 83)">
      <path id="Path_510" data-name="Path 510" d="M-2853.909,963v3.9h-14.935v14.935h-4.056V963Z" transform="translate(2872.9 -956.566)"/>
      <rect id="Rectangle_1024" data-name="Rectangle 1024" width="5" height="5" transform="translate(22.678 6)"/>
      <rect id="Rectangle_1025" data-name="Rectangle 1025" width="4" height="5" transform="translate(31.678 6)"/>
      <rect id="Rectangle_1026" data-name="Rectangle 1026" width="5" height="4" transform="translate(88.678)"/>
    </g>
    <g id="Group_561" data-name="Group 561" transform="translate(636.166 85.047)">
      <rect id="Rectangle_1027" data-name="Rectangle 1027" width="22" height="21" transform="translate(-0.166 -0.047)"/>
      <rect id="Rectangle_1028" data-name="Rectangle 1028" width="13" height="13" transform="translate(3.834 3.953)" fill="#fff"/>
      <rect id="Rectangle_1029" data-name="Rectangle 1029" width="4" height="4" transform="translate(8.834 8.953)"/>
    </g>
    <g id="Group_562" data-name="Group 562" transform="translate(559.832 127.748)">
      <rect id="Rectangle_1030" data-name="Rectangle 1030" width="4" height="4" transform="translate(0.168 76.252)"/>
      <rect id="Rectangle_1031" data-name="Rectangle 1031" width="5" height="4" transform="translate(8.168 76.252)"/>
      <rect id="Rectangle_1032" data-name="Rectangle 1032" width="4" height="4" transform="translate(17.168 76.252)"/>
      <rect id="Rectangle_1033" data-name="Rectangle 1033" width="21" height="4" transform="translate(76.168 0.252)"/>
      <rect id="Rectangle_1034" data-name="Rectangle 1034" width="21" height="4" transform="translate(76.168 8.252)"/>
      <rect id="Rectangle_1035" data-name="Rectangle 1035" width="21" height="5" transform="translate(76.168 16.252)"/>
      <rect id="Rectangle_1036" data-name="Rectangle 1036" width="21" height="4" transform="translate(76.168 25.252)"/>
      <rect id="Rectangle_1037" data-name="Rectangle 1037" width="21" height="4" transform="translate(76.168 34.252)"/>
      <rect id="Rectangle_1038" data-name="Rectangle 1038" width="59" height="4" transform="translate(38.168 42.252)"/>
      <rect id="Rectangle_1039" data-name="Rectangle 1039" width="59" height="4" transform="translate(38.168 50.252)"/>
      <rect id="Rectangle_1040" data-name="Rectangle 1040" width="59" height="4" transform="translate(38.168 59.252)"/>
      <rect id="Rectangle_1041" data-name="Rectangle 1041" width="59" height="4" transform="translate(38.168 68.252)"/>
      <rect id="Rectangle_1042" data-name="Rectangle 1042" width="34" height="4" transform="translate(38.168 76.252)"/>
    </g>
    <g id="Group_563" data-name="Group 563" transform="translate(538.774 98.208)">
      <rect id="Rectangle_1045" data-name="Rectangle 1045" width="93" height="68" transform="translate(0.226 -0.208)" fill="#e3e3e3"/>
      <rect id="Rectangle_1044" data-name="Rectangle 1044" width="85" height="60" transform="translate(4.226 3.792)" fill="#fff"/>
      <g id="Group_560" data-name="Group 560" transform="translate(8.482 8.189)">
        <rect id="Rectangle_1044-2" data-name="Rectangle 1044" width="76" height="51" transform="translate(-0.256 -0.397)" fill="#54e2e3"/>
        <path id="Path_512" data-name="Path 512" d="M-2815,1113.351l21.146-20.882,9.242,9.008,22.579-21.877,22.7,23.047v10.7Z" transform="translate(2815 -1062.461)" fill="#28cac9"/>
        <circle id="Ellipse_51" data-name="Ellipse 51" cx="7" cy="7" r="7" transform="translate(23.744 9.603)" fill="#fff"/>
      </g>
    </g>
    <g id="Group_559" data-name="Group 559" transform="translate(530 161.089)">
      <rect id="Rectangle_1043" data-name="Rectangle 1043" width="51" height="35" transform="translate(0 -0.089)" fill="#fd6626"/>
      <path id="Path_511" data-name="Path 511" d="M-2801.5,1253v8.345l8.764-4.212Z" transform="translate(2822.704 -1239.839)" fill="#fff"/>
    </g>
  </g>
</svg>
