<svg id="Layer_1" data-name="Layer 1" version="1.1" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style data-cssvars="skip" data-cssvars-job="10">
      .cls-1 {  fill-rule: initial; }
      .cls-1, .cls-2 { stroke-width: 0px; }
      .cls-3 { fill: none; stroke: rgb(255, 255, 255); stroke-miterlimit: 10; stroke-width: 1.2px; }
      .cls-2 { fill: rgb(255, 255, 255); }
    </style>
    <mask id="mask">
      <rect width="100%" height="100%" fill="white"></rect>
      <rect x="16.7" y="18.7" width="5.2" height="1" rx=".4" ry=".4" fill="black"></rect>
      <rect x="16.7" y="20" width="5.2" height="1" rx=".4" ry=".4" fill="black"></rect>
      <rect x="16.7" y="21.3" width="4.1" height="1" rx=".4" ry=".4" fill="black"></rect>
    </mask>
  </defs>
  <g>
    <path class="cls-1" d="M8.9,11.6c0-1.3,0-2.5.2-3.7h6.9c.1,1.2.2,2.4.2,3.7s0,2.5-.2,3.7h-6.9c-.1-1.2-.2-2.4-.2-3.7Z"></path>
    <path class="cls-1" d="M7.3,15.3c-.1-1.2-.2-2.4-.2-3.7s0-2.5.2-3.7H2.1c-.4,1.2-.6,2.4-.6,3.7s.2,2.5.6,3.7h5.1Z"></path>
    <path class="cls-1" d="M3,17.2h4.6c.2,1.2.5,2.3.9,3.3.2.6.5,1.2.8,1.8-2.7-.8-5-2.7-6.3-5.1Z"></path>
    <path class="cls-1" d="M9.5,17.2h6.3c-.2,1-.5,1.9-.8,2.6-.4,1-.8,1.8-1.3,2.3-.4.5-.8.6-1.1.6s-.7-.1-1.1-.6c-.4-.5-.9-1.3-1.3-2.3-.3-.8-.5-1.6-.8-2.6Z"></path>
    <path class="cls-1" d="M23.1,15.3c.4-1.2.6-2.4.6-3.7s-.2-2.5-.6-3.7h-5.1c.1,1.2.2,2.4.2,3.7s0,2.5-.2,3.7h5.1Z"></path>
    <path class="cls-1" d="M15,3.4c.3.8.5,1.6.8,2.6h-6.3c.2-1,.5-1.9.8-2.6.4-1,.8-1.8,1.3-2.3.4-.5.8-.6,1.1-.6s.7.1,1.1.6c.4.5.9,1.3,1.3,2.3Z"></path>
    <path class="cls-1" d="M17.7,6.1h4.6c-1.4-2.4-3.6-4.2-6.3-5.1.3.5.6,1.1.8,1.8.4,1,.7,2.1.9,3.3Z"></path>
    <path class="cls-1" d="M3,6.1h4.6c.2-1.2.5-2.3.9-3.3.2-.6.5-1.2.8-1.8-2.7.8-5,2.7-6.3,5.1Z"></path>
  </g>
  <path class="cls-1" d="M17.1,17h4.7s1.5-.1,1.5,1.8v2.9s0,1.6-1.6,1.6h-2.1l-1,.9s-1.4,1.5-1.4,0v-1s-1.7.3-1.6-1.8v-2.5s-.2-2,1.6-2Z" mask="url(#mask)"></path>
</svg>