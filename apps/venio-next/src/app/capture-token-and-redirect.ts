import {
  APP_INITIALIZER,
  EnvironmentProviders,
  Provider,
  PLATFORM_ID,
} from '@angular/core'
import { NavigationEnd, Router } from '@angular/router'
import { Location, isPlatformBrowser } from '@angular/common'
import { filter, take } from 'rxjs'
import { AuthStorageKeys } from '@venio/data-access/auth'
import { Message } from '@venio/data-access/iframe-messenger'

/**
 * Listens for a 'message' event on the window object, extracts tokens from the event payload,
 * and stores them in local storage. Specifically, it looks for an event with a `MICRO_APP_DATA_CHANGE` type,
 * and a payload indicating a `TOKEN_UPDATE`. Upon receiving such an event, it extracts the `accessToken`
 * and `refreshToken` from the payload, removes any double quotes from these token strings, and stores them
 * in local storage under predefined keys.
 *
 * This function is designed to be called with no arguments to set up the event listener.
 *
 * @function
 * @returns {void} Returns nothing.
 */
function setTokenInLocalStorage(): void {
  if (typeof window === 'undefined') return // Early return if running in a non-browser environment

  window.addEventListener('message', (event) => {
    // Check for the specific event type we are interested in
    if (event.data.type === 'MICRO_APP_DATA_CHANGE') {
      /** Destructuring assignment to extract iframeIdentity and payload from event data */
      const { iframeIdentity, payload } = event.data as Message

      /**
       * Determine whether the payload is an array or an object, and find the message
       * indicating a token update if it's an array.
       */
      const message = Array.isArray(payload)
        ? payload.find((c) => c.type === 'TOKEN_UPDATE')
        : payload

      // Validate necessary conditions before proceeding
      if (
        iframeIdentity === 'VENIO_NEXT' &&
        message?.type === 'TOKEN_UPDATE' &&
        message?.content?.['accessToken']
      ) {
        // Remove any double quotes from the tokens
        const accessToken = message?.content?.['accessToken'].replace(/"/g, '')
        const refreshToken = message.content?.['refreshToken'].replace(/"/g, '')

        // Store the sanitized tokens in local storage
        localStorage.setItem(AuthStorageKeys.AccessToken, accessToken)
        localStorage.setItem(AuthStorageKeys.RefreshToken, refreshToken)
      }
    }
  })
}

/**
 * Function to initialize authentication tokens.
 * This function is intended to be used with Angular APP_INITIALIZER.
 * @todo
 * NOTE: This needs to be removed once everything is covered on the Venio-Next APP.
 *
 * @param {Router} router - Angular Router instance.
 * @param {Location} loc - Angular Location instance.
 * @param {Object} platformId - Angular PLATFORM_ID token to identify the platform.
 *
 * @returns {Promise} A function that returns a Promise which resolves when the initialization is complete.
 */
export function authenticationTokenInitializer(
  router: Router,
  loc: Location,
  platformId: object // Removed the private keyword and @Inject
): () => Promise<unknown> {
  setTokenInLocalStorage()

  /**
   * Subscribe to router events to replace the URL state when needed.
   */
  if (isPlatformBrowser(platformId)) {
    router.events
      .pipe(
        filter((e) => e instanceof NavigationEnd),
        take(1)
      )
      .subscribe(() => {
        const url = new URL(window.location.href)
        const params = url.searchParams
        const hasAccessToken = params.get('accessToken')
        if (!hasAccessToken) return

        params.delete('accessToken')
        params.delete('refreshToken')
        // Finally, replace the URL state without token information.
        // TODO: only needed when we have redirection feature from different origins
        // uncomment this line when we have redirection feature from different origins
        // CAUTION: if you redirect the path with where the app is hosted on sub dir, the
        // path duplication issue arise and should handle.
        // e.g. app dir, /app1/app2/app3 and redirect to /app1/app2/app3//app1/app2/app3 notice the duplication
        // please handle this and remove this message or update it in near future.
        //loc.replaceState(url.pathname + '?' + params.toString())
      })
  }

  /**
   * The initializer function.
   *
   * @returns {Promise<unknown>} A Promise that resolves when the authentication tokens are initialized.
   */
  return (): Promise<unknown> => {
    return new Promise<void>((resolve) => {
      if (isPlatformBrowser(platformId)) {
        // Parse the current URL to extract query parameters.
        const url = new URL(window.location.href)
        const params = url.searchParams

        // Retrieve the access and refresh tokens from the query parameters.
        const accessToken = params.get('accessToken')
        const refreshToken = params.get('refreshToken')

        // Store the tokens in local storage.
        if (accessToken) {
          localStorage.setItem(AuthStorageKeys.AccessToken, accessToken)
        }
        if (refreshToken) {
          localStorage.setItem(AuthStorageKeys.RefreshToken, refreshToken)
        }
      }
      // Resolve the Promise to indicate that initialization is complete,
      // allowing the Angular application to proceed with bootstrapping.
      resolve()
    })
  }
}

export const CaptureTokenAndRedirect: Provider | EnvironmentProviders = {
  provide: APP_INITIALIZER,
  useFactory: authenticationTokenInitializer,
  deps: [Router, Location, PLATFORM_ID], // Added PLATFORM_ID to dependencies
  multi: true,
}
