import Dexie from 'dexie'

export class IndexedDbHandler extends <PERSON>ie {
  public HtmlParts: Dexie.Table<
    {
      ProjectId: string
      FileId: string
      PartIndex: number
      PartId: string
      UpdatedDate: Date
    },
    [string, string, number]
  >

  public FulltextParts: Dexie.Table<
    { FileId: string; PartIndex: number; PartId: string },
    [string, number]
  >

  public Spreadsheet: Dexie.Table<{ ProjectId: string; FileId: string }, string>

  constructor() {
    super('VenioDB')
    this.version(6).stores({
      HtmlParts: '[ProjectId+FileId+PartIndex], PartId, UpdatedDate',
      FulltextParts: '[FileId+PartIndex], PartId',
      Spreadsheet: '[ProjectId+FileId]',
    })

    this.HtmlParts = this.table('HtmlParts')
    this.FulltextParts = this.table('FulltextParts')
    this.Spreadsheet = this.table('Spreadsheet')

    this.open().catch((err) => {
      // eslint-disable-next-line no-console
      console.warn('Failed to open db:', err)
    })
  }
}
