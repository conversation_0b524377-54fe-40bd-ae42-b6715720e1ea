import { Injectable } from '@angular/core'
import { Subject } from 'rxjs'
import { Type } from '@progress/kendo-angular-notification'

@Injectable({ providedIn: 'root' })
export class NotificationTriggerService {
  private notificationTrigger = new Subject<{
    content: string
    type: Type
    hideAfter: number
    width: number
  }>()

  // Observable stream
  public notificationTrigger$ = this.notificationTrigger.asObservable()

  public triggerNotification(
    content: string,
    type: Type,
    hideAfter = 3500,
    width = 260
  ): void {
    this.notificationTrigger.next({ content, type, hideAfter, width })
  }
}
