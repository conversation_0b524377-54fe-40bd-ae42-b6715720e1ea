import {
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http'
import {
  APP_INITIALIZER,
  inject,
  Injector,
  isDevMode,
  NgModule,
  PLATFORM_ID,
} from '@angular/core'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import { RouterModule } from '@angular/router'
import { AuthModule } from '@venio/data-access/auth'
import { AppRoutingModule } from './app-routing.module'
import { AppComponent } from './app.component'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { RootStoreModule } from './store/root-store.module'
import { DataAccessReviewModule } from '@venio/data-access/review'
import { ServiceWorkerModule } from '@angular/service-worker'
import { GoldenLayoutContainerInjectionToken } from '@venio/golden-layout'
import { ComponentContainer } from 'golden-layout'
import {
  AppIdentitiesTypes,
  IframeMessengerFacade,
  IframeMessengerModule,
  Message,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { FeatureFlagModule } from '@venio/data-access/feature-flag'
import {
  HashLocationStrategy,
  isPlatformBrowser,
  LocationStrategy,
} from '@angular/common'
import { DialogModule } from '@progress/kendo-angular-dialog'
import { SvgRequestInterceptor } from '@venio/feature/shared/directives'
import { ControlSettingsModule } from '@venio/data-access/control-settings'
import { StoreRouterConnectingModule } from '@ngrx/router-store'
import { environment } from '@venio/shared/environments'
import { BreadcrumbModule } from '@venio/data-access/breadcrumbs'
import { ElasticApmVenioModule } from './elastic-apm-venio.module'

export function postMessageListenerControlledByParentWindow(
  iframeMessengerFacade: IframeMessengerFacade
): () => Promise<unknown> {
  const platformId = inject(PLATFORM_ID)

  // Check if running in the browser
  if (!isPlatformBrowser(platformId)) {
    return () => Promise.resolve(null)
  }

  // listen to messages of postMessage as soon as the app is initialized,
  // so we can store the initial state from the main app if any
  window.addEventListener('message', (event: MessageEvent): void => {
    const isMicroAppDataChange =
      (event.data as Message).type === 'MICRO_APP_DATA_CHANGE'
    // if the message was to be notified to the parent window, we don't need to do anything
    const wasNotificationForParent =
      (event.data as Message).eventTriggeredFor === 'PARENT_WINDOW'

    // if it isn't a micro app data change, we don't need to do anything
    if (!isMicroAppDataChange || wasNotificationForParent) return

    iframeMessengerFacade.storeMessageContent((event.data as Message).payload)

    // since we reach this far, it means that the iframe is loaded as a micro app
    iframeMessengerFacade.storeLoadedAsMicroApp(isMicroAppDataChange)
  })
  return () => Promise.resolve(null)
}

@NgModule({
  declarations: [AppComponent],
  bootstrap: [AppComponent],
  imports: [
    BrowserAnimationsModule,
    RouterModule,
    AppRoutingModule,
    AuthModule,
    ButtonsModule,
    RootStoreModule,
    DataAccessReviewModule,
    FeatureFlagModule,
    DialogModule,
    ServiceWorkerModule.register('ngsw-worker.js', {
      enabled: !isDevMode(),
      // Register the ServiceWorker as soon as the application is stable
      // or after 30 seconds (whichever comes first).
      registrationStrategy: 'registerWhenStable:30000',
    }),
    IframeMessengerModule.forRoot({
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      origin: environment.allowedOrigin,
    }),
    StoreRouterConnectingModule.forRoot(),
    ControlSettingsModule,
    BreadcrumbModule,
    ElasticApmVenioModule,
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: postMessageListenerControlledByParentWindow,
      deps: [IframeMessengerFacade, Injector],
      multi: true,
    },
    { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
    {
      provide: GoldenLayoutContainerInjectionToken,
      useValue: ComponentContainer,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: SvgRequestInterceptor,
      multi: true,
    },
    { provide: LocationStrategy, useClass: HashLocationStrategy },
    provideHttpClient(withInterceptorsFromDi()),
  ],
})
export class AppModule {}
