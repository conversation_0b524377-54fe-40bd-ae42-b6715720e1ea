import { Action } from '@ngrx/store'

import * as RootActions from './root.actions'
import { RootEntity } from './root.models'
import { initialRootState, rootReducer, RootState } from './root.reducer'

describe('Root Reducer', () => {
  const createRootEntity = (id: string, name = ''): RootEntity => ({
    id,
    name: name || `name-${id}`,
  })

  describe('valid Root actions', () => {
    it('loadRootSuccess should return the list of known Root', () => {
      const root = [
        createRootEntity('PRODUCT-AAA'),
        createRootEntity('PRODUCT-zzz'),
      ]
      const action = RootActions.loadRootSuccess({ root })

      const result: RootState = rootReducer(initialRootState, action)

      expect(result.loaded).toBe(true)
      expect(result.ids).toHaveLength(2)
    })
  })

  describe('unknown action', () => {
    it('should return the previous state', () => {
      const action = {} as Action

      const result = rootReducer(initialRootState, action)

      expect(result).toBe(initialRootState)
    })
  })
})
