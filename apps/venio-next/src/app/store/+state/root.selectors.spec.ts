import { RootEntity } from './root.models'
import { initialRootState, rootAdapter, RootPartialState } from './root.reducer'
import * as RootSelectors from './root.selectors'

describe('Root Selectors', () => {
  const error = 'No Error Available'
  const getRootId = (it: RootEntity): unknown => it.id
  const createRootEntity = (id: string, name = ''): RootEntity =>
    ({
      id,
      name: name || `name-${id}`,
    } as RootEntity)

  let state: RootPartialState

  beforeEach(() => {
    state = {
      root: rootAdapter.setAll(
        [
          createRootEntity('PRODUCT-AAA'),
          createRootEntity('PRODUCT-BBB'),
          createRootEntity('PRODUCT-CCC'),
        ],
        {
          ...initialRootState,
          selectedId: 'PRODUCT-BBB',
          error: error,
          loaded: true,
        }
      ),
    }
  })

  describe('Root Selectors', () => {
    it('getAllRoot() should return the list of Root', () => {
      const results = RootSelectors.getAllRoot(state)
      const selId = getRootId(results[1])

      expect(results).toHaveLength(3)
      expect(selId).toBe('PRODUCT-BBB')
    })

    it('getSelected() should return the selected Entity', () => {
      const result = RootSelectors.getSelected(state)
      const selId = getRootId(result)

      expect(selId).toBe('PRODUCT-BBB')
    })

    it('getRootLoaded() should return the current "loaded" status', () => {
      const result = RootSelectors.getRootLoaded(state)

      expect(result).toBe(true)
    })

    it('getRootError() should return the current "error" state', () => {
      const result = RootSelectors.getRootError(state)

      expect(result).toBe(error)
    })
  })
})
