import { NgModule } from '@angular/core'
import { TestBed } from '@angular/core/testing'
import { EffectsModule } from '@ngrx/effects'
import { Store, StoreModule } from '@ngrx/store'

import * as RootActions from './root.actions'
import { RootEffects } from './root.effects'
import { RootFacade } from './root.facade'
import { RootEntity } from './root.models'
import { rootReducer, RootState, ROOT_FEATURE_KEY } from './root.reducer'
import { firstValueFrom } from 'rxjs'

interface TestSchema {
  root: RootState
}

describe('RootFacade', () => {
  let facade: RootFacade
  let store: Store<TestSchema>
  const createRootEntity = (id: string, name = ''): RootEntity => ({
    id,
    name: name || `name-${id}`,
  })

  describe('used in NgModule', () => {
    beforeEach(() => {
      @NgModule({
        imports: [
          StoreModule.forFeature(ROOT_FEATURE_KEY, rootReducer),
          EffectsModule.forFeature([RootEffects]),
        ],
        providers: [RootFacade],
      })
      class CustomFeatureModule {}

      @NgModule({
        imports: [
          StoreModule.forRoot({}),
          EffectsModule.forRoot([]),
          CustomFeatureModule,
        ],
      })
      class RootModule {}
      TestBed.configureTestingModule({ imports: [RootModule] })

      store = TestBed.inject(Store)
      facade = TestBed.inject(RootFacade)
    })

    /**
     * The initially generated facade::loadAll() returns empty array
     */
    it('loadAll() should return empty list with loaded == true', async () => {
      let list = await firstValueFrom(facade.allRoot$)
      let isLoaded = await firstValueFrom(facade.loaded$)

      expect(list).toHaveLength(0)
      expect(isLoaded).toBe(false)

      facade.init()

      list = await firstValueFrom(facade.allRoot$)
      isLoaded = await firstValueFrom(facade.loaded$)

      expect(list).toHaveLength(0)
      expect(isLoaded).toBe(true)
    })

    /**
     * Use `loadRootSuccess` to manually update list
     */
    it('allRoot$ should return the loaded list; and loaded flag == true', async () => {
      let list = await firstValueFrom(facade.allRoot$)
      let isLoaded = await firstValueFrom(facade.loaded$)

      expect(list).toHaveLength(0)
      expect(isLoaded).toBe(false)

      store.dispatch(
        RootActions.loadRootSuccess({
          root: [createRootEntity('AAA'), createRootEntity('BBB')],
        })
      )

      list = await firstValueFrom(facade.allRoot$)
      isLoaded = await firstValueFrom(facade.loaded$)

      expect(list).toHaveLength(2)
      expect(isLoaded).toBe(true)
    })
  })
})
