<div class="t-bg-white t-block t-w-full t-h-full">
  @defer {
  <venio-document-toolbar-container *ngIf="!reviewSetState.isBatchReview()" />

  <venio-document-table />

  <venio-document-edit-container />

  <venio-edai-container />
  }

  <ng-container
    *ngComponentOutlet="exportToFileContainer | async"></ng-container>

  <ng-container
    *ngComponentOutlet="deleteDocumentContainer | async"></ng-container>

  <ng-container *ngComponentOutlet="folderingContainer | async"></ng-container>

  <ng-container
    *ngComponentOutlet="tallyDocumentContainer | async"></ng-container>

  <ng-container
    *ngComponentOutlet="bulkDocumentTagCodingContainer | async"></ng-container>

  <ng-container
    *ngComponentOutlet="printDownloadDialogContainer | async"></ng-container>

  <ng-container
    *ngComponentOutlet="convertDocumentContainer | async"></ng-container>

  <ng-container *ngComponentOutlet="bulkFolderContainer | async"></ng-container>
  <ng-container *ngComponentOutlet="saveSearchContainer | async"></ng-container>
  <ng-container
    *ngComponentOutlet="replaceFieldValueContainer | async"></ng-container>
  <ng-container
    *ngComponentOutlet="documentShareContainer | async"></ng-container>
  <ng-container *ngComponentOutlet="switchViewContainer | async"></ng-container>
  <ng-container
    *ngComponentOutlet="tagAllInclusiveContainer | async"></ng-container>
  <ng-container
    *ngComponentOutlet="showInclusiveContainer | async"></ng-container>
  <ng-container *ngComponentOutlet="productionContainer | async"></ng-container>
  <ng-container *ngComponentOutlet="moveToParent | async"></ng-container>

  <ng-container
    *ngComponentOutlet="responsivePstContainer | async"></ng-container>
  <ng-container
    *ngComponentOutlet="bulkRedactionContainer | async"></ng-container>

  <ng-container
    *ngComponentOutlet="rsmfCreationContainer | async"></ng-container>
  <ng-container
    *ngComponentOutlet="entityExtractionContainer | async"></ng-container>

  <ng-container
    *ngComponentOutlet="sendToAnalyzeContainer | async"></ng-container>
</div>
