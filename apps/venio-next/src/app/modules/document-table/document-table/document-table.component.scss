@layer {
  :host {
    @apply t-inline-block t-relative t-h-full t-w-full;
  }
}

.t-bg-gray-100-temp {
  background-color: rgb(243 244 246);
}

.t-border-gray-300-temp {
  border-color: rgb(209 213 219);
}

.generated-email {
  color: #bcb62f;
  filter: brightness(0) saturate(100%) invert(70%) sepia(80%) saturate(406%)
    hue-rotate(12deg) brightness(88%) contrast(88%);
}

.inclusive-email {
  color: #28a745;
  filter: brightness(0) saturate(100%) invert(52%) sepia(33%) saturate(1002%)
    hue-rotate(82deg) brightness(94%) contrast(90%);
}

.missing-email {
  color: #dc3545;
  filter: brightness(0) saturate(100%) invert(24%) sepia(78%) saturate(2029%)
    hue-rotate(332deg) brightness(97%) contrast(94%);
}

.t-text-blue-900-temp {
  color: rgb(30 58 138);
}
