import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  effect,
  EventEmitter,
  inject,
  Input,
  OnDestroy,
  Output,
  signal,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { InputsModule, TextAreaComponent } from '@progress/kendo-angular-inputs'
import { AbstractControl, FormGroup, ReactiveFormsModule } from '@angular/forms'
import {
  DocumentsService,
  SearchDupOption,
  SearchFacade,
  StartupsFacade,
  UserRights,
} from '@venio/data-access/review'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { SVGIconModule } from '@progress/kendo-angular-icons'
import {
  pencilIcon,
  xIcon,
  warningTriangleIcon,
} from '@progress/kendo-svg-icons'
import { PopoverModule, TooltipModule } from '@progress/kendo-angular-tooltip'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { debounceTime, filter, Subject, takeUntil, throwError } from 'rxjs'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { ActivatedRoute } from '@angular/router'
import { ValidatorService } from '@venio/data-access/common'
import { HttpErrorResponse } from '@angular/common/http'
import { NotificationDialogComponent } from '@venio/feature/notification'
import { catchError, take } from 'rxjs/operators'
import { DialogService } from '@progress/kendo-angular-dialog'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { environment } from '@venio/shared/environments'
import { toSignal } from '@angular/core/rxjs-interop'
type ControlActionTypes =
  | 'SEARCH'
  | 'CLEAR_INPUT'
  | 'RESET_SEARCH'
  | 'LAUNCH_AI_SEARCH'
@Component({
  selector: 'venio-document-search-control',
  standalone: true,
  imports: [
    CommonModule,
    InputsModule,
    ReactiveFormsModule,
    ButtonsModule,
    SvgLoaderDirective,
    SVGIconModule,
    TooltipModule,
    PopoverModule,
    IndicatorsModule,
  ],
  templateUrl: './document-search-control.component.html',
  styleUrls: ['./document-search-control.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentSearchControlComponent
  implements AfterViewInit, OnDestroy
{
  private readonly toDestroy$ = new Subject<void>()

  private activatedRoute = inject(ActivatedRoute)

  private dialogService = inject(DialogService)

  private validatorService = inject(ValidatorService)

  private documentsService = inject(DocumentsService)

  public isSyntaxValidating = signal(false)

  @ViewChild(TextAreaComponent, { static: true })
  private textAreaComponent: TextAreaComponent

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  @Input({ required: true })
  public searchFormGroup: FormGroup

  @Output()
  public readonly actionClicked = new EventEmitter<ControlActionTypes>()

  public isSearchLoading = this.searchFacade.getIsSearchLoading$

  public clearIcon = xIcon

  public pencilIcon = pencilIcon

  public validationWarningIcon = warningTriangleIcon

  public get searchExpressionControl(): AbstractControl {
    return this.searchFormGroup?.get('searchExpression')
  }

  public get includePCControl(): AbstractControl {
    return this.searchFormGroup?.get('includePC')
  }

  public get searchDuplicateOptionControl(): AbstractControl {
    return this.searchFormGroup?.get('searchDuplicateOption')
  }

  public isAiSearchEnabled = signal(environment.aiSearchEnabled)

  public isAllowedToSearch = toSignal(
    this.startupsFacade.hasGroupRight$(UserRights.ALLOW_SEARCH)
  )

  constructor(
    private searchFacade: SearchFacade,
    private changeDetectorRef: ChangeDetectorRef,
    private startupsFacade: StartupsFacade
  ) {
    effect(() => {
      const disableSearch = !this.isAllowedToSearch()
      this.#disableSearchControlsIfNoSearchRights(disableSearch)
    })
  }

  public ngAfterViewInit(): void {
    this.textAreaComponent.focus()
    this.#propagateChanges()
    this.#setSearchExpressionControlValue()
    this.#handleIncludePCValueChanges()
    this.#handleSearchDupOptionValueChanges()
    this.#setIncludePCControlValue()
    this.#setSearchDuplicateOptionControlValue()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Clears the selected Document
   * @returns {void}
   */
  #clearDocumentSelection(): void {
    this.documentsService.resetDocumentSelection$.next()
  }

  public actionClick(action: ControlActionTypes): void {
    if (action !== 'LAUNCH_AI_SEARCH') {
      this.#clearDocumentSelection()
    }
    if (action === 'SEARCH') {
      this.#validateSyntaxAndNotify()
      return
    }

    this.actionClicked.emit(action)
  }

  public keypressAction(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault()
      // if the syntax is validating, we don't want to trigger another validation
      if (!this.isSyntaxValidating()) {
        this.#clearDocumentSelection()
        this.#validateSyntaxAndNotify()
      }
    }
  }

  public handlePaste(event: ClipboardEvent): void {
    const expression = event.clipboardData.getData('text/plain')
    const splits = expression.trim().split(/\r\n|\r|\n/)

    // At least more than one valid element should be exists.
    if (!splits.filter((t) => t.trim())?.[1]) return

    event.preventDefault()

    // perform a cleanup of the expression if it is a multiline query
    const normalizedQuery = splits
      .filter(Boolean)
      .map((line) => {
        line = line.trim()

        if (!(line.startsWith('"') && line.endsWith('"'))) {
          // remove all partial quotes to avoid breaking the query
          line = line.replace(/"/g, '')
        }
        // Check if the line contains spaces, so we wrap them quotes
        return line.match(/\s+/g) ? `"${line}"` : line
      })
      .join(' OR ')

    if (normalizedQuery) {
      this.searchExpressionControl.setValue(`(${normalizedQuery})`)
      this.searchExpressionControl.updateValueAndValidity()
    }
  }

  #disableSearchControlsIfNoSearchRights(disable: boolean): void {
    if (disable) {
      this.searchExpressionControl.disable()
      this.includePCControl.disable()
      this.searchDuplicateOptionControl.disable()
    } else {
      this.searchExpressionControl.enable()
      this.includePCControl.enable()
      this.searchDuplicateOptionControl.enable()
    }
  }

  /**
   * This function shows a notification message.
   * @param {string} content The content of the notification message.
   * @returns {void}
   */
  #showNotificationMessage(content = ''): void {
    const notificationDialogRef = this.dialogService.open({
      content: NotificationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
      width: '35rem',
    })

    // Set the dialog input
    this.#setDialogInput(notificationDialogRef.content.instance, content)

    notificationDialogRef.result
      .pipe(
        filter((result) => typeof result === 'boolean' && result),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        notificationDialogRef.close()
      })
  }

  /**
   * This function sets the input for the dialog.
   * @param instance The instance of the NotificationDialogComponent.
   * @param content The content of the notification message.
   */

  #setDialogInput(instance: NotificationDialogComponent, content = ''): void {
    // Set the title of the dialog
    instance.title = 'VenioOne OnDemand'
    // Set the message of the dialog
    instance.message = content
  }

  #validateSyntaxAndNotify(): void {
    const searchExpression = this.searchExpressionControl.value?.trim()
    if (!searchExpression) {
      return
    }

    this.isSyntaxValidating.set(true)
    this.validatorService
      .validateSearchExpression({
        projectId: this.projectId,
        searchExpression,
        userType: 'INTERNAL',
      })
      .pipe(
        catchError((error: unknown) => {
          this.isSyntaxValidating.set(false)
          const response = (error as HttpErrorResponse).error as ResponseModel
          this.#showNotificationMessage(response?.message)
          return throwError(() => response)
        }),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response) => {
        this.isSyntaxValidating.set(false)
        if (response.data) {
          this.actionClicked.emit('SEARCH')
          this.searchFacade.setSearchExpression(null) // clear search expression
        } else {
          this.#showNotificationMessage(response.message)
        }
      })
  }

  /**
   * using form-group as an input and updating control value only seems
   * not triggering the update to the kendo text area component, so we need
   * to tell the lifecycle to update the view.
   * @return {void}
   */
  #propagateChanges(): void {
    this.searchExpressionControl.valueChanges
      .pipe(debounceTime(100), takeUntil(this.toDestroy$))
      .subscribe(() => {
        this.changeDetectorRef.markForCheck()
      })
  }

  #setSearchExpressionControlValue(): void {
    this.searchFacade.getSearchExpression$
      .pipe(
        filter((searchExpression) => !!searchExpression),
        takeUntil(this.toDestroy$)
      )
      .subscribe((searchExpression) =>
        this.searchExpressionControl.setValue(searchExpression)
      )
  }

  #handleIncludePCValueChanges(): void {
    this.includePCControl?.valueChanges
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((includePC: boolean) => {
        this.searchFacade.setIncludePC(includePC)
      })
  }

  #handleSearchDupOptionValueChanges(): void {
    this.searchDuplicateOptionControl?.valueChanges
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((searchDuplicateOption) => {
        this.searchFacade.setSearchDupOption(searchDuplicateOption)
      })
  }

  #setIncludePCControlValue(): void {
    this.searchFacade.getIncludePC$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((includePC) => this.includePCControl.setValue(includePC))
  }

  #setSearchDuplicateOptionControlValue(): void {
    this.searchFacade.getSearchDupOption$
      .pipe(
        filter(
          (searchDupOption) =>
            !!searchDupOption ||
            searchDupOption === SearchDupOption.HIDE_ALL_DUPS_DYNAMIC
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((searchDupOption) =>
        this.searchDuplicateOptionControl.setValue(searchDupOption)
      )
  }
}
