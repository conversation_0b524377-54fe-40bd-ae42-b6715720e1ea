import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { DialogRef } from '@progress/kendo-angular-dialog'

@Component({
  selector: 'venio-document-advanced-search-container',
  standalone: true,
  imports: [CommonModule, ButtonModule],
  templateUrl: './document-advanced-search-container.component.html',
  styleUrls: ['./document-advanced-search-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentAdvancedSearchContainerComponent {
  constructor(private dialogRef: DialogRef) {}

  public close(): void {
    this.dialogRef.close()
  }
}
