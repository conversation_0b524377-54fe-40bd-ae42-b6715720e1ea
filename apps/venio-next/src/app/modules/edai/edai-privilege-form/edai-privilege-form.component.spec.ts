import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing'
import { EdaiPrivilegeFormComponent } from './edai-privilege-form.component'
import { createJobFormGroup } from '../mocks/form-group.mock'
import { PrivilegeTypes } from '@venio/data-access/ai'
import { FormArray, FormGroup } from '@angular/forms'

describe('EdaiPrivilegeFormComponent', () => {
  let component: EdaiPrivilegeFormComponent
  let fixture: ComponentFixture<EdaiPrivilegeFormComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EdaiPrivilegeFormComponent],
    }).compileComponents()

    fixture = TestBed.createComponent(EdaiPrivilegeFormComponent)
    component = fixture.componentInstance

    const mockFormGroup = createJobFormGroup()
    fixture.componentRef.setInput('edaiFormGroup', mockFormGroup)

    fixture.detectChanges()
  })

  it('should successfully create this privilege form section', () => {
    // GIVEN the component is set up
    // THEN it should exist
    expect(component).toBeTruthy()
  })
  it('should have a default value for either attorney or work product error message if no user changes are made', fakeAsync(() => {
    // GIVEN no interactions
    // WHEN letting time pass
    tick()

    // THEN attorneyOrProductSelected should remain true by default
    expect(component.attorneyOrProductSelected()).toBe(true)
  }))
  it('should show error message if attorney and product controls are both touched/dirty but have no value', () => {
    // GIVEN the user touches both attorney client and work product checkboxes without checking them
    const privTypesArray = component
      .edaiFormGroup()
      .get('privilegeJobModel.privilegeTypes') as FormArray

    const attorneyClientGroup = privTypesArray.controls.at(0)
    const workProductGroup = privTypesArray.controls.at(1)

    attorneyClientGroup?.get('name')?.markAsDirty()
    attorneyClientGroup?.get('name')?.markAsTouched()
    workProductGroup?.get('name')?.markAsDirty()
    workProductGroup?.get('name')?.markAsTouched()

    // WHEN no boxes are checked (meaning name = false or undefined)
    // THEN attorneyOrProductSelected should become false
    expect(component.attorneyOrProductSelected()).toBe(true)
  })
  it('should remain attorney or product selected if user checks either attorney or product name', fakeAsync(() => {
    // GIVEN user toggles attorney client control
    const privTypesArray = component
      .edaiFormGroup()
      .get('privilegeJobModel.privilegeTypes') as FormArray
    const attorneyClientGroup = privTypesArray.controls.find(
      (c) => c.get('privilegeType')?.value === PrivilegeTypes.AttorneyClient
    )
    attorneyClientGroup?.get('name')?.setValue(true)

    fixture.detectChanges()
    tick()

    // THEN attorneyOrProductSelected is true
    expect(component.attorneyOrProductSelected()).toBe(true)
  }))
  it('should produce an array of boolean values for custom type definition validation', fakeAsync(() => {
    // GIVEN custom types are present
    tick()
    const validationResult = component.customTypesDefinitionValidation()
    // THEN it should be an array with length matching the number of custom types
    // For example, by default they might be `[true, true]` if empty or valid
    expect(Array.isArray(validationResult)).toBe(true)
    expect(validationResult).toHaveLength(2)
  }))
  it('should mark a custom type as invalid if name is non-empty but definition is empty', fakeAsync(() => {
    // GIVEN user typed a custom name
    const privTypesArray = component
      .edaiFormGroup()
      .get('privilegeJobModel.privilegeTypes') as FormArray
    const customType1Group = privTypesArray.controls.at(2)

    customType1Group?.get('name')?.setValue('Some Name')
    customType1Group?.get('description')?.setValue('')
    customType1Group?.get('description').markAsTouched()
    customType1Group?.get('description').markAsDirty()
    customType1Group?.get('description').updateValueAndValidity()
    fixture.detectChanges()
    tick()

    // WHEN we check customTypesDefinitionValidation
    const validityArray = component.customTypesDefinitionValidation()
    // THEN the first custom type should be false if description is empty
    // Usually it's array [true/false], we find the index for CustomType1
    const indexCustom1 = component['customTypes'].indexOf(
      PrivilegeTypes.CustomType1
    )
    expect(validityArray[indexCustom1]).toBe(false)
  }))
  it('should mark a custom type as valid if name is empty, no matter the description', fakeAsync(() => {
    // GIVEN an empty name and any description
    const privTypesArray = component
      .edaiFormGroup()
      .get('privilegeJobModel.privilegeTypes') as FormArray
    const customType2Group = privTypesArray.controls.find(
      (ctrl) => ctrl.value.privilegeType === PrivilegeTypes.CustomType2
    )
    customType2Group?.get('name')?.setValue('')
    customType2Group?.get('description')?.setValue('Random Description')
    fixture.detectChanges()
    tick()

    // WHEN we check customTypesDefinitionValidation
    const validityArray = component.customTypesDefinitionValidation()
    const indexCustom2 = component['customTypes'].indexOf(
      PrivilegeTypes.CustomType2
    )
    // THEN it should be true because name is empty => desc is not required
    expect(validityArray[indexCustom2]).toBe(true)
  }))
  it('should mark a custom type as valid if name and description are both non-empty', fakeAsync(() => {
    // GIVEN the user enters both name and description
    const privTypesArray = component
      .edaiFormGroup()
      .get('privilegeJobModel.privilegeTypes') as FormArray
    const customType1Group = privTypesArray.controls.find(
      (ctrl) => ctrl.value.privilegeType === PrivilegeTypes.CustomType1
    )

    customType1Group?.get('name')?.setValue('My Custom Type')
    customType1Group?.get('description')?.setValue('Detailed Explanation')
    fixture.detectChanges()
    tick()

    // WHEN we check the validation array
    const validityArray = component.customTypesDefinitionValidation()
    const indexCustom1 = component['customTypes'].indexOf(
      PrivilegeTypes.CustomType1
    )

    // THEN the corresponding entry should be true
    expect(validityArray[indexCustom1]).toBe(true)
  }))
  it('should treat a missing custom type group as valid by default', fakeAsync(() => {
    // GIVEN we remove CustomType1 from the form array
    const privilegeJobModel = component
      .edaiFormGroup()
      .get('privilegeJobModel') as FormGroup
    const privTypesArray = privilegeJobModel.get('privilegeTypes') as FormArray
    const index = privTypesArray.controls.findIndex(
      (ctrl) => ctrl.value.privilegeType === PrivilegeTypes.CustomType1
    )
    if (index !== -1) {
      privTypesArray.removeAt(index)
    }

    fixture.detectChanges()
    tick()

    // WHEN customTypesDefinitionValidation is checked
    const validityArray = component.customTypesDefinitionValidation()
    // THEN the missing type is effectively considered valid
    expect(validityArray).toContain(true)
  }))
  it('should continue treating both custom types as valid if the user never interacts with them', fakeAsync(() => {
    // GIVEN no changes to custom type fields
    fixture.detectChanges()
    tick()

    // WHEN we read the array
    const validityArray = component.customTypesDefinitionValidation()
    // THEN it should be `[true, true]` if everything is empty
    expect(validityArray.every((val) => val === true)).toBe(true)
  }))
  it('should handle toggling a custom type name from non-empty to empty, making the description optional again', fakeAsync(() => {
    // GIVEN a custom type starts with a non-empty name
    const privTypesArray = component
      .edaiFormGroup()
      .get('privilegeJobModel.privilegeTypes') as FormArray
    const customType2Group = privTypesArray.controls.find(
      (ctrl) => ctrl.value.privilegeType === PrivilegeTypes.CustomType2
    )
    customType2Group?.get('name')?.setValue('Initial Name')
    customType2Group?.get('description')?.setValue('')
    fixture.detectChanges()
    tick()

    // THEN customTypesDefinitionValidation for CustomType2 is false due to empty desc
    const initialValidity = component.customTypesDefinitionValidation()
    const indexOfCustom2 = component['customTypes'].indexOf(
      PrivilegeTypes.CustomType2
    )
    expect(initialValidity[indexOfCustom2]).toBe(false)

    // WHEN the user clears the name
    customType2Group?.get('name')?.setValue('')
    fixture.detectChanges()
    tick()

    // THEN the description is no longer required, so CustomType2 is valid again
    const updatedValidity = component.customTypesDefinitionValidation()
    expect(updatedValidity[indexOfCustom2]).toBe(true)
  }))
  it('should properly track dirty/touched states on attorney or work product when name is set to boolean true', fakeAsync(() => {
    // GIVEN the attorneyClient or workProduct name starts as false (unchecked)
    const privilegeTypesArray = component
      .edaiFormGroup()
      .get('privilegeJobModel.privilegeTypes') as FormArray
    const attorneyClientGroup = privilegeTypesArray.controls.find(
      (ctrl) => ctrl.value.privilegeType === PrivilegeTypes.AttorneyClient
    )
    expect(attorneyClientGroup?.get('name')?.value).toBeFalsy()

    // WHEN user checks the box
    attorneyClientGroup?.get('name')?.markAsDirty()
    attorneyClientGroup?.get('name')?.markAsTouched()
    attorneyClientGroup?.get('name')?.setValue(true)
    fixture.detectChanges()
    tick()

    // THEN attorneyOrProductSelected remains true, and the control is dirty/touched
    expect(component.attorneyOrProductSelected()).toBe(true)
    expect(attorneyClientGroup?.get('name')?.dirty).toBe(true)
    expect(attorneyClientGroup?.get('name')?.touched).toBe(true)
  }))
  it('should combine name + status changes correctly if the user flips multiple custom types rapidly', fakeAsync(() => {
    // GIVEN the user toggles both custom types in quick succession
    const privTypesArray = component
      .edaiFormGroup()
      .get('privilegeJobModel.privilegeTypes') as FormArray
    const customType1Group = privTypesArray.controls.find(
      (ctrl) => ctrl.value.privilegeType === PrivilegeTypes.CustomType1
    )
    const customType2Group = privTypesArray.controls.find(
      (ctrl) => ctrl.value.privilegeType === PrivilegeTypes.CustomType2
    )

    customType1Group?.get('name')?.setValue('X')
    customType1Group?.get('description')?.setValue('')
    customType2Group?.get('name')?.setValue('')
    customType2Group?.get('description')?.setValue('Y')
    fixture.detectChanges()
    tick()

    // THEN we see the array reflect that customType1 is invalid, customType2 is valid
    const validityAfterFirst = component.customTypesDefinitionValidation()
    const idxC1 = component['customTypes'].indexOf(PrivilegeTypes.CustomType1)
    const idxC2 = component['customTypes'].indexOf(PrivilegeTypes.CustomType2)
    expect(validityAfterFirst[idxC1]).toBe(false)
    expect(validityAfterFirst[idxC2]).toBe(true)

    // WHEN the user corrects customType1's description and clears customType2's description
    customType1Group?.get('description')?.setValue('Now Filled')
    customType2Group?.get('description')?.setValue('')
    fixture.detectChanges()
    tick()

    // THEN customType1 is valid, customType2 remains valid because its name is empty
    const validityAfterSecond = component.customTypesDefinitionValidation()
    expect(validityAfterSecond[idxC1]).toBe(true)
    expect(validityAfterSecond[idxC2]).toBe(true)
  }))
  it('should gracefully handle invalid data if "name" fields become strings instead of booleans for attorney client or work product', fakeAsync(() => {
    // GIVEN we forcibly set name to an unexpected string
    const privTypesArray = component
      .edaiFormGroup()
      .get('privilegeJobModel.privilegeTypes') as FormArray
    const attorneyClientGroup = privTypesArray.controls.find(
      (ctrl) => ctrl.value.privilegeType === PrivilegeTypes.AttorneyClient
    )
    attorneyClientGroup?.get('name')?.setValue('unexpectedString')
    fixture.detectChanges()
    tick()

    // THEN attorneyOrProductSelected remains true because name is truthy, no runtime errors
    expect(component.attorneyOrProductSelected()).toBe(true)
  }))
  it('should handle a scenario where attorney or work product controls are missing, defaulting to attorney or product', fakeAsync(() => {
    // GIVEN we remove attorneyClient and workProduct from the array
    const privTypesArray = component
      .edaiFormGroup()
      .get('privilegeJobModel.privilegeTypes') as FormArray
    const newArray = privTypesArray.controls.filter((ctrl) => {
      const currentType = ctrl.get('privilegeType')?.value
      return ![
        PrivilegeTypes.AttorneyClient,
        PrivilegeTypes.WorkProduct,
      ].includes(currentType)
    })
    while (privTypesArray.length > 0) {
      privTypesArray.removeAt(0)
    }
    newArray.forEach((ctrl) => privTypesArray.push(ctrl))
    fixture.detectChanges()
    tick()

    // THEN attorneyOrProductSelected remains true because the code sees no attorney or product controls
    expect(component.attorneyOrProductSelected()).toBe(true)
  }))
})
