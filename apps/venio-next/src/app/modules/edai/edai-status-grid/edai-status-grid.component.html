<div class="t-bock t-w-full t-h-full" #container>
  <kendo-grid
    class="t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto"
    [kendoGridBinding]="allJobStatuses()"
    [loading]="isEdaiStatusLoading()"
    kendoGridSelectBy="jobId"
    venioDynamicHeight
    [isKendoDialog]="true"
    [resizable]="true"
    filterable="menu"
    [pageable]="{ type: 'numeric', position: 'top' }">
    <div
      *kendoGridNoRecordsTemplate
      class="t-flex t-justify-center t-items-center">
      @if(!isEdaiStatusLoading()) {
      <span>No records found</span>
      }
    </div>
    <ng-template kendoPagerTemplate>
      <div class="t-flex t-gap-2"></div>
      <kendo-grid-spacer></kendo-grid-spacer>

      <venio-pagination
        [disabled]="totalRecords() === 0"
        [totalRecords]="totalRecords()"
        [pageSize]="100"
        [showPageJumper]="false"
        [showPageSize]="true"
        [showRowNumberInputBox]="true"
        (pageChanged)="pageChanged($event)"
        (pageSizeChanged)="pageSizeChanged($event)"
        class="t-px-5 t-block t-py-2">
      </venio-pagination>
    </ng-template>
    <kendo-grid-column
      field="sn"
      [width]="50"
      title="#"
      [filterable]="false"
      headerClass="t-text-primary">
      <ng-template kendoGridHeaderTemplate let-column>
        <span kendoTooltip class="t-text-ellipsis t-overflow-hidden">#</span>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      field="jobName"
      [filterable]="false"
      title="Job Name"
      headerClass="t-text-primary">
      <ng-template kendoGridHeaderTemplate let-column>
        <span kendoTooltip class="t-text-ellipsis t-overflow-hidden"
          >Job Name
        </span>
      </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
        <span
          (click)="performPrivilegeOrRelevanceDocumentSearch(dataItem)"
          class="t-text-ellipsis t-overflow-hidden"
          [ngClass]="{
            't-text-[#1DBADC] t-cursor-pointer':
              dataItem.status === 'Completed' &&
              (dataItem.jobType === jobType.Privilege ||
                dataItem.jobType === jobType.Relevance)
          }"
          >{{ dataItem.jobName }}</span
        >
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      field="jobType"
      title="Type"
      headerClass="t-text-primary">
      <ng-template
        kendoGridFilterMenuTemplate
        let-column="column"
        let-filter="filter"
        let-filterService="filterService">
        <div class="t-flex t-w-full t-text-[#1DBADC] t-font-semibold">
          Filter By Type
        </div>
        @defer{
        <venio-grid-custom-filter-checklist
          [field]="column.field"
          [filterChange]="filterService"
          [currentFilter]="filter"
          [data]="distinctPrimitive(column.field)">
        </venio-grid-custom-filter-checklist>
        } @placeholder {
        <p class="t-text-[#cccccc] t-text-center">Loading..</p>
        }
      </ng-template>

      <ng-template kendoGridHeaderTemplate let-column>
        <span kendoTooltip class="t-text-ellipsis t-overflow-hidden"
          >Type
        </span>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      [filterable]="false"
      field="createdBy"
      title="Redacted By & On"
      headerClass="t-text-primary">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Created By & On"
          >Created By & On</span
        >
      </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="t-flex t-gap-1 t-flex-col">
          <div>{{ dataItem.createdBy }}</div>
          <div class="t-text-xs">
            {{ dataItem.createdDate }} {{ dataItem.createdTime }}
          </div>
        </div>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      [filterable]="false"
      field="totalFiles"
      title="Total Documents Submitted"
      headerClass="t-text-primary">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Total Documents Submitted"
          >Total Documents Submitted
        </span>
      </ng-template>

      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="t-flex t-justify-end">
          {{ dataItem.totalFiles }}
        </div>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      [filterable]="false"
      field="processedFiles"
      title="Total Documents Processed"
      headerClass="t-text-primary">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Total Documents Processed "
          >Total Documents Processed
        </span>
      </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="t-flex t-justify-end">
          {{ dataItem.processedFiles }}
        </div>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      [filterable]="false"
      field="status"
      title="Status"
      headerClass="t-text-primary"
      [width]="200">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="t-flex t-items-center t-gap-4 t-justify-between">
          <div
            class="t-font-medium t-uppercase"
            [ngClass]="getStatusCssClass(dataItem.status)">
            {{ dataItem.status }}
          </div>

          <div class="t-flex t-items-center t-w-16 t-justify-end t-gap-2">
            <span
              #anchor
              *ngIf="
                dataItem.status === 'Completed' &&
                dataItem.jobType !== jobType.ECA
              "
              kendoPopoverAnchor
              [popover]="statusDetailPopover"
              (click)="setSelectedJobStatus(dataItem)"
              class="hover:t-cursor-pointer hover:t-text-[var(--kendo-custom-secondary-100)] t-flex t-w-full">
              <kendo-svg-icon
                class="hover:t-text-[#1EBADC]"
                [icon]="eyeIcon"></kendo-svg-icon>
            </span>
            <span
              *ngIf="dataItem.status === 'In Progress'"
              class="t-text-[#FFBB12] t-flex t-w-full">
              {{
                calculateCompletionPercentage(
                  dataItem.totalFiles,
                  dataItem.processedFiles
                )
              }}%
            </span>
          </div>

          <!-- Set dynamic width for the popover based on the container width-->
          <!-- 700 is the default width for the popover if the container width is not available-->

          <kendo-popover
            #statusDetailPopover
            position="left"
            [width]="
              (container?.clientWidth > 1366 &&
              dataItem.jobType === jobType.Relevance
                ? 1050
                : dataItem.jobType === jobType.PIIDetect ||
                  dataItem.jobType === jobType.PIIExtract
                ? 600
                : container?.clientWidth > 1366
                ? 1200
                : container?.clientWidth - 200) || 700
            ">
            <ng-template kendoPopoverBodyTemplate>
              @defer { @switch (getJobType(dataItem)){ @case (jobType.Relevance)
              {
              <venio-edai-relevance-status-detail />
              } @case (jobType.Privilege) {
              <venio-edai-privilege-status-detail />
              } @case (jobType.PIIDetect) {
              <venio-edai-pii-status-detail [selectedJobItem]="dataItem" />
              }@case (jobType.PIIExtract) {
              <venio-edai-pii-status-detail [selectedJobItem]="dataItem" />
              } } } @placeholder { @for(p of [1,2]; track p) {
              <div
                [id]="'container-' + p"
                class="t-flex t-justify-center t-flex-row t-mb-4 t-gap-4">
                @for(n of [1,2,3,4,5]; track n) {
                <kendo-skeleton
                  shape="rectangle"
                  width="calc(100% / 5)"
                  height="30px" />
                }
              </div>
              } }
            </ng-template>
          </kendo-popover>
        </div>
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
</div>
