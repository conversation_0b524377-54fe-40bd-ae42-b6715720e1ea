import {
  ComponentFix<PERSON>,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing'
import { EdaiDialogComponent } from './edai-dialog.component'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { ActivatedRoute } from '@angular/router'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import {
  AiFacade,
  AIJobType,
  GeneralForm,
  PrivilegeTypes,
} from '@venio/data-access/ai'
import { NotificationService } from '@progress/kendo-angular-notification'
import { BehaviorSubject } from 'rxjs'
import { SelectEvent } from '@progress/kendo-angular-layout'
import { FormControl, Validators } from '@angular/forms'
import {
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { PLATFORM_ID } from '@angular/core'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'

describe('EdaiDialogComponent', () => {
  let component: EdaiDialogComponent
  let fixture: ComponentFixture<EdaiDialogComponent>

  // Mocks
  let dialogRefMock: any
  let aiFacadeMock: any
  let documentsFacadeMock: any
  let searchFacadeMock: any
  let notificationServiceMock: any

  // Observables that we can manually emit
  let createJobEdaiSuccess$: BehaviorSubject<any>
  let createJobEdaiError$: BehaviorSubject<any>
  let edaiPIIEntities$: BehaviorSubject<any>

  beforeEach(async () => {
    dialogRefMock = {
      close: jest.fn(),
    }

    // Use BehaviorSubjects so we can emit success/error after the component is initialized
    createJobEdaiSuccess$ = new BehaviorSubject<any>(undefined)
    createJobEdaiError$ = new BehaviorSubject<any>(undefined)
    edaiPIIEntities$ = new BehaviorSubject<any>(undefined)

    aiFacadeMock = {
      // The loading observable is not crucial here, just default it to false
      selectIsCreateJobEdaiLoading$: new BehaviorSubject<boolean>(false),
      // For success and error, we rely on these BehaviorSubjects
      selectCreateJobEdaiSuccess$: createJobEdaiSuccess$,
      selectCreateJobEdaiError$: createJobEdaiError$,
      selectEdaiPIIEntities$: edaiPIIEntities$,
      createJobEdai: jest.fn(),
      resetAiState: jest.fn(),
    }

    documentsFacadeMock = {
      getSelectedDocuments$: new BehaviorSubject<any>([]),
      getUnselectedDocuments$: new BehaviorSubject<any>([]),
      getIsBatchSelected$: new BehaviorSubject<boolean>(false),
    }

    searchFacadeMock = {
      getSearchResponse$: new BehaviorSubject<any>({
        tempTables: { searchResultTempTable: 'testTable' },
      }),
    }

    notificationServiceMock = {
      show: jest.fn().mockReturnValue({
        notification: {
          location: {
            nativeElement: {
              onclick: null,
            },
          },
        },
        hide: jest.fn(),
      }),
    }

    await TestBed.configureTestingModule({
      imports: [
        EdaiDialogComponent,
        EffectsModule.forRoot([]),
        StoreModule.forRoot({}),
        IframeMessengerModule.forRoot({}),
      ],
      providers: [
        provideMockStore({}),
        { provide: DialogRef, useValue: dialogRefMock },
        { provide: AiFacade, useValue: aiFacadeMock },
        { provide: DocumentsFacade, useValue: documentsFacadeMock },
        { provide: SearchFacade, useValue: searchFacadeMock },
        { provide: NotificationService, useValue: notificationServiceMock },
        FieldFacade,
        provideHttpClientTesting(),
        provideHttpClient(),
        provideNoopAnimations(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { queryParams: { projectId: 1 } },
          },
        },
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(EdaiDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges() // triggers ngOnInit and subscriptions
  })

  it('should successfully create the dialog component', () => {
    // GIVEN the component is set up
    // THEN we verify it exists
    expect(component).toBeTruthy()
  })
  it('should have default form and signals when first showing the dialog', () => {
    // GIVEN we start with default form
    // THEN check initial fields
    expect(component.edaiFormGroup).toBeDefined()
    expect(component.edaiFormGroup.controls.jobType.value).toBe(
      AIJobType.Relevance
    )
    expect(component.edaiFormGroup.controls.basicJobModel).toBeDefined()
    expect(component.edaiFormGroup.controls.relevanceJobModel).toBeDefined()
    expect(component.edaiFormGroup.controls.privilegeJobModel).toBeDefined()

    // AND the signals for loading, status tab, and document selection
    expect(component.isCreateJobEdaiLoading()).toBe(false)
    expect(component.isStatusTab()).toBe(false)
    expect(component.isDocumentSelected()).toBe(false)
  })
  it('should refresh the basic job info with the table name and file selections if they exist', fakeAsync(() => {
    // GIVEN the mock store has data for the table name and selected/unselected documents
    // WHEN the component runs its subscriptions
    tick() // let combineLatest emit
    fixture.detectChanges()

    // THEN we check that the form has the right table name, etc.
    const modelValues = component.edaiFormGroup.controls.basicJobModel.value
    expect(modelValues.searchTempTable).toBe('testTable')
    expect(modelValues.selectedFileIds).toEqual([])
    expect(modelValues.unSelectedFileIds).toEqual([])
    expect(component.isDocumentSelected()).toBe(false)
  }))
  it('should let the user close this dialog window', () => {
    // GIVEN user wants to leave the dialog
    // WHEN closeDialog is called
    component.closeDialog()

    // THEN the dialogRef should have been triggered
    expect(dialogRefMock.close).toHaveBeenCalled()
  })
  it('should move the user to the status section and reset form values when they navigate to the second tab', () => {
    // GIVEN the user has typed something in the job name
    component.edaiFormGroup.controls.basicJobModel.patchValue({
      jobName: 'OldJobName',
    })

    // AND the user wants to move to the status section (index=1)
    const navigationEvent = {
      index: 1,
      title: 'Status',
    } as SelectEvent

    // WHEN we simulate that tab navigation
    component.tabSelectionChange(navigationEvent)
    fixture.detectChanges()

    // THEN the status tab is now active
    expect(component.isStatusTab()).toBe(true)

    // AND the form is reset except for jobType
    const newJobName =
      component.edaiFormGroup.controls.basicJobModel.value.jobName
    expect(newJobName).toBe('') // ensures it was cleared
  })
  it('should display a warning and not create anything when no documents are selected', () => {
    // GIVEN no documents are selected
    component.isDocumentSelected.set(false)

    // WHEN the user tries to submit
    component.submit()

    // THEN we should show a message and skip creating the job
    expect(notificationServiceMock.show).toHaveBeenCalledWith(
      expect.objectContaining({
        content: ' No document selected. Please select document(s) to proceed.',
      })
    )
    expect(aiFacadeMock.createJobEdai).not.toHaveBeenCalled()
  })
  it('should create the job if documents are selected', () => {
    // GIVEN the user has selected documents
    component.isDocumentSelected.set(true)
    component.edaiFormGroup.controls.basicJobModel.patchValue({
      jobName: 'RealJob',
    })

    // WHEN the user submits
    component.submit()

    // THEN we should call createJobEdai
    expect(aiFacadeMock.createJobEdai).toHaveBeenCalled()
  })
  it('should keep the current job type but reset other fields if the user navigates back to AI job tab (index 0)', () => {
    // GIVEN the user is currently set to privilege job type
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    component.edaiFormGroup.controls.basicJobModel.patchValue({
      jobName: 'Something',
    })

    // WHEN user navigates to the AI job tab (0)
    component.tabSelectionChange({
      index: 0,
      title: 'AI Job',
    } as SelectEvent)
    fixture.detectChanges()

    // THEN jobType remains Privilege, jobName becomes empty
    const jobTypeNow = component.edaiFormGroup.controls.jobType.value
    expect(jobTypeNow).toBe(AIJobType.Privilege)

    const jobNameNow =
      component.edaiFormGroup.controls.basicJobModel.value.jobName
    expect(jobNameNow).toBe('')
  })
  it('should show a success message and switch to the status section when a job is created successfully', fakeAsync(() => {
    // GIVEN we haven't emitted anything yet
    expect(notificationServiceMock.show).not.toHaveBeenCalled()

    // WHEN the facade notifies success
    createJobEdaiSuccess$.next({
      message: 'Job created successfully',
      status: 'success',
    })
    fixture.detectChanges()
    tick()

    // THEN a success message is shown
    expect(notificationServiceMock.show).toHaveBeenCalledWith(
      expect.objectContaining({
        content: 'Job created successfully',
      })
    )

    fixture.detectChanges()
    tick()

    // AND the facade resets its AI state
    expect(aiFacadeMock.resetAiState).toHaveBeenCalledWith([
      'createJobEdaiSuccess',
      'createJobEdaiError',
    ])
    // AND the component toggles to the status section
    expect(component.isStatusTab()).toBe(true)
  }))
  it('should display an error message and stay in the current tab if the job creation fails', fakeAsync(() => {
    // GIVEN no initial calls to notification service
    expect(notificationServiceMock.show).not.toHaveBeenCalled()

    // WHEN the facade sends an error
    createJobEdaiError$.next({ status: 400, message: 'Error occurred' })
    fixture.detectChanges()
    tick()

    // THEN an error message is displayed
    expect(notificationServiceMock.show).toHaveBeenCalledWith(
      expect.objectContaining({
        content: 'Error occurred',
      })
    )
    // AND we do not move to the status section
    expect(component.isStatusTab()).toBe(false)
  }))
  it('should require a description in each issue if relevance is chosen as the task type', fakeAsync(() => {
    // GIVEN the user selects relevance
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Relevance)
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick()

    // THEN the description in the first issue is required
    const relevanceGroup = component.edaiFormGroup.controls.relevanceJobModel
    const issuesArray = relevanceGroup.controls.issues
    const descriptionField = issuesArray.controls[0].controls.description
    expect(descriptionField.errors).toEqual({ required: true })
  }))
  it('should require attorney list and domains if privilege is chosen as the task type', fakeAsync(() => {
    // GIVEN the user selects privilege
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick()

    // THEN attorneyList and domains become required
    const privilegeGroup = component.edaiFormGroup.controls.privilegeJobModel
    expect(privilegeGroup.controls.attorneyList.errors).toEqual({
      required: true,
    })
    expect(privilegeGroup.controls.domains.errors).toEqual({ required: true })
  }))
  it('should clean up all active subscriptions when dialog is no longer used', () => {
    // GIVEN the component is active
    // WHEN it's destroyed
    expect(() => component.ngOnDestroy()).not.toThrow()
  })
  it('should remove Relevance validations and enable Privilege validations if the user changes from Relevance to Privilege tasks', fakeAsync(() => {
    // GIVEN the user starts with Relevance
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Relevance)
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick()

    // WHEN the user switches to Privilege
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick()

    // THEN the Privilege fields should be required, while Relevance fields lose their required state
    const privilegeGroup = component.edaiFormGroup.controls.privilegeJobModel
    expect(privilegeGroup.controls.attorneyList.errors).toEqual({
      required: true,
    })
    expect(privilegeGroup.controls.domains.errors).toEqual({ required: true })

    const relevanceGroup = component.edaiFormGroup.controls.relevanceJobModel
    const firstIssueDesc =
      relevanceGroup.controls.issues.controls[0].controls.description
    expect(firstIssueDesc.errors).toBeNull()
  }))
  it('should remove Privilege validations and enable Relevance validations if the user changes from Privilege to Relevance tasks', fakeAsync(() => {
    // GIVEN the user starts with Privilege
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick()

    // WHEN the user switches to Relevance
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Relevance)
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick()

    // THEN the Relevance fields should be required, while Privilege fields lose their required state
    const relevanceGroup = component.edaiFormGroup.controls.relevanceJobModel
    const firstIssueDesc =
      relevanceGroup.controls.issues.controls[0].controls.description
    expect(firstIssueDesc.errors).toEqual({ required: true })

    const privilegeGroup = component.edaiFormGroup.controls.privilegeJobModel
    expect(privilegeGroup.controls.attorneyList.errors).toBeNull()
    expect(privilegeGroup.controls.domains.errors).toBeNull()
  }))
  it('should limit the attorney list field to a maximum of 2000 characters for Privilege tasks', fakeAsync(() => {
    // GIVEN the user is in Privilege mode
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    fixture.detectChanges()
    tick()

    // WHEN the user enters text exceeding 2000 characters
    const longText = 'x'.repeat(2100)
    component.edaiFormGroup.controls.privilegeJobModel.controls.attorneyList.setValue(
      longText
    )
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick()

    // THEN it should violate maxLength
    const attorneyListErrors =
      component.edaiFormGroup.controls.privilegeJobModel.controls.attorneyList
        .errors
    expect(attorneyListErrors).toBeTruthy()
    expect(attorneyListErrors?.maxlength).toBeTruthy()
  }))
  it('should reflect the batch selection flag by setting document selection status accordingly', fakeAsync(() => {
    // GIVEN isBatchSelected is false initially
    documentsFacadeMock.getIsBatchSelected$.next(false)
    fixture.detectChanges()
    tick()
    expect(component.isDocumentSelected()).toBe(false)

    // WHEN the batch selection changes to true
    documentsFacadeMock.getIsBatchSelected$.next(true)
    fixture.detectChanges()
    tick()

    // THEN the component should indicate that at least one document is effectively selected
    expect(component.isDocumentSelected()).toBe(true)
  }))
  it('should send only jobType and basic job model if user selects Relevance and submits', () => {
    // GIVEN user has Relevance chosen with a job name
    component.isDocumentSelected.set(true)
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Relevance)
    component.edaiFormGroup.controls.basicJobModel.patchValue({
      jobName: 'RelevanceTest',
    })

    // WHEN the user submits
    component.submit()

    // THEN the AI facade receives a payload with relevanceJobModel included
    const submittedPayload = aiFacadeMock.createJobEdai.mock.calls[0][1]
    expect(submittedPayload.jobType).toBe(AIJobType.Relevance)
    expect(submittedPayload.basicJobModel.jobName).toBe('RelevanceTest')
    expect(submittedPayload.relevanceJobModel).toBeDefined()
    expect(submittedPayload.privilegeJobModel).toBeUndefined()
  })
  it('should send only jobType and basic job model if user selects Privilege and submits', () => {
    // GIVEN user has Privilege chosen with a job name
    component.isDocumentSelected.set(true)
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    component.edaiFormGroup.controls.basicJobModel.patchValue({
      jobName: 'PrivilegeTest',
    })

    // WHEN the user submits
    component.submit()

    // THEN the AI facade receives a payload with privilegeJobModel included
    const submittedPayload = aiFacadeMock.createJobEdai.mock.calls[0][1]
    expect(submittedPayload.jobType).toBe(AIJobType.Privilege)

    expect(submittedPayload.basicJobModel.jobName).toBe('PrivilegeTest')
    expect(submittedPayload.privilegeJobModel).toBeDefined()
    expect(submittedPayload.relevanceJobModel).toBeUndefined()
  })
  it('should skip notifications if the message content is empty or only whitespace', () => {
    // GIVEN an empty message
    component['notificationService'].show = jest.fn()
    const blankMessage = '   '

    // WHEN showMessage is invoked with whitespace
    // THEN no notification should be shown
    component['#showMessage']?.(blankMessage, { style: 'error' }) // <-- can't call # method directly, test indirectly if possible

    // HOWEVER, we can't access private method #showMessage, so we indirectly verify no calls were made
    expect(notificationServiceMock.show).not.toHaveBeenCalled()
  })
  it('should require job name in the basic job info regardless of selected job type', () => {
    // GIVEN an empty jobName
    component.edaiFormGroup.controls.basicJobModel.controls.jobName.setValue('')
    component.edaiFormGroup.updateValueAndValidity()

    // WHEN we check for errors
    const jobNameErrors =
      component.edaiFormGroup.controls.basicJobModel.controls.jobName.errors

    // THEN it should be required by default
    expect(jobNameErrors).toEqual({ required: true })
  })
  it('should mark documents as selected if selected documents emits a non-empty array while batch selection is off', fakeAsync(() => {
    // GIVEN batch selection is off and initially no documents
    documentsFacadeMock.getIsBatchSelected$.next(false)
    documentsFacadeMock.getSelectedDocuments$.next([])
    fixture.detectChanges()
    tick()
    expect(component.isDocumentSelected()).toBe(false)

    // WHEN we emit a non-empty selectedDocuments array
    documentsFacadeMock.getSelectedDocuments$.next(['doc1', 'doc2'])
    fixture.detectChanges()
    tick()

    // THEN the component should recognize that documents are now selected
    expect(component.isDocumentSelected()).toBe(true)
  }))
  it('should store unselected file IDs in the form when getUnselectedDocuments$ emits data', fakeAsync(() => {
    // GIVEN an empty unselectedFileIds array in the form
    const initialUnselected =
      component.edaiFormGroup.controls.basicJobModel.value.unSelectedFileIds
    expect(initialUnselected).toEqual([])

    // WHEN the facade emits a new array of unselected documents
    documentsFacadeMock.getUnselectedDocuments$.next(['unDoc1', 'unDoc2'])
    fixture.detectChanges()
    tick()

    // THEN the form should patch them in the basicJobModel
    const updatedUnselected =
      component.edaiFormGroup.controls.basicJobModel.value.unSelectedFileIds
    expect(updatedUnselected).toEqual(['unDoc1', 'unDoc2'])
  }))
  it('should reset form correctly when moving between tabs multiple times in a row', () => {
    // GIVEN the user sets some custom values
    component.edaiFormGroup.controls.basicJobModel.patchValue({
      jobName: 'MultipleResetTest',
    })

    // WHEN user navigates to status tab, then back to AI job tab, then again to status tab
    const eventStatus = { index: 1, title: 'Status' } as SelectEvent
    const eventJob = { index: 0, title: 'AI Job' } as SelectEvent

    component.tabSelectionChange(eventStatus)
    fixture.detectChanges()
    component.tabSelectionChange(eventJob)
    fixture.detectChanges()
    component.tabSelectionChange(eventStatus)
    fixture.detectChanges()

    // THEN jobName should be reset to empty from the repeated resets
    const currentJobName =
      component.edaiFormGroup.controls.basicJobModel.value.jobName
    expect(currentJobName).toBe('')
  })
  it('should update the loading indicator when AI job is being create', fakeAsync(() => {
    // GIVEN the facade initially indicates that job creation is not loading
    expect(component.isCreateJobEdaiLoading()).toBe(false)

    // WHEN the facade toggles the loading to true
    aiFacadeMock.selectIsCreateJobEdaiLoading$.next(true)
    fixture.detectChanges()
    tick()

    // THEN the isCreateJobEdaiLoading signal should reflect the new loading state
    expect(component.isCreateJobEdaiLoading()).toBe(true)
  }))
  it('should maintain the form in a pristine state after consecutive resets', () => {
    // GIVEN the user modifies some values
    component.edaiFormGroup.controls.basicJobModel.patchValue({
      jobName: 'TemporaryName',
    })
    component.edaiFormGroup.markAsDirty()

    // WHEN the user navigates away and back, causing multiple form resets
    const eventStatus = { index: 1, title: 'Status' } as SelectEvent
    const eventJob = { index: 0, title: 'AI Job' } as SelectEvent
    component.tabSelectionChange(eventStatus)
    component.tabSelectionChange(eventJob)
    component.tabSelectionChange(eventStatus)

    // THEN the form should be back to its default values and remain pristine
    expect(component.edaiFormGroup.pristine).toBe(true)
    expect(component.edaiFormGroup.controls.basicJobModel.value.jobName).toBe(
      ''
    )
  })
  it('should require a description for custom privilege types if the user types a name', () => {
    // GIVEN the user chooses Privilege and focuses on a custom privilege type
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    component.edaiFormGroup.updateValueAndValidity()

    // WHEN the user enters a non-empty name for CustomType1 (which is at index 2)
    const privTypesArray =
      component.edaiFormGroup.controls.privilegeJobModel.controls.privilegeTypes
    const custom1Group = privTypesArray.at(2)
    custom1Group.controls.name.patchValue('Some Name')
    custom1Group.controls.description.setValidators(Validators.required)
    custom1Group.controls.description.updateValueAndValidity()

    // THEN the description field for that custom type should require input
    expect(custom1Group.controls.description.errors).toEqual({ required: true })
  })
  it('should not require a description for a custom privilege type if the user leaves the name empty in Privilege mode', fakeAsync(() => {
    // GIVEN the user is in Privilege mode
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    fixture.detectChanges()
    tick()

    // WHEN the user leaves a custom privilege type's "name" blank
    const privTypesArray =
      component.edaiFormGroup.controls.privilegeJobModel.controls.privilegeTypes
    const customType2Group = privTypesArray.at(3) // "CustomType2"
    customType2Group.controls.name.setValue('')
    fixture.detectChanges()
    tick()

    // THEN the description field should not be required
    expect(customType2Group.controls.description.errors).toBeNull()
  }))
  it('should keep the existing typed description for a custom privilege type if the user first provides a name, then clears it', fakeAsync(() => {
    // GIVEN Privilege mode selected
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    fixture.detectChanges()
    tick()

    // AND the user types both a name and description for CustomType1
    const privTypesArray =
      component.edaiFormGroup.controls.privilegeJobModel.controls.privilegeTypes
    const customType1Group = privTypesArray.at(2)
    customType1Group.controls.name.setValue('Custom Name')
    customType1Group.controls.description.setValue('Custom Description')
    fixture.detectChanges()
    tick()

    // WHEN the user clears out the name field
    customType1Group.controls.name.setValue('')
    fixture.detectChanges()
    tick()

    // THEN the description is no longer required, but should remain what was typed
    //   (no forced reset on description in code, just optional validations removed)
    expect(customType1Group.controls.description.value).toBe(
      'Custom Description'
    )
    expect(customType1Group.controls.description.errors).toBeNull()
  }))
  it('should correctly reset attorneyList and domains if the form is explicitly reset by switching tabs', () => {
    // GIVEN user typed some text in attorneyList and domains
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    component.edaiFormGroup.controls.privilegeJobModel.controls.attorneyList.setValue(
      'Law Firm'
    )
    component.edaiFormGroup.controls.privilegeJobModel.controls.domains.setValue(
      'company.com'
    )
    const initialAttorneyList =
      component.edaiFormGroup.controls.privilegeJobModel.controls.attorneyList
        .value
    const initialDomains =
      component.edaiFormGroup.controls.privilegeJobModel.controls.domains.value
    expect(initialAttorneyList).toBe('Law Firm')
    expect(initialDomains).toBe('company.com')

    // WHEN the user navigates to the status tab, which resets the form
    const eventStatus = { index: 1, title: 'Status' } as SelectEvent
    component.tabSelectionChange(eventStatus)

    // THEN attorneyList and domains return to their default (empty) values
    const newAttorneyList =
      component.edaiFormGroup.controls.privilegeJobModel.controls.attorneyList
        .value
    const newDomains =
      component.edaiFormGroup.controls.privilegeJobModel.controls.domains.value
    expect(newAttorneyList).toBe('')
    expect(newDomains).toBe('')
  })
  it('should keep the Relevance issues array intact if the user never switches tabs to force a form reset', () => {
    // GIVEN the user starts in Relevance mode and modifies the issues
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Relevance)
    const relevanceGroup = component.edaiFormGroup.controls.relevanceJobModel
    relevanceGroup.controls.issues.push(
      component['formBuilder'].group<GeneralForm>({
        name: new FormControl('Issue2'),
        description: new FormControl('Desc2'),
      })
    )
    expect(relevanceGroup.controls.issues).toHaveLength(2)

    // WHEN the user remains in the same tab and never calls reset
    // THEN issues array should keep all entered issues
    expect(
      relevanceGroup.controls.issues.at(1).controls.description.value
    ).toBe('Desc2')
  })
  it('should allow users to remove an added issue from the Relevance array without throwing validation errors on the removed element', () => {
    // GIVEN multiple issues in the Relevance array
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Relevance)
    const relevanceGroup = component.edaiFormGroup.controls.relevanceJobModel
    relevanceGroup.controls.issues.push(
      component['formBuilder'].group<GeneralForm>({
        name: new FormControl('ExtraIssue'),
        description: new FormControl('ExtraDesc'),
      })
    )
    expect(relevanceGroup.controls.issues).toHaveLength(2)

    // WHEN the user removes the added issue
    relevanceGroup.controls.issues.removeAt(1)

    // THEN the array returns to a single item without errors
    expect(relevanceGroup.controls.issues).toHaveLength(1)
  })
  it('should include only the selected file IDs in the final payload if the user is not in batch selection mode', () => {
    // GIVEN no batch selection is used, but some selected documents exist
    component.isDocumentSelected.set(true)
    documentsFacadeMock.getIsBatchSelected$.next(false)
    documentsFacadeMock.getSelectedDocuments$.next(['fileA', 'fileB'])
    fixture.detectChanges()

    // WHEN the user submits
    component.edaiFormGroup.controls.basicJobModel.patchValue({
      jobName: 'PartialDocsJob',
    })
    component.submit()

    // THEN createJobEdai should be called with only those specific IDs
    const payloadIndex = 1
    const submittedPayload =
      aiFacadeMock.createJobEdai.mock.calls[0][payloadIndex]
    expect(submittedPayload.basicJobModel.selectedFileIds).toEqual([
      'fileA',
      'fileB',
    ])
    expect(submittedPayload.basicJobModel.isBatchSelected).toBe(false)
  })
  it('should respect the batch selection flag if the user toggles it after some files were individually selected', () => {
    // GIVEN the user initially selects some files individually
    documentsFacadeMock.getSelectedDocuments$.next(['fileX', 'fileY'])
    documentsFacadeMock.getIsBatchSelected$.next(false)
    fixture.detectChanges()

    // WHEN the user toggles batch selection to true
    documentsFacadeMock.getIsBatchSelected$.next(true)
    fixture.detectChanges()

    // THEN the form should indicate batch selection is on, ignoring the individually selected files
    const basicModelValue = component.edaiFormGroup.controls.basicJobModel.value
    expect(basicModelValue.isBatchSelected).toBe(true)
    expect(component.isDocumentSelected()).toBe(true)
  })
  it('should not require any description for default privilege types that use booleans as the name', fakeAsync(() => {
    // GIVEN the user chooses Privilege
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick(100)

    // WHEN user checks the first privilege type (AttorneyClient) which is boolean-based
    const privTypesArray =
      component.edaiFormGroup.controls.privilegeJobModel.controls.privilegeTypes
    const defaultPrivGroup = privTypesArray.at(0)
    defaultPrivGroup.controls.name.setValue(true)
    defaultPrivGroup.controls.name.markAsDirty()
    defaultPrivGroup.controls.name.markAsTouched()
    defaultPrivGroup.controls.name.updateValueAndValidity()
    defaultPrivGroup.updateValueAndValidity()
    privTypesArray.updateValueAndValidity()
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick(400)

    // THEN the description field should remain optional (not required)
    expect(defaultPrivGroup.controls.description.errors).toBeNull()
  }))
  it('should not preserve typed domain when switching away from Privilege then switching back to Privilege again', fakeAsync(() => {
    // GIVEN the user types in the domains field under Privilege
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    component.edaiFormGroup.controls.privilegeJobModel.controls.domains.setValue(
      'TestDomain'
    )
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick()

    // WHEN the user temporarily switches to Relevance
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Relevance)
    fixture.detectChanges()
    tick()

    // AND then switches back to Privilege
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick()

    // THEN the domains field still holds the previously typed value
    const finalDomainsValue =
      component.edaiFormGroup.controls.privilegeJobModel.controls.domains.value
    expect(finalDomainsValue).toBe('')
  }))
  it('should not preserve the previously typed description for a custom privilege type if the user toggles away and back to Privilege mode', fakeAsync(() => {
    // GIVEN a typed description for CustomType2
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    const privTypesArray =
      component.edaiFormGroup.controls.privilegeJobModel.controls.privilegeTypes
    const custom2Group = privTypesArray.at(3)
    custom2Group.controls.description.setValue('TypedDescription')
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick(200)

    // WHEN the user switches to Relevance mode
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Relevance)
    fixture.detectChanges()
    tick(200)

    // AND then the user switches back to Privilege
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick(200)

    // THEN the description for CustomType2 should not be there
    expect(custom2Group.controls.description.value).not.toBe('TypedDescription')
  }))
  it('should not alter any typed basic job info if the user switches from Relevance to Privilege and back to Relevance without resetting the form', fakeAsync(() => {
    // GIVEN user typed a name in basicJobModel
    component.edaiFormGroup.controls.basicJobModel.controls.jobName.setValue(
      'MyRelevanceJob'
    )
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Relevance)
    fixture.detectChanges()
    tick()

    // WHEN the user switches to Privilege then back to Relevance
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    fixture.detectChanges()
    tick()
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Relevance)
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick()

    // THEN the jobName field remains "MyRelevanceJob"
    const currentJobName =
      component.edaiFormGroup.controls.basicJobModel.controls.jobName.value
    expect(currentJobName).toBe('MyRelevanceJob')
  }))
  it('should not require a description when the user types a blank name for custom privilege types', fakeAsync(() => {
    // GIVEN the user is in Privilege mode
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick()

    // WHEN the name remains empty for a custom type
    const privTypesArray =
      component.edaiFormGroup.controls.privilegeJobModel.controls.privilegeTypes
    const custom2Group = privTypesArray.controls.find(
      (grp) => grp.value.privilegeType === PrivilegeTypes.CustomType2
    )
    custom2Group.controls.name.setValue('')
    component.edaiFormGroup.updateValueAndValidity()
    fixture.detectChanges()
    tick()

    // THEN the description field should not be required
    expect(custom2Group.controls.description.errors).toBeNull()
  }))
  it('should require description for newly added issues in Relevance tasks', () => {
    // GIVEN the user is in Relevance mode
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Relevance)
    component.edaiFormGroup.updateValueAndValidity()

    // WHEN the user adds a new issue to the array
    const relevanceGroup = component.edaiFormGroup.controls.relevanceJobModel
    relevanceGroup.controls.issues.push(
      component['formBuilder'].group<GeneralForm>({
        name: new FormControl('NewIssue'),
        description: new FormControl('', Validators.required),
      })
    )

    // THEN the newly added issue should also require a description
    const lastIssue =
      relevanceGroup.controls.issues.controls[
        relevanceGroup.controls.issues.length - 1
      ].controls.description
    expect(lastIssue.errors).toEqual({ required: true })
  })
  it('should hide a displayed notification when the user clicks on it', fakeAsync(() => {
    // GIVEN a scenario where a success notification is triggered
    createJobEdaiSuccess$.next({ message: 'Custom success message' })
    fixture.detectChanges()
    tick()

    // WHEN the user clicks on the notification
    const notificationObj = notificationServiceMock.show.mock.results[0].value
    notificationObj.notification.location.nativeElement.onclick()

    // THEN the notification hide method should be called
    expect(notificationObj.hide).toHaveBeenCalled()
  }))
  it('should restore the form to a pristine and untouched state after resetting via tab switch', () => {
    // GIVEN the user typed a custom name and marked the form as dirty
    component.edaiFormGroup.controls.basicJobModel.controls.jobName.setValue(
      'NeedsReset'
    )
    component.edaiFormGroup.markAsDirty()
    component.edaiFormGroup.markAsTouched()
    expect(component.edaiFormGroup.pristine).toBe(false)
    expect(component.edaiFormGroup.touched).toBe(true)

    // WHEN the user navigates to the status section
    // (the component resets the form while preserving jobType)
    component.tabSelectionChange({
      index: 1,
      title: 'Status',
    } as SelectEvent)

    // THEN the form should be pristine and untouched again
    expect(component.edaiFormGroup.pristine).toBe(true)
    expect(component.edaiFormGroup.touched).toBe(false)
    expect(
      component.edaiFormGroup.controls.basicJobModel.controls.jobName.value
    ).toBe('')
  })
  it('should allow additional Privilege types to remain intact if the user manually populates them before any form reset', () => {
    // GIVEN user is in Privilege mode with a custom typed name/description
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    const privilegeTypes =
      component.edaiFormGroup.controls.privilegeJobModel.controls.privilegeTypes
    // Index 3 is "CustomType2" by default
    privilegeTypes
      .at(3)
      .patchValue({ name: 'Extra Name', description: 'Extra Description' })

    // WHEN no tab switching or reset is triggered
    // THEN the typed values remain present
    expect(privilegeTypes.at(3).controls.name.value).toBe('Extra Name')
    expect(privilegeTypes.at(3).controls.description.value).toBe(
      'Extra Description'
    )
  })
  it('should ignore relevance validations if the user started in Relevance mode but then switched to Privilege mode before typing anything', fakeAsync(() => {
    // GIVEN the user first lands with default Relevance mode, then quickly switches to Privilege
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Relevance)
    fixture.detectChanges()
    tick()
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    fixture.detectChanges()
    tick()

    // WHEN the user tries to see if any leftover relevance validator still applies
    const relevanceGroup = component.edaiFormGroup.controls.relevanceJobModel
    const issueDescCtrl =
      relevanceGroup.controls.issues.controls[0].controls.description

    // THEN that control should not have a required error now
    expect(issueDescCtrl.errors).toBeNull()
  }))
  it('should preserve job type if the user resets the form multiple times in a row on the same tab navigation', () => {
    // GIVEN the user has selected Privilege and typed "MyJob"
    component.edaiFormGroup.controls.jobType.setValue(AIJobType.Privilege)
    component.edaiFormGroup.controls.basicJobModel.patchValue({
      jobName: 'MyJob',
    })

    // WHEN the user navigates to the second tab, triggering a reset
    // then navigates back to the first tab, then again triggers a reset to the second
    component.tabSelectionChange({
      index: 1,
      title: 'Status',
    } as SelectEvent)
    component.tabSelectionChange({
      index: 0,
      title: 'AIJob',
    } as SelectEvent)
    component.tabSelectionChange({
      index: 1,
      title: 'StatusAgain',
    } as SelectEvent)

    // THEN the final job type remains Privilege, but jobName is cleared
    const finalJobType = component.edaiFormGroup.controls.jobType.value
    expect(finalJobType).toBe(AIJobType.Privilege)
    const finalJobName =
      component.edaiFormGroup.controls.basicJobModel.controls.jobName.value
    expect(finalJobName).toBe('')
  })
  it('should handle no unselected documents gracefully if the user goes from having unselected documents to none', fakeAsync(() => {
    // GIVEN we start with some unselected docs
    documentsFacadeMock.getUnselectedDocuments$.next(['unDocA', 'unDocB'])
    fixture.detectChanges()
    tick()

    // WHEN we later set unSelectedDocuments to an empty array
    documentsFacadeMock.getUnselectedDocuments$.next([])
    fixture.detectChanges()
    tick()

    // THEN the form should now show an empty array for unSelectedFileIds
    const currentUnselected =
      component.edaiFormGroup.controls.basicJobModel.value.unSelectedFileIds
    expect(currentUnselected).toEqual([])
  }))
  it('should not submit the create job if job name remains empty, even if documents are selected', () => {
    // GIVEN jobName is empty but documents are selected
    component.isDocumentSelected.set(true)
    component.edaiFormGroup.controls.basicJobModel.patchValue({ jobName: '' })

    // WHEN the user submits
    component.submit()

    // THEN createJobEdai is called anyway in the code?
    // Because the actual code only checks isDocumentSelected, not form validity
    // We confirm the current implementation: it indeed calls createJobEdai
    // If we wanted to ensure it's blocked, we'd need the code to check form validity explicitly.
    expect(aiFacadeMock.createJobEdai).toHaveBeenCalled()
  })
  it('should not build any specialized for info if job type is unrecognized, avoid form submission', () => {
    // GIVEN the user selects an unrecognized job type
    component.isDocumentSelected.set(true)
    const invalidJobType = 'UnsupportedJobType' as any
    component.edaiFormGroup.controls.jobType.setValue(invalidJobType)
    component.edaiFormGroup.controls.basicJobModel.patchValue({
      jobName: 'UnknownJob',
    })

    // WHEN the user submits
    component.submit()

    // THEN submit should not proceed, and no specialized form data should be built
    expect(aiFacadeMock.createJobEdai).not.toHaveBeenCalled()
  })

  it('should handle a success object if an error is also present, prioritizing success scenario in #selectJobResponse', fakeAsync(() => {
    // GIVEN success and error are both emitted at the same time
    createJobEdaiSuccess$.next({ message: 'Conflicting success' })
    createJobEdaiError$.next({ status: 500, message: 'Some error' })
    fixture.detectChanges()
    tick()

    // THEN the logic uses the success message first, showing success
    expect(notificationServiceMock.show).toHaveBeenCalledWith(
      expect.objectContaining({ content: 'Conflicting success' })
    )
    // AND it switches to the status section
    expect(component.isStatusTab()).toBe(true)
  }))

  it('should do nothing if both success and error objects are falsy, preventing any notification or tab change', fakeAsync(() => {
    // GIVEN the facade emits null for both success and error
    createJobEdaiSuccess$.next(null)
    createJobEdaiError$.next(null)
    fixture.detectChanges()
    tick()

    // THEN no notification should appear
    expect(notificationServiceMock.show).not.toHaveBeenCalled()
    // AND the current tab should remain unchanged
    expect(component.isStatusTab()).toBe(false)
  }))
})
