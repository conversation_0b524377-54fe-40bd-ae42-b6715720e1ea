import { ComponentFixture, TestBed } from '@angular/core/testing'
import { EdaiDocumentPrivilegeListComponent } from './edai-document-privilege-list.component'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { ActivatedRoute } from '@angular/router'
import { AiFacade } from '@venio/data-access/ai'
import { of } from 'rxjs'

describe('EdaiDocumentRelevancyListComponent', () => {
  let component: EdaiDocumentPrivilegeListComponent
  let fixture: ComponentFixture<EdaiDocumentPrivilegeListComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EdaiDocumentPrivilegeListComponent],
      providers: [
        { provide: DialogRef, useValue: {} },
        provideMockStore({}),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        provideHttpClientTesting(),
        provideHttpClient(),
        provideNoopAnimations(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 1,
              },
            },
          },
        },
        {
          provide: AiFacade,
          useValue: {
            selectIsEdaiDocumentPrivilegeLoading$: of(false),
            selectEdaiDocumentPrivilegeSuccess$: of(undefined),
            selectEdaiDocumentPrivilegeError$: of(undefined),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(EdaiDocumentPrivilegeListComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create instance', () => {
    expect(component).toBeTruthy()
  })
})
