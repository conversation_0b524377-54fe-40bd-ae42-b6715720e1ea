import { ComponentFixture, TestBed } from '@angular/core/testing'
import { EdaiPiiStatusDetailComponent } from './edai-pii-status-detail.component'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { ActivatedRoute } from '@angular/router'
import { Observable, of } from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { AiSearchService } from '@venio/data-access/ai'
import { WINDOW, windowFactory } from '@venio/data-access/iframe-messenger'
import { PLATFORM_ID } from '@angular/core'

describe('EdaiStatusDetailComponent', () => {
  let component: EdaiPiiStatusDetailComponent
  let fixture: ComponentFixture<EdaiPiiStatusDetailComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EdaiPiiStatusDetailComponent],
      providers: [
        { provide: DialogRef, useValue: {} },
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
        provideMockStore({}),
        {
          provide: DocumentsFacade,
          useValue: {},
        },
        {
          provide: SearchFacade,
          useValue: {},
        },
        {
          provide: FieldFacade,
          useValue: {},
        },
        provideHttpClientTesting(),
        provideHttpClient(),
        provideNoopAnimations(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 1,
              },
            },
          },
        },
        {
          provide: AiSearchService,
          useValue: {
            fetchJobStatusDetails: (): Observable<ResponseModel> =>
              of({} as ResponseModel),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(EdaiPiiStatusDetailComponent)
    component = fixture.componentInstance
    fixture.componentRef.setInput('selectedJobItem', { jobId: 1 })
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
