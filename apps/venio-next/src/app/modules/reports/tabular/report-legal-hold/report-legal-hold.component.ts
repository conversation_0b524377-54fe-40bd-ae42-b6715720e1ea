import {
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
  Injector,
  OnInit,
  runInInjectionContext,
  signal,
  ViewChild,
  OnDestroy,
  ChangeDetectorRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DropDownsModule,
  MultiSelectComponent,
  RemoveTagEvent,
} from '@progress/kendo-angular-dropdowns'
import { IconsModule } from '@progress/kendo-angular-icons'
import { caretAltDownIcon } from '@progress/kendo-svg-icons'
import { distinctUntilChanged, filter, Subject, takeUntil } from 'rxjs'
import { LegalHoldReportFacade } from '@venio/data-access/common'
import {
  CustodianListModel,
  LegalHoldsModel,
} from '@venio/shared/models/interfaces'
import { CheckBoxModule, InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { ReportsFacade } from '@venio/data-access/reports'
import { ReportTypes } from '@venio/shared/models/constants'
import { toSignal } from '@angular/core/rxjs-interop'
import { ButtonsModule } from '@progress/kendo-angular-buttons'

@Component({
  selector: 'venio-report-legal-hold',
  standalone: true,
  imports: [
    CommonModule,
    DropDownsModule,
    IconsModule,
    CheckBoxModule,
    LabelModule,
    InputsModule,
    ButtonsModule,
  ],
  templateUrl: './report-legal-hold.component.html',
  styleUrl: './report-legal-hold.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReportLegalHoldComponent implements OnInit, OnDestroy {
  @ViewChild('custodianSelection')
  private custodianSelection!: MultiSelectComponent

  private readonly toDestroy$ = new Subject<void>()

  private readonly reportFacade = inject(ReportsFacade)

  private readonly legalHoldReportFacade = inject(LegalHoldReportFacade)

  private injector = inject(Injector)

  private cdr = inject(ChangeDetectorRef)

  @ViewChild(MultiSelectComponent)
  public multiSelect: MultiSelectComponent

  public readonly filteredLegalHold = signal<any[]>([])

  public readonly legalHold = signal<LegalHoldsModel[]>([])

  public readonly isAllLegalHoldChecked = signal<boolean>(true)

  public readonly selectedLegalHold = signal<number[]>([])

  public readonly selectedReportType = signal<ReportTypes>(
    ReportTypes.MATTER_DETAIL_REPORT
  )

  public readonly chevronDownIcon = caretAltDownIcon

  public readonly filteredCustodians = signal<any[]>([])

  public readonly custodians = signal<CustodianListModel[]>([])

  public readonly isAllCustodiansChecked = signal<boolean>(true)

  public readonly selectedCustodians = signal<number[]>([])

  public readonly filteredStatuses = signal<any[]>([])

  public readonly statuses = signal<any[]>([])

  public readonly isAllStatusesChecked = signal<boolean>(true)

  public readonly selectedStatuses = signal<number[]>([])

  public isCustodianListLoading = toSignal(
    this.legalHoldReportFacade.selectCustodianList$,
    { initialValue: false }
  )

  public legalHoldSelectionChange(userIds: number[]): void {
    this.selectedLegalHold.set(userIds)
    if (this.selectedLegalHold().length) {
      this.isAllLegalHoldChecked.set(false)
    }
    this.#fetchCustodianList(this.selectedLegalHold())
    this.#storeSelectedLegalHold()
  }

  public custodianSelectionChange(userIds: number[]): void {
    this.selectedCustodians.set(userIds)
    if (this.selectedCustodians().length) {
      this.isAllCustodiansChecked.set(false)
    }
    this.#storeSelectedCustodians()
  }

  public allCustodiansSelectionChange(checked: boolean): void {
    this.isAllCustodiansChecked.set(checked)
    if (checked) {
      this.selectedCustodians.set(
        this.custodians().map((item) => item.custodianId)
      )
    } else {
      this.selectedCustodians.set([])
      this.multiSelect.tags = this.multiSelect.tags.filter(
        (tag) => tag.custodianId !== null
      )
    }
  }

  public statusSelectionChange(statusIds: number[]): void {
    this.selectedStatuses.set(statusIds)
    if (this.selectedStatuses().length) {
      this.isAllStatusesChecked.set(false)
    }
    this.#storeSelectedStatuses()
  }

  public allStatusesSelectionChange(checked: boolean): void {
    this.isAllStatusesChecked.set(checked)
    if (checked) {
      this.selectedStatuses.set(this.statuses().map((item) => item.statusID))
    } else {
      this.selectedStatuses.set([])
      this.multiSelect.tags = this.multiSelect.tags.filter(
        (tag) => tag.statusID !== null
      )
    }
  }

  public tagMapper = (items: any[]): any[] | any[][] => {
    if (this.isAllLegalHoldChecked()) {
      // if there is a default value or all users, which in this case, should be shown as well.
      return [
        {
          holdId: null,
          clientMatterName: 'All Legal Holds',
        } as LegalHoldsModel,
      ]
    }

    if (!items?.[0]) return []

    return items.length === 1 ? items : [items]
  }

  public custodianTagMapper = (items: any[]): any[] | any[][] => {
    if (this.isAllCustodiansChecked()) {
      return [
        {
          custodianId: null,
          custodianName: 'All Custodians',
        } as CustodianListModel,
      ]
    }

    if (!items?.[0]) return []

    return items.length === 1 ? items : [items]
  }

  public statusTagMapper = (items: any[]): any[] | any[][] => {
    if (this.isAllStatusesChecked()) {
      return [
        {
          statusID: null,
          statusName: 'All Status',
        } as any,
      ]
    }

    if (!items?.[0]) return []

    return items.length === 1 ? items : [items]
  }

  public ngOnInit(): void {
    this.#selectSelectedReportType()
    this.#fetchLegalHoldList()
    this.#fetchStatusList()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public filterLegalHolds(value: string): void {
    const filterValue = value.toLowerCase()
    this.filteredLegalHold.set(
      this.legalHold().filter((item) =>
        item.clientMatterName.toLowerCase().includes(filterValue)
      )
    )
  }

  public filterCustodians(value: string): void {
    const filterValue = value.toLowerCase()
    this.filteredCustodians.set(
      this.custodians().filter((item) =>
        item.custodianName.toLowerCase().includes(filterValue)
      )
    )
  }

  public filterStatuses(value: string): void {
    const filterValue = value.toLowerCase()
    this.filteredStatuses.set(
      this.statuses().filter((item) =>
        item.statusName.toLowerCase().includes(filterValue)
      )
    )
  }

  public chevDownIconClick(userSelection: MultiSelectComponent): void {
    userSelection.toggle()
    userSelection.focus()
  }

  public allLegalHoldSelectionChange(checked: boolean): void {
    this.isAllLegalHoldChecked.set(checked)
    if (checked) {
      this.selectedLegalHold.set(this.legalHold().map((item) => item.holdId))
      this.#fetchCustodianList(this.legalHold().map((item) => item.holdId))
    } else {
      this.selectedLegalHold.set([])
      this.multiSelect.tags = this.multiSelect.tags.filter(
        (tag) => tag.holdId !== null
      )
    }
  }

  public handleAllLegalHoldClick(
    event: MouseEvent,
    input: HTMLInputElement
  ): void {
    if (event.target['nodeName'] !== 'INPUT') {
      event.stopPropagation()
      event.preventDefault()
      input.checked = !input.checked
    }

    this.allLegalHoldSelectionChange(input.checked)
  }

  public handleAllCustodiansClick(
    event: MouseEvent,
    input: HTMLInputElement
  ): void {
    if (event.target['nodeName'] !== 'INPUT') {
      event.stopPropagation()
      event.preventDefault()
      input.checked = !input.checked
    }

    this.allCustodiansSelectionChange(input.checked)
  }

  public handleAllStatusesClick(
    event: MouseEvent,
    input: HTMLInputElement
  ): void {
    if (event.target['nodeName'] !== 'INPUT') {
      event.stopPropagation()
      event.preventDefault()
      input.checked = !input.checked
    }

    this.allStatusesSelectionChange(input.checked)
  }

  public removeTag(event: RemoveTagEvent): void {
    const updates = [
      {
        condition: event.dataItem.holdId === null,
        signal: this.isAllLegalHoldChecked,
        selection: this.selectedLegalHold,
      },
      {
        condition: event.dataItem.statusID === null,
        signal: this.isAllStatusesChecked,
        selection: this.selectedStatuses,
      },
      {
        condition: event.dataItem.custodianId === null,
        signal: this.isAllCustodiansChecked,
        selection: this.selectedCustodians,
      },
    ]

    let shouldDetectChanges = false

    updates.forEach(({ condition, signal, selection }) => {
      if (condition) {
        signal.set(false)
        selection.set([])
        shouldDetectChanges = true
      }
    })

    if (shouldDetectChanges) {
      this.cdr.detectChanges()
    }
  }

  #fetchLegalHoldList(): void {
    this.legalHoldReportFacade.fetchLegalHoldList()
    this.legalHoldReportFacade.selectLegalHoldList$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((response: any) => {
        if (response) {
          this.selectedCustodians.set(response.map((item) => item.holdId))
          this.#fetchCustodianList(this.selectedCustodians())
          this.legalHold.set(response)
          this.filteredLegalHold.set(this.legalHold())
          if (this.isAllLegalHoldChecked()) {
            this.selectedLegalHold.set(
              this.legalHold().map((item) => item.holdId)
            )
          }
        }
      })
  }

  #fetchCustodianList(holdIds: number[]): void {
    this.legalHoldReportFacade.fetchCustodianList(holdIds)
    this.legalHoldReportFacade.selectCustodianListSuccessResponse$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((response: any) => {
        if (response) {
          this.custodians.set(response?.data)
          this.filteredCustodians.set(this.custodians())
          if (this.isAllCustodiansChecked()) {
            this.selectedCustodians.set(
              this.custodians().map((item) => item.custodianId)
            )
          }
        }
      })
  }

  #fetchStatusList(): void {
    this.legalHoldReportFacade.fetchStatus(),
      this.legalHoldReportFacade.selectStatusList$
        .pipe(takeUntil(this.toDestroy$))
        .subscribe((response) => {
          this.statuses.set(response?.data)
          this.filteredStatuses.set(this.statuses())

          if (this.isAllStatusesChecked()) {
            this.selectedStatuses.set(
              this.statuses().map((item) => item.statusID)
            )
          }
        })
  }

  #storeSelectedLegalHold(): void {
    runInInjectionContext(this.injector, () =>
      effect(
        () => {
          this.reportFacade.storeSelectedLegalHold(this.selectedLegalHold())
        },
        { allowSignalWrites: true }
      )
    )
  }

  #storeSelectedCustodians(): void {
    runInInjectionContext(this.injector, () =>
      effect(
        () => {
          this.reportFacade.storeSelectedCustodians(this.selectedCustodians())
        },
        { allowSignalWrites: true }
      )
    )
  }

  #storeSelectedStatuses(): void {
    runInInjectionContext(this.injector, () =>
      effect(
        () => {
          this.reportFacade.storeSelectedStatuses(this.selectedStatuses())
        },
        { allowSignalWrites: true }
      )
    )
  }

  #selectSelectedReportType(): void {
    this.reportFacade.selectedSelectedReportType$
      .pipe(
        distinctUntilChanged(),
        filter((type) => Boolean(type)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((type) => {
        this.selectedReportType.set(type)
      })
  }
}
