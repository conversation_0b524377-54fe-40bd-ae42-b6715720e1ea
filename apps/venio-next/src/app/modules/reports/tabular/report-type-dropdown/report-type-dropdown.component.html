@if((isMicroApp | async) === false) {
<div class="t-text-primary t-font-bold t-uppercase t-text-lg">
  {{ selectedReportType?.description }}
</div>
}
<div class="t-flex t-flex-row t-gap-6">
  <kendo-dropdownlist
    class="t-max-w-[18rem]"
    [(ngModel)]="selectedReportType"
    [data]="listItems"
    (valueChange)="reportTypeChange($event)"
    valueField="type"
    textField="label">
    <ng-template kendoSuffixTemplate>
      <kendo-svg-icon [icon]="chevronDownIcon"></kendo-svg-icon>
    </ng-template>
  </kendo-dropdownlist>
  @if(isExporting()){
  <div class="t-flex t-flex-col t-grow t-self-end t-relative t-max-w-[26.7rem]">
    <div
      class="t-text-success t-text-base t-font-bold t-flex t-justify-between">
      {{ selectedReportType?.description }} Download in progress
      <span class="t-text-primary">{{ computedProgressBarValue() }}%</span>
    </div>

    <kendo-progressbar
      class="t-h-1.5"
      progressCssClass="t-bg-primary t-h-1.5 t-rounded"
      [label]="false"
      [animation]="progressBarAnimation"
      [value]="progressBarValue()"></kendo-progressbar>
  </div>
  }
</div>
