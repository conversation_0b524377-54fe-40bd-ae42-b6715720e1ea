import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReportContainerComponent } from './report-container.component'
import { provideMockStore } from '@ngrx/store/testing'
import { NotificationService } from '@progress/kendo-angular-notification'
import { provideAnimations } from '@angular/platform-browser/animations'
import {
  AppIdentitiesTypes,
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { PLATFORM_ID } from '@angular/core'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { StartupsFacade } from '@venio/data-access/review'

describe('ReportContainerComponent', () => {
  let component: ReportContainerComponent
  let fixture: ComponentFixture<ReportContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReportContainerComponent,
        EffectsModule.forRoot([]),
        StoreModule.forRoot({}),
        IframeMessengerModule.forRoot({
          origin: '*',
          iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
        }),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        NotificationService,
        provideAnimations(),
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
        {
          provide: StartupsFacade,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReportContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
