import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReportUserDropdownComponent } from './report-user-dropdown.component'
import { provideMockStore } from '@ngrx/store/testing'
import { provideAnimations } from '@angular/platform-browser/animations'

describe('ReportUserDropdownComponent', () => {
  let component: ReportUserDropdownComponent
  let fixture: ComponentFixture<ReportUserDropdownComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReportUserDropdownComponent],
      providers: [provideMockStore({}), provideAnimations()],
    }).compileComponents()

    fixture = TestBed.createComponent(ReportUserDropdownComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
