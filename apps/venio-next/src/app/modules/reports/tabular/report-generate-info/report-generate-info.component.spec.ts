import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReportGenerateInfoComponent } from './report-generate-info.component'
import { provideMockStore } from '@ngrx/store/testing'

describe('ReportGenerateInfoComponent', () => {
  let component: ReportGenerateInfoComponent
  let fixture: ComponentFixture<ReportGenerateInfoComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReportGenerateInfoComponent],
      providers: [provideMockStore({})],
    }).compileComponents()

    fixture = TestBed.createComponent(ReportGenerateInfoComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
