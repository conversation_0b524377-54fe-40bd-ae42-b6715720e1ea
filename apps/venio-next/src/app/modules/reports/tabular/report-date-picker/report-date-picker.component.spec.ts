import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReportDatePickerComponent } from './report-date-picker.component'
import { provideMockStore } from '@ngrx/store/testing'
import { provideAnimations } from '@angular/platform-browser/animations'

describe('ReportDatePickerComponent', () => {
  let component: ReportDatePickerComponent
  let fixture: ComponentFixture<ReportDatePickerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReportDatePickerComponent],
      providers: [provideMockStore({}), provideAnimations()],
    }).compileComponents()

    fixture = TestBed.createComponent(ReportDatePickerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
