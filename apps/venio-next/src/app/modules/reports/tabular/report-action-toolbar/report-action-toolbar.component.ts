import { ChangeDetectionStrategy, Component, inject } from '@angular/core'
import { CommonModule } from '@angular/common'
import { UiPaginationModule } from '@venio/ui/pagination'
import {
  copyIcon,
  dataCsvIcon,
  exportIcon,
  fileExcelIcon,
  fileIcon,
  filePdfIcon,
  SVGIcon,
} from '@progress/kendo-svg-icons'
import {
  CommonActionTypes,
  ExportReportFormatTypes,
} from '@venio/shared/models/constants'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { SVGIconModule } from '@progress/kendo-angular-icons'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { ReportsFacade } from '@venio/data-access/reports'
type ExportTypes = 'PDF' | 'CSV' | 'EXCEL' | 'PRINT'

interface FileExportActions {
  text: string
  svgIcon: SVGIcon
  exportType?: ExportTypes
  actionType: CommonActionTypes
}

@Component({
  selector: 'venio-report-action-toolbar',
  standalone: true,
  imports: [
    CommonModule,
    UiPaginationModule,
    ButtonsModule,
    SVGIconModule,
    TooltipsModule,
  ],
  templateUrl: './report-action-toolbar.component.html',
  styleUrl: './report-action-toolbar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReportActionToolbarComponent {
  public readonly exportReportFormatType = ExportReportFormatTypes

  public actionButtons = [
    // TODO: print seems not to be used
    // {
    //   svgIcon: printIcon,
    //   actionType: CommonActionTypes.PRINT,
    // },
    {
      svgIcon: exportIcon,
      actionType: CommonActionTypes.EXPORT,
    },
  ]

  public printOptions = [
    {
      text: 'Print Current Page',
      svgIcon: fileIcon,
      actionType: CommonActionTypes.PRINT,
      page: 'current',
    },
    {
      text: 'Print All Pages',
      svgIcon: copyIcon,
      actionType: CommonActionTypes.PRINT,
      page: 'all',
    },
  ]

  public actions: FileExportActions[] = [
    {
      text: 'Export to PDF',
      svgIcon: filePdfIcon,
      exportType: ExportReportFormatTypes.PDF,
      actionType: CommonActionTypes.EXPORT,
    },
    {
      text: 'Export to CSV',
      svgIcon: dataCsvIcon,
      exportType: ExportReportFormatTypes.CSV,
      actionType: CommonActionTypes.EXPORT,
    },
    {
      text: 'Export to Excel',
      svgIcon: fileExcelIcon,
      exportType: ExportReportFormatTypes.EXCEL,
      actionType: CommonActionTypes.EXPORT,
    },
  ]

  private readonly reportFacade = inject(ReportsFacade)

  public readonly selectTotalReportCount$ =
    this.reportFacade.selectTotalReportCount$

  public readonly selectPaging$ = this.reportFacade.selectPaging$

  public readonly totalCount$ = this.selectTotalReportCount$

  public pagingChange(event: { pageNumber: number; pageSize: number }): void {
    this.reportFacade.storePaging(event)
    this.reportFacade.fetchReports()
  }

  public exportReportTo(exportType: object): void {
    this.reportFacade.notifyExportReportToGivenFormat(exportType)
  }

  public largeDataWarningClass(item: unknown, count: number): string {
    if (item['exportType'] === ExportReportFormatTypes.PDF)
      return count > 1000 ? 't-text-error hover:t-text-white' : ''
  }
}
