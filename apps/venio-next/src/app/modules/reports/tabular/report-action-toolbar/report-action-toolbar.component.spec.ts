import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReportActionToolbarComponent } from './report-action-toolbar.component'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideAnimations } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'

describe('ReportActionToolbarComponent', () => {
  let component: ReportActionToolbarComponent
  let fixture: ComponentFixture<ReportActionToolbarComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReportActionToolbarComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        provideAnimations(),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReportActionToolbarComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
