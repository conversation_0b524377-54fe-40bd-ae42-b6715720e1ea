import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReportTableComponent } from './report-table.component'
import { provideMockStore } from '@ngrx/store/testing'
import { NotificationService } from '@progress/kendo-angular-notification'

describe('ReportTableComponent', () => {
  let component: ReportTableComponent
  let fixture: ComponentFixture<ReportTableComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReportTableComponent],
      providers: [provideMockStore({}), NotificationService],
    }).compileComponents()

    fixture = TestBed.createComponent(ReportTableComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
