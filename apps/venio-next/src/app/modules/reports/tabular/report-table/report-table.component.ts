import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { GridComponent, PDFModule } from '@progress/kendo-angular-grid'

import { ReusableGridComponent } from '../reusable-grid/reusable-grid.component'
import { ReportTableConfigService } from './report-table-config.service'
import { UserFacade } from '@venio/data-access/common'
import dayjs from 'dayjs'

@Component({
  selector: 'venio-report-table',
  standalone: true,
  providers: [ReportTableConfigService],
  imports: [CommonModule, ReusableGridComponent, PDFModule],
  templateUrl: './report-table.component.html',
  styleUrl: './report-table.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReportTableComponent implements OnInit, AfterViewInit {
  @ViewChild(ReusableGridComponent, { static: true })
  private reusableGrid: ReusableGridComponent

  /**
   * Login date template
   */
  @ViewChild('loginDateTemplate', { static: true })
  public loginDateTemplate: TemplateRef<any>

  /**
   * Logout date template
   */
  @ViewChild('logoutDateTemplate', { static: true })
  public logoutDateTemplate: TemplateRef<any>

  /**
   * Locked date template
   */
  @ViewChild('lockedDateTemplate', { static: true })
  public lockedDateTemplate: TemplateRef<any>

  @ViewChild('pdfHeaderFooterTemplate', { static: true })
  public pdfHeaderFooterTemplate: TemplateRef<any>

  @ViewChild('dataExportStartDateTemplate', { static: true })
  public dataExportStartDateTemplate: TemplateRef<any>

  @ViewChild('dataExportEndDateTemplate', { static: true })
  public dataExportEndDateTemplate: TemplateRef<any>

  @ViewChild('userCreationPerformedDateTemplate', { static: true })
  public userCreationPerformedDateTemplate: TemplateRef<any>

  @ViewChild('roleChangeRoleFromToTemplate', { static: true })
  public roleChangeRoleFromToTemplate: TemplateRef<any>

  @ViewChild('roleChangeRoleUpdatedDateTemplate', { static: true })
  public roleChangeRoleUpdatedDateTemplate: TemplateRef<any>

  @ViewChild('deletedExportsDeletedOnDateTemplate', { static: true })
  public deletedExportsDeletedOnDateTemplate: TemplateRef<any>

  @ViewChild('projectAccessDateTemplate', { static: true })
  public projectAccessDateTemplate: TemplateRef<any>

  @ViewChild('activityDateTemplate', { static: true })
  public activityDateTemplate: TemplateRef<any>

  @ViewChild('legalHoldNoticeTemplate', { static: true })
  public legalHoldNoticeTemplate: TemplateRef<any>

  @ViewChild('legalHoldTemplateType', { static: true })
  public legalHoldTemplateType: TemplateRef<any>

  @ViewChild('legalHoldStatusTemplate', { static: true })
  public legalHoldStatusTemplate: TemplateRef<any>

  @ViewChild('sentOnDateTemplate', { static: true })
  public sentOnDateTemplate: TemplateRef<any>

  @ViewChild('acceptedOnDateTemplate', { static: true })
  public acceptedOnDateTemplate: TemplateRef<any>

  @ViewChild('legalHoldHiredDateTemplate', { static: true })
  public legalHoldHiredDateTemplate: TemplateRef<any>

  @ViewChild('legalHoldTerminatedDateTemplate', { static: true })
  public legalHoldTerminatedDateTemplate: TemplateRef<any>

  public readonly reportTableConfigService = inject(ReportTableConfigService)

  public readonly userFacade = inject(UserFacade)

  public readonly userName$ = this.userFacade.selectCurrentUsername$

  private readonly changeDetectorRef = inject(ChangeDetectorRef)

  public currentDate: string

  public currentTime: string

  /**
   * Grid component getter. The grid component is used to access the kendo grid API.
   */
  private get grid(): GridComponent {
    return this.reusableGrid.grid
  }

  public ngOnInit(): void {
    this.#initSelects()
  }

  public ngAfterViewInit(): void {
    this.#initGridConfig()
    this.reportTableConfigService.selectSelectedReportType()
  }

  #setCurrentDate(): void {
    this.currentDate = dayjs().format('DD-MM-YYYY')
    this.currentTime = dayjs().format('h:mm A')
  }

  #initSelects(): void {
    this.reportTableConfigService.selectPaging()
    this.reportTableConfigService.selectReportResponses()
    this.reportTableConfigService.selectExportReportToFormat()
  }

  #initGridConfig(): void {
    this.changeDetectorRef.markForCheck()
    // Set the grid component in the service so that it can be accessed from the service
    this.reportTableConfigService.setGridComponent = this.grid

    const templateMaps = [
      'loginDateTemplate',
      'logoutDateTemplate',
      'lockedDateTemplate',
      'dataExportStartDateTemplate',
      'dataExportEndDateTemplate',
      'userCreationPerformedDateTemplate',
      'roleChangeRoleFromToTemplate',
      'roleChangeRoleUpdatedDateTemplate',
      'pdfHeaderFooterTemplate',
      'deletedExportsDeletedOnDateTemplate',
      'projectAccessDateTemplate',
      'activityDateTemplate',
      'sentOnDateTemplate',
      'acceptedOnDateTemplate',
      'legalHoldStatusTemplate',
      'legalHoldTemplateType',
      'legalHoldNoticeTemplate',
      'legalHoldHiredDateTemplate',
      'legalHoldTerminatedDateTemplate',
    ].reduce((template, key) => {
      template[key] = this[key]
      return template
    }, {} as Record<string, TemplateRef<any>>)

    this.reportTableConfigService.setTemplates = templateMaps
  }
}
