import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReportGenerateButtonComponent } from './report-generate-button.component'
import { provideMockStore } from '@ngrx/store/testing'

describe('ReportGenerateButtonComponent', () => {
  let component: ReportGenerateButtonComponent
  let fixture: ComponentFixture<ReportGenerateButtonComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReportGenerateButtonComponent],
      providers: [provideMockStore({})],
    }).compileComponents()

    fixture = TestBed.createComponent(ReportGenerateButtonComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
