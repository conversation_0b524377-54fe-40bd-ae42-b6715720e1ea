import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LoginForgotPasswordComponent } from './login-forgot-password.component'
import { DialogRef } from '@progress/kendo-angular-dialog'
import {
  AppIdentitiesTypes,
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { EffectsModule } from '@ngrx/effects'
import { provideMockStore } from '@ngrx/store/testing'
import { provideAnimations } from '@angular/platform-browser/animations'
import { PLATFORM_ID } from '@angular/core'
import { StoreModule } from '@ngrx/store'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('LoginForgotPasswordComponent', () => {
  let component: LoginForgotPasswordComponent
  let fixture: ComponentFixture<LoginForgotPasswordComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        LoginForgotPasswordComponent,
        IframeMessengerModule.forRoot({
          origin: '*',
          iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
        }),
        EffectsModule.forRoot([]),
        StoreModule.forRoot({}),
      ],
      providers: [
        provideMockStore({}),
        provideAnimations(),
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
        {
          provide: DialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(LoginForgotPasswordComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
