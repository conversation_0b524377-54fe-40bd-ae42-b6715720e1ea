<kendo-dialog-titlebar>
  <button class="t-flex t-items-center" (click)="closeDialog()">
    <kendo-svgicon
      [icon]="iconLeft"
      class="t-text-[#9BD2A7] t-w-8 t-h-7"></kendo-svgicon>
    <kendo-label class="t-text-xs t-font-black t-text-[#000000]">
      LOGIN
    </kendo-label>
  </button>
</kendo-dialog-titlebar>

<div *ngIf="resetLinkRequestResponse()">
  <div class="t-flex t-items-start t-space-x-1 t-mt-4">
    <div class="t-flex-shrink-0">
      <div
        class="t-w-[37px] t-h-[37px] t-flex t-items-center t-justify-center t-rounded-full t-bg-[#F1EFEF] t-mr-2">
        <img
          ngSrc="assets/svg/icon-email-new.svg"
          alt="Forgot Password Icon"
          class="t-items-start"
          height="17"
          width="23" />
      </div>
    </div>

    <div class="t-flex t-flex-col">
      <span
        class="t-text-[1.65rem] t-font-black t-mb-2"
        [ngClass]="
          resetLinkRequestResponse().type === 'error'
            ? 't-text-error'
            : 't-text-[#030303]'
        "
        >{{
          resetLinkRequestResponse().type === 'success' ? 'EMAIL SENT' : 'ERROR'
        }}
      </span>
      <span
        *ngIf="resetLinkRequestResponse().type === 'success'"
        class="t-text-sm/4 t-font-normal t-mt-4 t-mb-10 t-align-left t-text-left"
        >Email with reset password link was successfully sent to your registered
        email ID.
        <br />
        <br />
        Please click on the link and follow the instruction</span
      >
      <span
        *ngIf="resetLinkRequestResponse().type === 'error'"
        class="t-text-sm/4 t-font-normal t-mt-4 t-mb-10 t-align-left t-text-left t-text-error">
        {{ resetLinkRequestResponse().message }}
      </span>
    </div>
  </div>
</div>
<div *ngIf="!resetLinkRequestResponse()">
  <form
    #forgotPasswordForm="ngForm"
    (keydown.enter)="forgetPasswordClick(forgotPasswordForm)">
    <div class="t-flex t-flex-col t-items-center t-justify-center t-p-4">
      <div class="t-flex t-w-full t-flex-col">
        <div class="t-flex t-flex-row t-items-start">
          <div
            class="t-w-[37px] t-h-[37px] t-flex t-items-center t-justify-center t-rounded-full t-bg-[#F1EFEF] t-mr-2">
            <img
              ngSrc="assets/svg/icon-forgot-password.svg"
              height="24"
              width="24"
              alt="Forgot Password Icon" />
          </div>
          <div class="t-flex t-flex-col t-items-start t-justify-center">
            <p class="t-text-[#030303] t-text-[1.65rem] t-font-black t-mb-2">
              FORGOT PASSWORD
            </p>
            <p
              class="t-text-[#FFBB12] t-text-lg/5 t-font-black t-mb-6 t-align-left t-text-left t-mr-4">
              Please enter your email.
            </p>
          </div>
        </div>
      </div>
      <kendo-formfield class="t-w-full t-align-left t-mb-2">
        <kendo-textbox
          #usernameInput
          #usernameModel="ngModel"
          name="username"
          required
          [(ngModel)]="username"
          class="t-w-full t-align-left v-input-shadow"
          type="text"
          id="username"
          placeholder="Email Address">
        </kendo-textbox>
        <div
          *ngIf="
            (usernameModel.errors &&
              usernameModel.dirty &&
              usernameModel.touched) ||
            (usernameModel.dirty &&
              usernameModel.touched &&
              usernameModel.value?.trim() === '')
          "
          class="t-m-1 t-accent-error t-text-error">
          {{
            usernameModel.hasError('required')
              ? 'Email address is required'
              : 'Please enter a valid email address'
          }}
        </div>
      </kendo-formfield>
      <button
        (click)="forgetPasswordClick(forgotPasswordForm)"
        kendoButton
        [disabled]="isLinkRequesting()"
        class="t-bg-[#9BD2A7] t-text-white t-w-full t-py-2 t-px-4 t-mb-3 t-mt-2 t-h-[2.5rem] t-rounded-xl t-drop-shadow-md t-font-sans t-text-sm t-border-none">
        <kendo-loader
          *ngIf="isLinkRequesting()"
          type="pulsing"
          themeColor="success"></kendo-loader>
        Submit
      </button>
    </div>
  </form>
</div>
