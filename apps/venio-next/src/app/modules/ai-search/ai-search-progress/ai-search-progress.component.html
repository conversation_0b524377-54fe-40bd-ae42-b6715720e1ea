<div
  #parentElement
  class="t-flex t-flex-col t-gap-1 t-p-1 t-mt-4 t-h-[336px] t-overflow-auto t-relative">
  @for(m of progressMessages(); track m.message;let last = $last) {
  <div class="t-inline-block t-relative">
    <div [innerHTML]="formatMarkdown(m.message)"></div>
    @if(last && isSearchLoading()){
    <kendo-skeleton
      venioAutoScrollOrFocus
      scrollBehavior="instant"
      [parentElement]="parentElement"
      [shouldFocusOrScroll]="updateTrigger"
      class="t-inline-block t-bg-success"
      shape="circle"
      [height]="10"
      [width]="10"></kendo-skeleton>
    }
  </div>
  } @if(progressMessages().length === 0 && isSearchLoading()) {
  <kendo-skeleton
    venioAutoScrollOrFocus
    scrollBehavior="instant"
    [shouldFocusOrScroll]="updateTrigger"
    [height]="15"
    width="100%"></kendo-skeleton>
  <kendo-skeleton [height]="15" width="90%"></kendo-skeleton>
  <kendo-skeleton [height]="15" width="70%"></kendo-skeleton>
  }
</div>
