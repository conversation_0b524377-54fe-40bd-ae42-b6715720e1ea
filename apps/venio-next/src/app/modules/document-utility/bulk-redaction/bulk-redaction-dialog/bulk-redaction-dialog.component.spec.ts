import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BulkRedactionDialogComponent } from './bulk-redaction-dialog.component'
import { ActivatedRoute } from '@angular/router'
import {
  BulkRedactFacade,
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http'
import { VenioNotificationService } from '@venio/feature/notification'
import { NotificationService } from '@progress/kendo-angular-notification'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'

describe('BulkRedactionDialogComponent', () => {
  let component: BulkRedactionDialogComponent
  let fixture: ComponentFixture<BulkRedactionDialogComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BulkRedactionDialogComponent, NoopAnimationsModule],
      providers: [
        provideMockStore({}),
        provideHttpClient(withInterceptorsFromDi()),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        BulkRedactFacade,
        BreadcrumbFacade,
        VenioNotificationService,
        NotificationService,
        StartupsFacade,
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 2,
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(BulkRedactionDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
