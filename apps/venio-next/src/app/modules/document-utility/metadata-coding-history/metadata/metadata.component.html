<div class="t-flex">
  <div class="t-flex t-p-3 t-flex-col t-w-full">
    <div class="t-relative">
      <div
        *ngIf="isMetadataLoading$ | async"
        class="k-i-loading t-absolute t-h-full t-w-full t-bg-[rgba(255,255,255,0.47)] t-text-[rgba(18,17,17,0.93)] t-top-0 t-left-0 t-right-0 t-bottom-0 t-text-[58px] t-z-10"></div>
      <div class="t-flex t-flex-col t-min-h-full">
        @if (isFieldsExists()) {
        <cdk-virtual-scroll-viewport
          class="v-custom-metadata-viewport"
          itemSize="15">
          <div
            *cdkVirtualFor="
              let metadata of documentMetadata;
              trackBy: trackByMetadataFn
            ">
            <div class="t-pr-3 t-pt-1 t-pt-1">
              <kendo-label
                kendoTooltip
                class="t-text-xs t-uppercase t-mb-3 t-tracking-widest t-font-bold">
                {{ metadata.key }}
              </kendo-label>
              <div
                class="t-flex t-gap-4 t-text-sm t-text-[#7e7e7e] t-pl-1 t-mt-1">
                {{ metadata.value }}
              </div>
            </div>
          </div>
        </cdk-virtual-scroll-viewport>
        } @else{
        <div class="t-gap-3">
          <p class="t-text-center">No record available.</p>
        </div>
        }
      </div>
    </div>
  </div>
</div>
