import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentPrintDownloadContainerComponent } from './document-print-download-container.component'
import {
  DocumentsFacade,
  FieldFacade,
  PrintDocumentFacade,
  PrintDocumentService,
  SearchFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DialogContainerService,
  DialogService,
} from '@progress/kendo-angular-dialog'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideRouter } from '@angular/router'

describe('DocumentPrintDownloadContainerComponent', () => {
  let component: DocumentPrintDownloadContainerComponent
  let fixture: ComponentFixture<DocumentPrintDownloadContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentPrintDownloadContainerComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideRouter([]),
        DialogService,
        DialogContainerService,
        DocumentsFacade,
        PrintDocumentFacade,
        PrintDocumentService,
        provideMockStore({}),
        SearchFacade,
        FieldFacade,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentPrintDownloadContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
