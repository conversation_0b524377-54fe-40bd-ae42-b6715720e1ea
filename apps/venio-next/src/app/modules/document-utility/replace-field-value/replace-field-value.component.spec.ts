import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReplaceFieldValueComponent } from './replace-field-value.component'
import { DialogService } from '@progress/kendo-angular-dialog'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideRouter } from '@angular/router'

describe('ReplaceFieldComponent', () => {
  let component: ReplaceFieldValueComponent
  let fixture: ComponentFixture<ReplaceFieldValueComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReplaceFieldValueComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideRouter([]),
        DocumentsFacade,
        DialogService,
        SearchFacade,
        FieldFacade,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReplaceFieldValueComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
