import {
  AfterViewInit,
  Component,
  HostListener,
  OnDestroy,
  Type,
  inject,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Subject, distinctUntilChanged, filter, takeUntil } from 'rxjs'
import {
  WindowMessage,
  WindowMessageType,
  WindowMessengerService,
} from '../../../../services/window.messenger.service'
import { LocalStorage } from '@venio/shared/storage'
import {
  DataAccessDocumentUtilityModule,
  PanelContentType,
  ReviewPanelFacade,
  ReviewPanelModel,
  ReviewPanelViewState,
  TagGroupInfo,
  UtilityPanel,
  UtilityPanelFacade,
} from '@venio/data-access/document-utility'
import { UtilityPanelStoreUpdateService } from '../../utility-services/utility-panel-store-update'
import {
  CompositeLayoutState,
  DocumentSearchScopeModel,
  FieldFacade,
  ReviewPanelFieldSelectionModel,
  ReviewPanelType,
  SearchResultFacade,
} from '@venio/data-access/review'
import { toSignal } from '@angular/core/rxjs-interop'

@Component({
  selector: 'venio-document-utility-panel-container',
  standalone: true,
  imports: [CommonModule, DataAccessDocumentUtilityModule],
  templateUrl: './document-utility-panel-container.component.html',
  styleUrl: './document-utility-panel-container.component.scss',
})
export class DocumentUtilityPanelContainerComponent
  implements AfterViewInit, OnDestroy
{
  private messengerService = inject(WindowMessengerService)

  private utilityPanelStoreUpdateService = inject(
    UtilityPanelStoreUpdateService
  )

  private utilityPanelFacade = inject(UtilityPanelFacade)

  private reviewPanelFacade = inject(ReviewPanelFacade)

  private fieldFacade: FieldFacade = inject(FieldFacade)

  private searchResultFacade = inject(SearchResultFacade)

  private reviewPanelViewState = inject(ReviewPanelViewState)

  private utilityPanelUIState: UtilityPanel

  private reviewPanelUIState: ReviewPanelModel

  private panelContent: PanelContentType

  private toDestroy$ = new Subject<void>()

  private fieldPanelMap: {
    [name in ReviewPanelType]?: ReviewPanelFieldSelectionModel
  }

  private currentDocumentSearchScope: DocumentSearchScopeModel

  public get isTagPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isTagPanelPopout')
  }

  public get isReviewPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isReviewPanelPopout')
  }

  public get isUtilityPanelDataSynced(): boolean {
    return LocalStorage.get<boolean>('isUtilityPanelDataSynced')
  }

  private layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  private searchResultEntities = toSignal(
    this.searchResultFacade.getAllSearchResultEntities
  )

  public documentUtilityPanelComponent: Promise<Type<unknown>>

  @HostListener('window:beforeunload')
  public readonly beforeUnload = (): void => {
    const messageType = this.isReviewPanelPopout
      ? WindowMessageType.REVIEW_PANEL_CLOSED
      : WindowMessageType.UTILITY_PANEL_CLOSED
    this.#preparePanelContent()
    this.#sendMessageToCloseWindow(messageType, this.panelContent)
  }

  public ngAfterViewInit(): void {
    this.#SelectPanelDataFromMessengerService()
    this.#selectUtitlityPanelUIState()
    this.#selectReviewPanelUIState()
    this.#selectReviewPanelFields()
    this.#selectDocumentExistsInSearchScope()

    this.#sendMessageToOpenerWindow(WindowMessageType.POPUP_UTILITY_PANEL_READY)

    if (!this.isTagPanelPopout) this.#loadLazyComponents()
  }

  #preparePanelContent(): void {
    const selectedTagGroup: TagGroupInfo = {
      title: this.reviewPanelViewState.tagGroupName(),
      actionType: this.reviewPanelViewState.selectedTagGroup(),
    }
    this.panelContent = {
      utilityPanel: this.utilityPanelUIState,
      reviewPanel: this.reviewPanelUIState,
      fieldPanelMap: this.fieldPanelMap,
      selectedTagGroup: selectedTagGroup,
      userSelectedLayout: this.layoutState.userSelectedLayout(),
      currentDocumentSearchScope: this.currentDocumentSearchScope,
      searchResultEntities: this.searchResultEntities(),
    }
  }

  #loadLazyComponents(): void {
    this.documentUtilityPanelComponent = import(
      '../document-utility-panel/document-utility-panel.component'
    ).then(({ DocumentUtilityPanelComponent }) => DocumentUtilityPanelComponent)
  }

  /**
   * Sends a message to the parent window to indicate that the utility panel is ready.
   * @param {WindowMessageType} messageType - The type of message to send.
   * @returns {void} This method does not return anything.
   */
  #sendMessageToOpenerWindow(messageType: WindowMessageType): void {
    const hasNoPopupWindow = !window.opener && !this.isTagPanelPopout
    if (hasNoPopupWindow || this.isUtilityPanelDataSynced) return

    if (this.isUtilityPanelDataSynced) return
    this.messengerService.sendMessage(
      {
        payload: { type: messageType, content: null },
      },
      window.opener
    )
  }

  #selectUtitlityPanelUIState(): void {
    this.utilityPanelFacade.selectUtilityPanel$
      .pipe(
        filter((result) => Boolean(result)),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe((utilityPanel: UtilityPanel) => {
        this.utilityPanelUIState = utilityPanel
      })
  }

  #selectReviewPanelUIState(): void {
    this.reviewPanelFacade.selectReviewPanelData$
      .pipe(
        filter((result) => Boolean(result)),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe((reviewPanel: ReviewPanelModel) => {
        this.reviewPanelUIState = reviewPanel
      })
  }

  #selectReviewPanelFields(): void {
    this.fieldFacade.selectFieldSelection$
      .pipe(
        filter((result) => Boolean(result)),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe((fieldPanelMap) => {
        this.fieldPanelMap = fieldPanelMap
      })
  }

  #selectDocumentExistsInSearchScope(): void {
    this.searchResultFacade.getDocumentExistsInSearchScope$
      .pipe(distinctUntilChanged(), takeUntil(this.toDestroy$))
      .subscribe((currentDocumentSearchScope) => {
        this.currentDocumentSearchScope = currentDocumentSearchScope
      })
  }

  /**
   * Sends a message to the parent window to inform about the closure of the utility panel
   * and sends the utility panel UI state.
   * @param {WindowMessageType} messageType - The type of message to send.
   * @param {PanelContentType} content - The content to include in the message.
   * @returns {void} This method does not return anything.
   */
  #sendMessageToCloseWindow(
    messageType: WindowMessageType,
    content: PanelContentType
  ): void {
    const hasNoPopupWindow =
      !window.opener && (!this.isTagPanelPopout || !this.isReviewPanelPopout)
    if (hasNoPopupWindow) return
    this.messengerService.sendMessage(
      {
        payload: { type: messageType, content: content },
      },
      window.opener
    )
  }

  /**
   * Updates store data on initial load or parent document change in the new window.
   * @returns {void} This method does not return anything.
   */
  #SelectPanelDataFromMessengerService(): void {
    if (!this.isTagPanelPopout) return
    this.messengerService.messageReceived
      .pipe(
        filter(
          (message: WindowMessage) =>
            Boolean(message?.payload?.type) &&
            this.isTagPanelPopout &&
            (message.payload.type === WindowMessageType.VIEW_PANEL ||
              message.payload.type === WindowMessageType.FILEID_CHANGED)
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((message: WindowMessage) => {
        this.utilityPanelStoreUpdateService.setMessengerData(message)
        if (message.payload.type === WindowMessageType.VIEW_PANEL) {
          console.log('VIEW_PANEL SET MESSENGER')
          this.#loadLazyComponents()
        }
      })
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
