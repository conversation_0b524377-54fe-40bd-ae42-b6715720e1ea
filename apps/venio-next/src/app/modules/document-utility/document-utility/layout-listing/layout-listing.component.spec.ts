import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LayoutListingComponent } from './layout-listing.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { ActivatedRoute } from '@angular/router'
import { VenioNotificationService } from '@venio/feature/notification'
import { StartupsFacade } from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { Observable, of } from 'rxjs'

describe('LayoutListingComponent', () => {
  let component: LayoutListingComponent
  let fixture: ComponentFixture<LayoutListingComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LayoutListingComponent],
      providers: [
        {
          provide: StartupsFacade,
          useValue: {
            hasGlobalRight$: (): Observable<any> => of(undefined),
          },
        },
        provideMockStore(),
        provideHttpClient(),
        provideHttpClientTesting(),
        VenioNotificationService,
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                id: '1',
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(LayoutListingComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
