import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LayoutMenuComponent } from './layout-menu.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { FieldFacade, StartupsFacade } from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { ActivatedRoute } from '@angular/router'

describe('LayoutMenuComponent', () => {
  let component: LayoutMenuComponent
  let fixture: ComponentFixture<LayoutMenuComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LayoutMenuComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore(),
        FieldFacade,
        StartupsFacade,
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                id: '1',
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(LayoutMenuComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
