import {
  ChangeDetectionStrategy,
  Component,
  computed,
  EventEmitter,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
  Output,
  signal,
  WritableSignal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import {
  eyeIcon,
  chevronDownIcon,
  chevronLeftIcon,
} from '@progress/kendo-svg-icons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import {
  FormsModule,
  ReactiveFormsModule,
  FormControl,
  FormGroup,
  FormBuilder,
  Validators,
  AsyncValidatorFn,
  AbstractControl,
} from '@angular/forms'
import { GridModule } from '@progress/kendo-angular-grid'
import { LabelModule } from '@progress/kendo-angular-label'
import { ListViewModule } from '@progress/kendo-angular-listview'
import {
  DialogRef,
  DialogService,
  DialogsModule,
  WindowRef,
} from '@progress/kendo-angular-dialog'
import {
  ProjectInfo,
  ClientModel,
  CompositeLayoutFacade,
  CompositeLayoutState,
  GroupInfo,
  LayoutPanel,
  LayoutProjectUserGroups,
  VIEWER_PANELS,
  Layout,
  Field,
  LayoutField,
  LayoutCreateRequestModel,
  LayoutResponseModel,
  LayoutClientProjectUserGroups,
} from '@venio/data-access/review'
import {
  EMPTY,
  Subject,
  switchMap,
  takeUntil,
  take,
  catchError,
  of,
  debounceTime,
  map,
  filter,
  distinctUntilChanged,
} from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { HttpErrorResponse } from '@angular/common/http'
import { VenioNotificationService } from '@venio/feature/notification'
import { PascalToSpacePipe } from '@venio/util/utilities'
import {
  CommonActionTypes,
  ExcludedPanelsFromRight,
  titleMapping,
} from '@venio/shared/models/constants'
import { UserFacade } from '@venio/data-access/common'
interface LayoutFormModel {
  layoutName: FormControl<string>
  sourceLayoutId: FormControl<number>
  isPrivate: FormControl<boolean>
  client: FormControl<ClientModel[]>
  case: FormControl<ProjectInfo[]>
  userGroup: FormControl<GroupInfo[]>
  isPanelSelected: FormControl<boolean>
  isViewerSelected: FormControl<boolean>
}

@Component({
  selector: 'venio-layout-panel-selection',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    InputsModule,
    IconsModule,
    DynamicHeightDirective,
    SvgLoaderDirective,
    IconsModule,
    DropDownsModule,
    FormsModule,
    ReactiveFormsModule,
    ListViewModule,
    GridModule,
    LabelModule,
    DialogsModule,
    PascalToSpacePipe,
  ],
  templateUrl: './layout-panel-selection.component.html',
  styleUrl: './layout-panel-selection.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LayoutPanelSelectionComponent implements OnInit, OnDestroy {
  @Output() public readonly mainLayout: EventEmitter<string> =
    new EventEmitter<string>()

  private layoutFacade: CompositeLayoutFacade = inject(CompositeLayoutFacade)

  public layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  public formBuilder: FormBuilder = inject(FormBuilder)

  private notificationFacade = inject(VenioNotificationService)

  private userFacade: UserFacade = inject(UserFacade)

  public selectedLayout = input<LayoutResponseModel>()

  public action = input<
    CommonActionTypes.CLONE | CommonActionTypes.EDIT | CommonActionTypes.CREATE
  >(CommonActionTypes.CREATE)

  public clientCaseGroupInfoOfLayout: LayoutClientProjectUserGroups

  private isEdit = computed(() => this.action() === CommonActionTypes.EDIT)

  private selectedLayoutId = computed(
    () => this.selectedLayout()?.layoutId ?? -1
  )

  public icons = {
    chevronDownIcon: chevronDownIcon,
    chevronLeftIcon: chevronLeftIcon,
    eyeIcon: eyeIcon,
  }

  public windowRef: WindowRef = inject(WindowRef) // Reference to the parent window

  public selectedPanelIds: number[] = []

  private toDestroy$: Subject<void> = new Subject<void>()

  private fetchCaseAction: Subject<number[]> = new Subject<number[]>()

  private fetchUseGroupAction: Subject<number[]> = new Subject<number[]>()

  public clients: WritableSignal<Array<ClientModel>> = signal([])

  public cases: WritableSignal<Array<ProjectInfo>> = signal([])

  public userGroups: WritableSignal<Array<GroupInfo>> = signal([])

  public currentUserRole: WritableSignal<string> = signal('')

  public viewablePanels: WritableSignal<Array<LayoutPanel>> = signal([])

  public nonViewablePanels = computed(() => {
    return this.viewablePanels().filter((f) => !f.isSelected)
  })

  public viewerPanels: WritableSignal<Array<LayoutPanel>> = signal([])

  public layoutFormGroup: FormGroup<LayoutFormModel>

  public get formControls(): LayoutFormModel {
    return this.layoutFormGroup?.controls
  }

  constructor(private dialogService: DialogService) {}

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngOnInit(): void {
    this.#initForm()
    this.#handlePrivateLayoutChange()
    this.#init()
    this.selectCurrentUserRole()
  }

  private selectCurrentUserRole(): void {
    this.userFacade.selectCurrentUserRole$
      .pipe(
        filter((role: string) => Boolean(role)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((role: string) => {
        this.currentUserRole.set(role)
        this.#handleIfReviewer()
      })
  }

  #initForm(): void {
    this.layoutFormGroup = this.formBuilder.group<LayoutFormModel>({
      layoutName: this.formBuilder.control('', {
        validators: [Validators.required],
        asyncValidators: [
          uniqueLayoutNameValidator(
            this.layoutFacade,
            this.isEdit() ? this.selectedLayoutId() : -1
          ),
        ],
      }),
      sourceLayoutId: this.formBuilder.control(-1),
      isPrivate: this.formBuilder.control(false),
      client: this.formBuilder.control([], {
        validators: [Validators.required],
      }),
      case: this.formBuilder.control([], {
        validators: [Validators.required],
      }),
      userGroup: this.formBuilder.control([], {
        validators: [Validators.required],
      }),
      isPanelSelected: this.formBuilder.control(true, {
        validators: Validators.requiredTrue,
      }),
      isViewerSelected: this.formBuilder.control(true, {
        validators: Validators.requiredTrue,
      }),
    })
  }

  #init(): void {
    this.#fetchLayoutPanels()
    this.#fetchClients()
    this.#fetchCases()
    this.#fetchUserGroups()
    this.#handleClientChange()
    this.#handleCaseChange()
    this.#handleUserGroupChange()
  }

  #handleIfReviewer(): void {
    if (this.currentUserRole() === 'Reviewer') {
      this.formControls.isPrivate.setValue(true)
      this.formControls.isPrivate.disable()
    }
  }

  #fetchClients(): void {
    this.layoutFacade
      .fetchClients$()
      .pipe(
        switchMap((clientsResponse: ResponseModel) => {
          this.clients.set(
            clientsResponse?.data.map((client) => ({
              ...client,
              parentId: null,
            }))
          )
          if (this.selectedLayout())
            return this.layoutFacade.fetchLayoutClientProjectUsergroups(
              this.selectedLayoutId()
            )
          return of(undefined)
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel | undefined) => {
        if (response) {
          this.clientCaseGroupInfoOfLayout = response.data
          this.#selectDefaultClientsFromSelectedLayout()
          this.#selectDefaultValuesOfSelectedLayout()
        }
      })
  }

  #selectDefaultValuesOfSelectedLayout(): void {
    this.formControls.isPrivate.setValue(
      this.selectedLayout()?.isPrivate ?? false
    )
    if (this.action() === CommonActionTypes.CLONE)
      this.formControls.layoutName.setValue(
        `${this.selectedLayout()?.layoutName}_Clone`
      )
    else
      this.formControls.layoutName.setValue(
        this.selectedLayout()?.layoutName ?? ''
      )
  }

  #selectDefaultClientsFromSelectedLayout(): void {
    const selectedClients = this.clients().filter((client) =>
      this.clientCaseGroupInfoOfLayout?.clientIds?.some(
        (c) => client.clientId === c
      )
    )
    this.formControls.client.setValue(selectedClients)
  }

  #fetchCases(): void {
    this.fetchCaseAction
      .pipe(
        filter((clients) => clients?.length > 0),
        debounceTime(200),
        distinctUntilChanged(),
        switchMap((clientIds: number[]) =>
          this.layoutFacade.fetchCasesByClientIds({ clientIds })
        ),
        takeUntil(this.toDestroy$)
      )

      .subscribe((projects: ProjectInfo[]) => {
        this.cases.set(projects.map((p) => ({ ...p, parentId: null })))
        if (projects?.length > 0 && this.clientCaseGroupInfoOfLayout) {
          this.#selectDefaultCasesOfSelectedLayout()
        }
      })
  }

  #selectDefaultCasesOfSelectedLayout(): void {
    const selectedProjectsIds = this.clientCaseGroupInfoOfLayout.projects.map(
      (p) => p.projectId
    )
    const selectedProjects = this.cases().filter((project) =>
      selectedProjectsIds.some((p) => p === project.projectId)
    )
    if (selectedProjects?.length > 0) {
      this.formControls.case.setValue(selectedProjects, {
        emitEvent: false,
      })
      this.fetchUseGroupAction.next(selectedProjectsIds)
    }
  }

  #fetchUserGroups(): void {
    this.fetchUseGroupAction
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        switchMap((caseIds) => {
          return this.layoutFacade.fetchProjectUserGroups$(caseIds)
        }),

        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        const userGroups: GroupInfo[] = response.data
        this.userGroups.set(userGroups.map((ug) => ({ ...ug, parentId: null })))
        if (userGroups?.length > 0 && this.clientCaseGroupInfoOfLayout) {
          this.#selectDefaultUserGroupsOfSelectedLayout()
        }
      })
  }

  #selectDefaultUserGroupsOfSelectedLayout(): void {
    const selectedUserGroups = this.userGroups().filter((group) =>
      this.clientCaseGroupInfoOfLayout.projects.some(
        (project) =>
          project.projectId === group.projectId &&
          project.userGroupIds.includes(group.groupId)
      )
    )
    this.formControls.userGroup.setValue(selectedUserGroups, {
      emitEvent: false,
    })
  }

  #handleClientChange(): void {
    this.formControls.client.valueChanges
      .pipe(
        filter((clients) => Boolean(clients)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((clients: ClientModel[]) => {
        this.cases.set([])
        this.userGroups.set([])
        this.formControls.client.setValue(clients, { emitEvent: false })
        this.fetchCaseAction.next(clients.map((c) => c.clientId))
      })
  }

  #handleCaseChange(): void {
    this.formControls.case.valueChanges
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((projectInfos: ProjectInfo[]) => {
        this.userGroups.set([])
        this.formControls.case.setValue(projectInfos, {
          emitEvent: false,
        })
        this.fetchUseGroupAction.next(projectInfos.map((p) => p.projectId))
      })
  }

  #handleUserGroupChange(): void {
    this.formControls.userGroup.valueChanges
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((selectedProjectInfo: GroupInfo[]) => {
        this.formControls.userGroup.setValue(selectedProjectInfo, {
          emitEvent: false,
        })
      })
  }

  #fetchLayoutPanels(): void {
    this.layoutFacade
      .fetchLayoutPanels$(this.selectedLayoutId())
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((response: ResponseModel) => {
        const layoutPanels: Array<LayoutPanel> = response?.data?.map((p) => {
          if (p.panelName === 'NearNativeViewer')
            return { ...p, panelName: 'NativeViewer' }
          return p
        })
        this.viewablePanels.set(
          layoutPanels
            ?.filter((f) => !ExcludedPanelsFromRight.includes(f.panelName))
            .map((p) => ({ ...p, panelName: titleMapping[p.panelName] }))
        )
        this.viewerPanels.set(
          layoutPanels?.filter((f) => VIEWER_PANELS.includes(f.panelName))
        )
        //check grid checkboxes for selected panels
        this.selectedPanelIds = this.viewablePanels()
          .filter((p) => p.isSelected)
          .map((p) => p.panelId)
      })
  }

  public onSelectionChange(event: any): void {
    const selectedItems = event.selectedRows.map((row) => row.dataItem)
    const deselectedItems = event.deselectedRows.map((row) => row.dataItem)

    if (selectedItems.length > 0)
      this.viewablePanels.set(
        this.viewablePanels().map((f) => ({
          ...f,
          isSelected: selectedItems.some((s) => s.panelName === f.panelName)
            ? true
            : f.isSelected,
        }))
      )
    if (deselectedItems.length > 0)
      this.viewablePanels.set(
        this.viewablePanels().map((f) => ({
          ...f,
          isSelected: deselectedItems.some((s) => s.panelName === f.panelName)
            ? false
            : f.isSelected,
        }))
      )

    this.#updatePanelSelectedFormControlValue()
  }

  #updatePanelSelectedFormControlValue(): void {
    const selectedReviewPanels = [
      ...this.viewablePanels().filter((f) => f.isSelected),
    ]
    const selectedViewerPanels: LayoutPanel[] = this.viewerPanels().filter(
      (f) => f.isSelected
    )

    //const selectedPanels = [...selectedReviewPanels, ...selectedViewerPanels]
    this.formControls.isPanelSelected.setValue(selectedReviewPanels.length > 0)
    this.formControls.isPanelSelected.updateValueAndValidity()
    this.formControls.isViewerSelected.setValue(selectedViewerPanels.length > 0)
    this.formControls.isViewerSelected.updateValueAndValidity()
  }

  public closeWindow(): void {
    if (this.windowRef) {
      this.windowRef.close()
      this.layoutState.showLayoutListing.set(false)
    }
  }

  public openFieldSelectionDialog(data: LayoutPanel): void {
    import(
      '../layout-panel-field-selection/layout-panel-field-selection.component'
    ).then((td) => {
      const dialog: DialogRef = this.dialogService.open({
        content: td.LayoutPanelFieldSelectionComponent,
        maxWidth: '1100px',
        maxHeight: '710px',
        width: '80%',
        height: '90vh',
      })
      dialog.content.instance.selectedLayoutPanel = data

      dialog.result.subscribe((result) => {
        // if (result instanceof DialogCloseResult) {
        // }
      })
    })
  }

  #handlePrivateLayoutChange(): void {
    this.formControls.isPrivate.valueChanges
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((value) => {
        const formcontrols = [
          this.formControls.case,
          this.formControls.client,
          this.formControls.userGroup,
        ]
        formcontrols.forEach((ctrl) => {
          if (value) {
            ctrl.disable()
            ctrl.removeValidators(Validators.required)
          } else {
            ctrl.enable()
            ctrl.addValidators(Validators.required)
          }
          ctrl.updateValueAndValidity()
        })
      })
  }

  public oncheckChanged(data: LayoutPanel, event: any): void {
    const isChecked = (event.target as HTMLInputElement).checked
    const viewers = this.viewerPanels().map((p) => {
      if (p.panelId === data.panelId) return { ...p, isSelected: isChecked }
      return p
    })
    this.viewerPanels.set(viewers)
    this.#updatePanelSelectedFormControlValue()
  }

  public saveLayout(): void {
    Object.entries(this.formControls).forEach(([key, control]) => {
      control.markAsTouched()
      control.markAsDirty()
    })

    const selectedViewablePanels: LayoutPanel[] = this.viewablePanels().filter(
      (f) => f.isSelected
    )
    const selectedViewerPanels: LayoutPanel[] = this.viewerPanels().filter(
      (f) => f.isSelected
    )
    const selectedPanels = [...selectedViewablePanels, ...selectedViewerPanels]

    if (!this.layoutFormGroup.valid) return

    const allSelectedPanels = selectedPanels.map(
      (p) =>
        ({
          hasField: p.hasField,
          panelId: p.panelId,
          panelName: p.panelName,
          fields: p.hasField
            ? this.#getLayoutFields(
                this.layoutState.layoutPanelFieldMapping()[p.panelId]
              )
            : null,
        } as LayoutPanel)
    )

    const layoutFormValues = this.layoutFormGroup.getRawValue()
    const layout: Layout = {
      layoutName: layoutFormValues.layoutName,
      isPrivate: layoutFormValues.isPrivate,
      layoutPanels: allSelectedPanels,
    }
    const payload: LayoutCreateRequestModel = {
      layoutModel: layout,
      sourceLayoutId: this.selectedLayoutId(),
      projectUserGroups: this.#convertToLayoutProjectUserGroups(
        layoutFormValues.userGroup
      ),
    }

    let obs = this.layoutFacade.createLayout$(payload)
    if (this.isEdit())
      obs = this.layoutFacade.updateLayout$(this.selectedLayoutId(), payload)
    obs
      .pipe(
        catchError((error: unknown) => {
          const httpError = error as HttpErrorResponse
          this.notificationFacade.showError(httpError.error.message)
          return EMPTY
        }),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        this.notificationFacade.showSuccess(response.message)
        this.closeWindow()
      })
  }

  #getLayoutFields(fields: Field[] = []): LayoutField[] {
    return fields.map((f: Field) => ({
      isSelected: true,
      fieldId: f.venioFieldId,
      fieldName: f.displayFieldName,
      fieldOrder: f.displayOrder,
      isCustomField: f.isCustomField,
    }))
  }

  #convertToLayoutProjectUserGroups(
    groupInfoArray: GroupInfo[]
  ): LayoutProjectUserGroups[] {
    const grouped = groupInfoArray.reduce((acc, curr) => {
      // Check if the projectId already exists in the accumulator
      const existingGroup = acc.find(
        (item) => item.projectId === curr.projectId
      )
      if (existingGroup) {
        // If it exists, add the groupId to the userGroupIds array
        existingGroup.userGroupIds.push(curr.groupId)
      } else {
        // Otherwise, create a new entry for this projectId
        acc.push({
          projectId: curr.projectId,
          userGroupIds: [curr.groupId],
        })
      }
      return acc
    }, [] as LayoutProjectUserGroups[])

    return grouped
  }

  public goBack(): void {
    this.mainLayout.emit('true')
  }
}

export function uniqueLayoutNameValidator(
  layoutFacade: CompositeLayoutFacade,
  layoutId: number
): AsyncValidatorFn {
  return (control: AbstractControl) => {
    if (!control.value) {
      return of(null)
    }

    return of(control.value).pipe(
      debounceTime(300),
      switchMap((layoutName) =>
        layoutFacade.isLayoutNameTaken$(layoutId, layoutName).pipe(
          map((response: ResponseModel) =>
            response.data ? { layoutNameTaken: true } : null
          ),
          catchError(() => of(null))
        )
      )
    )
  }
}
