import {
  ChangeDetectionStrategy,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  WritableSignal,
  inject,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { LabelModule } from '@progress/kendo-angular-label'
import { chevronDownIcon } from '@progress/kendo-svg-icons'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms'

import { LoaderModule } from '@progress/kendo-angular-indicators'
import {
  CompositeFilterDescriptor,
  FilterDescriptor,
} from '@progress/kendo-data-query'
import {
  CompositeLayoutFacade,
  CompositeLayoutState,
  Field,
  FieldFacade,
  LayoutField,
  LayoutPanel,
  ProjectInfo,
} from '@venio/data-access/review'
import { filter, Subject, switchMap, takeUntil } from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { uniq, uniqBy } from 'lodash'
import { PascalToSpacePipe } from '@venio/util/utilities'

@Component({
  selector: 'venio-layout-panel-field-selection',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    DropDownsModule,
    GridModule,
    IconsModule,
    InputsModule,
    SvgLoaderDirective,
    DynamicHeightDirective,
    LabelModule,
    ButtonsModule,
    FormsModule,
    LoaderModule,
    ReactiveFormsModule,
    PascalToSpacePipe,
  ],
  templateUrl: './layout-panel-field-selection.component.html',
  styleUrl: './layout-panel-field-selection.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LayoutPanelFieldSelectionComponent implements OnInit, OnDestroy {
  public icons = {
    chevronDownIcon: chevronDownIcon,
  }

  public defaultCaseItem: ProjectInfo = {
    projectId: null,
    projectName: 'Select a case',
    allowTiff: null,
    enableNativeAutoPrefetch: null,
  }

  // List of filter operators for the dropdown
  public fieldGroups = [
    { text: 'Common', value: 1 },
    { text: 'Email', value: 2 },
    { text: 'Edoc', value: 3 },
    { text: 'Custom', value: 4 },
    { text: 'Export', value: 5 },
    { text: 'Image', value: 6 },
    { text: 'Work Product', value: 7 },
  ]

  public gridAvailableFieldFilter: CompositeFilterDescriptor | null = null

  public gridVisibleFieldFilter: CompositeFilterDescriptor | null = null

  private toDestroy$: Subject<void> = new Subject<void>()

  public selectedLayoutPanel: LayoutPanel

  private layoutFacade: CompositeLayoutFacade = inject(CompositeLayoutFacade)

  private fieldFacade: FieldFacade = inject(FieldFacade)

  private layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  public allVenioFields: WritableSignal<Array<Field>> = signal([])

  public availableFields: WritableSignal<Field[]> = signal([])

  public visibleFields: WritableSignal<Field[]> = signal([])

  public get panelSelectedField(): Array<Field> {
    return this.layoutState.layoutPanelFieldMapping()[
      this.selectedLayoutPanel.panelId
    ]
  }

  public cases: WritableSignal<Array<ProjectInfo>> = signal([])

  public caseCtrl: FormControl = new FormControl(null)

  public availableFieldGroup: FormControl = new FormControl()

  public availableFieldSelectedKeys: WritableSignal<string[]> = signal([])

  public visibleFieldSelectedKeys: string[]

  private selectedAvailableFieldGroupIds: WritableSignal<number[]> = signal([])

  private selectedVisibleFieldGroupIds: WritableSignal<number[]> = signal([])

  constructor(private dialogRef: DialogRef) {}

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngOnInit(): void {
    this.#fetchCases()
    this.#initializeVenioFields()
    this.#handleCaseChange()
    this.#handleCustomFieldFetch()
  }

  #initializeVenioFields(): void {
    this.fieldFacade.selectAllVenioFields$
      .pipe(
        switchMap((venioFields: Field[]) => {
          this.allVenioFields.set(venioFields)
          return this.layoutFacade.fetchLayoutPanelFields$(
            -1,
            this.selectedLayoutPanel.panelId
          )
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        const selectedFields: LayoutField[] = response.data
        const allVenioFields = this.allVenioFields()
        if (this.selectedLayoutPanel.panelName !== 'Coding')
          this.availableFields.set(allVenioFields)

        if (this.panelSelectedField?.length > 0) {
          this.visibleFields.set(this.panelSelectedField)
          this.availableFieldSelectedKeys.set(
            this.panelSelectedField.map((f) => f.displayFieldName)
          )
        } else {
          const mappedSelectedFields = allVenioFields.filter((f) =>
            selectedFields
              .map((sf) => sf.fieldName)
              .includes(f.displayFieldName)
          )
          this.visibleFields.set(mappedSelectedFields)
          this.availableFieldSelectedKeys.set(
            mappedSelectedFields.map((f) => f.displayFieldName)
          )
        }
      })
  }

  #fetchCases(): void {
    this.layoutFacade
      .fetchCases$()
      .pipe(
        filter((cases: ProjectInfo[]) => cases?.length > 0),
        takeUntil(this.toDestroy$)
      )
      .subscribe((projects: ProjectInfo[]) => {
        this.cases.set(projects)
        if (this.selectedLayoutPanel.panelName === 'Coding')
          this.caseCtrl.setValue(projects?.[0]?.projectId)
        else this.caseCtrl.setValue(null)
      })
  }

  #handleCaseChange(): void {
    this.caseCtrl.valueChanges
      .pipe(
        filter((project) => project),
        takeUntil(this.toDestroy$)
      )
      .subscribe((projectId) => {
        this.fieldFacade.setAllCustomFields([])
        this.fieldFacade.fetchAllCustomFields(projectId)
      })
  }

  #handleCustomFieldFetch(): void {
    this.fieldFacade.selectAllCustomFields$
      .pipe(
        filter((fields) => fields?.length > 0),
        takeUntil(this.toDestroy$)
      )
      .subscribe((allCustomFields: Field[]) => {
        let customFields = []
        if (this.selectedLayoutPanel.panelName === 'Coding')
          customFields = allCustomFields.filter((f) => f.allowCoding)
        else customFields = allCustomFields
        const availableFilterFieldsWithoutCustomFields =
          this.availableFields().filter((f) => !f.isCustomField)

        this.availableFields.set([
          ...availableFilterFieldsWithoutCustomFields,
          ...customFields,
        ])
        const alreadySelectedCustomFields = customFields
          .filter((f) =>
            this.visibleFields()
              .map((vf) => vf.displayFieldName)
              .includes(f.displayFieldName)
          )
          .map((f) => f.displayFieldName)
        this.availableFieldSelectedKeys.set([
          ...this.availableFieldSelectedKeys(),
          ...alreadySelectedCustomFields,
        ])
      })
  }

  public onSelectionChangeAvailableFields(event: any): void {
    const selectedItems: Array<Field> = event.selectedRows.map(
      (row) => row.dataItem
    )
    const deselectedItems: Array<Field> = event.deselectedRows.map(
      (row) => row.dataItem
    )
    this.addToVisibleFields(deselectedItems, selectedItems)
  }

  private addToVisibleFields(
    deselectedItems: Field[],
    selectedItems: Field[]
  ): void {
    this.visibleFields.set(
      uniqBy(
        [
          ...this.visibleFields().filter((f) => !deselectedItems.includes(f)),
          ...selectedItems,
        ],
        'displayFieldName'
      )
    )
  }

  public onSelectionChangeViewableFields(event: any): void {
    // const selectedItems: Array<Field> = event.selectedRows.map(
    //   (row) => row.dataItem
    // )
  }

  #upsertDictionary(key: number, value: Array<Field>): void {
    if (!key) return
    if (this.layoutState.layoutPanelFieldMapping()[key]) {
      // If the key exists, update the value
      this.layoutState.layoutPanelFieldMapping()[key] = value
    } else {
      // If the key does not exist, add the key-value pair
      this.layoutState.layoutPanelFieldMapping()[key] = value
    }
  }

  public save(): void {
    this.#upsertDictionary(
      this.selectedLayoutPanel.panelId,
      this.visibleFields().map((f, index) => ({
        ...f,
        displayOrder: index + 1,
      }))
    )
    this.dialogRef.close()
  }

  public close(): void {
    this.dialogRef.close()
  }

  public onFieldFilterChange(text: string, isAvailableField: boolean): void {
    //if (text) {
    const filter: FilterDescriptor = {
      field: 'displayFieldName',
      operator: 'contains',
      value: text,
    }

    if (isAvailableField)
      this.gridAvailableFieldFilter = {
        logic: 'and',
        filters: [filter],
      }
    else {
      this.gridVisibleFieldFilter = {
        logic: 'and',
        filters: [filter],
      }
    }
    //}
  }

  public onOperatorSelect(event: { text: string; value: number }): void {
    const selectedFields = this.availableFields().filter(
      (f) => f.fieldGroupId === event.value
    )
    const fieldnames = selectedFields.map((f) => f.displayFieldName)
    this.availableFieldSelectedKeys.set(
      uniq([...this.availableFieldSelectedKeys(), ...fieldnames])
    )
    this.addToVisibleFields([], selectedFields)
  }

  public onAvailableFieldGroupSelected(event, fieldGroupId: number): void {
    const isChecked = (event.target as HTMLInputElement).checked
    if (isChecked) {
      this.selectedAvailableFieldGroupIds.set([
        ...this.selectedAvailableFieldGroupIds(),
        fieldGroupId,
      ])
      const selectedFields = this.availableFields().filter(
        (f) => f.fieldGroupId === fieldGroupId
      )
      const addedFieldnames = selectedFields.map((f) => f.displayFieldName)
      this.availableFieldSelectedKeys.set(
        uniq([...this.availableFieldSelectedKeys(), ...addedFieldnames])
      )
      this.addToVisibleFields([], selectedFields)
    } else {
      this.selectedAvailableFieldGroupIds.set(
        this.selectedAvailableFieldGroupIds().filter((f) => f !== fieldGroupId)
      )
      const removedFields = this.availableFields().filter(
        (f) => f.fieldGroupId === fieldGroupId
      )
      const removedFieldNames = removedFields.map((f) => f.displayFieldName)
      this.availableFieldSelectedKeys.set(
        this.availableFieldSelectedKeys().filter(
          (f) => !removedFieldNames.includes(f)
        )
      )
      this.addToVisibleFields(removedFields, [])
    }
  }

  public isAvailableFieldGroupSelected(fieldGroupId: number): boolean {
    return this.selectedAvailableFieldGroupIds().some((g) => g === fieldGroupId)
  }

  public onVisibleFieldGroupSelected(event, fieldGroupId: number): void {
    const isChecked = (event.target as HTMLInputElement).checked
    if (isChecked)
      this.selectedVisibleFieldGroupIds.set([
        ...this.selectedVisibleFieldGroupIds(),
        fieldGroupId,
      ])
    else
      this.selectedVisibleFieldGroupIds.set(
        this.selectedVisibleFieldGroupIds().filter((f) => f !== fieldGroupId)
      )
  }

  public isVisibleFieldGroupSelected(fieldGroupId: number): boolean {
    return this.selectedVisibleFieldGroupIds().some((g) => g === fieldGroupId)
  }
}
