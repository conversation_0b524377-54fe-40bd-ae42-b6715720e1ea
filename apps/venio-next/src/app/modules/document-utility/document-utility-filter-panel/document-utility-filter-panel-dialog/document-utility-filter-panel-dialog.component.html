<kendo-dialog-titlebar (close)="close()">
  <div>
    {{ dialogTitle }}
  </div>
</kendo-dialog-titlebar>

<div class="t-flex t-flex-col t-gap-2 t-w-full">
  <kendo-textbox
    class="!t-w-[20rem] !t-mt-2"
    placeholder="Enter the field name"
    [clearButton]="true"
    (venioAfterValueChanged)="onFilter($event)">
    <ng-template kendoTextBoxSuffixTemplate>
      <button class="t-flex t-items-center">
        <kendo-svgicon
          [icon]="icons.search"
          themeColor="info"
          size="large"
          class="t-text-[#1EBADC] t-w-4 t-h-3.5 t-mx-4"></kendo-svgicon>
      </button>
    </ng-template>
  </kendo-textbox>

  <div
    class="t-flex t-flex-col t-w-full"
    venioDynamicHeight
    [isKendoDialog]="true">
    <kendo-grid
      [ngClass]="'v-custom-panel-filter t-h-full t-w-full !t-border-0 '"
      scrollable="virtual"
      [data]="gridData"
      [loading]="!isDataLoaded()"
      [selectable]="selectableSettings"
      kendoGridSelectBy="displayFieldName"
      [(selectedKeys)]="selectedKeys"
      [sortable]="true">
      <kendo-grid-checkbox-column
        class="!t-border-0"
        [width]="47"
        [minResizableWidth]="47"
        [showSelectAll]="true">
      </kendo-grid-checkbox-column>
      <kendo-grid-column
        field="displayFieldName"
        title="Select All"
        [sortable]="true"
        headerClass="!t-font-normal !t-tracking-normal !t-font-semibold"
        class="!t-border-0 t-text-sm t-font-medium">
      </kendo-grid-column>
    </kendo-grid>
  </div>
</div>

<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="save()"
      class="v-custom-secondary-button"
      fillMode="outline"
      themeColor="secondary">
      APPLY
    </button>
    <button kendoButton (click)="close()" themeColor="dark" fillMode="outline">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
