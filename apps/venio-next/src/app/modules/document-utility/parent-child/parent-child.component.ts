import {
  AfterViewChecked,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  Inject,
  <PERSON><PERSON><PERSON>,
  OnDestroy,
  OnInit,
  TrackByFunction,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ActivatedRoute } from '@angular/router'
import {
  DocumentMetadata,
  ParentChild,
  ReviewPanelFacade,
  ParentChildPayloadModel,
  ReviewPanelSelectedDocumentModel,
} from '@venio/data-access/document-utility'
import {
  CompositeLayoutState,
  DocumentsFacade,
  FieldFacade,
  ReviewPanelType,
  SearchFacade,
  SearchResultFacade,
  TempTableResponseModel,
} from '@venio/data-access/review'
import {
  Observable,
  Subject,
  combineLatest,
  filter,
  of,
  take,
  takeUntil,
} from 'rxjs'
import { cloneDeep, isEqual, union } from 'lodash'
import {
  TreeListComponent,
  TreeListItem,
  TreeListModule,
} from '@progress/kendo-angular-treelist'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import {
  AppIdentitiesTypes,
  MessageType,
  WINDOW,
} from '@venio/data-access/iframe-messenger'
import { LocalStorage } from '@venio/shared/storage'
import { environment } from '@venio/shared/environments'
import { PageControlActionType } from '@venio/shared/models/constants'
import { eyeIcon } from '@progress/kendo-svg-icons'
import { IconsModule } from '@progress/kendo-angular-icons'

@Component({
  selector: 'venio-parent-child',
  standalone: true,
  imports: [
    CommonModule,
    TreeListModule,
    IndicatorsModule,
    TooltipModule,
    IconsModule,
  ],
  templateUrl: './parent-child.component.html',
  styleUrl: './parent-child.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ParentChildComponent
  implements OnInit, OnDestroy, AfterViewChecked
{
  public parentChild: ParentChild[]

  public tempTables: TempTableResponseModel

  public isFieldsExists = true

  public selectedDocuments: number[]

  public currentDocument: number

  public allFileIds: number[]

  public expandedKeys: number[] = []

  /**
   * List of metadata to show in the treelist.
   */
  public headers: string[]

  public selectedFields: string[]

  private sortedMetadataFields: string[]

  private defaultFieldIds: number[]

  private venioFieldIds: number[]

  private customFieldIds: number[]

  public isParentChildLoading$ =
    this.reviewPanelFacade.selectIsParentChildLoading$

  public unsubscribed$: Subject<void> = new Subject<void>()

  public get isTagPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isTagPanelPopout')
  }

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public childrenNodes = (dataitem: any): Observable<any[]> =>
    of(dataitem.children)

  public hasChildrenNodes = (dataitem: any): boolean =>
    !!dataitem.children && dataitem.children.length > 0

  public rowClass = (args): { [className: string]: boolean } => ({
    'k-selected': args.dataItem.fileId === this.currentDocument,
  })

  @ViewChild(TreeListComponent)
  public treelist: TreeListComponent

  // store the last known width of the treelist
  public lastKnownWidth = 0

  public icons = {
    eyeIcon: eyeIcon,
  }

  private layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  constructor(
    private reviewPanelFacade: ReviewPanelFacade,
    private searchFacade: SearchFacade,
    private searchResultFacade: SearchResultFacade,
    private documentsFacade: DocumentsFacade,
    private fieldFacade: FieldFacade,
    private activatedRoute: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
    @Inject(WINDOW) private windowRef: Window,
    private ngZone: NgZone
  ) {}

  public ngOnInit(): void {
    this.#selectAllFileIdsFromCurrentPage()
    this.#getParentChildData()
    this.#selectParentChild()
    this.fieldFacade.notifyFieldChanges.next()
  }

  #selectAllFileIdsFromCurrentPage(): void {
    this.searchResultFacade.getSearchResultFileIds
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((fileIds: number[]) => {
        this.allFileIds = fileIds
      })
  }

  #getParentChildData(): void {
    combineLatest([
      this.documentsFacade.getSelectedDocuments$,
      this.searchFacade.getSearchTempTables$,
      this.fieldFacade.notifyFieldChanges,
    ])
      .pipe(
        filter(([selectedDocuments, fieldSelection]) =>
          Boolean(selectedDocuments?.length > 0 && fieldSelection)
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(([selectedDocuments, tempTables]) => {
        const hasFieldsChanged = this.#updateFields()
        const hasFileIdChanged = !isEqual(
          selectedDocuments,
          this.selectedDocuments
        )
        if (!hasFieldsChanged && !hasFileIdChanged) return
        this.tempTables = tempTables
        this.selectedDocuments = selectedDocuments
        this.currentDocument = selectedDocuments[0]
        this.#fetchParentChild()
      })
  }

  #fetchParentChild(): void {
    if (this.currentDocument < 0) {
      this.#resetPanelData()
      return
    }
    const parentChildPayload: ParentChildPayloadModel = {
      fileId: this.currentDocument,
      projectId: this.projectId,
      tempTable: this.tempTables,
      venioFieldIds: this.venioFieldIds,
      customFieldIds: this.customFieldIds,
    }
    this.reviewPanelFacade.getParentChild(parentChildPayload)
  }

  #selectParentChild(): void {
    this.reviewPanelFacade.selectParentChild$
      .pipe(
        filter((parentChild) => parentChild?.length > 0),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((parentChild) => {
        this.#showHideFields(this.selectedFields)
        if (!this.isFieldsExists) return
        this.parentChild = cloneDeep(parentChild)
        const metadata: DocumentMetadata[] =
          this.#getParentChildWithFields(parentChild)?.metadata?.slice()
        this.headers = this.layoutState
          .getFieldsSortedByLayoutOrder(
            metadata,
            'key',
            ReviewPanelType.ParentChild
          )
          .map((field) => field.key)
          .filter((f) => this.selectedFields.includes(f))

        this.parentChild?.forEach((thread) => {
          this.#parse(thread)
        })
        this.fitColumns()
        this.changeDetectorRef.detectChanges()
      })
  }

  #getParentChildWithFields(parentChild: ParentChild[]): ParentChild | null {
    for (const node of parentChild) {
      if (node?.metadata?.length > 0) {
        return node // Found the ParentChild with metadata!
      } else if (node?.children?.length > 0) {
        // Recursively search children
        const result = this.#getParentChildWithFields(node.children)
        if (result) {
          return result // Propagate the result up
        }
      }
    }
    return null // ParentChild with metadata not found
  }

  /**
   * Parse the parent child data in the metadata field.
   * Adds the property in parentChild object from the metadata object which is key and value property.
   * Eg:{key:'internal_file_id, value:1} gets transform to {internal_file_id:1}.
   * This is required to show data in treelist.
   */
  #parse(parentChild: ParentChild): void {
    this.expandedKeys = union(this.expandedKeys, [parentChild.fileId])
    for (const field of this.headers) {
      parentChild[field] = parentChild?.metadata?.find(
        (data) => data.key === field
      )?.value
    }

    if (parentChild.children?.length > 0) {
      parentChild.children.forEach((pc) => {
        this.#parse(pc)
      })
    }
  }

  public parentChildTreeTrackByFn = (
    _: number,
    item: TreeListItem
  ): TrackByFunction<TreeListItem> =>
    item.data['fileId'] as TrackByFunction<TreeListItem>

  public onDetailsClicked(dataItem): void {
    const fileId = dataItem?.fileId
    const selectReviewPanelDocument: ReviewPanelSelectedDocumentModel = {
      currentfileId: this.currentDocument,
      documentNo: fileId,
      isDocumentExistsInSearchScope: dataItem.isFileExistsSearchScope,
    }

    this.reviewPanelFacade.setSelectedReviewPanelDocument(
      selectReviewPanelDocument
    )
    // Send an action event from the popout window to update data in the parent window.
    if (this.isTagPanelPopout) {
      this.#sendParentChildActionEvent(selectReviewPanelDocument)
      return
    }
  }

  /**
   * Sends a parent-child action event to the parent window.
   * @param {ReviewPanelSelectedDocumentModel} selectReviewPanelDocument - The selected document to include in the event payload.
   * @returns {void} This method does not return anything.
   */
  #sendParentChildActionEvent(
    selectReviewPanelDocument: ReviewPanelSelectedDocumentModel
  ): void {
    // Popout window is not open then return
    if (!this.isTagPanelPopout) return

    this.windowRef.opener.postMessage(
      {
        type: 'MICRO_APP_DATA_CHANGE',
        payload: {
          type: MessageType.WINDOW_CHANGE,
          content: {
            selectReviewPanelDocument: selectReviewPanelDocument,
            pageControlActionType: PageControlActionType.PARENT_CHILD_DOCUMENT,
          },
        },
        eventTriggeredBy: AppIdentitiesTypes.UTILITY_PANEL_ACTION,
        iframeIdentity: AppIdentitiesTypes.UTILITY_PANEL,
        eventTriggeredFor: 'ALL_WINDOW',
      },
      environment.allowedOrigin
    )
  }

  #updateFields(): boolean {
    const selectedFields = this.layoutState
      .userSelectedLayout()
      .layoutPanels.find((p) => p.panelName === ReviewPanelType.ParentChild)
      ?.fields.filter((f) => f.isSelected)
    const hasFieldsChanged = !isEqual(
      this.selectedFields,
      selectedFields.map((f) => f.fieldName)
    )
    this.selectedFields = selectedFields.map((f) => f.fieldName)

    this.venioFieldIds = selectedFields
      .filter((f) => !f.isCustomField)
      .map((f) => f.fieldId)
    this.customFieldIds = selectedFields
      .filter((f) => f.isCustomField)
      .map((f) => f.fieldId)
    if (this.customFieldIds?.length === 0) this.customFieldIds = [-1]
    return hasFieldsChanged
  }

  #showHideFields(visibleMetaDataFields: string[]): void {
    this.isFieldsExists = Boolean(visibleMetaDataFields?.[0])
    // clear the headers and parentChild if no fields are visible
    if (!this.isFieldsExists) {
      this.#resetPanelData()
    }
  }

  #resetPanelData(): void {
    this.headers = []
    this.parentChild = []
    this.changeDetectorRef.detectChanges()
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }

  // Workaround solution for the issue where the treelist does not fit/resize columns and scrollbar glitch even after with ngAfterViewInit.
  public ngAfterViewChecked(): void {
    if (this.treelist && this.lastKnownWidth === 0) {
      const currentWidth = this.treelist.wrapper.nativeElement.offsetWidth
      // Call fitColumns when the last known width is less than 10.
      if (this.lastKnownWidth < 10) {
        this.fitColumns()
        this.lastKnownWidth = currentWidth
      }
    }
  }

  public fitColumns(): void {
    this.ngZone.onStable
      .asObservable()
      .pipe(take(1))
      .subscribe(() => {
        this.treelist.autoFitColumns()
      })
  }
}
