import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentUtilityActionBarComponent } from './document-utility-action-bar.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('DocumentUtilityActionBarComponent', () => {
  let component: DocumentUtilityActionBarComponent
  let fixture: ComponentFixture<DocumentUtilityActionBarComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentUtilityActionBarComponent],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentUtilityActionBarComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
