import { ComponentFixture, TestBed } from '@angular/core/testing'
import { MoveToParentContainerComponent } from './move-to-parent-container.component'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import {
  DialogContainerService,
  DialogService,
} from '@progress/kendo-angular-dialog'
import { VenioNotificationService } from '@venio/feature/notification'
import { provideMockStore } from '@ngrx/store/testing'
import { NotificationService } from '@progress/kendo-angular-notification'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('MoveToParentContainerComponent', () => {
  let component: MoveToParentContainerComponent
  let fixture: ComponentFixture<MoveToParentContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MoveToParentContainerComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentsFacade,
        DialogContainerService,
        NotificationService,
        SearchFacade,
        FieldFacade,
        DialogService,
        VenioNotificationService,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(MoveToParentContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
