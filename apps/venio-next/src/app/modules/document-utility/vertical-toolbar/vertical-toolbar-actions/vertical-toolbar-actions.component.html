<div
  class="t-flex t-flex-col t-border t-border-l-1 t-border-r-0 t-border-b-0 t-border-t-0 t-border-[#dbdbdb]"
  kendoTooltip>
  <button
    kendoButton
    #parentEl
    *ngFor="let icon of svgIconForTagsToolbar"
    class="!t-p-[0.85rem] !t-border !t-border-t-1 !t-border-l-0 !t-border-r-0 !t-border-b-1 !t-border-[#dbdbdb]"
    (click)="browseActionClicked(icon.actionType)"
    fillMode="clear"
    [title]="icon.actionType"
    size="none">
    <span
      [parentElement]="parentEl.element"
      venioSvgLoader
      hoverColor="#FFBB12"
      [svgUrl]="icon.iconPath"
      height="1.3rem"
      width="1.3rem">
      <kendo-loader size="small"></kendo-loader>
    </span>
  </button>
</div>
