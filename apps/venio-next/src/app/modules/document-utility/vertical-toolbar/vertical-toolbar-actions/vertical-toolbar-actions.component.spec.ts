import { ComponentFixture, TestBed } from '@angular/core/testing'
import { VerticalToolbarActionsComponent } from './vertical-toolbar-actions.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('VerticalToolbarActionsComponent', () => {
  let component: VerticalToolbarActionsComponent
  let fixture: ComponentFixture<VerticalToolbarActionsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [VerticalToolbarActionsComponent],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(VerticalToolbarActionsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
