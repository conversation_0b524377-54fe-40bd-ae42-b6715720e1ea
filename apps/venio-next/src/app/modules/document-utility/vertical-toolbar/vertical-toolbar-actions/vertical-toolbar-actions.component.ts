import { CommonModule } from '@angular/common'
import { ChangeDetectionStrategy, Component } from '@angular/core'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { DocuemntDetailToolBarActionType } from '@venio/shared/models/constants'
import { LoaderModule } from '@progress/kendo-angular-indicators'

@Component({
  selector: 'venio-vertical-toolbar-actions',
  templateUrl: './vertical-toolbar-actions.component.html',
  styleUrls: ['./vertical-toolbar-actions.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    SvgLoaderDirective,
    ButtonsModule,
    InputsModule,
    TooltipsModule,
    LoaderModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VerticalToolbarActionsComponent {
  public svgIconForTagsToolbar = [
    {
      actionType: DocuemntDetailToolBarActionType.REVIEW_TAG,
      iconPath: 'assets/svg/icon-review-tag.svg',
    },
    {
      actionType: DocuemntDetailToolBarActionType.NOTES,
      iconPath: 'assets/svg/icon-review-paper.svg',
    },
    {
      actionType: DocuemntDetailToolBarActionType.PRINT,
      iconPath: 'assets/svg/icon-review-printer.svg',
    },
    {
      actionType: DocuemntDetailToolBarActionType.SHARE,
      iconPath: 'assets/svg/icon-review-share.svg',
    },
  ]

  public browseActionClicked(
    actionType: DocuemntDetailToolBarActionType
  ): void {
    switch (actionType) {
      case 'REVIEW_TAG':
        // Invoke methods
        break
      case 'NOTES':
        // Invoke methods
        break
      case 'PRINT':
        // Invoke methods
        break
      case 'SHARE':
        // Invoke methods
        break
    }
  }
}
