import {
  ChangeDetectionStrategy,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DocumentsFacade,
  ReviewFacade,
  ReviewViewType,
  SearchFacade,
} from '@venio/data-access/review'
import {
  DialogCloseResult,
  DialogRef,
  DialogService,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import { Subject, filter, take, takeUntil, withLatestFrom } from 'rxjs'
import { DocumentMenuType } from '@venio/shared/models/constants'
import {
  ConfirmationDialogComponent,
  NotificationDialogComponent,
} from '@venio/feature/notification'

@Component({
  selector: 'venio-export-to-file-container',
  standalone: true,
  imports: [CommonModule, DialogsModule],
  templateUrl: './export-to-file-container.component.html',
  styleUrls: ['./export-to-file-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ExportToFileContainerComponent implements OnInit, OnD<PERSON>roy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly confirmDialogTitle = 'Export to file'

  private readonly message =
    `Exporting large file will take time to prepare the csv file.` +
    `\nDo you want to continue with current selection?`

  constructor(
    private documentsFacade: DocumentsFacade,
    private searchFacade: SearchFacade,
    private dialogService: DialogService,
    private reviewFacade: ReviewFacade
  ) {}

  public ngOnInit(): void {
    this.#selectedDocumentEvent()
  }

  /**
   * When the dialog is closed, it is no point to keep the menu event state
   * @returns {void}
   */
  #resetMenuEventState(): void {
    this.documentsFacade.resetDocumentState('menuEventPayload')
  }

  /**
   * When a menu link is clicked, the loading indicator is displayed to
   * indicate the user that there is something happening in the background
   * such as loading the dialog component lazily and then once loaded, turn off the indicator
   * @returns {void}
   */
  #resetMenuLoadingState(): void {
    this.documentsFacade.resetDocumentState('isDocumentMenuLoading')
  }

  /**
   * Select the document menu event state when it is triggered
   * @returns {void}
   */
  #selectedDocumentEvent(): void {
    this.documentsFacade.selectDocumentMenuEvent$
      .pipe(
        filter((event) => event === DocumentMenuType.EXPORT_TO_FILE),
        withLatestFrom(
          this.reviewFacade.getReviewViewType$,
          this.documentsFacade.getIsBatchSelected$,
          this.documentsFacade.getSelectedDocuments$,
          this.documentsFacade.getUnselectedDocuments$,
          this.searchFacade.getTotalHitCount$
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe(
        ([
          _,
          reviewViewType,
          isBatchSelected,
          selectedDocuments,
          unselectedDocuments,
          totalSearchCount,
        ]) => {
          let docCount = totalSearchCount

          // for now, the normal view will export only selected documents whereas thread view will export all documents
          if (reviewViewType === ReviewViewType.Search) {
            // Calculate selected document count based on batch selection status
            if (isBatchSelected) {
              // If batch is selected, calculate selected documents as total count minus unselected documents
              docCount = totalSearchCount - unselectedDocuments.length
            } else {
              // If batch is not selected, count selected documents
              docCount = selectedDocuments.length
            }
          }

          if (docCount > 1000) {
            this.ShowConfirmDialogToDownload()
          } else if (docCount === 0) {
            this.#showNotificationMessage(
              'Please select at least one document to export.'
            )
          } else {
            this.documentsFacade.exportToFile()
            this.resetEvents()
          }
        }
      )
  }

  private ShowConfirmDialogToDownload(): void {
    const dialogRef = this.openConfirmDialog()
    dialogRef.result
      .pipe(
        filter((result) => !!result),
        take(1)
      )
      .subscribe((result) => {
        if (result instanceof DialogCloseResult) {
          /* empty */
        } else {
          if (result) {
            this.documentsFacade.exportToFile()
          }
        }
        this.resetEvents()
      })
  }

  private openConfirmDialog(): DialogRef {
    const dialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      title: this.confirmDialogTitle,
      cssClass: 'v-confirmation-dialog v-dialog-save',
    })
    dialogRef.content.instance.message = this.message

    return dialogRef
  }

  #showNotificationMessage(message: string): void {
    const notificationDialogRef = this.dialogService.open({
      content: NotificationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
      width: '35rem',
    })
    notificationDialogRef.content.instance.message = message
    notificationDialogRef.content.instance.title = 'Export to file'

    notificationDialogRef.result
      .pipe(
        filter((result) => typeof result === 'boolean' && result === true),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.resetEvents()
      })
  }

  public resetEvents(): void {
    this.#resetMenuLoadingState()
    this.#resetMenuEventState()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetMenuEventState()
  }
}
