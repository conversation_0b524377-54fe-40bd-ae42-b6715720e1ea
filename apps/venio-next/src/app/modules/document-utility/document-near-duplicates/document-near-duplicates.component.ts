import {
  AfterViewChecked,
  ChangeDetectorRef,
  Component,
  Inject,
  Ng<PERSON><PERSON>,
  OnDestroy,
  OnInit,
  TrackByFunction,
  ViewChild,
  ViewContainerRef,
  signal,
  inject,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ActivatedRoute } from '@angular/router'
import {
  GridItem,
  GridComponent,
  GridModule,
} from '@progress/kendo-angular-grid'
import {
  NearDuplicateDocument,
  NearDuplicateModel,
  NearDuplicatePayloadModel,
  ReviewPanelFacade,
  ReviewPanelSelectedDocumentModel,
} from '@venio/data-access/document-utility'
import {
  AppIdentitiesTypes,
  MessageType,
  WINDOW,
} from '@venio/data-access/iframe-messenger'
import {
  TempTableResponseModel,
  SearchFacade,
  FieldFacade,
  DocumentsFacade,
  ReviewPanelType,
  CompositeLayoutState,
} from '@venio/data-access/review'
import { LocalStorage } from '@venio/shared/storage'
import { Subject, combineLatest, filter, take, takeUntil } from 'rxjs'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import { cloneDeep, isEqual } from 'lodash'
import { eyeIcon } from '@progress/kendo-svg-icons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { environment } from '@venio/shared/environments'
import { PageControlActionType } from '@venio/shared/models/constants'
import { Type as NotificationType } from '@progress/kendo-angular-notification'

@Component({
  selector: 'venio-document-near-duplicates',
  standalone: true,
  imports: [
    CommonModule,
    TreeListModule,
    GridModule,
    IndicatorsModule,
    TooltipModule,
    IconsModule,
  ],
  templateUrl: './document-near-duplicates.component.html',
  styleUrl: './document-near-duplicates.component.scss',
})
export class DocumentNearDuplicatesComponent
  implements OnInit, OnDestroy, AfterViewChecked
{
  public documentNearDuplicates: NearDuplicateDocument[]

  public tempTables: TempTableResponseModel

  public selectedDocuments: number[]

  public currentDocument: number

  public allFileIds: number[]

  public venioFieldIds: number[]

  public expandedKeys: number[] = []

  public icons = { eyeIcon: eyeIcon }

  public isNDDFormed = signal<boolean>(true)

  public totalCount = signal<number>(0)

  private customFieldIds: number[]

  private sortedMetadataFields: string[]

  public isFieldsExists = true

  public headers: string[] = []

  public selectedFields: string[] = []

  public isNearDuplicateLoading$ =
    this.reviewPanelFacade.selectIsNearDuplicateLoading$

  public unsubscribed$: Subject<void> = new Subject<void>()

  public get isTagPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isTagPanelPopout')
  }

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public nearDuplicateTreeTrackByFn = (
    _: number,
    item: GridItem
  ): TrackByFunction<GridItem> =>
    item.data['fileId'] as TrackByFunction<GridItem>

  public rowClass = (args): { [className: string]: boolean } => ({
    'k-selected': args.dataItem.fileId === this.currentDocument,
  })

  @ViewChild(GridComponent)
  public treelist: GridComponent

  // store the last known width of the treelist
  public lastKnownWidth = 0

  // Notification Config
  public content =
    'NDD is not performed, please perform NDD to view the information.'

  public type: NotificationType = { style: 'error', icon: true }

  public notificationPanelWidth = 400

  @ViewChild('appendNotification', { read: ViewContainerRef, static: false })
  public appendTo: ViewContainerRef

  private layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  constructor(
    private reviewPanelFacade: ReviewPanelFacade,
    private searchFacade: SearchFacade,
    private fieldFacade: FieldFacade,
    private documentsFacade: DocumentsFacade,
    private activatedRoute: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
    @Inject(WINDOW) private windowRef: Window,
    private ngZone: NgZone
  ) {}

  public ngOnInit(): void {
    this.#getNearDuplicateData()
    this.#selectNearDuplicateData()
    this.fieldFacade.notifyFieldChanges.next()

    this.changeDetectorRef.markForCheck()
  }

  // Workaround solution for the issue where the treelist does not fit/resize columns and scrollbar glitch even after with ngAfterViewInit.
  public ngAfterViewChecked(): void {
    if (this.treelist && this.lastKnownWidth === 0) {
      const currentWidth = this.treelist.wrapper.nativeElement.offsetWidth
      // Call fitColumns when the last known width is less than 10.
      if (this.lastKnownWidth < 10) {
        this.fitColumns()
        this.lastKnownWidth = currentWidth
      }
    }
  }

  #getNearDuplicateData(): void {
    combineLatest([
      this.documentsFacade.getSelectedDocuments$,
      this.searchFacade.getSearchTempTables$,
      this.fieldFacade.notifyFieldChanges,
    ])
      .pipe(
        filter(([selectedDocuments]) => Boolean(selectedDocuments?.length > 0)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(([selectedDocuments, tempTables]) => {
        const hasFieldsChanged = this.#updateFields()
        const hasFileIdChanged = !isEqual(
          selectedDocuments,
          this.selectedDocuments
        )
        if (!hasFieldsChanged && !hasFileIdChanged) return

        this.tempTables = tempTables
        this.selectedDocuments = selectedDocuments
        this.currentDocument = selectedDocuments[0]
        this.#fetchNearDuplicateData()
      })
  }

  #fetchNearDuplicateData(): void {
    const nearDuplicatePayload: NearDuplicatePayloadModel = {
      fileId: this.currentDocument,
      projectId: this.projectId,
      tempTable: this.tempTables,
      venioFieldIds: this.venioFieldIds,
    }
    this.reviewPanelFacade.getNearDuplicateDocumentdata(nearDuplicatePayload)
  }

  #selectNearDuplicateData(): void {
    this.reviewPanelFacade.selectNearDuplicateDocument$
      .pipe(
        filter((nearDuplicateDocument) => Boolean(nearDuplicateDocument)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((nearDuplicateDocument: NearDuplicateModel) => {
        this.documentNearDuplicates = cloneDeep(
          nearDuplicateDocument?.nearDuplicates
        )
        this.isNDDFormed.set(nearDuplicateDocument.isNDDFormed)
        this.totalCount.set(this.documentNearDuplicates?.length)
        if (!this.isNDDFormed() || this.totalCount() === 0) return
        this.headers = this.#updateSelectedFields(this.selectedFields)
        this.documentNearDuplicates?.forEach((doc) => {
          for (const field of this.headers) {
            const value = doc?.metadata?.find(
              (data) => data.key === field
            )?.value
            doc[field] = this.#isCentroidColumn(field)
              ? value
                ? 'Yes'
                : 'No'
              : value
          }
        })
        this.fitColumns()
        this.changeDetectorRef.markForCheck()
      })
  }

  //To show as Internal File id | Original File name | Centroid | Similarity
  #updateSelectedFields(fields: string[]): string[] {
    let predefinedOrder = ['Internal File Id', 'Original File Name']
    predefinedOrder = [
      ...predefinedOrder.filter((f) => fields.includes(f)),
      'Centroid',
      'Similarity',
    ]
    const datasource = fields.filter((f) => !predefinedOrder.includes(f))
    const filteredFields = this.layoutState.getStringFieldsSortedByLayoutOrder(
      datasource,
      '',
      ReviewPanelType.NearDuplicate
    )
    return [...predefinedOrder, ...filteredFields]
  }

  #clearNearDuplicateData(): void {
    this.totalCount.set(0)
    this.documentNearDuplicates = []
    this.lastKnownWidth = 0
    this.changeDetectorRef.markForCheck()
    this.reviewPanelFacade.resetReviewPanelState(['nearDuplicateDocument'])
  }

  public onDataStateChange(): void {
    this.fitColumns()
  }

  public fitColumns(): void {
    this.ngZone.onStable
      .asObservable()
      .pipe(take(1))
      .subscribe(() => {
        this.treelist.autoFitColumns()
      })
  }

  public onDetailsClicked(dataItem): void {
    const fileId = dataItem?.fileId
    const selectReviewPanelDocument: ReviewPanelSelectedDocumentModel = {
      currentfileId: this.currentDocument,
      documentNo: fileId,
      isDocumentExistsInSearchScope: dataItem.isFileExistsSearchScope,
    }

    this.reviewPanelFacade.setSelectedReviewPanelDocument(
      selectReviewPanelDocument
    )
    // Send an action event from the popout window to update data in the parent window.
    if (this.isTagPanelPopout) {
      this.#sendDocumentDuplicatedActionEvent(selectReviewPanelDocument)
      return
    }
  }

  /**
   * Sends a near duplicate action event to the parent window.
   * @param {ReviewPanelSelectedDocumentModel} selectReviewPanelDocument - The selected document to include in the event payload.
   * @returns {void} This method does not return anything.
   */
  #sendDocumentDuplicatedActionEvent(
    selectReviewPanelDocument: ReviewPanelSelectedDocumentModel
  ): void {
    // Popout window is not open then return
    if (!this.isTagPanelPopout) return

    this.windowRef.opener.postMessage(
      {
        type: 'MICRO_APP_DATA_CHANGE',
        payload: {
          type: MessageType.WINDOW_CHANGE,
          content: {
            selectReviewPanelDocument: selectReviewPanelDocument,
            pageControlActionType: PageControlActionType.NEAR_DUPLICATE,
          },
        },
        eventTriggeredBy: AppIdentitiesTypes.UTILITY_PANEL_ACTION,
        iframeIdentity: AppIdentitiesTypes.UTILITY_PANEL,
        eventTriggeredFor: 'ALL_WINDOW',
      },
      environment.allowedOrigin
    )
  }

  public getCellClass(
    field: string,
    dataItem: any
  ): { 't-text-error': boolean; 't-text-secondary': boolean } {
    return {
      't-text-error': this.#isCentroidColumn(field) && dataItem[field] === 'No',
      't-text-secondary':
        this.#isCentroidColumn(field) && dataItem[field] === 'Yes',
    }
  }

  #isCentroidColumn(field: string): boolean {
    return field.toLowerCase() === 'centroid'
  }

  #updateFields(): boolean {
    const selectedFields = this.layoutState
      .userSelectedLayout()
      .layoutPanels.find((p) => p.panelName === ReviewPanelType.Metadata)
      ?.fields.filter((f) => f.isSelected)
    const hasFieldsChanged = !isEqual(
      this.selectedFields,
      selectedFields.map((f) => f.fieldName)
    )
    this.selectedFields = selectedFields.map((f) => f.fieldName)

    this.venioFieldIds = selectedFields
      .filter((f) => !f.isCustomField)
      .map((f) => f.fieldId)
    this.customFieldIds = selectedFields
      .filter((f) => f.isCustomField)
      .map((f) => f.fieldId)
    if (this.customFieldIds?.length === 0) this.customFieldIds = [-1]
    return hasFieldsChanged
  }

  public ngOnDestroy(): void {
    this.#clearNearDuplicateData()
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }
}
