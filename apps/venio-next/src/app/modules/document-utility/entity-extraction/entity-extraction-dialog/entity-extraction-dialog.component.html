<kendo-dialog
  *ngIf="isEntitySelectionDialogVisible"
  [minWidth]="300"
  [width]="'40%'">
  <kendo-dialog-titlebar (close)="close()">
    <div class="t-flex t-items-center t-gap-2">
      <!-- Icon -->
      <button
        class="t-inline-block t-w-[35px] t-p-0 t-bg-[#F3F3F3] t-rounded-full t-mr-2 t-h-[35px] t-flex t-items-center t-cursor-default"
        fillMode="clear"
        kendoButton
        #actionGrid4>
        <span
          [parentElement]="actionGrid4.element"
          venioSvgLoader
          svgUrl="assets/svg/icon-entity-extraction.svg"
          height="1rem"
          width="1rem">
        </span>
      </button>
      <!-- Title -->
      <div class="t-block">{{ dialogTitle }}</div>
    </div>
  </kendo-dialog-titlebar>
  <div class="t-mt-3 t-mb-3 t-block t-font-bold t-text-lg">
    Select Entity Name to be Extracted
  </div>
  <kendo-grid
    [kendoGridBinding]="entityList"
    [kendoGridSelectBy]="'entityId'"
    [(selectedKeys)]="selectedEntities"
    class="t-flex t-flex-col-reverse t-overflow-y-auto"
    [selectable]="true"
    (selectionChange)="onEntitySelectionChange()">
    <!-- Row Number Column -->
    <kendo-grid-column title="#" [width]="40">
      <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
        {{ rowIndex + 1 }}
      </ng-template>
    </kendo-grid-column>

    <!-- Checkbox Selection Column -->
    <kendo-grid-checkbox-column [showSelectAll]="true" [width]="40">
    </kendo-grid-checkbox-column>

    <!-- Entity Name Column -->
    <kendo-grid-column
      field="entityDisplayName"
      title="Custom Entity Name"
      headerClass="t-text-primary">
    </kendo-grid-column>

    <!-- No Records Template -->
    <ng-template kendoGridNoRecordsTemplate>
      No entities to display.
    </ng-template>
  </kendo-grid>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <!-- Extract Button -->
      <button
        kendoButton
        [disabled]="!isEntitySelected"
        (click)="extractEntities()"
        class="v-custom-secondary-button"
        themeColor="secondary"
        fillMode="outline"
        data-qa="extract-button">
        EXTRACT
      </button>

      <!-- Cancel Button -->
      <button
        kendoButton
        (click)="close()"
        themeColor="dark"
        fillMode="outline"
        data-qa="cancel-button">
        CANCEL
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>
