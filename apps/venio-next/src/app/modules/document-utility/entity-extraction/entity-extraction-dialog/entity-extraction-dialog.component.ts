import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnD<PERSON>roy,
  OnInit,
  Optional,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { GridModule } from '@progress/kendo-angular-grid'
import { DialogModule, DialogRef } from '@progress/kendo-angular-dialog'
import {
  DocSelectionType,
  DocumentsFacade,
  EntityExtractionRequest,
  EntityExtractionService,
  SearchFacade,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { ConfirmationDialogService } from '../../../../services/confirmation-dialog-service'
import {
  combineLatest,
  filter,
  map,
  Subject,
  switchMap,
  take,
  takeUntil,
} from 'rxjs'
import { HttpErrorResponse } from '@angular/common/http'
import { VenioNotificationService } from '@venio/feature/notification'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-entity-extraction-dialog',
  standalone: true,
  imports: [
    CommonModule,
    GridModule,
    DialogModule,
    ButtonsModule,
    SvgLoaderDirective,
  ],
  templateUrl: './entity-extraction-dialog.component.html',
  styleUrl: './entity-extraction-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EntityExtractionDialogComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  public isEntitySelectionDialogVisible = true // Controls visibility of the main dialog

  public dialogTitle = 'Entity Extraction'

  private get projectId(): number {
    return Number(this.activatedRoute.snapshot.queryParams['projectId'])
  }

  public entityList = []

  public isEntitySelected = true

  public selectedEntities: number[] = [] // Array of selected entity IDs

  public selectionTypeEnum: DocSelectionType

  public fileIdList: number[] = []

  public payload: EntityExtractionRequest

  public tempSearchResultTable: string

  constructor(
    private activatedRoute: ActivatedRoute,
    private entityExtractionService: EntityExtractionService,
    private confirmationDialogService: ConfirmationDialogService,
    private documentsFacade: DocumentsFacade,
    private searchFacade: SearchFacade,
    private notificationService: VenioNotificationService,
    private cdr: ChangeDetectorRef,
    @Optional()
    private dialogRef: DialogRef
  ) {}

  public ngOnInit(): void {
    this.#GetEntities()
  }

  #GetEntities(): void {
    this.entityExtractionService
      .getEntities$<ResponseModel>(this.projectId)
      .pipe(take(1))
      .subscribe({
        next: (entities: ResponseModel) => {
          this.cdr.markForCheck()
          if (entities?.data) {
            this.entityList = entities.data

            // Select all entities by default, but only store entityId in selectedEntities
            this.selectedEntities = this.entityList.map(
              (entity) => entity.entityId
            )
          }
        },
        error: (ex: unknown) => {
          const error = (ex as HttpErrorResponse).error
          this.notificationService.showError(error.message)
        },
      })
  }

  public onEntitySelectionChange(): void {
    this.isEntitySelected = this.selectedEntities.length > 0
  }

  // Method to handle the Extract button click
  public extractEntities(): void {
    this.#showConfirmationDialog()
    this.isEntitySelectionDialogVisible = false
  }

  /**
   * Load the confirmation dialog
   * @returns {void}
   */
  #showConfirmationDialog(): void {
    const title = 'Entity Extraction'
    const content =
      'Are you sure you want to queue selected files for entity extraction?'

    this.confirmationDialogService
      .showConfirmationDialog(title, content)
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe((confirmed) => {
        if (confirmed) {
          // If the user confirmed, proceed with document selection
          this.#handleDocumentSelection()
        } else {
          // If the user did not confirm (clicked "No"), close the dialog
          this.close() // Close or hide the dialog
        }
      })
  }

  /**
   * Handles document selection.
   * It takes the latest document selection
   * @returns {void}
   */
  #handleDocumentSelection(): void {
    combineLatest([
      this.documentsFacade.getIsBatchSelected$,
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.getUnselectedDocuments$,
      this.searchFacade.getTotalHitCount$,
      this.searchFacade.getSearchTempTables$,
    ])
      .pipe(
        map(
          ([
            isBatchSelected,
            selectedDocs,
            unselectedDocs,
            totalHitCount,
            searchTempTables,
          ]) => {
            const selectedDocCount = isBatchSelected
              ? totalHitCount - unselectedDocs.length
              : selectedDocs.length

            if (selectedDocCount > 0) {
              this.selectionTypeEnum = DocSelectionType.SelectedFilesOnly

              if (isBatchSelected && unselectedDocs.length > 0) {
                this.selectionTypeEnum = DocSelectionType.AllFilesExceptSelected
                this.fileIdList = unselectedDocs
              } else if (isBatchSelected && unselectedDocs.length <= 0) {
                this.selectionTypeEnum = DocSelectionType.AllFiles
              } else {
                this.selectionTypeEnum = DocSelectionType.SelectedFilesOnly
                this.fileIdList = selectedDocs
              }

              this.tempSearchResultTable =
                searchTempTables.searchResultTempTable
            }

            return selectedDocCount > 0
          }
        ),
        take(1),
        filter((hasSelectedDocs) => hasSelectedDocs), // Proceed only if documents are selected
        switchMap(() => {
          this.payload = {
            selectionType: this.selectionTypeEnum,
            fileIdList: this.fileIdList,
            tempTable: this.tempSearchResultTable,
            entities: this.selectedEntities.join(','),
          }
          return this.entityExtractionService.queueEntityForExtraction$(
            this.projectId,
            this.payload
          )
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (response: ResponseModel) => {
          this.close()
          if (response.status === 'Success') {
            this.notificationService.showSuccess(response.message)
          } else {
            this.notificationService.showError(response.message)
          }
        },
        error: (ex: unknown) => {
          const error = (ex as HttpErrorResponse).error
          this.notificationService.showError(error.message)
        },
      })
  }

  public close(): void {
    this.dialogRef.close()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
