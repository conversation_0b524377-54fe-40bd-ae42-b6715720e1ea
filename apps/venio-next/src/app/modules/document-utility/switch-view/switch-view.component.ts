import {
  ChangeDetectionStrategy,
  Component,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DocumentsFacade,
  ReviewFacade,
  ReviewViewType,
} from '@venio/data-access/review'
import {
  DialogCloseResult,
  DialogRef,
  DialogService,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import { Subject, filter, take, takeUntil } from 'rxjs'
import { DocumentMenuType } from '@venio/shared/models/constants'
import {
  ConfirmationDialogComponent,
  NotificationDialogComponent,
} from '@venio/feature/notification'

@Component({
  selector: 'venio-review-switch-view',
  standalone: true,
  imports: [CommonModule, DialogsModule],
  template: '',
  styles: '',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SwitchViewComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly confirmDialogTitle = 'Switch to Email Thread View'

  private currentViewType = ReviewViewType.Search

  private message = ''

  constructor(
    private documentsFacade: DocumentsFacade,
    private dialogService: DialogService,
    private reviewFacade: ReviewFacade
  ) {}

  public ngOnInit(): void {
    this.#handleReviewViewTypeChange()
    this.#handleEmailThreadViewStatusResult()
    this.#selectedDocumentEvent()
  }

  /**
   * When the dialog is closed, it is no point to keep the menu event state
   * @returns {void}
   */
  #resetMenuEventState(): void {
    this.documentsFacade.resetDocumentState('menuEventPayload')
  }

  /**
   * When a menu link is clicked, the loading indicator is displayed to
   * indicate the user that there is something happening in the background
   * such as loading the dialog component lazily and then once loaded, turn off the indicator
   * @returns {void}
   */
  #resetMenuLoadingState(): void {
    this.documentsFacade.resetDocumentState('isDocumentMenuLoading')
  }

  /**
   * Select the document menu event state when it is triggered
   * @returns {void}
   */
  #selectedDocumentEvent(): void {
    this.documentsFacade.selectDocumentMenuEvent$
      .pipe(
        filter((event) => event === DocumentMenuType.SWITCH_VIEW),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        if (this.currentViewType === ReviewViewType.Search) {
          this.reviewFacade.fetchEmailThreadViewStatus()
        } else {
          this.reviewFacade.isSwitchingView(true)
          this.reviewFacade.setReviewViewType(ReviewViewType.Search)
        }
        this.resetEvents()
      })
  }

  #handleReviewViewTypeChange(): void {
    this.reviewFacade.getReviewViewType$
      .pipe(
        filter(
          (v) => v === ReviewViewType.EmailThread || v === ReviewViewType.Search
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((viewType) => {
        this.currentViewType = viewType
      })
  }

  #handleEmailThreadViewStatusResult(): void {
    this.reviewFacade.getEmailThreadViewStatus$
      .pipe(
        filter((s) => !!s),
        takeUntil(this.toDestroy$)
      )
      .subscribe((status) => {
        if (status !== 'MEDIA_HAS_EMAIL_THREAD') {
          this.showConfirmDialog(status)
        } else {
          this.reviewFacade.isSwitchingView(true)
          this.reviewFacade.setReviewViewType(ReviewViewType.EmailThread)
        }
      })
  }

  private setConfirmMessage(status: string): void {
    switch (status) {
      case 'NO_EMAIL_FILES':
        this.message = "Search result doesn't have email files."
        break
      case 'EMAIL_THREAD_NOT_PREPARED':
        this.message = 'Email thread has not been populated.'
        break
      case 'SOME_MEDIA_HAS_NO_EMAIL_THREAD':
        this.message =
          'Some media(s) do not have email thread populated. Do you want to switch to email thread view?'
        break
      default:
        this.message = "Search result doesn't have email files."
        break
    }
  }

  private showConfirmDialog(status: string): void {
    this.setConfirmMessage(status)
    const someEmailAvailable = status === 'SOME_MEDIA_HAS_NO_EMAIL_THREAD'
    const dialogRef = someEmailAvailable
      ? this.openConfirmDialog()
      : this.openNotificationDialog()

    dialogRef.result
      .pipe(
        filter((result) => !!result),
        take(1)
      )
      .subscribe((result) => {
        if (
          !(result instanceof DialogCloseResult) &&
          result &&
          someEmailAvailable
        ) {
          this.reviewFacade.isSwitchingView(true)
          this.reviewFacade.setReviewViewType(ReviewViewType.EmailThread)
        }
        this.resetEvents()
      })
  }

  private openConfirmDialog(): DialogRef {
    const dialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      title: this.confirmDialogTitle,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
    })
    dialogRef.content.instance.message = this.message

    return dialogRef
  }

  private openNotificationDialog(): DialogRef {
    const dialogRef = this.dialogService.open({
      content: NotificationDialogComponent,
      title: this.confirmDialogTitle,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
    })
    dialogRef.content.instance.message = this.message

    return dialogRef
  }

  public resetEvents(): void {
    this.#resetMenuLoadingState()
    this.#resetMenuEventState()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetMenuEventState()
    this.reviewFacade.resetEmailThreadViewStatus()
  }
}
