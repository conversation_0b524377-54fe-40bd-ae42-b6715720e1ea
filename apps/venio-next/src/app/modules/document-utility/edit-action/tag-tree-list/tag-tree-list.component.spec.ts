import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagTreeListComponent } from './tag-tree-list.component'
import { provideMockStore } from '@ngrx/store/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideRouter } from '@angular/router'

describe('TagTreeListComponent', () => {
  let component: TagTreeListComponent
  let fixture: ComponentFixture<TagTreeListComponent>

  // Unless we do testing on this component, we simply automatically mock it.
  jest.doMock('@progress/kendo-angular-treelist')

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [NoopAnimationsModule, TagTreeListComponent],
      providers: [provideRouter([]), provideMockStore({})],
    }).compileComponents()

    fixture = TestBed.createComponent(TagTreeListComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
