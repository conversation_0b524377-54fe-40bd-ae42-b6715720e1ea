import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CodingFormComponent } from './coding-form.component'
import { provideMockStore } from '@ngrx/store/testing'
import { NotificationService } from '@progress/kendo-angular-notification'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { EventEmitter } from '@angular/core'
import { provideRouter } from '@angular/router'
import { FieldFacade } from '@venio/data-access/review'

describe('CodingFormComponent', () => {
  let component: CodingFormComponent
  let fixture: ComponentFixture<CodingFormComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CodingFormComponent, NoopAnimationsModule],
      providers: [
        provideRouter([]),
        provideMockStore({}),
        FieldFacade,
        NotificationService,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(CodingFormComponent)
    component = fixture.componentInstance
    component.isCodingFormValid = new EventEmitter<boolean>()
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
