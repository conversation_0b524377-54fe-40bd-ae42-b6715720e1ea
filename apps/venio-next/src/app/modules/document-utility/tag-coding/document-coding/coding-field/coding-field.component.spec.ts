import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CodingFieldComponent } from './coding-field.component'
import { StoreModule } from '@ngrx/store'
import { DocumentCodingFacade } from '@venio/data-access/document-utility'
import { DocumentCodingViewModel } from '@venio/shared/models/interfaces'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('CodingFieldComponent', () => {
  let component: CodingFieldComponent
  let fixture: ComponentFixture<CodingFieldComponent>

  const field: DocumentCodingViewModel = {
    shouldShowTextField: true,
    customFieldInfoId: 0,
    fieldName: '',
    description: '',
    displayName: '',
    uiInputType: '',
    createdOn: '',
    createdBy: 0,
    allowMultipleCodingValues: false,
    delimiterForCodingValues: '',
    allowPredefinedCodingValuesOnly: false,
    detailDataCount: 0,
    scale: 0,
    length: 0,
    multiValuedCodingOptions: 0,
    fieldCodingValues: [],
    updatedFieldValue: '',
    currentFieldValue: '',
    dateFormat: '',
    checkForDateType: false,
    shouldShowPredefinedCodingField: false,
    shouldShowDelimiter: false,
    shouldShowMultipleCodingValuesField: false,
    extractedDelimiter: '',
    shouldShowField: false,
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CodingFieldComponent, StoreModule.forRoot({})],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentCodingFacade,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(CodingFieldComponent)
    component = fixture.componentInstance
    component.field = field
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
