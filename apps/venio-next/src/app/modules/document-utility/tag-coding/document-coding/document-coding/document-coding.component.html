<div class="t-flex">
  <div class="t-flex t-p-3 t-flex-col t-w-full">
    <div class="t-flex-none t-font-bold t-text-base" *ngIf="!isBulkDocument">
      Coding
      <!-- Expand/Collapse Buttons -->
      <ng-container *ngFor="let icon of svgIconForToggleControls">
        <button
          class="t-p-1 t-ml-2 t-cursor-pointer"
          (click)="toggleExpandCollapse(icon.actionType)"
          kendoTooltip
          [title]="icon.actionText">
          <span
            venioSvgLoader
            [svgUrl]="icon.iconPath"
            [color]="icon.iconColor"
            height="1rem"
            width="1rem"></span>
        </button>
      </ng-container>
    </div>

    <div class="t-relative" #formElement *ngIf="isCodingExpanded">
      <div
        *ngIf="isDocumentCodingLoading"
        class="k-i-loading t-absolute t-h-full t-w-full t-bg-[rgba(255,255,255,0.47)] t-text-[rgba(18,17,17,0.93)] t-top-0 t-left-0 t-right-0 t-bottom-0 t-text-[58px] t-z-10"></div>
      <div class="t-flex t-mt-2">
        <ng-container *ngIf="!isCodingFieldsExists()">
          <div class="t-flex t-w-full t-flex-col t-gap-3">
            <div>
              <p class="t-italic t-text-center">No coding fields available</p>
            </div>
          </div>
        </ng-container>
        <ng-container *ngIf="isCodingFieldsExists() && isComponentReady">
          <form
            class="t-w-full t-flex-col t-gap-3 t-flex-wrap"
            [formGroup]="codingForm"
            venioDynamicHeight
            [isKendoDialog]="isBulkDocument ? true : false"
            [minHeight]="200"
            (heightSet)="onFormHeightSet($event)">
            <cdk-virtual-scroll-viewport
              *ngIf="isHeightReady"
              [style.height.px]="calculatedHeight"
              class="v-hide-scrollbar"
              [itemSize]="50">
              <ng-container
                *cdkVirtualFor="
                  let field of codingFields;
                  trackBy: codingFieldTrackByFn;
                  let i = index
                ">
                <div
                  class="t-flex t-gap-3 t-mb-2 t-w-full"
                  [formGroupName]="field.fieldName">
                  <div
                    class="t-flex t-items-center t-w-5"
                    *ngIf="isBulkDocument">
                    <input
                      [id]="'checkbox-' + i"
                      type="checkbox"
                      class="t-mt-6"
                      kendoCheckBox
                      [formControl]="
                        getControl(field.fieldName, 'isChecked')
                      " />
                  </div>
                  <div class="t-flex t-flex-col t-flex-1">
                    <venio-coding-field
                      [control]="getControl(field.fieldName, 'fieldValue')"
                      [field]="field"
                      [isBulkDocument]="isBulkDocument"></venio-coding-field>
                  </div>
                </div>
              </ng-container>
            </cdk-virtual-scroll-viewport>
          </form>
        </ng-container>
      </div>
    </div>
  </div>
</div>

<ng-template #loading>
  <div class="t-flex">
    <kendo-skeleton
      shape="text"
      animation="pulse"
      [width]="40"
      [height]="40"
      class="t-mr-3"></kendo-skeleton>
    <kendo-skeleton
      shape="text"
      animation="pulse"
      width="10rem"></kendo-skeleton>
  </div>
</ng-template>
