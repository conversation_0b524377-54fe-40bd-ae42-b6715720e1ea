import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DocumentCodingModel,
  DocumentCodingViewModel,
  SelectedMultiCodingValueModel,
} from '@venio/shared/models/interfaces'
import { DocumentCodingControlActionType } from '@venio/shared/models/constants'
import { Subject } from 'rxjs'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { DocumentCodingFacade } from '@venio/data-access/document-utility'

@Component({
  selector: 'venio-coding-field',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    InputsModule,
    LabelModule,
    TooltipsModule,
    DropDownListModule,
    IndicatorsModule,
    SvgLoaderDirective,
    ButtonsModule,
  ],
  templateUrl: './coding-field.component.html',
  styleUrl: './coding-field.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CodingFieldComponent implements OnDestroy {
  @Input()
  public control: FormControl

  @Input()
  public field: DocumentCodingViewModel

  @Input()
  public isBulkDocument: boolean

  public currentField: DocumentCodingViewModel

  protected readonly codingActionEvent = DocumentCodingControlActionType

  public unsubscribed$: Subject<void> = new Subject<void>()

  constructor(
    private documentCodingFacade: DocumentCodingFacade,
    private changeDetectorRef: ChangeDetectorRef
  ) {}

  public getErrorKeys(errors: { [key: string]: any }): string[] {
    return Object.keys(errors)
  }

  public setMultiCodingValue(
    actionType: DocumentCodingControlActionType,
    selectedField: DocumentCodingModel
  ): void {
    if (actionType === DocumentCodingControlActionType.MULTIDOCUMENT) {
      selectedField = { ...selectedField, currentFieldValue: '' }
    }
    const selectedMultiCodingValue: SelectedMultiCodingValueModel = {
      eventType: actionType,
      selectedField: selectedField,
      selectedValues: undefined,
      selectedOperationType: undefined,
      selectedNewValues: undefined,
      isBulkDocument: this.isBulkDocument,
    }

    this.documentCodingFacade.setMultiCodingValue(
      actionType,
      selectedMultiCodingValue
    )
  }

  /**
   * display tooltip for coding field
   */
  public codingInfoToggleTemplate(field: DocumentCodingViewModel): void {
    this.currentField = field
    this.changeDetectorRef.markForCheck()
  }

  public clearCodingInfo(): void {
    this.currentField = null
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }
}
