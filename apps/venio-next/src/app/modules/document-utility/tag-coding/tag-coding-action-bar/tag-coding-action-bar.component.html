<div>
  <div
    class="t-flex t-flex-1 t-justify-between t-items-center t-p-1.5 t-self-start t-px-4">
    <div class="t-flex t-items-center t-flex-wrap">
      <span class="t-font-bold t-text-base t-inline-block t-ml-2">
        {{ tagGroupName() }}
        <!-- Conditionally Hide & Show Icon -->
        <kendo-svg-icon
          *ngIf="(isTagRuleListLoaded$ | async) && !isAIGroup()"
          class="t-text-[var(--tb-kendo-success-100)] t-w-[1.25rem] t-h-[1.25rem] t-cursor-pointer"
          [icon]="infoCircleIcon"
          kendoPopoverAnchor
          [popover]="tagRuleDescriptionPopover"
          showOn="click"
          (click)="showAllTagRules()">
        </kendo-svg-icon>
      </span>
    </div>
  </div>
</div>

<kendo-popover
  #tagRuleDescriptionPopover
  position="bottom"
  [animation]="{ type: 'slide', direction: 'right', duration: 100 }">
  <ng-template kendoPopoverBodyTemplate>
    <ng-container *ngComponentOutlet="tagRuleInfoComp | async"></ng-container>
  </ng-template>
</kendo-popover>

<div
  #actionBar
  class="t-flex t-flex-wrap t-gap-3 t-px-3 t-pb-1 t-min-w-0 t-items-center">
  <div class="t-flex md:t-flex-1 t-basis-4/12">
    <kendo-textbox
      class="!t-border-[#ccc] md:t-min-w-[200px]"
      placeholder="Search For Tags"
      [formControl]="searchTagControl"
      [clearButton]="true"
      (venioAfterValueChanged)="onFilter($event)">
      <ng-template kendoTextBoxSuffixTemplate>
        <kendo-textbox-separator></kendo-textbox-separator>
        <button kendoButton fillMode="clear">
          <span
            venioSvgLoader
            svgUrl="assets/svg/icon-search.svg"
            height="1rem"
            width="1rem">
            <kendo-loader size="small"></kendo-loader>
          </span>
        </button>
      </ng-template>
    </kendo-textbox>
  </div>

  <div
    class="t-flex t-w-full t-min-w-[200px] md:t-w-auto t-overflow-hidden t-rounded-[4px] t-border-[1px] t-border-[#cccccc]"
    kendoTooltip>
    @for (icon of svgIconForTagControls; track icon.actionType) {
    <button
      kendoButton
      #parentElTag
      *venioHasUserGroupRights="icon.allowedPermission"
      class="!t-p-[0.3rem] t-h-fit t-w-full t-shadow t-shadow-slate-200 !t-border-0 !t-rounded-[0px] hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
      [ngClass]="{
        't-rounded-l-sm': icon.isFirst,
        't-rounded-r-sm': icon.isLast,
        'hover:!t-bg-[#9BD2A7] hover:!t-border-[#9BD2A7]': icon.actionText === DocumentActionTypeTitleInstance.SAVE_TEXT,
      }"
      (click)="browseActionClicked(icon.actionType)"
      size="none"
      fillMode="outline"
      [title]="icon.actionText"
      rounded="none"
      [disabled]="
        icon.actionText === DocumentActionTypeTitleInstance.SAVE_TEXT
          ? !isCodingDataValid() && canViewCodingPanel()
          : false
      ">
      <span
        [parentElement]="parentElTag.element"
        venioSvgLoader
        [svgUrl]="icon.iconPath"
        height="0.85rem"
        hoverColor="#ffffff"
        [color]="
          icon.actionText === DocumentActionTypeTitleInstance.SAVE_TEXT
            ? '#9BD2A7'
            : '#979797'
        "
        width="1rem">
        <kendo-loader size="small"></kendo-loader>
      </span>
    </button>
    }
  </div>
</div>

<ng-container *ngComponentOutlet="documentEditContainer | async"></ng-container>
