import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagCodingActionBarComponent } from './tag-coding-action-bar.component'
import {
  DocumentCodingFacade,
  DocumentTagFacade,
} from '@venio/data-access/document-utility'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
  SearchResultFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { DocumentTagUtilityService } from '../../utility-services/document-tag-utility'
import { ActivatedRoute } from '@angular/router'
import { WINDOW } from '@venio/data-access/iframe-messenger'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { BehaviorSubject } from 'rxjs'

describe('TagCodingActionBarComponent', () => {
  let component: TagCodingActionBarComponent
  let fixture: ComponentFixture<TagCodingActionBarComponent>
  let queryParamsSubject: BehaviorSubject<any>

  beforeEach(async () => {
    queryParamsSubject = new BehaviorSubject({ test: 'value' })

    await TestBed.configureTestingModule({
      imports: [TagCodingActionBarComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: WINDOW, useValue: jest.fn() },
        DocumentTagFacade,
        DocumentsFacade,
        DocumentCodingFacade,
        SearchFacade,
        SearchResultFacade,
        FieldFacade,
        DocumentTagUtilityService,
        StartupsFacade,
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { queryParams: {} },
            queryParams: queryParamsSubject.asObservable(),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(TagCodingActionBarComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
