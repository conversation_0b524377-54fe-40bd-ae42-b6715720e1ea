import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  inject,
  OnDestroy,
  Type,
} from '@angular/core'

import { Subject } from 'rxjs'
import { UserRights } from '@venio/data-access/review'

@Component({
  selector: 'venio-tag-coding-container',
  templateUrl: './tag-coding-container.component.html',
  styleUrls: ['./tag-coding-container.component.scss'],
})
export class TagCodingContainerComponent implements AfterViewInit, OnDestroy {
  private toDestroy$ = new Subject<void>()

  private changeDetectorRef = inject(ChangeDetectorRef)

  public UserRights = UserRights

  public tagCodingActionBarComponent: Promise<Type<unknown>>

  public documentTagContainerComponent: Promise<Type<unknown>>

  public documentCodingContainerComponent: Promise<Type<unknown>>

  public tagCodingFilterContainer: Promise<Type<unknown>>

  public ngAfterViewInit(): void {
    this.#loadLazyComponents()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #loadLazyComponents(): void {
    this.tagCodingActionBarComponent = import(
      '../tag-coding-action-bar/tag-coding-action-bar.component'
    ).then(({ TagCodingActionBarComponent }) => TagCodingActionBarComponent)

    this.documentTagContainerComponent = import(
      '../document-tag/document-tag-container/document-tag-container.component'
    ).then(({ DocumentTagContainerComponent }) => DocumentTagContainerComponent)

    this.documentCodingContainerComponent = import(
      '../document-coding/document-coding-container/document-coding-container.component'
    ).then(
      ({ DocumentCodingContainerComponent }) => DocumentCodingContainerComponent
    )

    this.tagCodingFilterContainer = import(
      '../../tag-coding/tag-coding-filter/tag-coding-filter-container/tag-coding-filter-container.component'
    ).then((m) => m.TagCodingFilterContainerComponent)

    // The lazy components may use onPush change detection strategy, so we need to manually trigger change detection
    // when they are loaded again after the first disposal.
    this.changeDetectorRef.detectChanges()
  }
}
