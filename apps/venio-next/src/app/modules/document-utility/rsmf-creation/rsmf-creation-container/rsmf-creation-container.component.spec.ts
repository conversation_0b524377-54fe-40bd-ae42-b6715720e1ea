import { TestBed } from '@angular/core/testing'
import { RsmfCreationContainerComponent } from './rsmf-creation-container.component'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import {
  DialogContainerService,
  DialogService,
} from '@progress/kendo-angular-dialog'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http'
import { VenioNotificationService } from '@venio/feature/notification'

describe('RsmfCreationContainerComponent', () => {
  let component: RsmfCreationContainerComponent
  let fixture: any
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RsmfCreationContainerComponent],
      providers: [
        provideMockStore({}),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        DialogService,
        DialogContainerService,
        VenioNotificationService,
        provideHttpClient(withInterceptorsFromDi()),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(RsmfCreationContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
