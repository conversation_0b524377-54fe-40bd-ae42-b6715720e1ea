import {
  ChangeDetectionStrategy,
  Component,
  OnD<PERSON>roy,
  OnInit,
  Type,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Subject, combineLatest, filter, take, takeUntil } from 'rxjs'
import {
  DialogRef,
  DialogService,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import { DocumentsFacade } from '@venio/data-access/review'
import { DocumentMenuType } from '@venio/shared/models/constants'
import { NotificationDialogComponent } from '@venio/feature/notification'

@Component({
  selector: 'venio-bulk-tag-coding-aciton-container',
  standalone: true,
  imports: [CommonModule, DialogsModule],
  templateUrl: './bulk-tag-coding-aciton-container.component.html',
  styleUrls: ['./bulk-tag-coding-aciton-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BulkTagCodingAcitonContainerComponent
  implements OnInit, OnD<PERSON>roy
{
  private readonly toDestroy$ = new Subject<void>()

  private dialogRef: DialogRef

  constructor(
    private documentsFacade: DocumentsFacade,
    private dialogService: DialogService
  ) {}

  public ngOnInit(): void {
    this.#selectedDocumentEvent()
  }

  #resetMenuEventState(): void {
    this.documentsFacade.resetDocumentState('menuEventPayload')
  }

  #resetMenuLoadingState(): void {
    this.documentsFacade.resetDocumentState('isDocumentMenuLoading')
  }

  #handleEditDialogCloseEvent(): void {
    this.dialogRef.dialog.onDestroy(() => {
      this.#resetMenuEventState()
    })
  }

  #launchDialogContent(dialogContent: Type<unknown>): void {
    this.dialogRef = this.dialogService.open({
      content: dialogContent,
      maxWidth: '1100px',
      maxHeight: '650px',
      width: '80%',
      height: '90vh',
    })
  }

  #handleLazyLoadedDialog(): void {
    import(
      '../bulk-tag-coding-aciton-dialog/bulk-tag-coding-aciton-dialog.component'
    ).then((d) => {
      // reset the loading indicator
      this.#resetMenuLoadingState()

      // launch the dialog
      this.#launchDialogContent(d.BulkTagCodingAcitonDialogComponent)

      // once the dialogRef instance is created
      this.#handleEditDialogCloseEvent()
    })
  }

  /**
   * Select the document menu event state when it is triggered
   * @returns {void}
   */
  #selectedDocumentEvent(): void {
    combineLatest([
      this.documentsFacade.selectDocumentMenuEvent$,
      this.documentsFacade.getSelectedDocuments$,
    ])
      .pipe(
        filter(([menuEventType, selectedDocuments]) =>
          Boolean(menuEventType === DocumentMenuType.TAGS && selectedDocuments)
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([menuEventType, selectedDocuments]) => {
        if (selectedDocuments && selectedDocuments.length === 0) {
          this.#showNotificationMessage()
          return
        }
        // launch the dialo
        this.#handleLazyLoadedDialog()
      })
  }

  #showNotificationMessage(): void {
    const notificationDialogRef = this.dialogService.open({
      content: NotificationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
      width: '35rem',
    })

    this.#setDialogInput(notificationDialogRef.content.instance)

    notificationDialogRef.result
      .pipe(
        filter((result) => typeof result === 'boolean' && result === true),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.#resetMenuLoadingState()
        this.#resetMenuEventState()
      })
  }

  #setDialogInput(instance: NotificationDialogComponent): void {
    instance.title = 'Bulk Tags & Coding'
    instance.message = `Please select at least one document to apply bulk tags & coding`
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetMenuEventState()
  }
}
