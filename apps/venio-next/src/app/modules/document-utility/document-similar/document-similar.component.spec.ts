import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentSimilarComponent } from './document-similar.component'
import { PLATFORM_ID } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideMockStore } from '@ngrx/store/testing'
import {
  ReviewPanelFacade,
  SimilarDocumentModel,
  SimilarDocumentResponseModel,
  SimilaritySearchScopeType,
} from '@venio/data-access/document-utility'
import {
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import {
  SearchFacade,
  DocumentsFacade,
  FieldFacade,
} from '@venio/data-access/review'
import { BehaviorSubject, of } from 'rxjs'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { By } from '@angular/platform-browser'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('DocumentSimilarComponent', () => {
  let component: DocumentSimilarComponent
  let fixture: ComponentFixture<DocumentSimilarComponent>
  let mockReviewPanelFacade: any

  const mockSimilarityScore$ = new BehaviorSubject<number>(50)
  const mockSimilaritySearchScopeType$ =
    new BehaviorSubject<SimilaritySearchScopeType>(
      SimilaritySearchScopeType.ALL_DOCUMENTS
    )
  const selectedDocumentSeqNo$ = new BehaviorSubject<number>(1)
  const mockSimilarDocumentResponse: SimilarDocumentResponseModel = {
    totalHitCount: 10,
    tempTables: {
      similarHitTableName: '##TempSimilarDocs',
      similarResultTableName: '##SimilarDocsResult',
    },
    searchTerms: ['ring', 'hou', 'pm', 'zl', 'attribution', 'technologies'],
  }

  const mockSimilarDocuments: SimilarDocumentModel[] = [
    {
      fileId: 1,
      metadata: [
        { key: 'Original File name', value: 'Document 1' },
        { key: 'Score', value: '50' },
        { key: 'FileId', value: '2' },
      ],
    },
  ]

  beforeEach(async () => {
    // Create a spy object for ReviewPanelFacade
    mockReviewPanelFacade = {
      setSimilarityScore: jest.fn(),
      setSimilaritySearchScopeType: jest.fn(),
      selectSimilarityScore$: mockSimilarityScore$.asObservable(),
      selectSimilaritySearchScopeType$:
        mockSimilaritySearchScopeType$.asObservable(),
      selectSimilarDocumentResponse$: of(mockSimilarDocumentResponse),
      selectSimilarDocument$: of(mockSimilarDocuments),
      selectDocumentSeqNo$: of(selectedDocumentSeqNo$),
      resetReviewPanelState: jest.fn(),
    }

    await TestBed.configureTestingModule({
      imports: [
        DocumentSimilarComponent,
        BrowserAnimationsModule,
        InputsModule,
        ButtonsModule,
        FormsModule,
        ReactiveFormsModule,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
        IframeMessengerModule.forRoot({}),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
        { provide: ReviewPanelFacade, useValue: mockReviewPanelFacade },
        SearchFacade,
        DocumentsFacade,
        FieldFacade,
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '2' }),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentSimilarComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should initialize form controls properly', () => {
    // GIVEN initial values for similarity search scope type and score
    const initialSimilaritySearchScopeType =
      SimilaritySearchScopeType.ALL_DOCUMENTS
    const initialSimilarityScore = 50

    // WHEN the component is initialized
    component.ngOnInit()

    // THEN the form controls should be initialized with the correct values
    expect(component.searchScopeForm.get('searchScopeType').value).toEqual(
      initialSimilaritySearchScopeType
    )
    expect(component.searchScopeForm.get('slider').value).toEqual(
      initialSimilarityScore
    )
  })

  it('should update form control value on radio button selection', () => {
    // GIVEN a specific search scope value
    const searchScope = SimilaritySearchScopeType.ALL_DOCUMENTS

    // WHEN a radio button is selected
    const radioInput = fixture.debugElement.query(By.css('input[type=radio]'))
    radioInput.triggerEventHandler('click', null)

    // THEN the form control value should be updated with the selected search scope
    expect(component.searchScopeForm.get('searchScopeType').value).toEqual(
      searchScope
    )
  })

  it('should update initial Similarity Score', () => {
    // GIVEN an initial slider value
    const initialSliderValue = 50

    // WHEN the component is initialized
    component.ngOnInit()
    const sliderElement = fixture.debugElement.query(By.css('kendo-slider'))

    // THEN the slider value should be initialized with the correct value
    expect(sliderElement.componentInstance.value).toEqual(initialSliderValue)
  })

  it('should update form control value when radio button value changes', () => {
    // GIVEN the component has been initialized
    component.ngOnInit()
    fixture.detectChanges()
    const allDocRadioButton = fixture.debugElement.query(
      By.css('#allDoc')
    ).nativeElement
    const selMediaRadioButton = fixture.debugElement.query(
      By.css('#selMedia')
    ).nativeElement

    // WHEN the "ALL_DOCUMENTS" radio button is clicked
    allDocRadioButton.click()
    fixture.detectChanges()

    // THEN the form control value should be updated to "ALL_DOCUMENTS"
    expect(component.searchScopeForm.get('searchScopeType').value).toBe(
      component.searchScope.ALL_DOCUMENTS
    )

    // WHEN the "SELECTED_MEDIA" radio button is clicked
    selMediaRadioButton.click()
    fixture.detectChanges()

    // THEN the form control value should be updated to "SELECTED_MEDIA"
    expect(component.searchScopeForm.get('searchScopeType').value).toBe(
      component.searchScope.SELECTED_MEDIA
    )
  })

  it('should toggle search scope state when button is clicked', () => {
    // GIVEN the initial state of the search scope is 'up'
    const hideSearchFilter = 'up'
    const showSearchFilter = 'down'
    const toggleButton = fixture.debugElement.query(
      By.css('#toogleSearchBtn')
    ).nativeElement
    expect(component.state).toBe(hideSearchFilter)

    // WHEN the toggle button is clicked
    toggleButton.dispatchEvent(new Event('click'))
    fixture.detectChanges()

    // THEN the search scope state should change to 'down'
    expect(component.state).toBe(showSearchFilter)

    // WHEN the toggle button is clicked again
    toggleButton.dispatchEvent(new Event('click'))
    fixture.detectChanges()

    // THEN the search scope state should revert back to 'up'
    expect(component.state).toBe(hideSearchFilter)
  })

  it('should update Similarity Score value', () => {
    // GIVEN values for similarity score
    const similarityScore = 50

    // WHEN the method to update the score in the store is invoked
    component.updateStoreScoreAndScope()

    // Check if setSimilarityScore was called with the correct similarity values
    expect(mockReviewPanelFacade.setSimilarityScore).toHaveBeenCalledWith(
      similarityScore
    )
  })

  it('should update search scope type value', () => {
    // GIVEN values for similarity search scope type
    const selectedSearchScope = SimilaritySearchScopeType.ALL_DOCUMENTS

    // WHEN the method to update the score in the store is invoked
    component.updateStoreScoreAndScope()

    // Check if setSimilaritySearchScopeType was called with the correct selected scope values
    expect(
      mockReviewPanelFacade.setSimilaritySearchScopeType
    ).toHaveBeenCalledWith(selectedSearchScope)
  })
})
