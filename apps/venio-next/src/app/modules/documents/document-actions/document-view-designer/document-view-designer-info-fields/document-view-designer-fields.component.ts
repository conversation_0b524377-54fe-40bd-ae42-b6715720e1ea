import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  Field,
  FieldFacade,
  ViewField,
  ViewModel,
} from '@venio/data-access/review'
import { filter, startWith, Subject, switchMap, takeUntil } from 'rxjs'
import { ButtonModule } from '@progress/kendo-angular-buttons'

import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { CommonActionTypes } from '@venio/shared/models/constants'
import {
  ArrayMovementManager,
  FieldManagerModel,
  MoveResult,
} from '@venio/util/utilities'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { DocumentViewDesignerFieldSelectionComponent } from './document-view-designer-field-selection/document-view-designer-field-selection.component'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { DocumentViewFacade } from '@venio/data-access/common'
import { debounceTime, distinctUntilChanged } from 'rxjs/operators'
import { isEqual } from 'lodash'

@Component({
  selector: 'venio-document-view-designer-fields',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    IndicatorsModule,
    DocumentViewDesignerFieldSelectionComponent,
    TooltipsModule,
    SvgLoaderDirective,
  ],
  templateUrl: './document-view-designer-fields.component.html',
  styleUrl: './document-view-designer-fields.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentViewDesignerFieldsComponent implements OnInit, OnDestroy {
  private readonly arrayMovementManager = new ArrayMovementManager()

  private changeDetectorRef = inject(ChangeDetectorRef)

  private documentViewFacade = inject(DocumentViewFacade)

  private toDestroy$ = new Subject<void>()

  private fieldFacade = inject(FieldFacade)

  private permittedFields = signal<Field[]>([])

  public leftFields: FieldManagerModel[] = []

  public rightFields: FieldManagerModel[] = []

  public selectedRightFields: FieldManagerModel[] = []

  public selectedLeftFields: FieldManagerModel[] = []

  public middleActionButtonSvg = [
    {
      title: 'Move All to SELECTED',
      svgUrl: 'assets/svg/icon-e-next-svg.svg',
      hoverColor: '#FFFFFF',
      dataQa: 'iconLeft',
      actionType: CommonActionTypes.RIGHT,
      clickAction: (): void =>
        this.fieldSelectionActionClick(CommonActionTypes.RIGHT, true),
      isDisabled: (): boolean => false,
    },
    {
      title: 'Swap Fields',
      svgUrl: 'assets/svg/icon-exchange-svg.svg',
      hoverColor: '#FFFFFF',
      dataQa: 'iconRight',
      actionType: CommonActionTypes.SWAP,
      clickAction: (): void =>
        this.fieldSelectionActionClick(CommonActionTypes.SWAP),
      isDisabled: (): boolean =>
        this.leftFields.length <= 0 && this.rightFields.length <= 0,
    },
    {
      title: 'Move Checked to SELECTED',
      svgUrl: 'assets/svg/icon-right-arrow-svg.svg',
      hoverColor: '#FFFFFF',
      dataQa: 'iconRightArrow',
      actionType: CommonActionTypes.RIGHT,
      clickAction: (): void =>
        this.fieldSelectionActionClick(CommonActionTypes.RIGHT),
      isDisabled: (): boolean => this.selectedLeftFields.length === 0,
    },
    {
      title: 'Move Checked to UNSELECTED',
      svgUrl: 'assets/svg/icon-arrow-left-5-svg.svg',
      hoverColor: '#FFFFFF',
      dataQa: 'iconLeftArrow',
      actionType: CommonActionTypes.LEFT,
      clickAction: (): void =>
        this.fieldSelectionActionClick(CommonActionTypes.LEFT),
      isDisabled: (): boolean => this.selectedRightFields.length === 0,
    },
    {
      title: 'Move All to UNSELECTED',
      svgUrl: 'assets/svg/icon-e-back-svg.svg',
      hoverColor: '#FFFFFF',
      dataQa: 'iconElement',
      actionType: CommonActionTypes.LEFT,
      clickAction: (): void =>
        this.fieldSelectionActionClick(CommonActionTypes.LEFT, true),
      isDisabled: (): boolean => false,
    },
  ]

  public rightSideActions = [
    {
      title: 'Move Top',
      svgUrl: 'assets/svg/icon-e-back-svg.svg',
      hoverColor: '#FFFFFF',
      dataQa: 'iconTop',
      clickAction: (): void => this.fieldReorder(CommonActionTypes.TOP),
      isDisabled: (): boolean => this.selectedRightFields.length === 0,
      extraClasses: '!t-rotate-90',
    },
    {
      title: 'Move Up',
      svgUrl: 'assets/svg/icon-up-chevron-svg.svg',
      hoverColor: '#FFFFFF',
      dataQa: 'iconUp',
      clickAction: (): void => this.fieldReorder(CommonActionTypes.UP),
      isDisabled: (): boolean => this.selectedRightFields.length === 0,
    },
    {
      title: 'Move Down',
      svgUrl: 'assets/svg/icon-down-chevron-svg.svg',
      hoverColor: '#FFFFFF',
      dataQa: 'iconRightDown',
      clickAction: (): void => this.fieldReorder(CommonActionTypes.DOWN),
      isDisabled: (): boolean => this.selectedRightFields.length === 0,
    },
    {
      title: 'Move Bottom',
      svgUrl: 'assets/svg/icon-e-next-svg.svg',
      hoverColor: '#FFFFFF',
      dataQa: 'iconBottom',
      clickAction: (): void => this.fieldReorder(CommonActionTypes.BOTTOM),
      isDisabled: (): boolean => this.selectedRightFields.length === 0,
      extraClasses: '!t-rotate-90',
    },
  ]

  public ngOnInit(): void {
    this.#selectFieldAndMapViewFields()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Handles changes in the selection of items, either on the right or left side.
   * Updates the selected items array and marks the component for check in the change detection cycle.
   *
   * @param {FieldManagerModel[]} items - Array of selected FieldManagerModel items.
   * @param {boolean} [isRight=false] - Boolean indicating if the selection change is for the right side.
   * @returns {void}
   */
  public selectionItemChanged(
    items: FieldManagerModel[],
    isRight = false
  ): void {
    this.changeDetectorRef.markForCheck()
    if (isRight) {
      this.selectedLeftFields.length = 0
      this.selectedRightFields = items
    } else {
      this.selectedRightFields.length = 0
      this.selectedLeftFields = items
    }
  }

  public fieldSelectionActionClick(
    commonActionTypes: CommonActionTypes,
    isAll = false
  ): void {
    switch (commonActionTypes) {
      case CommonActionTypes.RIGHT:
        if (isAll) {
          this.arrayMovementManager.moveAllToRight()
        } else {
          this.arrayMovementManager.moveSelectedToRight(this.selectedLeftFields)
        }
        break
      case CommonActionTypes.LEFT:
        if (isAll) {
          this.arrayMovementManager.moveAllToLeft()
        } else {
          this.arrayMovementManager.moveSelectedToLeft(this.selectedRightFields)
        }
        break
      case CommonActionTypes.SWAP:
        this.arrayMovementManager.swapItems()
        break
    }
    this.#recalculateModifiedFields(true)
    this.#storeUpdatesOfSelectedFields()
  }

  public fieldReorder(commonActionTypes: CommonActionTypes): void {
    let shouldResetSelected = false
    let moveResult: MoveResult
    switch (commonActionTypes) {
      case CommonActionTypes.TOP:
        this.arrayMovementManager.moveToTop(this.selectedRightFields)
        shouldResetSelected = true
        break
      case CommonActionTypes.UP:
        moveResult = this.arrayMovementManager.moveUp(this.selectedRightFields)
        break
      case CommonActionTypes.DOWN:
        moveResult = this.arrayMovementManager.moveDown(
          this.selectedRightFields
        )
        break
      case CommonActionTypes.BOTTOM:
        this.arrayMovementManager.moveToBottom(this.selectedRightFields)
        shouldResetSelected = true
        break
    }
    shouldResetSelected =
      shouldResetSelected ||
      moveResult === MoveResult.ReachedTop ||
      moveResult === MoveResult.ReachedBottom ||
      moveResult === MoveResult.Unchanged

    this.#recalculateModifiedFields(shouldResetSelected)
    this.#storeUpdatesOfSelectedFields()
  }

  #mapBaseArray(fields: Field[]): FieldManagerModel[] {
    return fields.map(
      (c) =>
        ({
          displayFieldName: c.displayFieldName,
          venioFieldId: c.venioFieldId,
          isCustomField: c.isCustomField,
        } as FieldManagerModel)
    )
  }

  #mapViewFields(
    baseFields: Field[],
    viewFields: ViewField[]
  ): FieldManagerModel[] {
    if (!viewFields?.length) return []

    return viewFields.map((c) => {
      const field = baseFields.find((d) => d.displayFieldName === c.fieldName)
      return {
        displayFieldName: field?.displayFieldName,
        venioFieldId: field?.venioFieldId,
        isCustomField: field?.isCustomField,
      } as FieldManagerModel
    })
  }

  #initializeLeftArrayBaseFields(): void {
    this.changeDetectorRef.markForCheck()
    const fields = this.permittedFields()

    const initialLeftArray = this.#mapBaseArray(fields)
    this.arrayMovementManager.initialize(initialLeftArray)
  }

  #recalculateModifiedFields(shouldResetSelected = false): void {
    this.changeDetectorRef.markForCheck()
    this.leftFields = [...this.arrayMovementManager.getLeftArray()]
    this.rightFields = [...this.arrayMovementManager.getRightArray()]

    if (!shouldResetSelected) return

    this.selectedRightFields = []
    this.selectedLeftFields = []
  }

  #setSavedFormData(viewModel: ViewModel): void {
    const savedFieldFormData = this.#mapViewFields(
      this.permittedFields(),
      viewModel?.viewFields
    )
    this.arrayMovementManager.moveSelectedToRight(savedFieldFormData)
    this.#recalculateModifiedFields()
  }

  #selectFieldAndMapViewFields(): void {
    let comparerViewFields = []
    this.fieldFacade.getPermittedFields$
      .pipe(
        startWith([] as Field[]),
        filter((fields) => fields?.length > 0),
        switchMap((fields) => {
          const onlySearchableFields = fields.filter(
            (f) => f.isSearchField || f.isCustomField
          )
          this.permittedFields.set(onlySearchableFields)
          this.#initializeLeftArrayBaseFields()
          this.#recalculateModifiedFields()
          return this.documentViewFacade.selectCurrentFormData$
        }),
        distinctUntilChanged((prev, curr) =>
          isEqual(comparerViewFields, curr?.viewFields)
        ),
        debounceTime(500),
        takeUntil(this.toDestroy$)
      )
      .subscribe((formData) => {
        comparerViewFields = formData?.viewFields
        this.#setSavedFormData(formData)
        this.#storeUpdatesOfSelectedFields()
      })
  }

  #storeUpdatesOfSelectedFields(): void {
    const selectedFields = this.rightFields

    const viewFields = selectedFields.map(
      (c) =>
        ({
          fieldId: c.venioFieldId,
          fieldName: c.displayFieldName,
          isCustomField: c.isCustomField,
        } as ViewField)
    )

    // Avoid duplicate field being populated and sent to store
    this.documentViewFacade.storeCurrentFormData({
      viewFields,
    })
  }
}
