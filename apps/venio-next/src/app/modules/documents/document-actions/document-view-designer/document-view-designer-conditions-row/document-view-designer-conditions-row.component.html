<div class="v-custom-condition t-flex t-flex-wrap t-w-full t-gap-2 t-relative">
  <div class="t-flex-grow t-basis-1/4 t-flex">
    <kendo-dropdownlist
      [(ngModel)]="condition.fieldName"
      [title]="condition.fieldName || 'Select a field'"
      (ngModelChange)="fieldChange($event)"
      data-qa="field-name"
      [popupSettings]="{ popupClass: '' }"
      class="t-flex-grow t-basis-[calc(100%_-_9.2rem)]"
      [filterable]="true"
      [virtual]="{ itemHeight: 28 }"
      [kendoDropDownFilter]="{
        caseSensitive: false,
        operator: 'contains'
      }"
      [data]="fields"
      [valuePrimitive]="true"
      textField="displayName"
      valueField="displayName">
      <ng-template kendoDropDownListItemTemplate let-data>
        <span
          class="t-whitespace-nowrap t-overflow-hidden t-overflow-ellipsis"
          [title]="data.displayName"
          >{{ data.displayName }}</span
        >
      </ng-template>
    </kendo-dropdownlist>
    <kendo-dropdownlist
      *ngIf="isSizeTypeVisible()"
      [(ngModel)]="condition.sizeType"
      [title]="condition.sizeType || 'Select a size'"
      (ngModelChange)="sizeChange($event)"
      data-qa="size"
      [popupSettings]="{ popupClass: '' }"
      class="t-flex-grow t-w-5 t-gap-0"
      [data]="['bytes', 'kb', 'mb', 'gb']">
    </kendo-dropdownlist>
  </div>
  <kendo-dropdownlist
    [title]="condition.operator || 'Select an operator'"
    [disabled]="!condition?.fieldName"
    [(ngModel)]="condition.operator"
    (ngModelChange)="operatorChange($event)"
    data-qa="operator"
    class="t-flex-grow t-basis-0"
    [popupSettings]="{ popupClass: 't-w-[16rem]' }"
    [filterable]="true"
    [valuePrimitive]="true"
    [virtual]="{ itemHeight: 28 }"
    [kendoDropDownFilter]="{
      caseSensitive: false,
      operator: 'contains'
    }"
    [data]="operatorData()"
    textField="displayName"
    valueField="operator">
    <ng-template kendoDropDownListItemTemplate let-data>
      <span
        class="t-whitespace-nowrap t-overflow-hidden t-overflow-ellipsis"
        [title]="data.displayName"
        >{{ data.displayName }}</span
      >
    </ng-template>
  </kendo-dropdownlist>

  @if(conditionElementRender().isSingleSelect ||
  conditionElementRender().isMultiSelect ||
  conditionElementRender()?.isBoolean){
  <kendo-dropdownlist
    *ngIf="
      conditionElementRender()?.isSingleSelect ||
      conditionElementRender()?.isBoolean
    "
    [title]="condition.primaryValue || 'Select a value'"
    [disabled]="!condition.operator"
    [(ngModel)]="condition.primaryValue"
    (ngModelChange)="valueChange()"
    data-qa="field-name"
    class="t-flex-grow t-basis-0"
    [popupSettings]="{ popupClass: 't-w-[16rem]' }"
    [filterable]="true"
    [valuePrimitive]="true"
    [virtual]="{ itemHeight: 28 }"
    [kendoDropDownFilter]="{
      caseSensitive: false,
      operator: 'contains'
    }"
    valueField="value"
    textField="value"
    [data]="primaryDropdownValues()">
    <ng-template kendoDropDownListItemTemplate let-data>
      <span
        class="t-whitespace-nowrap t-overflow-hidden t-overflow-ellipsis"
        [title]="data.value"
        >{{ data.value }}</span
      >
    </ng-template>
  </kendo-dropdownlist>
  <kendo-multiselect
    *ngIf="conditionElementRender()?.isMultiSelect"
    [disabled]="!condition.operator"
    [title]="condition.primaryValue || 'Select values'"
    [(ngModel)]="condition.primaryValue"
    (ngModelChange)="valueChange()"
    data-qa="field-name"
    class="t-flex-grow t-basis-0"
    [popupSettings]="{ popupClass: 't-w-[16rem]' }"
    [filterable]="true"
    [valuePrimitive]="true"
    [virtual]="{ itemHeight: 28 }"
    [kendoDropDownFilter]="{
      caseSensitive: false,
      operator: 'contains'
    }"
    valueField="value"
    textField="value"
    [data]="primaryDropdownValues()">
  </kendo-multiselect>
  }

  <ng-container
    *ngTemplateOutlet="
      reuseInputs;
      context: {
        isPrimary: isPrimaryValueVisible(),
        isSecondary: isSecondaryValueVisible(),
        condition: condition
      }
    " />
  <div
    class="t-flex t-w-1/6 v-custom-condition__action-container"
    [ngClass]="{
      '!t-opacity-100 !t-visible v-open-active': moreButtonDropdown.isOpen
    }">
    <div class="t-flex t-items-center">
      <button
        kendoButton
        title="Add new condition"
        fillMode="clear"
        size="none"
        data-qa="iconAddNew4"
        (click)="actionClicked(commonActionTypes.ADD)"
        #operatorButton>
        <span
          title="Add new condition"
          venioSvgLoader
          [parentElement]="operatorButton.element"
          svgUrl="assets/svg/icon-plus-large-svg.svg"
          hoverColor="#FFBB12"
          color="#979797"
          height="1rem"
          width="1rem">
          <kendo-loader size="small"></kendo-loader>
        </span>
      </button>
    </div>
    <div class="t-flex">
      <button
        kendoButton
        title="Remove this condition"
        fillMode="clear"
        size="none"
        data-qa="iconRemove4"
        (click)="actionClicked(commonActionTypes.DELETE)"
        #removeButton>
        <span
          title="Remove this condition"
          venioSvgLoader
          [parentElement]="removeButton.element"
          svgUrl="assets/svg/icon-trash-bin-minimalistic-svg.svg"
          hoverColor="#ED7425"
          color="#979797"
          height=".9rem"
          width=".9rem">
          <kendo-loader size="small"></kendo-loader>
        </span>
      </button>
    </div>
    <div class="t-flex" #moreButton>
      <kendo-dropdownbutton
        #moreButtonDropdown
        title="Add another condition group"
        data-qa="iconDots4"
        [data]="groupOperators"
        (itemClick)="actionClicked(commonActionTypes.ADD, $event)"
        fillMode="none"
        buttonClass="!t-border-0 !t-w-4 !t-p-0 !t-relative !t-flex !t-items-center t-h-full">
        <span
          title="Add another condition group"
          class="t-relative"
          applyEffectsTo="both"
          venioSvgLoader
          [parentElement]="moreButton"
          svgUrl="assets/svg/icon-dots-3-vertical-svg.svg"
          hoverColor="#FFBB12"
          color="#979797"
          height="1rem"
          width="1rem">
          <kendo-loader size="small"></kendo-loader>
        </span>
      </kendo-dropdownbutton>
    </div>
  </div>
  <div
    class="t-flex t-justify-center t-items-center t-pr-4"
    kendoTooltip
    *ngIf="!generatedSyntax()">
    <kendo-svg-icon
      [icon]="iconInfo"
      themeColor="error"
      title="Fields that are not inserted with values will be ignored when saving."></kendo-svg-icon>
  </div>
</div>

<ng-template
  #reuseInputs
  let-isPrimary="isPrimary"
  let-isSecondary="isSecondary"
  let-condition="condition">
  <ng-template [ngIf]="isPrimary">
    <kendo-textbox
      [disabled]="!condition.operator"
      *ngIf="conditionElementRender().isText"
      class="t-flex-grow t-basis-0"
      [title]="condition.primaryValue || 'Enter a value'"
      [(ngModel)]="condition.primaryValue"
      (ngModelChange)="valueChange()"></kendo-textbox>
    <kendo-numerictextbox
      [disabled]="!condition.operator"
      *ngIf="conditionElementRender().isNumber"
      format="##"
      [min]="0"
      class="t-flex-grow t-basis-0"
      [title]="condition.primaryValue || 'Enter a value'"
      [(ngModel)]="condition.primaryValue"
      (ngModelChange)="valueChange()"></kendo-numerictextbox>
    <kendo-datepicker
      [disabled]="!condition.operator"
      *ngIf="conditionElementRender().isDate"
      [allowCaretMode]="true"
      format="MM/dd/yyyy"
      class="t-flex-grow t-basis-0"
      [title]="condition.primaryValue || 'Enter a value'"
      [(ngModel)]="condition.primaryValue"
      (ngModelChange)="valueChange()"></kendo-datepicker>
  </ng-template>
  <ng-template [ngIf]="isSecondary">
    <kendo-textbox
      [disabled]="!condition.operator"
      *ngIf="conditionElementRender().isText"
      class="t-flex-grow t-basis-0"
      [title]="condition.secondaryValue || 'Enter a value'"
      [(ngModel)]="condition.secondaryValue"
      (ngModelChange)="valueChange()"></kendo-textbox>
    <kendo-numerictextbox
      [disabled]="!condition.operator"
      *ngIf="conditionElementRender().isNumber"
      format="##"
      [min]="0"
      class="t-flex-grow t-basis-0"
      [title]="condition.secondaryValue || 'Enter a value'"
      [(ngModel)]="condition.secondaryValue"
      (ngModelChange)="valueChange()"></kendo-numerictextbox>
    <kendo-datepicker
      [disabled]="!condition.operator"
      *ngIf="conditionElementRender().isDate"
      [allowCaretMode]="true"
      format="MM/dd/yyyy"
      class="t-flex-grow t-basis-0"
      [min]="condition.primaryValue || null"
      [title]="condition.secondaryValue || 'Enter a value'"
      [(ngModel)]="condition.secondaryValue"
      (ngModelChange)="valueChange()"></kendo-datepicker>
  </ng-template>
</ng-template>
