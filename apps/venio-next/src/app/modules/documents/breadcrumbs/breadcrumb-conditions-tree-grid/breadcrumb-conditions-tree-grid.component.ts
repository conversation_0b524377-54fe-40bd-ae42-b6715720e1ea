import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  Optional,
  Output,
  signal,
  SimpleChanges,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import { DialogRef } from '@progress/kendo-angular-dialog'

@Component({
  selector: 'venio-breadcrumb-conditions-tree-grid',
  standalone: true,
  imports: [CommonModule, TreeListModule],
  templateUrl: './breadcrumb-conditions-tree-grid.component.html',
  styleUrl: './breadcrumb-conditions-tree-grid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BreadcrumbConditionsTreeGridComponent implements OnChanges {
  @Input()
  public data: any[] = []

  @Input()
  public shouldSkipParentCount = true

  public treeData = signal([])

  // Default height of the tree list
  public treeListHeight = 800

  @Input()
  public columns: Array<{
    field: string
    title: string
    filterable?: boolean
  }> = []

  @Output()
  public readonly selectedItems = new EventEmitter<unknown[]>()

  @Optional()
  private dialogRef = inject(DialogRef)

  public selected = []

  public selectionItemChanged(event: Array<{ itemKey: number }>): void {
    const items = this.treeData().filter(({ id }) =>
      event.some(({ itemKey }) => itemKey === id)
    )
    this.selectedItems.emit(items)
  }

  public ngOnChanges(changes: SimpleChanges): void {
    if (changes.data?.currentValue) {
      this.#calculateDialogContentHeight()
      this.#setDataSource()
    }
  }

  /**
   * Dynamically calculates the height of the dialog content and sets the height of the tree list.
   *
   * The height of the tree list is calculated by subtracting the padding threshold from the content height.
   * @return {void}
   */
  #calculateDialogContentHeight(): void {
    const containerEl = this.dialogRef.dialog.location
      .nativeElement as HTMLElement

    if (!containerEl) return

    const dialogSelector = '.k-dialog .k-window-content'

    const contentEl = containerEl.querySelector(dialogSelector)

    if (!contentEl) return

    const contentHeight = contentEl.clientHeight

    const paddingThreshold = 70

    this.treeListHeight = contentHeight - paddingThreshold
  }

  #setDataSource(): void {
    this.treeData.set(this.#extendTree(this.data))
  }

  #extendTree(data: any[]): any[] {
    const [_, theData] = data.reduce(
      ([acc, upd], { treeKeyId, treeParentId, ...rest }) => [
        {
          ...acc,
          [treeKeyId]: (acc[treeKeyId] ??= Object.keys(acc).length + 1),
          [treeParentId]:
            treeParentId !== '-1'
              ? (acc[treeParentId] ??= Object.keys(acc).length + 1)
              : null,
        },
        [
          ...upd,
          {
            ...rest,
            id: acc[treeKeyId],
            parentId: treeParentId !== '-1' ? acc[treeParentId] : null,
          },
        ],
      ],
      [{}, []]
    )
    return theData
  }
}
