import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  inject,
  Input,
  OnDestroy,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { CardModule } from '@progress/kendo-angular-layout'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { SVGIcon, xCircleIcon } from '@progress/kendo-svg-icons'
import { CheckBoxModule } from '@progress/kendo-angular-inputs'
import {
  AutoScrollOrFocusDirective,
  TextTruncateDirective,
} from '@venio/feature/shared/directives'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import {
  ConditionGroup,
  ConditionType,
  GroupOperator,
  GroupStackType,
} from '@venio/shared/models/interfaces'
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { filter, Subject, take, takeUntil } from 'rxjs'
import { DialogService } from '@progress/kendo-angular-dialog'
import {
  BreadcrumbFacade,
  BreadcrumbService,
} from '@venio/data-access/breadcrumbs'

@Component({
  selector: 'venio-breadcrumb-stack-content',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    ButtonsModule,
    CheckBoxModule,
    TextTruncateDirective,
    TooltipsModule,
    FormsModule,
    ReactiveFormsModule,
    AutoScrollOrFocusDirective,
  ],
  templateUrl: './breadcrumb-stack-content.component.html',
  styleUrl: './breadcrumb-stack-content.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BreadcrumbStackContentComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  @Input({ required: true })
  public breadcrumb: ConditionGroup

  @ViewChild('syntaxContainerElement', { read: HTMLElement, static: true })
  public syntaxContainerElement: ElementRef<HTMLElement>

  private dialogService = inject(DialogService)

  private breadcrumbFacade = inject(BreadcrumbFacade)

  public breadcrumbService = inject(BreadcrumbService)

  public xCircleIcon: SVGIcon = xCircleIcon

  public conditionCheckboxControl = new FormControl(false)

  public hasTruncatedText = signal<boolean>(false)

  public isTextTruncated(isTruncated: boolean): void {
    this.hasTruncatedText.set(isTruncated)
  }

  public syntax: string

  public ngOnInit(): void {
    this.#setConditionCheckboxControlValue()
    this.#setBreadcrumbSyntax()
    this.#handleBreadcrumbCheckboxChange()
    this.#subscribeToConditionChecked()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public deleteBreadcrumb(): void {
    const notificationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
      width: '35rem',
    })

    this.#setDialogInput(notificationDialogRef.content.instance)

    notificationDialogRef.result
      .pipe(
        filter((result) => typeof result === 'boolean' && result === true),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.breadcrumbFacade.deleteBreadcrumb(this.breadcrumb.id)
      })
  }

  #setDialogInput(instance: ConfirmationDialogComponent): void {
    instance.title = 'Remove Breadcrumb'
    instance.message = 'Are you sure you want to remove this breadcrumb?'
  }

  #getFieldSyntax(): string {
    if (!this.breadcrumb.children?.length) {
      return ''
    }

    const getOperatorString = (operator: string): string =>
      ` ${operator === GroupOperator.NOT ? `AND ${operator}` : `${operator} `}`

    const buildConditionGroupSyntax = (
      conditionGroup: ConditionGroup
    ): string => {
      if (conditionGroup.conditionType === ConditionType.Operator) {
        return getOperatorString(conditionGroup.operator)
      }

      const hasAnyConditions = conditionGroup.conditions?.length > 0
      if (!hasAnyConditions) {
        return ''
      }

      const conditionSyntax = conditionGroup.conditions
        .map((condition) => condition.conditionSyntax)
        .join(` ${conditionGroup.operator} `)

      return `(${conditionSyntax})`
    }

    return this.breadcrumb.children.map(buildConditionGroupSyntax).join('')
  }

  #setRegularSyntax(): void {
    let syntax = this.breadcrumb?.conditions
      ?.map((condition) => condition.conditionSyntax)
      .join(` ${this.breadcrumb?.operator} `)
      .trim()
    if (!syntax.startsWith('(') && !syntax.endsWith(')')) {
      syntax = `(${syntax})`
    }
    this.syntax = syntax
  }

  #setBreadcrumbSyntax(): void {
    switch (this.breadcrumb.groupStackType) {
      case GroupStackType.FIELDS:
        this.syntax = this.#getFieldSyntax()
        break
      default:
        this.#setRegularSyntax()

        break
    }
  }

  #setConditionCheckboxControlValue(): void {
    this.conditionCheckboxControl.setValue(this.breadcrumb?.checked)
  }

  #subscribeToConditionChecked(): void {
    this.breadcrumbService
      .getConditionChecked$(this.breadcrumb.groupStackType)
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((value) => {
        this.conditionCheckboxControl.setValue(value)
      })
  }

  #handleBreadcrumbCheckboxChange(): void {
    this.conditionCheckboxControl.valueChanges
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(() =>
        this.breadcrumbFacade.updateBreadcrumb({
          ...this.breadcrumb,
          checked: this.conditionCheckboxControl.value,
        })
      )
  }
}
