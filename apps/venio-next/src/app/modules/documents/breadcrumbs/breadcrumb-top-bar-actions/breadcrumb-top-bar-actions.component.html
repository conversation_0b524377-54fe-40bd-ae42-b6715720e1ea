@defer (on viewport) {

<div class="t-flex t-w-full t-justify-between">
  <div class="t-flex t-gap-3">
    <kendo-dropdownbutton
      #conditionDropdown
      [data]="breadcrumbConditionDropdown"
      class="!t-border-[#BEBEBE] t-flex-grow t-w-auto !t-flex t-items-center"
      buttonClass="t-transform-none"
      fillMode="outline"
      themeColor="none"
      [popupSettings]="{ popupClass: 't-w-full t-bg-white' }"
      (itemClick)="conditionItemClick($event)">
      Condition
      <kendo-svg-icon
        [icon]="chevronDown"
        class="t-text-[#212121]"></kendo-svg-icon>
      <ng-template kendoDropDownButtonItemTemplate let-dataItem>
        <span [ngStyle]="{ 'min-width.px': dropdownButtonWidth }">
          {{ dataItem }}
        </span>
      </ng-template>
    </kendo-dropdownbutton>

    <button
      kendoButton
      class="t-h-full t-w-4"
      fillMode="outline"
      [disabled]="!completeBreadcrumbSyntax()"
      (click)="performBreadcrumbSearch()">
      <kendo-svg-icon
        [icon]="searchIconSvg"
        class="t-text-[#1EBADC]"
        size="large"></kendo-svg-icon>
    </button>
  </div>

  <div class="t-flex">
    <kendo-label
      class="t-justify-between t-flex t-font-semibold t-items-center t-text-sm t-text-[#000000]"
      text="AUTO RUN">
      <kendo-switch
        [ngModel]="shouldAutoRunSearch()"
        offLabel=""
        onLabel=""
        (valueChange)="toggleAutoRunSearch($event)"
        class="t-mx-1"
        size="large"></kendo-switch>
    </kendo-label>
  </div>
</div>

} @placeholder {
<div class="t-flex t-flex-grow t-gap-2 t-absolute t-w-full t-px-2 t-pb-5">
  <kendo-skeleton
    shape="text"
    animation="pulse"
    width="50%"
    height="30px"></kendo-skeleton>
  <kendo-skeleton
    shape="text"
    animation="pulse"
    width="25px"
    height="30px"></kendo-skeleton>
  <kendo-skeleton
    shape="circle"
    animation="pulse"
    width="60px"
    height="20px"></kendo-skeleton>
</div>
}
