import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  Optional,
  Self,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import {
  DocumentViewFacade,
  ProjectFacade,
  TagsFacade,
} from '@venio/data-access/common'
import {
  ConditionElement,
  ConditionGroup,
  ConditionType,
  ConditionUiType,
  GroupOperator,
  GroupStackType,
  TagsModel,
} from '@venio/shared/models/interfaces'
import { filter, Subject, take, takeUntil } from 'rxjs'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { BreadcrumbControlContainerComponent } from '../breadcrumb-control-container/breadcrumb-control-container.component'
import { BreadcrumbConditionsTreeGridComponent } from '../breadcrumb-conditions-tree-grid/breadcrumb-conditions-tree-grid.component'
import { UuidGenerator } from '@venio/util/uuid'
import { ActivatedRoute } from '@angular/router'
import { FolderModel } from '@venio/data-access/review'

@Component({
  selector: 'venio-breadcrumb-conditions-dialog-container',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    DialogsModule,
    BreadcrumbControlContainerComponent,
    BreadcrumbConditionsTreeGridComponent,
  ],
  templateUrl: './breadcrumb-conditions-dialog-container.component.html',
  styleUrl: './breadcrumb-conditions-dialog-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BreadcrumbConditionsDialogContainerComponent
  implements OnInit, OnDestroy
{
  private readonly toDestroy$ = new Subject<void>()

  public readonly conditionUiType = ConditionUiType.BREADCRUMB_CONDITION

  public readonly commonActionType = CommonActionTypes

  private readonly documentViewFacade = inject(DocumentViewFacade)

  private readonly breadcrumbFacade = inject(BreadcrumbFacade)

  private readonly projectFacade = inject(ProjectFacade)

  private readonly tagsFacade = inject(TagsFacade)

  private readonly activatedRoute = inject(ActivatedRoute)

  public selectedGroupStackType = signal<GroupStackType>(undefined)

  public tags = signal<TagsModel[]>([])

  private selectedTags = signal<TagsModel[]>([])

  public tagColumnSettings = [
    { field: 'tagName', title: 'Tag Name', filterable: true },
    { field: 'fileCount', title: 'Tag Count' },
  ]

  public folders = signal<FolderModel[]>([])

  private selectedFolders = signal<FolderModel[]>([])

  public folderColumnSettings = [
    { field: 'folderName', title: 'Folder Name', filterable: true },
    { field: 'fileCount', title: 'File Count' },
  ]

  public readonly groupStackTypes = GroupStackType

  @Optional()
  @Self()
  public dialogRef = inject(DialogRef)

  @Input()
  public set groupStackType(value: GroupStackType) {
    this.selectedGroupStackType.set(value)
  }

  public lazyConditionsComponent = import(
    '../../document-actions/document-view-designer/document-view-designer-info-conditions/document-view-designer-conditions.component'
  ).then(
    ({ DocumentViewDesignerConditionsComponent }) =>
      DocumentViewDesignerConditionsComponent
  )

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  private breadcrumbStacks: ConditionGroup = {
    id: UuidGenerator.uuid,
    conditions: [],
    children: [],
    conditionType: ConditionType.Group,
    checked: true,
    operator: GroupOperator.AND,
  }

  public ngOnInit(): void {
    this.#initialActions()
    this.#fetchSearchFields()
    this.#selectConditions()
    this.#selectFolderTreeWithCount()
    this.#selectTagTreeWithCount()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public breadcrumbAction(actionType: CommonActionTypes): void {
    switch (actionType) {
      case CommonActionTypes.SAVE:
        this.#saveBreadcrumb()
        break
      case CommonActionTypes.CANCEL:
        this.dialogRef.close()
        break
    }
  }

  public containerOperatorChanged(groupOperator: GroupOperator): void {
    this.breadcrumbStacks.operator = groupOperator
    this.#createSyntaxFromTreeData()
  }

  public treeSelectionChanged(items: unknown[]): void {
    switch (this.selectedGroupStackType()) {
      case GroupStackType.TAGS:
        this.selectedTags.set((items as TagsModel[]) || [])
        break
      case GroupStackType.FOLDERS:
        this.selectedFolders.set(items as FolderModel[] | [])
        break
    }

    this.#createSyntaxFromTreeData()
  }

  #createSyntaxFromTreeData(): void {
    const hasNoItems =
      (this.selectedGroupStackType() === GroupStackType.TAGS &&
        this.selectedTags().length === 0) ||
      (this.selectedGroupStackType() === GroupStackType.FOLDERS &&
        this.selectedFolders().length === 0)
    if (hasNoItems) {
      this.breadcrumbStacks.conditions = []
      return
    }

    const conditionElement = { id: UuidGenerator.uuid } as ConditionElement
    switch (this.selectedGroupStackType()) {
      case GroupStackType.TAGS:
        {
          const tagNames = this.selectedTags()
            .filter((c) => c.treeParentId !== '-1' && c.tagId > 0)
            .map((item) => `"${item.tagName}"`)
            .join(` ${this.breadcrumbStacks.operator} `)
          conditionElement.conditionSyntax = `TAGS(${tagNames})`
        }
        break
      case GroupStackType.FOLDERS:
        {
          const folderNames = this.selectedFolders()
            .map((item) => `"${item.folderLineage}"`)
            .join(` ${this.breadcrumbStacks.operator} `)
          conditionElement.conditionSyntax = `FOLDERS(${folderNames})`
        }
        break
    }
    this.breadcrumbStacks.conditions = [conditionElement]
  }

  #initialActions(): void {
    this.projectFacade.fetchProjectFolderTreeWithCount(this.projectId)
    this.tagsFacade.fetchTagTreeWithCount(this.projectId)
  }

  #fetchSearchFields(): void {
    this.documentViewFacade.fetchSearchFields()
  }

  #saveBreadcrumb(): void {
    this.dialogRef.close()
    this.breadcrumbStacks.groupStackType = this.selectedGroupStackType()
    this.breadcrumbFacade.setAutoSearchControllerFlag(true)
    this.breadcrumbFacade.selectAutoSearchControllerFlag$
      .pipe(
        filter((autoSearchFlag) => autoSearchFlag),
        take(1)
      )
      .subscribe(() => {
        this.breadcrumbFacade.storeBreadcrumbs([this.breadcrumbStacks])
      })
  }

  #selectConditions(): void {
    this.documentViewFacade.selectConditionGroup$
      .pipe(
        // currently it is only applicable for fields.
        // later if we plan to make editable the breadcrumb item, additional effort or logic is needed.
        filter(() => this.selectedGroupStackType() === GroupStackType.FIELDS),
        takeUntil(this.toDestroy$)
      )
      .subscribe((conditionGroup) => {
        this.breadcrumbStacks.children = conditionGroup
      })
  }

  #selectFolderTreeWithCount(): void {
    this.projectFacade.selectProjectFolderTreeWithCountSuccessResponse$
      .pipe(
        filter((response) => Boolean(response)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response) => {
        this.folders.set(
          response.data.map((folder: FolderModel) => ({
            ...folder,
            treeKeyId: `${folder.folderId}`,
            treeParentId: `${folder.parentFolderId}`,
          }))
        )
      })
  }

  #selectTagTreeWithCount(): void {
    this.tagsFacade.selectTagTreeWithCount$
      .pipe(
        filter((response) => Boolean(response)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response) => {
        this.tags.set(response.data)
      })
  }
}
