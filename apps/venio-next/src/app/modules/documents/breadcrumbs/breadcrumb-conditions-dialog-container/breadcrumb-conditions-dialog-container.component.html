<div class="t-flex t-flex-wrap t-flex-row t-w-full">
  <div class="t-mb-3 t-mt-5 t-font-medium t-uppercase t-tracking-wide">
    <h3>{{ selectedGroupStackType() }}</h3>
  </div>
  <div class="t-w-full t-h-full t-relative t-block">
    @switch (selectedGroupStackType()) { @case (groupStackTypes.FIELDS) {
    <ng-container
      *ngComponentOutlet="
        lazyConditionsComponent | async;
        inputs: {
          conditionUiType: conditionUiType
        }
      " />
    } @case (groupStackTypes.TAGS) {
    <venio-breadcrumb-control-container
      (operatorChanged)="containerOperatorChanged($event)">
      <venio-breadcrumb-conditions-tree-grid
        [data]="tags()"
        [columns]="tagColumnSettings"
        (selectedItems)="treeSelectionChanged($event)" />
    </venio-breadcrumb-control-container>
    } @case (groupStackTypes.FOLDERS) {
    <venio-breadcrumb-control-container
      (operatorChanged)="containerOperatorChanged($event)">
      <venio-breadcrumb-conditions-tree-grid
        (selectedItems)="treeSelectionChanged($event)"
        [data]="folders()"
        [columns]="folderColumnSettings"
        [shouldSkipParentCount]="false" />
    </venio-breadcrumb-control-container>
    } }
  </div>
</div>
<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      class="v-custom-secondary-button"
      fillMode="outline"
      themeColor="secondary"
      (click)="breadcrumbAction(commonActionType.SAVE)">
      SAVE
    </button>
    <button
      kendoButton
      themeColor="dark"
      fillMode="outline"
      (click)="breadcrumbAction(commonActionType.CANCEL)">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
