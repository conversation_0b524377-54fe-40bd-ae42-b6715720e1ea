import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'

@Component({
  selector: 'venio-breadcrumb-conditions-fields',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './breadcrumb-conditions-fields.component.html',
  styleUrl: './breadcrumb-conditions-fields.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BreadcrumbConditionsFieldsComponent {}
