import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnDestroy,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { filter, Subject, switchMap, take, takeUntil, tap } from 'rxjs'
import { DialogService } from '@progress/kendo-angular-dialog'
import {
  BreadcrumbFacade,
  BreadcrumbService,
} from '@venio/data-access/breadcrumbs'
import { SearchFacade } from '@venio/data-access/review'
import { toSignal } from '@angular/core/rxjs-interop'
import { GroupStackType } from '@venio/shared/models/interfaces'

@Component({
  selector: 'venio-breadcrumb-bottom-actions',
  standalone: true,
  imports: [CommonModule, ButtonsModule],
  templateUrl: './breadcrumb-bottom.component.html',
  styleUrl: './breadcrumb-bottom.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BreadcrumbBottomComponent implements OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private dialogService = inject(DialogService)

  private breadcrumbFacade = inject(BreadcrumbFacade)

  private breadcrumbService = inject(BreadcrumbService)

  private searchFacade = inject(SearchFacade)

  private completeSyntax = toSignal(
    this.breadcrumbFacade.selectCompleteBreadcrumbSyntax$
  )

  public clearBreadcrumbs(): void {
    const notificationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
      width: '35rem',
    })

    this.#setDialogInput(notificationDialogRef.content.instance)

    notificationDialogRef.result
      .pipe(
        filter((result) => typeof result === 'boolean' && result === true),
        tap(() => {
          this.#resetBreadcrumbState()
        }),
        switchMap(() => {
          return this.breadcrumbFacade.selectCompleteBreadcrumbSyntax$
        }),

        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((expression) => {
        this.searchFacade.updateTagRuleIdPatternList({
          ruleIds: [],
          syntax: [],
        })
        this.#performDefaultSearch()
        this.#resetCurrentSearchInputControl()
      })
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #performDefaultSearch(): void {
    this.searchFacade.search({
      searchExpression: this.completeSyntax(),
      isResetBaseGuid: true,
    })
  }

  #resetCurrentSearchInputControl(): void {
    this.searchFacade.resetSearchInputControls()
  }

  #resetBreadcrumbState(): void {
    this.breadcrumbFacade.resetBreadcrumbCurrentStates()
    this.breadcrumbService.setConditionChecked(GroupStackType.VIEW_SEARCH, true)
  }

  #setDialogInput(instance: ConfirmationDialogComponent): void {
    instance.title = 'Clear Breadcrumbs'
    instance.message =
      'This action will clear all breadcrumbs and reset the search to its default state. Are you sure you want to proceed?'
  }
}
