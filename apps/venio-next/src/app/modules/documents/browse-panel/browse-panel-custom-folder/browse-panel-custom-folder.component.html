<div class="t-absolute t-w-full t-h-full">
  <kendo-treelist
    [hideHeader]="true"
    [initiallyExpanded]="false"
    [(expandedKeys)]="expandedFolderIds"
    [kendoTreeListFlatBinding]="treeData"
    [trackBy]="customFolderTrackByFn"
    idField="id"
    parentIdField="parentId"
    class="v-custom-folder-container !t-border-0 t-w-[calc(100%_-_1px)] t-h-full t-bg-[#FAFAFA]"
    kendoTreeListExpandable
    [rowHeight]="36"
    [pageSize]="50"
    scrollable="virtual"
    [autoSize]="true"
    [columnMenu]="false"
    kendoTreeListSelectable
    (selectionChange)="onSelectionChange($event)"
    [resizable]="true"
    (cellClick)="onCellClick($event)"
    [(selectedItems)]="selectedFolderItems"
    itemKey="id"
    [navigable]="false">
    <ng-template kendoTreeListToolbarTemplate>
      <kendo-textbox
        class="!t-border-[#ccc] !t-w-full !t-flex"
        placeholder="Filter"
        (valueChange)="onFilter($event)">
      </kendo-textbox>
    </ng-template>
    <kendo-treelist-column
      [expandable]="true"
      [filterable]="true"
      field="folderName"
      title="folderName"
      class="!t-border-b-0 !t-py-[0.65rem] !t-flex t-cursor-pointer t-grow t-justify-between v-custom-folder-container-cell">
      <ng-template
        kendoTreeListCellTemplate
        let-dataItem
        let-isExpanded="isExpanded">
        <div
          class="t-flex t-gap-0.5 t-grow t-items-center t-justify-between t-w-full t-relative t-self-stretch">
          <div class="t-flex t-gap-1 t-items-center">
            <span
              venioSvgLoader
              height="1rem"
              width="1rem"
              [svgUrl]="
                isExpanded
                  ? 'assets/svg/icon-folder-fclv-open.svg'
                  : 'assets/svg/icon-folder-fclv.svg'
              ">
              <kendo-loader size="small"></kendo-loader>
            </span>
            <span class="t-flex-auto t-truncate t-max-w-[150px]">
              <span
                kendoTooltip
                [title]="
                  dataItem.fileCount > 0
                    ? dataItem.folderName + ' (' + dataItem.fileCount + ')'
                    : dataItem.folderName
                "
                >{{ dataItem.folderName }}
                <ng-container *ngIf="dataItem.fileCount > 0">
                  &nbsp;({{ dataItem.fileCount }})
                </ng-container>
              </span>
            </span>
          </div>
          @if((dataItem.isDynamicFolder && dataItem.folderId > 1) ||
          (!dataItem.isDynamicFolder && dataItem.folderId > 0)) {
          <ng-container
            *ngTemplateOutlet="
              folderActions;
              context: { dataItem }
            "></ng-container>
          }
          <div
            *ngIf="dataItem.parentId === null"
            class="t-flex t-gap-[0.5rem] t-right-2"
            kendoTooltip>
            <button
              #expandBtn
              kendoButton
              class="t-p-0 t-cursor-pointer"
              fillMode="clear"
              title="Expand All"
              (click)="expandAllChildNodes(dataItem)">
              <span
                venioSvgLoader
                color="#979797"
                hoverColor="#FFBB12"
                applyEffectsTo="fill"
                [parentElement]="expandBtn.element"
                svgUrl="assets/svg/plus-circle-expand-icon-svg.svg"
                height="1rem"
                width="1rem">
                <kendo-loader size="small"></kendo-loader>
              </span>
            </button>
            <button
              kendoButton
              #collapseBtn
              class="t-p-0 t-cursor-pointer"
              fillMode="clear"
              title="Collapse All"
              (click)="collapseAllChildNodes(dataItem)">
              <span
                venioSvgLoader
                color="#979797"
                hoverColor="#FFBB12"
                applyEffectsTo="fill"
                [parentElement]="collapseBtn.element"
                svgUrl="assets/svg/minus-circle-collapse-svg.svg"
                height="1rem"
                width="1rem">
                <kendo-loader size="small"></kendo-loader>
              </span>
            </button>
          </div>
        </div>
      </ng-template>
    </kendo-treelist-column>
  </kendo-treelist>

  <ng-template #folderActions let-dataItem="dataItem">
    <div
      (click)="$event.stopPropagation()"
      [ngClass]="{
        'v-custom-folder-cell-actions': !(
          selectedAction.get(dataItem.folderId) &&
          selectedFolder?.folderId === dataItem.folderId
        )
      }"
      class="t-flex t-items-center t-gap-2"
      kendoTooltip
      *ngIf="dataItem.folderId > 0">
      @for (icon of folderActionIcons(); track icon.actionType) { @if
      (shouldAllowRenderAction(dataItem, icon)){
      <button
        kendoButton
        #folderAction
        *venioCombinedRights="
          dataItem.isDynamicFolder && dataItem.isGlobal
            ? userRights.ALLOW_TO_MANAGE_GLOBAL_DYNAMIC_FOLDER
            : icon.allowedPermission
        "
        [ngClass]="getButtonClass(icon, dataItem)"
        (click)="tagActionClicked(icon.actionType, dataItem)"
        [disabled]="isButtonDisabled(icon, dataItem)"
        fillMode="clear"
        [title]="icon.actionType | titlecase"
        size="none">
        <ng-container
          *ngIf="
            icon.isLoading &&
              selectedAction.get(dataItem.folderId) === icon.actionType &&
              selectedFolder?.folderId === dataItem.folderId;
            else iconElement
          ">
          <kendo-loader
            class="t-mt-[-0.2rem]"
            size="small"
            type="infinite-spinner"></kendo-loader>
        </ng-container>

        <ng-template #iconElement>
          <span
            [parentElement]="folderAction.element"
            venioSvgLoader
            [hoverColor]="getIconHoverColor(icon.actionType)"
            color="#979797"
            [svgUrl]="icon.iconPath"
            height="0.9rem"
            width="1rem">
            <kendo-loader size="small"></kendo-loader>
          </span>
        </ng-template>
      </button>
      } }
    </div>
  </ng-template>
</div>
