import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  On<PERSON>estroy,
  OnInit,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  FlatBindingDirective,
  TreeListComponent,
  TreeListModule,
} from '@progress/kendo-angular-treelist'
import {
  FolderFacade,
  FolderModel,
  ReviewParamService,
  SearchFacade,
  FolderTabTreeState,
} from '@venio/data-access/review'
import { Subject, filter, map, take, takeUntil } from 'rxjs'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import { FolderTabType } from '@venio/golden-layout'
import {
  ConditionElement,
  ConditionType,
  GroupStackType,
} from '@venio/shared/models/interfaces'
import { UuidGenerator } from '@venio/util/uuid'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-browse-panel-auto-folder',
  standalone: true,
  imports: [
    CommonModule,
    TreeListModule,
    SvgLoaderDirective,
    InputsModule,
    LoaderModule,
    ButtonsModule,
  ],
  templateUrl: './browse-panel-auto-folder.component.html',
  styleUrls: ['./browse-panel-auto-folder.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BrowsePanelAutoFolderComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  private unsubscribed$ = new Subject<void>()

  public treeData: FolderModel[] = []

  @ViewChild(FlatBindingDirective)
  public dataBinding: FlatBindingDirective

  @ViewChild(TreeListComponent)
  public autoFolderTreeList: TreeListComponent

  private breadcrumbFacade = inject(BreadcrumbFacade)

  public defaultExpandedFolderIds: number[]

  public selected: { itemKey: number }[] = []

  public rowIndex: number

  constructor(
    private folderFacade: FolderFacade,
    private reviewParamService: ReviewParamService,
    private cdr: ChangeDetectorRef,
    private searchFacade: SearchFacade
  ) {}

  public ngOnInit(): void {
    this.fetchAutoFolders()
    this.initSlices()
    this.#selectStaticFolderSearchScope()
    this.#selectSelectedFolderTreeState()
  }

  private fetchAutoFolders(): void {
    this.reviewParamService.projectId
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((projectId) => {
        this.folderFacade.fetchAutoFolders(projectId)
      })
  }

  private initSlices(): void {
    this.folderFacade.getAutoFolders$
      .pipe(
        map((folders) =>
          folders.map((folder) => ({
            ...folder,
            parentFolderId:
              folder.parentFolderId === -1 &&
              (folder.isSystemFolder || folder.isRelativePath)
                ? null
                : folder.parentFolderId,
            folderName:
              folder.parentFolderId === -1 &&
              (folder.isSystemFolder || folder.isRelativePath)
                ? 'By Relative File Path'
                : folder.folderName,
          }))
        ),
        filter((res) => !!res),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((transformedFolders: FolderModel[]) => {
        this.cdr.markForCheck()

        const isCustomFieldFolderExist =
          transformedFolders?.filter((m) => m.customFieldInfoId > 0)?.length > 0

        if (isCustomFieldFolderExist) {
          //add root folder 'By Custom Field' for all the auto folder generated for custom fields to appear under this folder
          const rootFolderForCustomField: FolderModel = {
            folderId: -1,
            folderName: 'By Custom Field',
            parentFolderId: null,
            accessType: '',
            folderLineage: '',
            folderIdlineage: '',
            fileCount: 0,
            isSystemFolder: false,
            isRelativePath: false,
            customFieldInfoId: 0,
            folderProjectGroupAssociations: [],
          }

          transformedFolders.push(rootFolderForCustomField)
        }
        this.treeData = transformedFolders

        if (!this.defaultExpandedFolderIds?.length)
          this.defaultExpandedFolderIds = this.treeData
            .filter((f) => f.parentFolderId === null)
            .map((f) => f.folderId)
      })
  }

  public ngAfterViewInit(): void {
    this.#scrollToSelectedNode()
  }

  /*when selection change, search documents in the selected folder and load in the search result
   */
  public async onSelectionChange(e): Promise<void> {
    const selectedFolder = e.items[0].dataItem

    if (!selectedFolder.parentFolderId) {
      // this.searchFacade.setDynamicFolderSearchScope(null)
      // this.searchFacade.setStaticFolderSearchScope(null)
      return
    }

    if (e.action === 'select') {
      // this.searchFacade.setStaticFolderSearchScope(selectedFolder)
      // this.searchFacade.setDynamicFolderSearchScope(null)
      if (selectedFolder.parentFolderId) {
        const payload = {
          id: UuidGenerator.uuid,
          groupStackType: GroupStackType.AUTO_FOLDER,
          checked: true,
          conditionType: ConditionType.Group,
          conditions: [
            {
              conditionSyntax: `FOLDERS("${selectedFolder.folderLineage}")`,
            },
          ] as ConditionElement[],
        }
        this.breadcrumbFacade.resetBreadcrumbCurrentStates()
        this.breadcrumbFacade.storeBreadcrumbs([payload])
        this.breadcrumbFacade.selectCompleteBreadcrumbSyntax$
          .pipe(take(1))
          .subscribe((searchExpression) => {
            this.searchFacade.search({
              searchExpression: searchExpression,
              isResetBaseGuid: true,
            })
          })
      }
    }
  }

  /*
  Filter the tree list based on the input value
  */
  public onFilter(value: string): void {
    this.dataBinding.filter = {
      logic: 'or',
      filters: [
        {
          field: 'folderName',
          operator: 'contains',
          value: value,
        },
      ],
    }
    this.dataBinding.rebind()
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }

  // Helper method to get all descendant folder names of a specific folder
  private getDescendantFolderNames(folderId: number): number[] {
    const descendants: number[] = []
    const stack = [folderId]

    while (stack.length > 0) {
      const currentId = stack.pop()
      const children = this.treeData.filter(
        (folder) => folder.parentFolderId === currentId
      )
      children.forEach((child) => {
        descendants.push(child.folderId)
        stack.push(child.folderId)
      })
    }

    return descendants
  }

  // Method to expand a node and all its descendants
  public expandAllChildNodes(dataItem: FolderModel): void {
    const descendantFolderNames = this.getDescendantFolderNames(
      dataItem.folderId
    )
    this.defaultExpandedFolderIds = [
      ...new Set([
        ...this.defaultExpandedFolderIds,
        dataItem.folderId,
        ...descendantFolderNames,
      ]),
    ]
    this.dataBinding.rebind()
  }

  // Method to collapse a node and all its descendants
  public collapseAllChildNodes(dataItem: FolderModel): void {
    const descendantFolderNames = new Set(
      this.getDescendantFolderNames(dataItem.folderId)
    )
    this.defaultExpandedFolderIds = this.defaultExpandedFolderIds.filter(
      (id) => id !== dataItem.folderId && !descendantFolderNames.has(id)
    )
    this.dataBinding.rebind()
  }

  /**
   * Selects and handles changes in the static folder search scope.
   * @returns {void}
   */
  #selectStaticFolderSearchScope(): void {
    this.searchFacade.getStaticFolderSearchScope$
      .pipe(
        filter((folder) => !!folder),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((folder) => {
        this.selected = [{ itemKey: folder.folderId }]
      })
  }

  #selectSelectedFolderTreeState(): void {
    this.folderFacade.getSelectedFolderTreeState$
      .pipe(
        filter(
          (selectedFolderTreeState) =>
            !!selectedFolderTreeState &&
            selectedFolderTreeState.actionType === FolderTabType.AUTO
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((selectedFolderTreeState: FolderTabTreeState) => {
        this.cdr.markForCheck()
        this.defaultExpandedFolderIds = selectedFolderTreeState.expandedIds
        this.rowIndex = selectedFolderTreeState.rowIndex
      })
  }

  public onCellClick(e): void {
    this.folderFacade.setSelectedFolderTreeState({
      actionType: FolderTabType.AUTO,
      expandedIds: this.defaultExpandedFolderIds,
      rowIndex: e.rowIndex,
    })
  }

  #scrollToSelectedNode(): void {
    this.autoFolderTreeList?.scrollTo({ row: this.rowIndex })
  }
}
