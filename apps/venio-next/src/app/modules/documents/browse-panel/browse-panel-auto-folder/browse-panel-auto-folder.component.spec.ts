import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BrowsePanelAutoFolderComponent } from './browse-panel-auto-folder.component'
import {
  FieldFacade,
  FolderFacade,
  FolderTabTreeState,
  SearchFacade,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import { BehaviorSubject, of } from 'rxjs'
import { provideMockStore } from '@ngrx/store/testing'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import { By } from '@angular/platform-browser'
import { FolderTabType } from '@venio/golden-layout'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('BrowsePanelAutoFolderComponent', () => {
  let component: BrowsePanelAutoFolderComponent
  let fixture: ComponentFixture<BrowsePanelAutoFolderComponent>
  let mockSearchFacade: SearchFacade

  const mockFolderTabTreeState$ = new BehaviorSubject<FolderTabTreeState>({
    actionType: FolderTabType.AUTO,
    expandedIds: [1],
    rowIndex: 0,
  })

  const mockFolders = [
    {
      folderId: 1,
      folderName: 'Folder 1',
      parentFolderId: null,
      accessType: 'PUBLIC',
      folderLineage: '\\System Folder',
      folderIdlineage: '\\1',
      fileCount: 0,
      isSystemFolder: true,
      isRelativePath: true,
      customFieldInfoId: null,
      folderProjectGroupAssociations: [],
      groupAccess: null,
      userGroupPermissionFolder: 'READ_ONLY',
      parentFileCount: 0,
      description: '',
      folderOrder: 0,
      belowFolderId: 0,
    },
    {
      folderId: 2,
      folderName: 'Folder 2',
      parentFolderId: 1,
      accessType: 'PUBLIC',
      folderLineage: '\\System Folder\\Folder 2',
      folderIdlineage: '\\1\\2',
      fileCount: 5,
      isSystemFolder: true,
      isRelativePath: true,
      customFieldInfoId: null,
      folderProjectGroupAssociations: [],
      groupAccess: null,
      userGroupPermissionFolder: 'READ_ONLY',
      parentFileCount: 0,
      description: '',
      folderOrder: 0,
      belowFolderId: 0,
    },
  ]

  const staticFolderSearchScope$ = new BehaviorSubject<any>(mockFolders[1])

  beforeEach(async () => {
    const mockFolderFacade = {
      getAutoFolders$: of(mockFolders), // Provide appropriate mock data
      getSelectedFolderTreeState$: mockFolderTabTreeState$.asObservable(),
    }

    const mockTotalHitCount$ = of(5) // Mock total hit count to match fileCount

    mockSearchFacade = {
      getTotalHitCount$: mockTotalHitCount$, // Mock the observable
      getStaticFolderSearchScope$: staticFolderSearchScope$.asObservable(),
    } as any

    await TestBed.configureTestingModule({
      imports: [BrowsePanelAutoFolderComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: BreadcrumbFacade,
          useValue: jest.fn(),
        },
        { provide: SearchFacade, useValue: mockSearchFacade }, // Provide the mock SearchFacade
        FieldFacade,
        { provide: FolderFacade, useValue: mockFolderFacade },
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '2' }),
          },
        },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents()

    fixture = TestBed.createComponent(BrowsePanelAutoFolderComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should display folder name with file count when fileCount > 0', () => {
    component.ngOnInit() // Trigger the component initialization
    fixture.detectChanges() // Trigger the change detection to apply the data

    // Select all elements that match the selector
    const folderCells = fixture.debugElement.queryAll(By.css('.t-flex'))

    // Find the correct cell containing "Folder 2 (5)"
    const matchingCell = folderCells.find((cell) => {
      const textContent = cell.nativeElement.textContent
        .replace(/\s+/g, ' ')
        .trim()
      return textContent.includes('Folder 2 (5)')
    })

    // Ensure the cell was found
    expect(matchingCell).toBeTruthy()

    // If found, validate the content
    const cellContent = matchingCell?.nativeElement.textContent
      .replace(/\s+/g, ' ')
      .trim()
    expect(cellContent).toContain('Folder 2 (5)')
  })

  it('should display folder name only when fileCount is 0', () => {
    component.ngOnInit() // Trigger the component initialization
    fixture.detectChanges() // Trigger the change detection to apply the data

    // Adjust the selector to match the actual DOM structure
    const folderCell = fixture.debugElement.query(By.css('.t-flex'))

    expect(folderCell).not.toBeNull() // Ensure the cell is found

    let cellContent = folderCell.nativeElement.textContent

    // Normalize whitespace
    cellContent = cellContent.replace(/\s+/g, ' ').trim()

    expect(cellContent).toMatch(/^Folder 1$/)
  })

  it('should match fileCount with totalHitCount', () => {
    const expectedFileCount = mockFolders[1].fileCount
    let actualTotalHitCount: number

    // Subscribe to the total hit count
    mockSearchFacade.getTotalHitCount$.subscribe((count) => {
      actualTotalHitCount = count
    })

    // Trigger component initialization
    component.ngOnInit()
    fixture.detectChanges()

    // Validate that fileCount matches totalHitCount
    expect(actualTotalHitCount).toEqual(expectedFileCount)
  })
})
