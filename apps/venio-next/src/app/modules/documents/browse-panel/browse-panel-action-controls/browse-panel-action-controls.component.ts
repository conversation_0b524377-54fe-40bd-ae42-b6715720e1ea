import {
  ChangeDetectionStrategy,
  Component,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core'
import { CommonModule, NgOptimizedImage } from '@angular/common'
import { IconsModule } from '@progress/kendo-angular-icons'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { FolderTabType, GoldenLayoutFacade } from '@venio/golden-layout'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { Subject, takeUntil } from 'rxjs'

@Component({
  selector: 'venio-browser-panel-action-controls',
  standalone: true,
  imports: [
    CommonModule,
    NgOptimizedImage,
    IconsModule,
    ButtonsModule,
    SvgLoaderDirective,
    TooltipsModule,
    IndicatorsModule,
  ],
  templateUrl: './browse-panel-action-controls.component.html',
  styleUrls: ['./browse-panel-action-controls.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BrowsePanelActionControlsComponent implements OnInit, OnDestroy {
  public selectedAction = FolderTabType.SYSTEM

  private unsubscribed$ = new Subject<void>()

  constructor(private goldenLayoutFacade: GoldenLayoutFacade) {}

  public ngOnInit(): void {
    this.#selectFolderTabType()
  }

  #selectFolderTabType(): void {
    this.goldenLayoutFacade.getFolderTabType$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((folderTab) => {
        this.selectedAction = folderTab
      })
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }

  public svgIconForControls = [
    {
      actionType: FolderTabType.SYSTEM,
      iconPath: 'assets/svg/icon-document-folder.svg',
      applyEffectTo: 'fill' as any,
    },
    {
      actionType: FolderTabType.AUTO,
      iconPath: 'assets/svg/icon-tree-view.svg',
      applyEffectTo: 'fill' as any,
    },
    {
      actionType: FolderTabType.CUSTOM,
      iconPath: 'assets/svg/icon-save-search.svg',
      applyEffectTo: 'fill' as any,
    },
  ]

  public browseActionClicked(folderTabType: FolderTabType): void {
    this.selectedAction = folderTabType
    this.goldenLayoutFacade.storeFolderTabType({
      folderTabType: folderTabType,
    })
  }

  protected readonly commonActionTypes = CommonActionTypes
}
