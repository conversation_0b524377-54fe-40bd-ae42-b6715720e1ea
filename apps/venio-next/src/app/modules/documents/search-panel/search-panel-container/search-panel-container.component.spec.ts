import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SearchPanelContainerComponent } from './search-panel-container.component'
import { GoldenLayoutContainerInjectionToken } from '@venio/golden-layout'
import { ComponentContainer } from 'golden-layout'
import {
  FieldFacade,
  SearchFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { ActivatedRoute } from '@angular/router'
import { NotificationService } from '@progress/kendo-angular-notification'
import { provideAnimations } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('SearchPanelContainerComponent', () => {
  let component: SearchPanelContainerComponent
  let fixture: ComponentFixture<SearchPanelContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SearchPanelContainerComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: GoldenLayoutContainerInjectionToken,
          useValue: ComponentContainer,
        },
        SearchFacade,
        FieldFacade,
        NotificationService,
        StartupsFacade,
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 2,
              },
            },
          },
        },
        provideAnimations(),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(SearchPanelContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
