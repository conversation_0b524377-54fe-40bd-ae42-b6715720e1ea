import { ChangeDetectionStrategy, Component, Optional } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { SearchScope } from '@venio/data-access/review'

@Component({
  selector: 'venio-search-history-confirmation-dialog',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    LabelModule,
    FormsModule,
    InputsModule,
  ],
  templateUrl: './search-history-confirmation-dialog.component.html',
  styleUrl: './search-history-confirmation-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchHistoryConfirmationDialogComponent {
  public dialogTitle = 'Search From History'

  public selectedScope = 'SAME_ORIGINAL'

  constructor(
    @Optional()
    public dialogRef: DialogRef
  ) {}

  public searchAction(status: boolean): void {
    if (status)
      this.dialogRef.close({
        status: status,
        scope:
          this.selectedScope === 'SAME_ORIGINAL'
            ? SearchScope.SAME_ORIGINAL
            : SearchScope.CURRENT,
      })
    else this.dialogRef.close({ status: status, scope: null })
  }
}
