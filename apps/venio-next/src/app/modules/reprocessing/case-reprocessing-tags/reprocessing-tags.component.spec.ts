import { TestBed, ComponentFixture } from '@angular/core/testing'
import { ReprocessingTagsComponent } from './reprocessing-tags.component'
import {
  ReprocessingFacade,
  ReprocessingFormService,
} from '@venio/data-access/common'
import { of } from 'rxjs'
import { FormGroup, FormControl } from '@angular/forms'

describe('ReprocessingTagsComponent', () => {
  let component: ReprocessingTagsComponent
  let fixture: ComponentFixture<ReprocessingTagsComponent>
  let mockReprocessingFacade: jest.Mocked<ReprocessingFacade>
  let mockReprocessingFormService: jest.Mocked<ReprocessingFormService>

  beforeEach(async () => {
    mockReprocessingFacade = {
      getTags$: of([]),
    } as unknown as jest.Mocked<ReprocessingFacade>

    mockReprocessingFormService = {
      reprocessForm: new FormGroup({
        reprocessOption: new FormControl('ALL_FILES'),
        selectedTagIds: new FormControl([]),
        recomputeData: new FormControl(false),
        loadDocument: new FormControl(false),
      }),
    } as unknown as jest.Mocked<ReprocessingFormService>

    await TestBed.configureTestingModule({
      imports: [ReprocessingTagsComponent],
      providers: [
        { provide: ReprocessingFacade, useValue: mockReprocessingFacade },
        {
          provide: ReprocessingFormService,
          useValue: mockReprocessingFormService,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReprocessingTagsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create the component', () => {
    expect(component).toBeTruthy()
  })
})
