import {
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
  input,
  output,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { distinctUntilChanged, filter, Observable, of, switchMap } from 'rxjs'
import { FormGroup } from '@angular/forms'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { DynamicHeightDirective } from '@venio/feature/shared/directives'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { xIcon } from '@progress/kendo-svg-icons'
import {
  CheckableSettings,
  CheckMode,
  TreeItemLookup,
  TreeViewModule,
} from '@progress/kendo-angular-treeview'
import {
  ReprocessingFacade,
  ReprocessingFormService,
} from '@venio/data-access/common'
import { Custodian, CustodianTree } from '@venio/shared/models/interfaces'
import { SkeletonModule } from '@progress/kendo-angular-indicators'
import { cloneDeep } from 'lodash'
import { toObservable, toSignal } from '@angular/core/rxjs-interop'

@Component({
  selector: 'venio-case-reprocessing-custodian-dialog',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    InputsModule,
    ButtonsModule,
    DynamicHeightDirective,
    TreeViewModule,
    SkeletonModule,
  ],
  templateUrl: './case-reprocessing-custodian-dialog.component.html',
  styleUrl: './case-reprocessing-custodian-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseReprocessingCustodianDialogComponent {
  private readonly reprocessingFacade = inject(ReprocessingFacade)

  private readonly reprocessingFormService = inject(ReprocessingFormService)

  public projectId = input.required<number>()

  public isFallbackIngestionEngine = input.required<boolean>()

  public isOpendAfterReprocess = input<boolean>()

  public isLoading = signal<boolean>(true)

  public closeCustodianDialog = output<boolean>()

  public treeViewData: any[] = []

  public selectedMedias: number[] = []

  public initialSelectedMedias: number[] = []

  public checkedKeys: any[] = []

  public expandedKeys: any[] = []

  public filterTerm = ''

  public checkMode: CheckMode = 'multiple'

  public get selectedMediaForm(): FormGroup {
    return this.reprocessingFormService.reprocessForm.get(
      'selectedMedias'
    ) as FormGroup
  }

  public get checkableSettings(): CheckableSettings {
    return {
      checkChildren: true,
      checkDisabledChildren: false,
      checkParents: true,
      enabled: true,
      mode: this.checkMode,
      checkOnClick: true,
      uncheckCollapsedChildren: false,
    }
  }

  private custodians$ = toSignal(
    toObservable(this.isFallbackIngestionEngine).pipe(
      filter(
        (isFallbackIngestionEngine) => isFallbackIngestionEngine !== undefined
      ),
      distinctUntilChanged(),
      switchMap(() =>
        this.reprocessingFacade.getCustodians$.pipe(
          filter((custodians) => !!custodians && custodians?.length > 0)
        )
      )
    )
  )

  constructor() {
    effect(
      () => {
        const custodians = this.custodians$()
        if (!custodians) return

        const _custodians = cloneDeep(custodians)

        if (!this.isFallbackIngestionEngine()) this.checkMode = 'single'
        else this.checkMode = 'multiple'

        this.treeViewData = this.mapToTreeViewData(_custodians)
        this.expandedKeys = this.getAllNodeIds(this.treeViewData)

        if (this.selectedMediaForm.value?.length > 0) {
          //after reprocess is queued, need to remove the queued media from the selected media list
          const selectedMedias = this.selectedMediaForm.value
          this.selectedMedias = this.initialSelectedMedias =
            selectedMedias?.filter((mediaId: number) =>
              _custodians.some((cus) => cus.mediaId === mediaId)
            )

          this.selectedMediaForm.setValue(this.selectedMedias ?? [])
          this.checkedKeys = this.getAllNodeIds(
            this.treeViewData,
            this.selectedMedias
          )
        } else {
          if (this.isFallbackIngestionEngine()) {
            this.checkedKeys = this.expandedKeys
            this.selectedMedias = this.getAllMediaIds(_custodians)
          } else {
            this.checkedKeys = []
            this.selectedMedias = []
          }
        }
        this.isLoading.set(false)
      },
      { allowSignalWrites: true }
    )
  }

  private getAllMediaIds(custodians: Custodian[]): number[] {
    const mediaIds: number[] = []
    custodians.forEach((custodian) => {
      mediaIds.push(custodian.mediaId)
    })
    return mediaIds
  }

  public mapToTreeViewData(custodians: Custodian[]): CustodianTree[] {
    let currentId = 0
    const groupedData: CustodianTree[] = Object.values(
      custodians
        .sort((a, b) => a.custodianId - b.custodianId || a.mediaId - b.mediaId)
        .reduce<Record<number, CustodianTree>>((acc, custodian) => {
          const custodianId = custodian.custodianId
          const custodianName = custodian.custodianName

          if (!acc[custodianId]) {
            acc[custodianId] = {
              Name: `Custodian Name - ${custodianName}`,
              id: currentId++,
              custodianId,
              items: [],
            }
          }

          const childId = acc[custodianId].items.length
          acc[custodianId].items.push({
            Name: custodian.mediaName,
            id: childId,
            mediaId: custodian.mediaId,
          })
          return acc
        }, {})
    )
    return groupedData
  }

  private getAllNodeIds(
    nodes: any[],
    selectedMedias: number[] = null
  ): string[] {
    let keys: string[] = []
    for (const node of nodes) {
      if (node.id >= 0) {
        if (selectedMedias === null) {
          keys.push(node.id.toString())
        } else {
          if (
            selectedMedias?.includes(node.mediaId) ||
            node.items?.some((item) => selectedMedias?.includes(item.mediaId))
          ) {
            keys.push(node.id.toString())
          }
        }
      }
      if (node.items) {
        keys = keys.concat(
          this.getAllNodeIds(node.items, selectedMedias).map(
            (id) => node.id + '_' + id
          )
        )
      }
    }
    return keys
  }

  public onCheckedChanged(item: TreeItemLookup): void {
    if (this.checkMode === 'single') {
      this.selectedMedias = []
    }

    if (Number(item.item.index) >= 0) {
      item.children.forEach((child) => {
        const selectedMedia = child.item.dataItem.mediaId
        if (this.isItemChecked(item.item.index) === 'checked') {
          this.selectedMedias = [...this.selectedMedias, selectedMedia]
        } else {
          this.selectedMedias = this.selectedMedias.filter(
            (media) => media !== selectedMedia
          )
        }
      })
    } else {
      const selectedMedia = item.item.dataItem.mediaId
      if (this.isItemChecked(item.item.index) === 'checked') {
        this.selectedMedias = [...this.selectedMedias, selectedMedia]
      } else {
        this.selectedMedias = this.selectedMedias.filter(
          (media) => media !== selectedMedia
        )
      }
    }
  }

  public isItemChecked(index: string): string {
    return this.checkedKeys.indexOf(index) > -1 ? 'checked' : 'none'
  }

  public onSave(): void {
    this.selectedMediaForm.setValue(this.selectedMedias)
    this.closeCustodianDialog.emit(
      !this.isSelectionSame(this.initialSelectedMedias, this.selectedMedias) ||
        this.isOpendAfterReprocess()
    )
  }

  public onCancel(): void {
    if (this.isOpendAfterReprocess())
      this.selectedMediaForm.setValue(this.initialSelectedMedias)
    this.closeCustodianDialog.emit(this.isOpendAfterReprocess())
  }

  public isSelectionSame(current: number[], selected: number[]): boolean {
    if (current.length !== selected.length) return false
    return current.every((value, index) => value === selected[index])
  }

  public onRefresh(): void {
    this.isLoading.set(true)
    this.filterTerm = ''
    this.treeViewData = []
    this.reprocessingFacade.fetchCustodianInfo(this.projectId())
  }

  public icons = {
    closeIcon: xIcon,
  }

  public hasCheckbox = (_: any, index: string): boolean => {
    // removes the checkboxes of all top level items if the legacy project
    // as feature reprocess in multiple media is not available
    if (!this.isFallbackIngestionEngine() && index.indexOf('_') === -1) {
      return false
    }
    return true
  }

  public hasChildren = (item: any): boolean => {
    return item.items && item.items.length > 0
  }

  public getChildren(node: any): Observable<any[]> {
    return of(node.items)
  }
}
