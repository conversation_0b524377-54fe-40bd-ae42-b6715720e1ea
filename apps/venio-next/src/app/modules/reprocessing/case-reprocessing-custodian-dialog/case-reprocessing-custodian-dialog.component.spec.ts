import { TestBed, ComponentFixture } from '@angular/core/testing'
import { CaseReprocessingCustodianDialogComponent } from './case-reprocessing-custodian-dialog.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { of } from 'rxjs'
import {
  ReprocessingFacade,
  ReprocessingFormService,
} from '@venio/data-access/common'
import { ChangeDetectorRef } from '@angular/core'
import { FormGroup, FormControl } from '@angular/forms'

describe('CaseReprocessingCustodianDialogComponent', () => {
  let component: CaseReprocessingCustodianDialogComponent
  let fixture: ComponentFixture<CaseReprocessingCustodianDialogComponent>
  let mockReprocessingFacade: jest.Mocked<ReprocessingFacade>
  let mockReprocessingFormService: jest.Mocked<ReprocessingFormService>
  let mockChangeDetectorRef: jest.Mocked<ChangeDetectorRef>

  beforeEach(async () => {
    mockReprocessingFacade = {
      getCustodians$: of([
        {
          custodianName: 'Test Custodian',
          custodianId: 1,
          custodainMedias: [
            { mediaId: 101, mediaName: 'Media 1', mediaStatus: 'processed' },
          ],
        },
      ]),
      fetchCustodianInfo: jest.fn(),
    } as unknown as jest.Mocked<ReprocessingFacade>

    mockReprocessingFormService = {
      reprocessForm: new FormGroup({
        selectedMedias: new FormControl([]),
      }),
    } as unknown as jest.Mocked<ReprocessingFormService>

    mockChangeDetectorRef = {
      detectChanges: jest.fn(),
    } as unknown as jest.Mocked<ChangeDetectorRef>

    await TestBed.configureTestingModule({
      imports: [CaseReprocessingCustodianDialogComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: ReprocessingFacade, useValue: mockReprocessingFacade },
        {
          provide: ReprocessingFormService,
          useValue: mockReprocessingFormService,
        },
        { provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(CaseReprocessingCustodianDialogComponent)
    component = fixture.componentInstance
    fixture.componentRef.setInput('projectId', 1)
    fixture.componentRef.setInput('isFallbackIngestionEngine', true)

    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
