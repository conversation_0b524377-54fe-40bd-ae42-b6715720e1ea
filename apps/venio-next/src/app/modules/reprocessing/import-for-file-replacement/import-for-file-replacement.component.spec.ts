import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ImportForFileReplacementComponent } from './import-for-file-replacement.component'
import { ControlSettingService } from '@venio/data-access/control-settings'
import { VenioNotificationService } from '@venio/feature/notification'
import { DialogRef } from '@progress/kendo-angular-dialog'

describe('ImportForFileReplacementComponent', () => {
  let component: ImportForFileReplacementComponent
  let fixture: ComponentFixture<ImportForFileReplacementComponent>
  let mockControlSettingService: any
  let mockToastService: any
  let mockDialogRef: any

  beforeEach(async () => {
    // Mock dependencies
    mockControlSettingService = {
      getControlSetting: {
        UPLOAD_CHUNK_SIZE: 15,
      },
    }

    mockToastService = {
      showError: jest.fn(),
      showSuccess: jest.fn(),
    }

    mockDialogRef = {
      close: jest.fn(),
    }

    await TestBed.configureTestingModule({
      imports: [ImportForFileReplacementComponent],
      providers: [
        { provide: ControlSettingService, useValue: mockControlSettingService },
        { provide: VenioNotificationService, useValue: mockToastService },
        { provide: DialogRef, useValue: mockDialogRef },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ImportForFileReplacementComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
