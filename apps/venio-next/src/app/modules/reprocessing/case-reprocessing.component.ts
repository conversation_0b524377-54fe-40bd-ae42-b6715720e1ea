import {
  ChangeDetectionStrategy,
  Component,
  effect,
  input,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core'
import { CommonModule, DatePipe } from '@angular/common'
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { GridModule } from '@progress/kendo-angular-grid'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { UiPaginationModule } from '@venio/ui/pagination'
import {
  LoaderModule,
  SkeletonComponent,
} from '@progress/kendo-angular-indicators'
import {
  DialogsModule,
  DialogAnimation,
  DialogService,
  DialogRef,
  DialogCloseResult,
} from '@progress/kendo-angular-dialog'
import { CaseReprocessingCustodianDialogComponent } from './case-reprocessing-custodian-dialog/case-reprocessing-custodian-dialog.component'
import { CaseReprocessingUpdateComponent } from './case-reprocessing-update/case-reprocessing-update.component'
import { ReprocessingDocumentComponent } from './case-reprocessing-document/reprocessing-document.component'
import { IconsModule } from '@progress/kendo-angular-icons'
import { xIcon } from '@progress/kendo-svg-icons'
import { ActivatedRoute } from '@angular/router'
import {
  ReprocessingFacade,
  ReprocessingFormService,
} from '@venio/data-access/common'
import { Subject, takeUntil } from 'rxjs'
import {
  FileReprocessModel,
  ReprocessData,
  ReprocessOption,
  ResponseModel,
  SettingsType,
} from '@venio/shared/models/interfaces'
import { ReprocessingExceptionComponent } from './case-reprocessing-exception/reprocessing-exception.component'
import { ReprocessingTagsComponent } from './case-reprocessing-tags/reprocessing-tags.component'
import { cloneDeep } from 'lodash'
import { ReprocessingStatusComponent } from './reprocessing-status/reprocessing-status.component'

@Component({
  selector: 'venio-case-reprocessing',
  standalone: true,
  imports: [
    CommonModule,
    InputsModule,
    LabelModule,
    FormsModule,
    GridModule,
    TooltipModule,
    ButtonsModule,
    UiPaginationModule,
    LoaderModule,
    DialogsModule,
    IconsModule,
    ReactiveFormsModule,
    CaseReprocessingCustodianDialogComponent,
    CaseReprocessingUpdateComponent,
    ReprocessingDocumentComponent,
    ReprocessingExceptionComponent,
    ReprocessingTagsComponent,
    SkeletonComponent,
    ReprocessingStatusComponent,
  ],
  providers: [DatePipe],
  templateUrl: './case-reprocessing.component.html',
  styleUrl: './case-reprocessing.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseReprocessingComponent implements OnInit, OnDestroy {
  private unsubscribe$ = new Subject<void>()

  public projectId = input.required<number>()

  public isFallbackIngestionEngine = input.required<boolean>()

  public isServiceTypeCase = input.required<boolean>()

  public isOpenFromEmail = input<boolean>()

  public settingId = input<number>()

  public token = input<string>()

  public isOpendAfterReprocess = signal<boolean>(false)

  public reprocessingType = 1

  public isAllFiles = signal<boolean>(false)

  public animation: boolean | DialogAnimation = {
    type: 'slide',
    direction: 'left',
    duration: 300,
  }

  //for custodian/media/bulksetting dialog
  public isOverlayActive = signal<boolean>(false)

  public activeComponent: string | null = null

  public overlayTitle = ''

  public overlayIconUrl = ''

  public bulkSettingUpdateData: {
    exceptionType: string
    settings: ReprocessData
  }

  public icons = {
    closeIcon: xIcon,
    custodianIcon: 'assets/svg/icon-custodian-or-media.svg',
    updateIcon: 'assets/svg/icon-reprocessing-update.svg',
  }

  public selectedTagIds: number[] = []

  public isFileinAllPageSelected = false

  public reprocessInNewMediaChecked = signal<boolean>(false)

  public defaultnewMediaName = ''

  //  selected media list
  public mediaList: number[] = []

  // [{"PASSWORD_PROTECTED": {setting} }]
  public bulkSettings: Record<string, ReprocessData>

  public defaultBulkSettings: Record<string, ReprocessData>

  //for fileid specific settings [{ 1: {'BULK_SETTINGS', setting}}, {2: {'CHANGE_SETTINGS', setting}}]
  public reprocessSettings: Record<
    number,
    { item1: SettingsType; item2: ReprocessData }
  >

  public timeoutValueInMin = 5

  public enablePasswordProtected = true

  public isLoadFileSaving = signal<boolean>(false)

  public showStatus = signal<boolean>(false)

  @ViewChild(ReprocessingDocumentComponent)
  public documentComponent: ReprocessingDocumentComponent

  public get selectedDocumentsCount(): number {
    return this.documentComponent?.selectedDocumentsCount()
  }

  public get totalDocumentsCount(): number {
    return this.documentComponent?.totalDocuments()
  }

  public get isReprocessQueuing(): boolean {
    return this.documentComponent?.isReprocessQueuing()
  }

  public get reprocessForm(): FormGroup {
    return this.reprocessingFormService.reprocessForm
  }

  public isExceptionTypeSelected(): boolean {
    return this.reprocessForm.get('reprocessingType').value === 1
  }

  constructor(
    private route: ActivatedRoute,
    private reprocessingFacade: ReprocessingFacade,
    private reprocessingFormService: ReprocessingFormService,
    private dialogService: DialogService,
    private datepipe: DatePipe
  ) {
    this.CustodianDialogEffect()
  }

  public CustodianDialogEffect(): void {
    effect(
      () => {
        // show custodian/media dialog after queuing for reprocessing is completed
        if (this.reprocessingFormService.showCustodianMediaDialog()) {
          this.isOpendAfterReprocess.set(true)
          this.openCustodianDialog()
        }
      },
      { allowSignalWrites: true }
    )
  }

  public ngOnInit(): void {
    this.loadDefaultSettings()
    this.fetchData()
    this.loadDefaultData()
    this.onReprocessOptionChange()
    // Open custodian/media dialog for media selection
    this.openCustodianDialog()

    // Extract URL parameter and trigger the appropriate overlay
    this.route.queryParams.subscribe((params) => {
      const overlayType = params['ind'] // Get the value of 'ind' from the URL
      if (overlayType === 'settings') {
        this.openOverlay('settings')
      } else if (overlayType === 'update') {
        this.openOverlay('update')
      } else if (overlayType === 'custodian') {
        this.openOverlay('custodian')
      }
    })

    if (this.isOpenFromEmail() === true || this.settingId()) {
      this.reprocessForm
        .get('reprocessOption')
        .setValue(ReprocessOption.EXCEPTION)
      this.reprocessForm.get('reprocessingType').disable()
      this.reprocessForm
        .get('exceptionType')
        .setValue(
          'CORRUPTED,CRASHED,TIME_OUT,NON_PROCESSED,CHILD_MISSING,PARTIAL_META_EXTRACTED'
        )
    }
  }

  public loadDefaultData(): void {
    if (this.isReprocessFromVODExportServiceNotification())
      this.reprocessForm.get('reprocessingType').disable()

    this.reprocessForm.get('loadDocument').setValue(false)
    this.reprocessForm.get('recomputeData').setValue(false)
    this.reprocessForm.get('settingId')?.setValue(this.settingId() ?? null)
    this.reprocessForm.get('selectedMedias').setValue([])
    this.reprocessForm.get('token')?.setValue(this.token() ?? null)
  }

  public loadDefaultSettings(): void {
    this.defaultBulkSettings = this.GetDefaultBulkSetting()
    const temp = this.defaultBulkSettings['DEFAULT']

    this.bulkSettings = { CORRUPTED: temp }
    this.bulkSettings['CRASHED'] = temp
    this.bulkSettings['CHILD_MISSING'] = temp
    this.bulkSettings['PARTIAL_META_EXTRACTED'] = temp
    this.bulkSettings['REPLACE_ANY'] = temp
    this.bulkSettings['ANY_FILES'] = temp
    this.bulkSettings['NOT_PROCESSED'] = temp
    const tempPass = cloneDeep(temp)
    tempPass.usePasswordBank = true
    this.bulkSettings['PASSWORD_PROTECTED'] = tempPass
    const tempTimeOut = cloneDeep(temp)
    tempTimeOut.useTimeOut = true
    this.bulkSettings['TIME_OUT'] = tempTimeOut

    this.reprocessingFormService.reprocessForm
      .get('bulkSettings')
      .setValue(this.bulkSettings)
  }

  public GetDefaultBulkSetting(): { [key: string]: ReprocessData } {
    return {
      DEFAULT: {
        replacementFilePath: '',
        fsid: 0,
        userIdPath: '',
        password: '',
        passwordBankId: 0,
        replacementType: '',
        timeoutValueInMin: 0,
        fileIndex: 0,
        fileType: 'UNKNOWN',
        extractor: null,
        metaExtractor: 0,
        textExtractor: 0,
        childExtractor: 0,
        updateMeta: true,
        updateEdocMeta: true,
        edocMeta: null,
        updateEmailMeta: true,
        emailMeta: null,
        updateFulltext: true,
        updateChild: true,
        rextractAllChild: false,
        extractOnlyMissingChild: true,
        updateHashValue: true,
        usePasswordBank: false,
        useTimeOut: false,
        repairPST: false,
      },
    }
  }

  private fetchData(): void {
    this.reprocessingFacade.fetchEdocFieldsForReprocessing(this.projectId())
    this.reprocessingFacade.fetchEmailFieldsForReprocessing(this.projectId())
    this.reprocessingFacade.fetchCustodianInfo(this.projectId())
  }

  public openOverlay(component: string): void {
    this.isOverlayActive.set(true)
    this.activeComponent = component

    if (component === 'custodian') {
      this.overlayTitle = 'Custodian or Media'
      this.overlayIconUrl = this.icons.custodianIcon
    } else if (component === 'update') {
      this.overlayTitle = 'Update Meta'
      this.overlayIconUrl = this.icons.updateIcon
    } else if (component === 'settings') {
      this.overlayTitle = 'Change Reprocessing Settings'
      this.overlayIconUrl = this.icons.updateIcon
    }
  }

  public closeOverlay(): void {
    this.isOverlayActive.set(false)
    this.activeComponent = null
    this.overlayTitle = ''
    this.overlayIconUrl = ''
  }

  public onDialogClosed(): void {
    this.isOverlayActive.update((isActive) => !isActive)
  }

  public openCustodianDialog(): void {
    this.openOverlay('custodian')
  }

  public openBulkSettingsDialog(type: string): void {
    this.bulkSettingUpdateData = {
      exceptionType: type,
      settings: cloneDeep(
        this.bulkSettings[type] ?? this.defaultBulkSettings['DEFAULT']
      ),
    }
    this.bulkSettingUpdateData.settings.timeoutValueInMin =
      this.timeoutValueInMin

    this.openOverlay('update')
  }

  public closeCustodianDialogEvent(isSelectionChanged: boolean): void {
    this.closeOverlay()

    this.reprocessingFormService.showCustodianMediaDialog.set(false)

    if (isSelectionChanged) {
      this.mediaList = this.reprocessForm.get('selectedMedias').value

      if (this.mediaList?.length > 0) {
        this.reprocessingFacade.fetchTagsForReprocessing(
          this.projectId(),
          this.mediaList.join(',')
        )
        this.loadDocumentForReprocessing()
      }
    }
  }

  public closeBulkSettingUpdateDialogEvent(settings: any): void {
    this.closeOverlay()
    this.isOpendAfterReprocess.set(false)

    if (settings) {
      this.bulkSettings[this.bulkSettingUpdateData.exceptionType] = settings
      if (this.bulkSettingUpdateData.exceptionType === 'TIME_OUT') {
        this.timeoutValueInMin =
          this.bulkSettings[
            this.bulkSettingUpdateData.exceptionType
          ].timeoutValueInMin
      }
      if (this.bulkSettingUpdateData.exceptionType === 'PASSWORD_PROTECTED') {
        this.enablePasswordProtected =
          this.bulkSettings[
            this.bulkSettingUpdateData.exceptionType
          ].usePasswordBank
      }
    }
  }

  public onAllFileChecked(): void {
    this.isAllFiles.update((isAll) => !isAll)

    if (this.isAllFiles()) {
      this.reprocessForm.get('reprocessingType').disable({ emitEvent: false })

      this.reprocessForm
        .get('reprocessOption')
        .setValue(ReprocessOption.ALL_FILES)
      this.loadDocumentForReprocessing()
    } else {
      if (!this.isReprocessFromVODExportServiceNotification())
        this.reprocessForm.get('reprocessingType').enable({ emitEvent: false })

      //if any exception type or tags are selected, load the document
      const reprocessingType = this.reprocessForm.get('reprocessingType').value
      if (reprocessingType === 1) {
        this.reprocessForm
          .get('reprocessOption')
          .setValue(ReprocessOption.EXCEPTION)
      } else {
        this.reprocessForm.get('reprocessOption').setValue(ReprocessOption.TAG)
      }

      const exceptionType = this.reprocessForm.get('exceptionType').value
      const selectedTagIds = this.reprocessForm.get('selectedTagIds').value

      if (exceptionType.length > 0 || selectedTagIds.length > 0) {
        this.loadDocumentForReprocessing()
      } else {
        this.reprocessingFacade.clearDocumentForReprocessing()
      }
    }
  }

  public onReprocessOptionChange(): void {
    this.reprocessForm
      .get('reprocessingType')
      .valueChanges.pipe(takeUntil(this.unsubscribe$))
      .subscribe((value) => {
        if (value === 1) {
          this.reprocessForm
            .get('reprocessOption')
            .setValue(ReprocessOption.EXCEPTION)
        } else {
          this.reprocessForm
            .get('reprocessOption')
            .setValue(ReprocessOption.TAG)
        }
        this.loadDocumentForReprocessing()
      })
  }

  public loadDocumentForReprocessing(): void {
    this.reprocessForm.get('recomputeData').setValue(true)
    this.reprocessForm.get('loadDocument').setValue(true)
  }

  public onReprocessinNewMediaClicked(): void {
    this.reprocessInNewMediaChecked.update((checked) => !checked)
    this.reprocessForm
      .get('reprocessInNewMedia')
      .setValue(this.reprocessInNewMediaChecked())
    if (this.reprocessInNewMediaChecked()) {
      this.isAllFiles.set(false)
      this.reprocessForm
        .get('reprocessOption')
        .setValue(ReprocessOption.EXCEPTION)
      this.reprocessForm
        .get('reprocessingType')
        .setValue(1, { emitEvent: false })
      this.populateNewMediaName()
      this.reprocessForm.get('recomputeData').setValue(true)
      this.reprocessForm.get('loadDocument').setValue(true)
    }
  }

  public populateNewMediaName(): void {
    if (
      !this.isFallbackIngestionEngine() &&
      this.reprocessInNewMediaChecked() &&
      this.mediaList.length > 0
    ) {
      this.reprocessingFacade
        .getMediaInfo(this.projectId(), this.mediaList[0])
        .subscribe((res: ResponseModel) => {
          if (res?.data) {
            this.defaultnewMediaName = this.buildNewMediaName(
              res?.data.mediaName
            )
            const newMediaName = this.defaultnewMediaName

            this.reprocessForm.get('newMediaName').setValue(newMediaName)

            if (this.isAllFiles()) {
              this.isAllFiles.set(false)
            }
          }
        })
    }
  }

  public onMediaNameBlur(): void {
    if (this.reprocessForm.get('newMediaName').value === '') {
      this.reprocessForm.get('newMediaName').setValue(this.defaultnewMediaName)
    }
  }

  public buildNewMediaName(originalMediaName: string): string {
    const currentDate = new Date()
    const formattedDate = this.datepipe.transform(currentDate, 'dd_MM_yyyy')

    return originalMediaName + '_Reprocess_' + formattedDate
  }

  public openLoadfileDialog(): void {
    import(
      './import-for-file-replacement/import-for-file-replacement.component'
    ).then((td) => {
      const dialog: DialogRef = this.dialogService.open({
        content: td.ImportForFileReplacementComponent,
        maxHeight: '300px',
        width: '540px',
        cssClass: 'v-dialog-custodian t-right-[220px]',
      })

      dialog.content.instance.data = {}

      dialog.result.subscribe((result) => {
        if (result instanceof DialogCloseResult) {
          return
        }
        this.reprocessingFormService.updateDocumentGrid.set(result)
      })
    })
  }

  public onSaveAsLoadFileClicked(): void {
    this.isLoadFileSaving.set(true)
    const bulkSettings = this.reprocessForm.get('bulkSettings').value
    const exceptionType = this.reprocessForm.get('exceptionType').value
    const reprocessOption = this.reprocessForm.get('reprocessOption').value
    const selectedTagIds = this.reprocessForm.get('selectedTagIds').value
    const reprocessInNewMedia = this.reprocessForm.get(
      'reprocessInNewMedia'
    ).value
    const selectedMedias = this.reprocessForm.get('selectedMedias').value
    const newMediaName = this.reprocessForm.get('newMediaName').value
    const token = this.reprocessForm.get('token').value
    const selectedFileids = this.documentComponent.selectedFileids()
    const useTempTableForJobQueuing =
      this.documentComponent.useTempTableForJobQueuing()
    const tempTable = this.documentComponent.tempTable
    const unSelectedFileids = this.documentComponent.unSelectedFileids()

    const payload: FileReprocessModel = {
      fileInfos: selectedFileids,
      bulkSettings: bulkSettings,
      reprocessSettings: this.reprocessSettings,
      maxTimevalue: -1,
      exceptionTypes:
        reprocessOption === ReprocessOption.EXCEPTION
          ? exceptionType?.toString() ?? ''
          : 'REPLACE_ANY',
      reprocessOption: reprocessOption,
      tagids: selectedTagIds,
      tempTable: useTempTableForJobQueuing ? tempTable : '',
      unselectedFileInfos: unSelectedFileids,
      originalMediaId: reprocessInNewMedia ? selectedMedias[0] : -1,
      mediaName: reprocessInNewMedia ? newMediaName.trim() : '',
      token: token,
    }

    this.reprocessingFacade
      .saveLoadFile(this.projectId(), payload)
      .subscribe((res: ResponseModel) => {
        if (res?.data) {
          const byteCharacters = atob(res?.data.buffer)
          const byteArrays = []
          for (let offset = 0; offset < byteCharacters.length; offset += 512) {
            const slice = byteCharacters.slice(offset, offset + 512)
            const byteNumbers = new Array(slice.length)
            for (let i = 0; i < slice.length; i++) {
              byteNumbers[i] = slice.charCodeAt(i)
            }
            const byteArray = new Uint8Array(byteNumbers)
            byteArrays.push(byteArray)
          }
          const blob = new Blob(byteArrays, {
            type: res?.data.mimeType,
          })
          const url = window.URL.createObjectURL(blob)

          const element: any = document.createElement('a')
          document.body.appendChild(element)
          element.style = 'display: none'
          element.href = url
          element.download = res?.data.fileName
          element.click()
          document.body.removeChild(element)
          window.URL.revokeObjectURL(url)

          this.isLoadFileSaving.set(false)
        }
      })
  }

  public showJobStatus(): void {
    this.showStatus.set(true)
  }

  public hideReprocessingStatus(event): void {
    this.showStatus.set(event)
  }

  public isReprocessFromVODExportServiceNotification(): boolean {
    return (
      this.isServiceTypeCase() &&
      this.settingId() !== null &&
      this.settingId() !== undefined
    )
  }

  public ngOnDestroy(): void {
    this.unsubscribe$.next()
    this.unsubscribe$.complete()
    this.reprocessingFacade.clearReprocessResponse()
    this.reprocessingFacade.resetAll()

    this.reprocessingFormService.resetForm()
  }
}
