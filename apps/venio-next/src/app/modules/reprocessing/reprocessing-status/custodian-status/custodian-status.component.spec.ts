import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CustodianStatusComponent } from './custodian-status.component'
import { provideHttpClient } from '@angular/common/http'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { Component, input } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ProgressBarComponent } from '@progress/kendo-angular-progressbar'

// Mock Kendo Components
@Component({
  standalone: true,
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'kendo-circularprogressbar',
  template: '',
})
class MockKendoCircularProgressBarComponent {
  public animation = input<boolean>()

  public value = input<number>()

  public progressColor = input<string>()
}

describe('CustodianStatusComponent', () => {
  let component: CustodianStatusComponent
  let fixture: ComponentFixture<CustodianStatusComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CustodianStatusComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    })
      .overrideComponent(CustodianStatusComponent, {
        set: {
          imports: [
            CommonModule,
            ProgressBarComponent,
            MockKendoCircularProgressBarComponent,
          ],
        },
      })
      .compileComponents()

    fixture = TestBed.createComponent(CustodianStatusComponent)
    component = fixture.componentInstance

    fixture.componentRef.setInput('reprocessingStatus', {
      custodianName: 'Custodian Name',
      mediaName: 'Media Name',
      createdDate: new Date(),
      currentlyInProgressJob: {
        taskName: 'Task Name',
        totalCount: '10',
        completedCount: '5',
        timeTaken: '10',
        percentage: '50',
        status: 'In Progress',
      },
      JobDetails: [
        {
          taskName: 'Task Name',
          totalCount: '10',
          completedCount: '5',
          timeTaken: '10',
          percentage: '50',
          status: 'In Progress',
        },
      ],
    })

    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
