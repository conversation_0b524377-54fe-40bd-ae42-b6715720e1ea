import {
  ChangeDetectionStrategy,
  Component,
  inject,
  input,
  OnDestroy,
  OnInit,
  output,
  signal,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { GridModule } from '@progress/kendo-angular-grid'
import { FormGroup } from '@angular/forms'
import { ReprocessingFormService } from '@venio/data-access/common'
import { Subject, takeUntil } from 'rxjs'
import { ExceptionType, ReprocessOption } from '@venio/shared/models/interfaces'
import { DialogService } from '@progress/kendo-angular-dialog'

@Component({
  selector: 'venio-reprocessing-exception',
  standalone: true,
  imports: [CommonModule, GridModule],
  templateUrl: './reprocessing-exception.component.html',
  styleUrl: './reprocessing-exception.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReprocessingExceptionComponent implements OnInit, OnDestroy {
  private readonly reprocessingFormService = inject(ReprocessingFormService)

  private readonly viewContainerRef = inject(ViewContainerRef)

  private readonly dialogService = inject(DialogService)

  private unsubscribe$ = new Subject<void>()

  public disableSelection = signal(false)

  public projectId = input.required<number>()

  public isFallbackIngestionEngine = input.required<boolean>()

  public bulkSettingsDialogAction = output<string>()

  public exceptionData: ExceptionType[] = []

  public selectedExceptionTypes: ExceptionType[] = []

  public selectedKeys: ExceptionType[] = []

  public get reprocessForm(): FormGroup {
    return this.reprocessingFormService.reprocessForm
  }

  public ngOnInit(): void {
    this.loadExceptionTypes()
    this.setDefaultExceptionTypeForLegacyProject()
    this.EnableDisableSelection()
    this.restoreSelection()
  }

  public loadExceptionTypes(): void {
    this.exceptionData = [
      {
        id: 1,
        exceptionType: 'Password Protected',
        value: 'PASSWORD_PROTECTED',
      },
      { id: 2, exceptionType: 'Corrupted', value: 'CORRUPTED' },
      { id: 3, exceptionType: 'Crashed', value: 'CRASHED' },
      { id: 4, exceptionType: 'Timed Out', value: 'TIME_OUT' },
      { id: 5, exceptionType: 'Non Processed', value: 'NON_PROCESSED' },
      { id: 6, exceptionType: 'Item Missing', value: 'CHILD_MISSING' },
      {
        id: 7,
        exceptionType: 'Partial Meta Extracted',
        value: 'PARTIAL_META_EXTRACTED',
      },
    ]
  }

  public restoreSelection(): void {
    const selectedExceptions = this.reprocessForm.get('exceptionType').value

    if (selectedExceptions.length > 0) {
      this.selectedKeys = selectedExceptions.split(',')?.map((x) => {
        return this.exceptionData.find((y) => y.value === x).id
      })
    }
  }

  public EnableDisableSelection(): void {
    this.reprocessForm
      .get('reprocessOption')
      .valueChanges.pipe(takeUntil(this.unsubscribe$))
      .subscribe((value) => {
        if (value === ReprocessOption.ALL_FILES) {
          this.disableSelection.set(true)
        } else {
          this.disableSelection.set(false)
        }
      })
  }

  public setDefaultExceptionTypeForLegacyProject(): void {
    //for legacy project, select all exception types except password protected by default
    if (!this.isFallbackIngestionEngine()) {
      this.reprocessForm
        .get('exceptionType')
        .setValue(
          'CORRUPTED,CRASHED,TIME_OUT,NON_PROCESSED,CHILD_MISSING,PARTIAL_META_EXTRACTED'
        )
    }
  }

  public onSelectionChange(event: any): void {
    const selectedTypes = this.selectedExceptionTypes.map((x) => x)
    event.deselectedRows?.forEach((row) => {
      if (selectedTypes.includes(row.dataItem)) {
        selectedTypes.splice(selectedTypes.indexOf(row.dataItem), 1)
      }
    })

    event.selectedRows?.forEach((row) => {
      if (selectedTypes.includes(row.dataItem)) return
      selectedTypes.push(row.dataItem)
    })
    this.selectedExceptionTypes = selectedTypes

    this.reprocessForm
      .get('exceptionType')
      .setValue(this.selectedExceptionTypes.map((x) => x.value).join(','))

    this.reprocessForm.get('recomputeData').setValue(true)
    this.reprocessForm.get('loadDocument').setValue(true)
  }

  public openBulkSettingsDialog(exceptionType: any): void {
    this.bulkSettingsDialogAction.emit(exceptionType.value)
  }

  public openPasswordBank(): void {
    // TODO: implement password bank here
    // need to open password bank dialog here for adding password
    this.#openPasswordBankDialog(this.projectId())
  }

  #openPasswordBankDialog(projectId: number): void {
    import('@venio/feature/password-bank').then((d) => {
      const dialogRef = this.dialogService.open({
        appendTo: this.viewContainerRef,
        content: d.PasswordBankContainerComponent,
        maxWidth: '500',
        maxHeight: '780',
      })
      const componentRef = dialogRef?.content
      if (componentRef) componentRef.setInput('projectId', projectId)
    })
  }

  public ngOnDestroy(): void {
    this.unsubscribe$.next()
    this.unsubscribe$.complete()
  }
}
