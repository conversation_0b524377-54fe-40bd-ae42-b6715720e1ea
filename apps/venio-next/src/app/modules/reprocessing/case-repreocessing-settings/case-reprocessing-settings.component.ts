import {
  ChangeDetectionStrategy,
  Component,
  OnDestroy,
  OnInit,
  output,
  input,
  signal,
  computed,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Observable, of, take } from 'rxjs'
import { FormsModule } from '@angular/forms'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { GridModule } from '@progress/kendo-angular-grid'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { IconsModule } from '@progress/kendo-angular-icons'
import {
  xIcon,
  moreVerticalIcon,
  fileZipIcon,
  fileImageIcon,
  filePdfIcon,
  fileCsvIcon,
  fileAudioIcon,
  fileExcelIcon,
  fileIcon,
} from '@progress/kendo-svg-icons'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { LayoutModule } from '@progress/kendo-angular-layout'
import {
  ReprocessFileInfo,
  ResponseModel,
} from '@venio/shared/models/interfaces'
import { cloneDeep } from 'lodash'
import { ReprocessingFacade } from '@venio/data-access/common'
import {
  PasswordBankFacade,
  PasswordBankModel,
} from '@venio/data-access/review'
import { toSignal } from '@angular/core/rxjs-interop'

@Component({
  selector: 'venio-case-reprocessing-settings',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    LabelModule,
    FormsModule,
    InputsModule,
    ButtonsModule,
    LoaderModule,
    SvgLoaderDirective,
    TooltipModule,
    TreeListModule,
    DynamicHeightDirective,
    GridModule,
    TreeViewModule,
    DropDownsModule,
    LayoutModule,
  ],
  templateUrl: './case-reprocessing-settings.component.html',
  styleUrl: './case-reprocessing-settings.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseReprocessingSettingsComponent implements OnInit, OnDestroy {
  public reprocessSettings = input<any>()

  public closeSettingUpdateDialog = output<any>()

  public projectId: number

  public sourceData: any[] = []

  public icons = {
    closeIcon: xIcon,
    moreVerticalIcon: moreVerticalIcon,
    fileZipIcon: fileZipIcon,
    fileImageIcon: fileImageIcon,
    filePdfIcon: filePdfIcon,
    fileCsvIcon: fileCsvIcon,
    fileAudioIcon: fileAudioIcon,
    fileExcelIcon: fileExcelIcon,
    fileIcon: fileIcon,
  }

  // for tree
  public systemDynamicFolders: any[] = [] // Data source for the TreeList

  public expandedFolderIds: any[] = [] // Tracks expanded folder IDs

  public selected: any[] = [] // Tracks selected items

  // for sliding animation for tab content
  public isExpanded = signal<boolean>(false)

  public isTransitioning = false

  public isVisible = signal<boolean>(false)

  private animationTimeout: any = null

  private resetTimeout: any = null

  public isAnimating = false

  public LoadFileHelpDialog = false

  public showRepositoryBrowser = false

  public selectedReplaceFilePath = ''

  public isTreeBoxOpened = false

  public supportedMetaExtractors = signal<{ id: number; name: string }[]>([])

  public supportedFullTextExtractors = signal<any[]>([])

  public supportedChildExtractors = signal<any[]>([])

  public isExtractorsLoading = false

  public currentFileData: ReprocessFileInfo

  public originalFileData: ReprocessFileInfo

  public isFallbackIngestionEngine = false

  public isReplacementFile = signal<boolean>(true)

  public replacementFilePath = ''

  public uploadChunkSize = 15

  public uploadRetryCount = 5

  public uploadRetryInterval = 5

  public mUploader: any = null

  public uploadCompletedPercentage = 0

  public fullReplaceFileName: string

  public selectedTabIndex = 0

  public timeoutValueInMin: number

  public passwordBankIds: number[] = []

  public defaultPassword: any

  public selectedPassword: number = null

  public isPasswordBankLoading = signal<boolean>(false)

  public nsfFileTypes: string[] = [
    'DXL_GENERIC',
    'LOTUSNOTESDB',
    'DXL_MAILMESSAGE',
    'XML_DXL',
  ]

  public isNsf = signal<boolean>(false)

  public customTextField(): string {
    return this.isNsf ? 'nsfUserIdFilePath' : 'password'
  }

  public readonly passwordBanks = toSignal(
    this.passwordBankFacade.selectPasswordBanks$
  )

  public filteredPasswordData = computed<PasswordBankModel[]>(() => {
    if (!this.passwordSearchValue().trim()) {
      return this.passwordBanks().filter((p) =>
        this.isNsf() ? !!p.isUserIdFile : !p.isUserIdFile
      ) as []
    }

    const lowerSearchValue = this.passwordSearchValue().toLowerCase()
    return this.passwordBanks().filter((p) => {
      const matchesFilePath: boolean = p.nsfUserIdFilePath
        ? p.nsfUserIdFilePath.toLowerCase().includes(lowerSearchValue)
        : false
      const matchesPassword: boolean = p.password
        ? p.password.toLowerCase().includes(lowerSearchValue)
        : false

      if (this.isNsf()) {
        return matchesFilePath || matchesPassword
      }
      return !p.isUserIdFile && matchesPassword
    }) as []
  })

  public passwordSearchValue = signal<string>('')

  constructor(
    private reprocessingFacade: ReprocessingFacade,
    private passwordBankFacade: PasswordBankFacade
  ) {}

  public ngOnInit(): void {
    this.projectId = this.reprocessSettings().projectId
    this.isFallbackIngestionEngine =
      this.reprocessSettings().isFallbackIngestionEngine

    const settings = cloneDeep(this.reprocessSettings())

    this.currentFileData = cloneDeep(settings.reprocessFileInfo)
    this.originalFileData = cloneDeep(settings.reprocessFileInfo)
    if (this.currentFileData.passwordBankId > 0) {
      this.passwordBankIds = [this.currentFileData.passwordBankId]
    }

    this.timeoutValueInMin = this.currentFileData.timeoutValueInMin

    this.isReplacementFile.set(
      false //disabled till upload is completed this.currentFileData?.replacementType?.toUpperCase() === 'REPLACE_ANY'
    )

    this.replacementFilePath = this.currentFileData?.replacementFilePath

    this.mUploader = null

    this.isNsf.set(this.nsfFileTypes.includes(this.currentFileData.fileType))
    this.defaultPassword = { password: 'Password', passwordBankId: -1 }
    this.passwordBankFacade.fetchPasswordBanks(this.projectId)
    this.selectedPassword = this.currentFileData.passwordBankId

    if (this.isFallbackIngestionEngine) {
      this.isExtractorsLoading = true

      this.reprocessingFacade
        .getSupportedExtractors(this.projectId, this.currentFileData.fileType)
        .pipe(take(1))
        .subscribe((res: ResponseModel) => {
          this.supportedMetaExtractors.set(
            res?.data.find((x) => x.operation === 0)?.extractors
          )
          if (
            this.supportedMetaExtractors()?.length > 0 &&
            !this.currentFileData?.metaExtractor
          ) {
            this.currentFileData.metaExtractor =
              this.supportedMetaExtractors()[0]?.id
          }

          this.supportedFullTextExtractors.set(
            res?.data.find((x) => x.operation === 1)?.extractors
          )
          if (
            this.supportedFullTextExtractors()?.length > 0 &&
            !this.currentFileData?.textExtractor
          ) {
            this.currentFileData.textExtractor =
              this.supportedFullTextExtractors()[0]?.id
          }

          this.supportedChildExtractors.set(
            res?.data.find((x) => x.operation === 2)?.extractors
          )
          if (
            this.supportedChildExtractors()?.length > 0 &&
            !this.currentFileData?.childExtractor
          ) {
            this.currentFileData.childExtractor =
              this.supportedChildExtractors()[0]?.id
          }

          this.isExtractorsLoading = false
        })
    }
  }

  public hasChildren = (item: any): boolean => {
    return item.items && item.items.length > 0
  }

  public getChildren(node: any): Observable<any[]> {
    return of(node.items)
  }

  public onSelect(e: any): void {
    // for selecting repository or local upload
  }

  public getFileIcon(fileType: string): any {
    const fileTypeIcons = {
      jpg: this.icons.fileImageIcon,
      zip: this.icons.fileZipIcon,
      pdf: this.icons.filePdfIcon,
      csv: this.icons.fileCsvIcon,
      aac: this.icons.fileAudioIcon,
      xls: this.icons.fileExcelIcon,
    }

    // Return the matching icon or the default file icon
    return fileTypeIcons[fileType] || this.icons.fileIcon
  }

  public toggleSlide(): void {
    // Prevent toggling while an animation is already in progress
    if (this.isAnimating) return

    this.isAnimating = true // Mark animation as in progress

    // Clear any existing timeouts to avoid memory leaks or duplicate execution
    if (this.animationTimeout) {
      clearTimeout(this.animationTimeout)
    }

    if (!this.isExpanded()) {
      // On expanding, make the element visible first
      this.isVisible.set(true)

      this.animationTimeout = setTimeout(() => {
        this.isExpanded.set(true) // Trigger the animation
      }, 10) // Ensure a small delay for DOM to apply initial styles
    } else {
      // On collapsing, reverse the animation first
      this.isExpanded.set(false)

      this.animationTimeout = setTimeout(() => {
        this.isVisible.set(false) // Remove the element from the DOM after animation
      }, 500) // Match the transition duration
    }

    // Reset `isAnimating` flag after the animation completes
    if (this.resetTimeout) {
      clearTimeout(this.resetTimeout)
    }
    this.resetTimeout = setTimeout(() => {
      this.isAnimating = false
    }, 500) // Match the total animation duration
  }

  public onSave(): void {
    // Set password bank id
    this.currentFileData.passwordBankId = this.selectedPassword

    if (this.timeoutValueInMin && this.timeoutValueInMin > 0)
      this.currentFileData.timeoutValueInMin = this.timeoutValueInMin

    //TODO Set replacement file

    const data = { setting: this.currentFileData }
    this.closeSettingUpdateDialog.emit(data)
  }

  public onCancel(): void {
    this.closeSettingUpdateDialog.emit(null)
  }

  public ngOnDestroy(): void {
    if (this.animationTimeout) {
      clearTimeout(this.animationTimeout)
    }
    if (this.resetTimeout) {
      clearTimeout(this.resetTimeout)
    }
  }

  public onPasswordFilter(value: string): void {
    this.passwordSearchValue.set(value.toLowerCase())
  }

  public onPasswordSelectionChange(value: number): void {
    this.selectedPassword = value
  }
}
