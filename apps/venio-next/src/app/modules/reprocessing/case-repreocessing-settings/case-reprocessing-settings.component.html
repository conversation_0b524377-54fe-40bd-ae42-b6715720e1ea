<div
  class="t-mt-4 t-flex t-flex-col t-gap-3 t-w-full t-overflow-x-hidden t-overflow-y-auto"
  venioDynamicHeight
  [extraSpacing]="70">
  <!-- File Info Section -->
  <div
    class="t-flex t-p-4 t-items-center t-bg-[#F9F9F9] t-items-start t-items-baseline">
    <div class="t-w-1/6">
      <span class="t-font-bold t-text-sm">FILE ID</span>
      <p class="t-text-sm t-text-[#707070]">{{ currentFileData.fileId }}</p>
    </div>
    <div class="t-w-1/6">
      <span class="t-font-bold t-text-sm">MEDIA NAME</span>
      <p class="t-text-sm t-text-[#707070]">{{ currentFileData.mediaName }}</p>
    </div>
    <div class="t-w-4/6">
      <span class="t-font-bold t-text-sm">ORIGINAL FILE PATH</span>
      <p class="t-text-sm t-text-[#707070]">
        {{ currentFileData.originalFilePath }}
      </p>
    </div>
  </div>
  <!-- Replacement File Section -->
  <div
    class="t-bg-[#F9F9F9] t-flex t-items-center t-p-4 t-w-full t-flex t-gap-0">
    <kendo-textbox
      placeholder="Replacement File"
      [disabled]="!isReplacementFile()"
      [value]="replacementFilePath"
      class="t-flex-1 t-border t-border-gray-300 !t-border-r-0 !t-rounded-l-md">
    </kendo-textbox>
    <button
      kendoButton
      themeColor="secondary"
      fillMode="outline"
      [disabled]="!isReplacementFile()"
      (click)="toggleSlide()"
      class="t-border-solid !t-border-[1px] !t-border-[#9BD2A7] t-text-[#9BD2A7] hover:!t-text-[#FFFFFF] hover:!t-bg-[#9BD2A7] !t-min-h-auto !t-rounded-none !t-rounded-r-md"
      title="moreVerticalIcon">
      <kendo-svgicon
        [icon]="icons.moreVerticalIcon"
        class="t-rotate-90 t-scale-150"></kendo-svgicon>
    </button>
  </div>

  <!-- sliding content -->
  <div class="t-w-full">
    <!-- Repository/Local Tabs -->
    <div
      class="t-bg-[#F9F9F9] t-p-4"
      *ngIf="isVisible()"
      [ngClass]="{
        't-max-h-0 t-opacity-0 t-translate-y-[-30px]': !isExpanded(),
        't-max-h-full t-opacity-100 t-translate-y-0': isExpanded()
      }"
      [style.transition]="
        'opacity 0.5s ease, max-height 0.5s ease, transform 0.5s ease 0.3s'
      ">
      <kendo-tabstrip (tabSelect)="onSelect($event)" class="v-custom-tabstrip">
        <kendo-tabstrip-tab title="Repository" [selected]="true">
          <ng-template kendoTabContent>
            <div class="t-flex t-gap-4 t-bg-[#F9F9F9] t-pt-4">
              <div class="t-relative t-flex t-flex-col t-gap-2 t-w-1/4">
                <kendo-textbox
                  class="!t-border-[#ccc] t-w-full"
                  placeholder="Search"
                  [clearButton]="true">
                  <ng-template kendoTextBoxSuffixTemplate>
                    <button
                      kendoButton
                      fillMode="clear"
                      class="t-text-[#1EBADC]"
                      imageUrl="assets/svg/icon-updated-search.svg"></button>
                  </ng-template>
                </kendo-textbox>

                <kendo-grid
                  class="t-w-full t-h-full !t-border t-border-[#DADADA] t-relative t-overflow-y-auto v-custom-repro-setting"
                  [kendoGridBinding]="sourceData"
                  [height]="300"
                  [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
                  kendoGridSelectBy="id"
                  [sortable]="true"
                  [groupable]="false"
                  [reorderable]="true"
                  [resizable]="true">
                  <!-- Header Template -->

                  <kendo-grid-checkbox-column
                    [showSelectAll]="true"
                    [width]="40">
                  </kendo-grid-checkbox-column>
                  <kendo-grid-column
                    field="name"
                    title="Select All"
                    headerClass="t-text-primary">
                    <ng-template kendoGridHeaderTemplate>
                      <div
                        class="t-flex t-items-center t-gap-2 t-w-full t-justify-between">
                        <span>Select All</span>
                        <button
                          kendoButton
                          themeColor="secondary"
                          class="v-custom-secondary-button t-mr-[-10px] t-w-9 t-h-9 hover:t-text-[#FFFFFF] hover:t-bg-[#9BD2A7]"
                          fillMode="outline"
                          #actionGrid1>
                          <span
                            venioSvgLoader
                            [parentElement]="actionGrid1.element"
                            applyEffectsTo="stroke"
                            hoverColor="#FFFFFF"
                            color="#9BD2A7"
                            svgUrl="assets/svg/icon-refresh-twoway.svg"
                            height="1.1rem"
                            width="1.1rem">
                            <kendo-loader size="small"></kendo-loader>
                          </span>
                        </button>
                      </div>
                    </ng-template>

                    <ng-template kendoGridCellTemplate let-dataItem>
                      <span>{{ dataItem.name }}</span>
                    </ng-template>
                  </kendo-grid-column>
                </kendo-grid>
              </div>

              <div class="t-relative t-w-2/3">
                <h3
                  class="t-font-semibold t-text-base t-text-primary t-my-2 t-border-[#CCCCCC] t-border-b-[1px] t-pb-2">
                  Repository Hierarchy
                </h3>

                <!-- Repository Hierarchy -->
                <div class="t-grid t-grid-cols-2 t-gap-4 t-bg-[#F9F9F9] t-pt-2">
                  <!-- File/Folder Name -->
                  <div class="t-flex t-flex-col">
                    <h3
                      class="t-font-semibold t-text-base t-text-primary t-px-3">
                      File Folder Name
                    </h3>

                    <kendo-treelist
                      [hideHeader]="false"
                      [initiallyExpanded]="true"
                      [height]="260"
                      [kendoTreeListFlatBinding]="systemDynamicFolders"
                      idField="folderId"
                      parentIdField="parentFolderId"
                      class="!t-border-0 t-bg-[#FAFAFA] v-hide-scrollbar v-reprocess-setting-treelist"
                      kendoTreeListExpandable
                      [autoSize]="true"
                      [columnMenu]="false"
                      kendoTreeListSelectable
                      [(expandedKeys)]="expandedFolderIds"
                      [(selectedItems)]="selected"
                      [filterable]="true"
                      itemKey="folderId">
                      <kendo-treelist-column
                        [expandable]="true"
                        field="folderName"
                        title=""
                        [resizable]="true"
                        class="!t-border-b-0 !t-py-0 !t-flex t-cursor-pointer">
                        <ng-template
                          kendoTreeListCellTemplate
                          let-item
                          let-isExpanded="isExpanded">
                          <div
                            class="t-flex t-gap-2 t-items-center -text-[#263238]">
                            <!-- Folder Icon -->
                            <span
                              venioSvgLoader
                              height="1rem"
                              width="1rem"
                              *ngIf="item.isFolder"
                              svgUrl="assets/svg/icon-folder-fclv-fill.svg">
                              <kendo-loader size="small"></kendo-loader>
                            </span>

                            <!-- File Icon -->
                            <kendo-svgicon
                              *ngIf="!item.isFolder"
                              [icon]="getFileIcon(item.fileType)"
                              class="t-text-[#8f8f8f]"></kendo-svgicon>

                            <!-- Folder or File Name -->
                            <span
                              class="t-text-[#263238]"
                              [ngClass]="{ 't-font-semibold': item.isFolder }">
                              {{ item.folderName }}
                            </span>
                          </div>
                        </ng-template>
                      </kendo-treelist-column>
                    </kendo-treelist>
                  </div>

                  <!-- Repository Name -->
                  <div class="t-flex t-flex-col">
                    <h3
                      class="t-font-semibold t-text-base t-text-primary t-px-3">
                      Repository Name
                    </h3>

                    <kendo-treelist
                      [hideHeader]="false"
                      [height]="260"
                      [initiallyExpanded]="true"
                      [kendoTreeListFlatBinding]="systemDynamicFolders"
                      idField="folderId"
                      parentIdField="parentFolderId"
                      class="!t-border-0 t-bg-[#FAFAFA] v-hide-scrollbar v-reprocess-setting-treelist"
                      kendoTreeListExpandable
                      [autoSize]="true"
                      [columnMenu]="false"
                      kendoTreeListSelectable
                      [(expandedKeys)]="expandedFolderIds"
                      [(selectedItems)]="selected"
                      [filterable]="true"
                      itemKey="folderId">
                      <kendo-treelist-column
                        [expandable]="true"
                        field="folderName"
                        title=""
                        [resizable]="true"
                        class="!t-border-b-0 !t-py-0 !t-flex t-cursor-pointer">
                        <ng-template
                          kendoTreeListCellTemplate
                          let-item
                          let-isExpanded="isExpanded()">
                          <div
                            class="t-flex t-gap-2 t-items-center -text-[#263238]">
                            <!-- Folder Icon -->
                            <span
                              venioSvgLoader
                              height="1rem"
                              width="1rem"
                              *ngIf="item.isFolder"
                              svgUrl="assets/svg/icon-folder-fclv-fill.svg">
                              <kendo-loader size="small"></kendo-loader>
                            </span>

                            <!-- File Icon -->
                            <kendo-svgicon
                              *ngIf="!item.isFolder"
                              [icon]="getFileIcon(item.fileType)"
                              class="t-text-[#8f8f8f]"></kendo-svgicon>

                            <!-- Folder or File Name -->
                            <span
                              class="t-text-[#263238]"
                              [ngClass]="{ 't-font-semibold': item.isFolder }">
                              {{ item.folderName }}
                            </span>
                          </div>
                        </ng-template>
                      </kendo-treelist-column>
                    </kendo-treelist>
                  </div>
                </div>
              </div>
            </div>
          </ng-template>
        </kendo-tabstrip-tab>

        <kendo-tabstrip-tab title="Local">
          <div class="t-p-4">
            <p class="t-text-gray-600">Local File Upload Section</p>
          </div>
        </kendo-tabstrip-tab>
      </kendo-tabstrip>
    </div>

    <!-- Options Section -->
    <div
      class="t-flex t-justify-between t-gap-4 t-bg-[#F9F9F9] t-w-full t-p-4 t-mt-4 t-flex-col">
      <div class="t-flex t-gap-3 t-w-full">
        <kendo-dropdownlist
          *ngIf="!isNsf()"
          [defaultItem]="defaultPassword"
          [data]="filteredPasswordData()"
          textField="password"
          valueField="passwordBankId"
          [valuePrimitive]="true"
          [(ngModel)]="selectedPassword"
          placeholder="Password"
          class="t-w-1/4"></kendo-dropdownlist>
        <kendo-multicolumncombobox
          [data]="filteredPasswordData()"
          [listHeight]="145"
          [popupSettings]="{ width: '240px' }"
          textField="nsfUserIdFilePath"
          valueField="passwordBankId"
          [valuePrimitive]="true"
          [(ngModel)]="selectedPassword"
          (valueChange)="onPasswordSelectionChange($event)"
          [filterable]="true"
          (filterChange)="onPasswordFilter($event)"
          [disabled]="isPasswordBankLoading()"
          placeholder="Password"
          *ngIf="isNsf()"
          class="t-w-1/4">
          <kendo-combobox-column
            *ngIf="isNsf()"
            field="nsfUserIdFilePath"
            title="NSF User ID"
            [width]="100">
            <ng-template
              kendoMultiColumnComboBoxColumnCellTemplate
              let-dataItem>
              <span>{{ dataItem.nsfUserIdFilePath }}</span>
            </ng-template>
            <ng-template kendoMultiColumnComboBoxColumnHeaderTemplate>
              <span>NSF User ID</span>
            </ng-template>
          </kendo-combobox-column>
          <kendo-combobox-column
            field="password"
            title="Password"
            [width]="100">
            <ng-template
              kendoMultiColumnComboBoxColumnCellTemplate
              let-dataItem>
              <span>{{ dataItem.password }}</span>
            </ng-template>
          </kendo-combobox-column>
        </kendo-multicolumncombobox>
        <div class="t-flex t-gap-2 t-items-center t-w-1/4">
          <kendo-numerictextbox
            format="# mins"
            [min]="1"
            [max]="240"
            [step]="1"
            [title]="'Set Timeout (In Mins)'"
            [(ngModel)]="timeoutValueInMin"
            [placeholder]="'Set Timeout (In Mins)'"></kendo-numerictextbox>
        </div>
      </div>

      <div class="t-flex t-gap-3 t-w-full" *ngIf="isFallbackIngestionEngine">
        <kendo-dropdownlist
          textField="name"
          valueField="id"
          [valuePrimitive]="true"
          [(value)]="currentFileData.metaExtractor"
          [data]="supportedMetaExtractors()"
          [defaultItem]="{ name: 'Meta Extraction', id: 0 }"
          class="t-w-1/4">
          <ng-template kendoDropDownListNoDataTemplate>
            No extractor available
          </ng-template>
        </kendo-dropdownlist>
        <kendo-dropdownlist
          textField="name"
          valueField="id"
          [valuePrimitive]="true"
          [(value)]="currentFileData.textExtractor"
          [data]="supportedFullTextExtractors()"
          [defaultItem]="{ name: 'Text Extraction', id: 0 }"
          class="t-w-1/4">
          <ng-template kendoDropDownListNoDataTemplate>
            No extractor available
          </ng-template>
        </kendo-dropdownlist>
        <kendo-dropdownlist
          textField="name"
          valueField="id"
          [valuePrimitive]="true"
          [(value)]="currentFileData.childExtractor"
          [data]="supportedChildExtractors()"
          [defaultItem]="{ name: 'Child Extraction', id: 0 }"
          class="t-w-1/4">
          <ng-template kendoDropDownListNoDataTemplate>
            No extractor available
          </ng-template>
        </kendo-dropdownlist>
      </div>
    </div>
  </div>
</div>

<!-- footer-->
<div class="t-flex t-w-full t-gap-4 t-justify-end t-pt-4">
  <button
    kendoButton
    class="v-custom-secondary-button"
    themeColor="secondary"
    fillMode="outline"
    (click)="onSave()"
    data-qa="save">
    SAVE
  </button>
  <button
    data-qa="cancel"
    kendoButton
    themeColor="dark"
    fillMode="outline"
    (click)="onCancel()">
    CANCEL
  </button>
</div>
