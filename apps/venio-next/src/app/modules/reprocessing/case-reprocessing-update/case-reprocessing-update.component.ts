import {
  ChangeDetectionStrategy,
  Component,
  input,
  OnD<PERSON>roy,
  OnInit,
  output,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'

import { FormsModule } from '@angular/forms'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { GridModule } from '@progress/kendo-angular-grid'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { DynamicHeightDirective } from '@venio/feature/shared/directives'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { IconsModule } from '@progress/kendo-angular-icons'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { State, FilterDescriptor } from '@progress/kendo-data-query'
import { ReprocessingFacade } from '@venio/data-access/common'
import { Subject, takeUntil } from 'rxjs'
import { ReprocessData } from '@venio/shared/models/interfaces'

@Component({
  selector: 'venio-case-reprocessing-update',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    LabelModule,
    FormsModule,
    InputsModule,
    ButtonsModule,
    LoaderModule,
    TooltipModule,
    TreeListModule,
    DynamicHeightDirective,
    GridModule,
    TreeViewModule,
    DropDownsModule,
  ],
  templateUrl: './case-reprocessing-update.component.html',
  styleUrl: './case-reprocessing-update.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseReprocessingUpdateComponent implements OnInit, OnDestroy {
  private unsubscribe$ = new Subject<void>()

  public edocMeta: Array<{ id: number; text: string }> = []

  public emailMeta: Array<{ id: number; text: string }> = []

  public childExtractionOptions = [
    'ReExtract all Child',
    'Extract only missing',
  ]

  public childExtractionSelected = signal('ReExtract all Child')

  public filteredEdocMeta: any[] = []

  public searchValue = ''

  public filterOptions: Array<string> = [
    'Author',
    'Company',
    'Title',
    'CreateDateTime',
  ]

  public state: State = {
    skip: 0,
    take: 10,
    filter: null,
  }

  public selectedEmailFields = signal<string[]>([])

  public selectedEdocFields = signal<string[]>([])

  public updateMeta = signal(true)

  public updateFulltext = signal(true)

  public updateHashValue = signal(true)

  public useTimeOut = signal(true)

  public timeoutValueInMin = signal(5)

  public updateChild = signal(true)

  public usePasswordBank = signal(false)

  public repairPST = signal(true)

  public exceptionType = signal<string>('')

  public data = input.required<{
    exceptionType: string
    settings: ReprocessData
  }>()

  public closeBulkSettingUpdateDialog = output<any>()

  constructor(private reprocessingFacade: ReprocessingFacade) {}

  public ngOnInit(): void {
    this.loadData()
  }

  public loadData(): void {
    const data = this.data()
    if (data.settings) {
      this.updateMeta.set(data.settings.updateMeta)
      this.updateFulltext.set(data.settings.updateFulltext)
      this.updateHashValue.set(data.settings.updateHashValue)
      this.useTimeOut.set(data.settings.useTimeOut)
      this.timeoutValueInMin.set(data.settings.timeoutValueInMin)
      this.updateChild.set(data.settings.updateChild)
      this.usePasswordBank.set(data.settings.usePasswordBank)
      this.repairPST.set(data.settings.repairPST)
      this.exceptionType.set(data.exceptionType)

      this.childExtractionSelected.set(
        data.settings.rextractAllChild
          ? 'ReExtract all Child'
          : 'Extract only missing'
      )
    }

    this.reprocessingFacade.getEdocFields$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((response: string[]) => {
        this.selectedEdocFields.set([])
        if (response) {
          this.edocMeta = []
          for (let i = 0; i < response.length; i++) {
            this.edocMeta.push({
              id: i + 1,
              text: response[i],
            })
          }

          if (this.data()?.settings && this.data()?.settings?.edocMeta) {
            this.selectedEdocFields.set([])
            for (const rItem in this.data().settings.edocMeta) {
              if (this.data().settings.edocMeta[rItem]) {
                this.selectedEdocFields.update((fields) => {
                  fields.push(rItem)
                  return fields
                })
              }
            }
          } else {
            this.selectedEdocFields.set(this.edocMeta.map((x) => x.text))
          }
        }
      })

    this.reprocessingFacade.getEmailFields$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((response: string[]) => {
        this.selectedEmailFields.set([])
        if (response) {
          this.emailMeta = []
          for (let i = 0; i < response.length; i++) {
            this.emailMeta.push({
              id: i + 1,
              text: response[i],
            })
          }
          if (this.data()?.settings && this.data()?.settings?.emailMeta) {
            this.selectedEmailFields.set([])
            for (const rItem in this.data().settings.emailMeta) {
              if (this.data().settings.emailMeta[rItem]) {
                this.selectedEmailFields.update((fields) => {
                  fields.push(rItem)
                  return fields
                })
              }
            }
          } else {
            this.selectedEmailFields.set(this.emailMeta.map((x) => x.text))
          }
        }
      })
  }

  public applyFilter(): void {
    const filterValue = this.searchValue.toLowerCase()
    this.filteredEdocMeta = this.edocMeta.filter((item) =>
      item.text.toLowerCase().includes(filterValue)
    )
  }

  public onFilterChange(value: string, filterService: any, column: any): void {
    const filter: FilterDescriptor = {
      field: column.field,
      operator: 'contains',
      value: value,
    }

    filterService.filter({
      filters: value ? [filter] : [],
      logic: 'and',
    })
  }

  public onUpdateSettingSaved(): void {
    const settings = this.data().settings
    settings.updateMeta = this.updateMeta()
    settings.updateFulltext = this.updateFulltext()
    settings.updateHashValue = this.updateHashValue()
    settings.useTimeOut = this.useTimeOut()
    settings.timeoutValueInMin = this.timeoutValueInMin()
    settings.updateChild = this.updateChild()
    settings.usePasswordBank = this.usePasswordBank()
    settings.repairPST = this.repairPST()
    settings.rextractAllChild =
      this.childExtractionSelected() === 'ReExtract all Child' ? true : false
    settings.extractOnlyMissingChild =
      this.childExtractionSelected() === 'Extract only missing' ? true : false

    if (settings.updateMeta) {
      settings.edocMeta = {}
      this.edocMeta.forEach((field) => {
        settings.edocMeta[field.text] = this.selectedEdocFields().includes(
          field.text
        )
      })

      settings.emailMeta = {}
      this.emailMeta.forEach((field) => {
        settings.emailMeta[field.text] = this.selectedEmailFields().includes(
          field.text
        )
      })
    }
    this.closeBulkSettingUpdateDialog.emit(settings)
  }

  public onCancel(): void {
    this.closeBulkSettingUpdateDialog.emit(null)
  }

  public ngOnDestroy(): void {
    this.unsubscribe$.next()
    this.unsubscribe$.complete()
  }
}
