<div
  class="t-flex t-flex-col t-flex-wrap t-gap-4 t-p-4"
  venioDynamicHeight
  [extraSpacing]="70">
  <!-- Update Meta Checkbox -->
  <div class="t-w-full t-flex t-gap-2 t-items-center">
    <input
      type="checkbox"
      #updateMetaCheckBox
      kendoCheckBox
      [checked]="true"
      [(ngModel)]="updateMeta" />
    <kendo-label
      class="t-flex t-items-center t-space-x-2"
      [for]="updateMetaCheckBox"
      text="Update Meta"></kendo-label>
  </div>

  <!-- Metadata Grids -->
  <div class="t-flex t-w-full t-gap-4">
    <!-- Edoc Meta -->
    <div class="t-w-2/4">
      <kendo-grid
        [kendoGridBinding]="edocMeta"
        [sortable]="true"
        [groupable]="false"
        [reorderable]="true"
        [resizable]="true"
        [filterable]="true"
        [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
        [(selectedKeys)]="selectedEdocFields"
        kendoGridSelectBy="text"
        [height]="245"
        [ngClass]="{ 't-pointer-events-none t-opacity-30': !updateMeta() }"
        class="t-w-full t-h-full t-relative t-overflow-y-auto t-border t-border-gray-200">
        <kendo-grid-checkbox-column [showSelectAll]="true" [width]="50">
        </kendo-grid-checkbox-column>
        <kendo-grid-column
          field="text"
          title="Edoc Meta"
          headerClass="t-text-primary">
        </kendo-grid-column>
      </kendo-grid>
    </div>

    <!-- Email Meta -->
    <div class="t-w-2/4">
      <kendo-grid
        [kendoGridBinding]="emailMeta"
        [sortable]="true"
        [groupable]="false"
        [reorderable]="true"
        [resizable]="true"
        [filterable]="true"
        [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
        [(selectedKeys)]="selectedEmailFields"
        kendoGridSelectBy="text"
        [height]="245"
        [ngClass]="{ 't-pointer-events-none t-opacity-30': !updateMeta() }"
        class="t-w-full t-h-full t-relative t-overflow-y-auto t-border t-border-gray-200">
        <kendo-grid-checkbox-column [showSelectAll]="true" [width]="50">
        </kendo-grid-checkbox-column>

        <kendo-grid-column
          field="text"
          title="Email Meta"
          headerClass="t-text-primary">
        </kendo-grid-column>
      </kendo-grid>
    </div>
  </div>

  <!-- Additional Options -->
  <div class="t-w-1/2 t-flex t-gap-5 t-mt-2">
    <div class="t-w-1/2 t-flex t-flex-col t-gap-0 t-space-y-1">
      <div class="t-flex t-gap-2 t-items-center t-h-[35px]">
        <input
          type="checkbox"
          #updateFulltextCheckBox
          kendoCheckBox
          [checked]="updateFulltext()"
          [(ngModel)]="updateFulltext" />
        <kendo-label
          [for]="updateFulltextCheckBox"
          text="Update Fulltext"></kendo-label>
      </div>

      <div class="t-flex t-gap-2 t-items-center t-h-[35px]">
        <input
          type="checkbox"
          #updateHasValueCheckBox
          kendoCheckBox
          [checked]="updateHashValue()"
          [(ngModel)]="updateHashValue" />
        <kendo-label
          [for]="updateHasValueCheckBox"
          text="Update Hasvalue"></kendo-label>
      </div>

      <div class="t-flex t-gap-2 t-items-center t-h-[35px]">
        <input
          type="checkbox"
          #updateChildCheckBox
          kendoCheckBox
          [checked]="updateChild()"
          [(ngModel)]="updateChild" />
        <kendo-label
          class="t-flex t-items-center t-w-full"
          [for]="updateChildCheckBox"
          text="">
          <kendo-dropdownlist
            [data]="childExtractionOptions"
            [(ngModel)]="childExtractionSelected"
            class="t-w-full">
          </kendo-dropdownlist>
        </kendo-label>
      </div>
    </div>

    <div class="t-w-1/2 t-flex t-flex-col t-gap-0 t-space-y-1">
      <div class="t-flex t-gap-2 t-items-center t-h-[35px]">
        <input
          type="checkbox"
          #setTimeoutCheckBox
          kendoCheckBox
          [checked]="useTimeOut()"
          [(ngModel)]="useTimeOut"
          [disabled]="!(exceptionType() === 'TIME_OUT' || useTimeOut())" />
        <kendo-label [for]="setTimeoutCheckBox" text="" class="t-flex t-w-full">
          <kendo-numerictextbox
            format="# mins"
            [step]="1"
            [min]="1"
            [max]="240"
            [title]="'Set Timeout (In Mins)'"
            [placeholder]="'Set Timeout (In Mins)'"
            [disabled]="exceptionType() !== 'TIME_OUT' || !useTimeOut()"
            [(ngModel)]="timeoutValueInMin"></kendo-numerictextbox>
        </kendo-label>
      </div>

      <div class="t-flex t-gap-2 t-items-center t-h-[35px]">
        <input
          type="checkbox"
          #usePasswordBankCheckBox
          kendoCheckBox
          [checked]="usePasswordBank()"
          [disabled]="exceptionType() !== 'PASSWORD_PROTECTED'"
          [(ngModel)]="usePasswordBank" />
        <kendo-label
          [for]="usePasswordBankCheckBox"
          text="Use Password Bank"></kendo-label>
      </div>

      <div class="t-flex t-gap-2 t-items-center t-h-[35px]">
        <input
          type="checkbox"
          #repairPSTCheckBox
          kendoCheckBox
          [checked]="repairPST()"
          [(ngModel)]="repairPST" />
        <kendo-label [for]="repairPSTCheckBox" text="Repair PST"></kendo-label>
      </div>
    </div>
  </div>
</div>

<!-- footer-->
<div class="t-flex t-w-full t-gap-4 t-justify-end t-pt-4">
  <button
    kendoButton
    class="v-custom-secondary-button"
    themeColor="secondary"
    fillMode="outline"
    data-qa="save"
    (click)="onUpdateSettingSaved()">
    SAVE
  </button>
  <button
    data-qa="cancel"
    kendoButton
    themeColor="dark"
    fillMode="outline"
    (click)="onCancel()">
    CANCEL
  </button>
</div>
