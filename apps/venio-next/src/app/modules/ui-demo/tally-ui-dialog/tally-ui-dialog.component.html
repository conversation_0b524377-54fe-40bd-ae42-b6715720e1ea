<div class="t-flex t-p-8">
  <button kendoButton (click)="openDialog()">Tally UI Dialog</button>
</div>

<kendo-dialog
  *ngIf="opened"
  (close)="close('cancel')"
  [maxHeight]="580"
  [height]="'80vh'"
  [minWidth]="250"
  [width]="'80%'">
  <kendo-dialog-titlebar>
    <div>
      {{ dialogTitle }}
    </div>
  </kendo-dialog-titlebar>

  <div class="t-flex t-flex-col">
    <div class="t-flex t-gap-5">
      <div class="t-flex t-flex-1 t-flex-col t-w-full">
        <div class="t-flex t-flex-col t-mt-4 t-gap-3 t-w-full">
          <kendo-grid
            [kendoGridBinding]="gridData"
            kendoGridSelectBy="id"
            [pageSize]="pageSize"
            [pageable]="{ type: 'numeric', position: 'top' }"
            [sortable]="true"
            [groupable]="false"
            [reorderable]="true"
            [resizable]="true"
            [selectable]="{ mode: 'multiple', drag: true }"
            [skip]="skip">
            <ng-template
              kendoPagerTemplate
              let-totalPages="totalPages"
              let-currentPage="currentPage"
              let-total="total">
              <kendo-grid-spacer></kendo-grid-spacer>
              <kendo-label class="k-form">
                <kendo-numerictextbox
                  class="!t-w-[3rem]"
                  format="number"
                  [step]="1"
                  [value]="currentPage"
                  [min]="1"
                  [max]="totalPages"
                  [spinners]="false"
                  [selectOnFocus]="true"></kendo-numerictextbox>
              </kendo-label>
              <span> - {{ pageSize }} Of {{ gridData.length }} </span>
              <kendo-dropdownlist
                class="!t-w-[5rem] !t-border-[#707070]"
                [data]="sizes"
                [value]="pageSize"></kendo-dropdownlist>
              per page
              <div class="t-flex t-gap-2">
                <button
                  kendoButton
                  #parentEl
                  *ngFor="let icon of svgIconForPageControls"
                  class="!t-p-[0.3rem]"
                  (click)="browseActionClicked(icon.actionType)"
                  fillMode="outline"
                  size="none">
                  <span
                    [parentElement]="parentEl.element"
                    venioSvgLoader
                    hoverColor="#FFFFFF"
                    [svgUrl]="icon.iconPath"
                    height="0.8rem"
                    width="1rem"></span>
                </button>
              </div>
            </ng-template>

            <ng-template kendoGridToolbarTemplate>
              <kendo-textbox
                class="!t-border-[#ccc] !t-w-[25rem]"
                placeholder="Search the Value"
                [clearButton]="true">
                <ng-template kendoTextBoxSuffixTemplate>
                  <kendo-textbox-separator></kendo-textbox-separator>
                  <button kendoButton fillMode="clear">
                    <span
                      venioSvgLoader
                      svgUrl="assets/svg/icon-search.svg"
                      height="1rem"
                      width="1rem"></span>
                  </button>
                </ng-template>
              </kendo-textbox>

              <kendo-dropdownlist
                [defaultItem]="defaultItemCategories"
                [data]="defaultItems"
                textField="text"
                class="!t-w-52"
                valueField="value">
              </kendo-dropdownlist>

              <button
                kendoButton
                class="v-custom-secondary-button"
                fillMode="outline"
                themeColor="secondary">
                Find
              </button>

              <button kendoButton themeColor="dark">Clear</button>
              <kendo-grid-spacer></kendo-grid-spacer>
              <button
                kendoButton
                fillMode="outline"
                class="t-capitalize !t-border-[#ccc]">
                Export to CSV
              </button>
            </ng-template>

            <kendo-grid-checkbox-column
              [showSelectAll]="true"
              [width]="50"
              class="!t-py-[0.6rem]"></kendo-grid-checkbox-column>

            <kendo-grid-column
              headerClass="t-text-primary"
              field="id"
              title="Internal File Id">
            </kendo-grid-column>

            <kendo-grid-column
              headerClass="t-text-primary"
              field="budget"
              title="Count"
              [width]="150">
            </kendo-grid-column>
          </kendo-grid>
        </div>
      </div>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="close('yes')"
        themeColor="dark"
        fillMode="outline">
        CANCEL
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>
