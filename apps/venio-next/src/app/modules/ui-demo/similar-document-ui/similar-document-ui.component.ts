import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { SVGIcon, gearIcon, searchIcon } from '@progress/kendo-svg-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms'
import { GridModule } from '@progress/kendo-angular-grid'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { UiPaginationModule } from '@venio/ui/pagination'
import { trigger, state, style, transition, animate } from '@angular/animations'

@Component({
  selector: 'venio-similar-document-ui',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    IconsModule,
    InputsModule,
    LabelModule,
    FormsModule,
    ReactiveFormsModule,
    GridModule,
    TooltipModule,
    SvgLoaderDirective,
    UiPaginationModule,
  ],
  templateUrl: './similar-document-ui.component.html',
  styleUrl: './similar-document-ui.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('slide', [
      state(
        'up',
        style({
          height: '0',
          overflow: 'hidden',
        })
      ),
      state(
        'down',
        style({
          height: '*',
          overflow: 'hidden',
        })
      ),
      transition(
        'up <=> down',
        animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)')
      ), // Cubic-bezier for smoother transition
    ]),
  ],
})
export class SimilarDocumentUiComponent implements OnInit {
  public gearIcon: SVGIcon = gearIcon

  public searchIcon: SVGIcon = searchIcon

  public valueVertical = 0

  public searchOptions = {
    allDoc: 'All documents in the project',
    selectedOpt: 'Selected Scope',
  }

  public state = 'down' // Default state of the search scope

  public searchScopeForm: FormGroup

  public gridData: any[] = []

  public pageSize = 10

  public totalPages = 0

  public total = 100 // Total number of records, can be dynamically set

  constructor() {
    this.searchScopeForm = new FormGroup({
      searchScope: new FormControl(''), // Default value can be set here if needed
      slider: new FormControl(5), // Default value can be set here if needed
    })
  }

  public ngOnInit(): void {
    // Initialize the grid with dummy data
    this.gridData = this.getDummyData()
    this.totalPages = Math.ceil(this.total / this.pageSize)
  }

  public onDetailsClicked(dataItem: any): void {}

  // generate dummy data for the grid
  private getDummyData(): any[] {
    const data: any[] = []
    for (let i = 0; i < 100; i++) {
      data.push({
        Field: i,
        Details: 'Detail ' + i,
        Similarity: (Math.random() * 100).toFixed(2) + '%',
        InternalFileID: i + (Math.random() * 100).toFixed(0),
      })
    }
    return data
  }

  // toggle animation for search scope
  public toggleSearchScope(): void {
    this.state = this.state === 'down' ? 'up' : 'down'
  }
}
