import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LoginReportCaselaunchpadComponent } from './login-report-caselaunchpad.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('LoginReportCaselaunchpadComponent', () => {
  let component: LoginReportCaselaunchpadComponent
  let fixture: ComponentFixture<LoginReportCaselaunchpadComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LoginReportCaselaunchpadComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(LoginReportCaselaunchpadComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
