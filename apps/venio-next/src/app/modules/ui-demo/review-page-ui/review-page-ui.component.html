<div class="t-p-5">
  <div class="t-text-lg t-pb-3 t-pt-3">Review Detail UI</div>
</div>

<div class="t-flex t-flex-col">
  <!-- for heder ui-->
  <div class="t-flex t-p-5">
    <div class="t-flex-auto">
      <button
        kendoButton
        [svgIcon]="chevronLeftIcon"
        fillMode="outline"
        class="t-capitalize !t-border-[#ccc]">
        Exit Viwer
      </button>
    </div>
    <div class="t-flex t-flex-1 t-text-base t-justify-center">
      All Documents
    </div>
    <div class="t-flex t-flex-auto t-items-end t-justify-end t-pr-2">
      <div class="t-flex t-gap-2 t-items-center">
        <ng-container
          *ngFor="let icon of svgIconForPageControls; let i = index">
          <button
            kendoButton
            class="!t-p-[0.3rem]"
            (click)="browseActionClicked(icon.actionType)"
            fillMode="outline"
            size="none">
            <span
              venioSvgLoader
              hoverColor="#FFFFFF"
              [svgUrl]="icon.iconPath"
              height="0.8rem"
              width="1rem"></span>
          </button>

          <!-- Conditionally render the input field after the first two elements -->
          <div *ngIf="i === 1">
            <div class="t-flex t-items-center">
              <div class="t-flex-1">
                <kendo-textbox
                  class="!t-border-[#ccc] !t-w-[2rem]"
                  size="large"
                  placeholder="1">
                  1
                </kendo-textbox>
              </div>
              <div class="t-ml-2 t-flex-auto">/ 10000</div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>

  <!-- Main content-->
  <div class="t-flex t-h-[calc(100vh-4.9rem)]">
    <div class="t-flex t-flex-1">
      <div class="t-flex t-border t-border-1 t-border-[#dbdbdb]">
        <div
          class="t-flex t-flex-col t-gap-3 t-mt-10 t-border t-border-l-0 t-border-r-0 t-border-b-0 t-border-t-1 t-border-[#dbdbdb] t-p-2">
          <button
            kendoButton
            #parentEl
            *ngFor="let icon of svgIconForPageDetail"
            class="!t-p-[0.3rem]"
            (click)="browseActionClicked(icon.actionType)"
            fillMode="clear"
            size="none">
            <span
              [parentElement]="parentEl.element"
              venioSvgLoader
              hoverColor="#FFBB12"
              [svgUrl]="icon.iconPath"
              height="1.3rem"
              width="1.3rem"></span>
          </button>
        </div>
      </div>

      <div
        class="t-flex t-flex-col t-flex-1 t-relative t-border t-border-l-0 t-border-r-0 t-border-b-0 t-border-t-1 t-border-[#dbdbdb]">
        <div class="t-flex t-absolute t-top-1.5 t-right-2 t-z-10">
          <button
            kendoButton
            #parentFullscreen
            class="!t-p-[0.3rem]"
            fillMode="clear"
            size="none">
            <span
              [parentElement]="parentFullscreen.element"
              venioSvgLoader
              hoverColor="#FFBB12"
              [svgUrl]="'assets/svg/icon-review-fullscreen.svg'"
              height="1.2rem"
              width="1.2rem"></span>
          </button>
        </div>
        <kendo-tabstrip
          (tabSelect)="onTabSelect($event)"
          class="t-w-full t-h-full">
          <kendo-tabstrip-tab title="Native" [selected]="true">
            <ng-template kendoTabContent>
              <div class="content">
                <div class="t-flex t-p-2">
                  <button
                    kendoButton
                    #parentDownload
                    class="!t-p-[0.3rem]"
                    fillMode="clear"
                    size="none">
                    <span
                      [parentElement]="parentDownload.element"
                      venioSvgLoader
                      hoverColor="#FFBB12"
                      [svgUrl]="'assets/svg/icon-preview-download.svg'"
                      height="1.5rem"
                      width="1.5rem"></span>
                  </button>

                  <button
                    kendoButton
                    #parentRefresh
                    class="!t-p-[0.3rem]"
                    fillMode="clear"
                    size="none">
                    <span
                      [parentElement]="parentRefresh.element"
                      venioSvgLoader
                      hoverColor="#FFBB12"
                      [svgUrl]="'assets/svg/icon-preview-refresh.svg'"
                      height="1.5rem"
                      width="1.5rem"></span>
                  </button>
                </div>
                <div
                  class="t-flex t-border t-border-l-0 t-border-r-0 t-border-b-0 t-border-t-1 t-border-[#dbdbdb] t-p-4">
                  <img
                    src="https://square-mailstrom-production.s3.amazonaws.com/invoices/templates/img/en-us-invoice-template-576474.jpg?v=2"
                    alt="preview"
                    class="t-w-full" />
                </div>
              </div>
            </ng-template>
          </kendo-tabstrip-tab>
          <kendo-tabstrip-tab title="Extracted Text">
            <ng-template kendoTabContent>
              <div class="content">
                <span>Dummy content #### - part 1</span>
              </div>
            </ng-template>
          </kendo-tabstrip-tab>
          <kendo-tabstrip-tab title="PDF">
            <ng-template kendoTabContent>
              <div class="content">
                <span>Dummy content #### - part 2</span>
              </div>
            </ng-template>
          </kendo-tabstrip-tab>
        </kendo-tabstrip>
      </div>
    </div>

    <div
      class="t-flex-none t-flex-wrap t-w-2/6 t-border t-border-1 t-border-[#dbdbdb]">
      <div
        class="t-flex t-flex-1 t-justify-between t-items-center t-p-1.5 t-border t-border-l-0 t-border-r-0 t-border-b-1 t-border-t-0 t-border-[#dbdbdb] t-self-start t-px-4">
        <div class="t-flex-none t-font-bold t-text-base">Tags</div>
        <div class="t-flex-none">
          <button
            kendoButton
            #parentFullscreenTag
            class="!t-p-[0.3rem]"
            fillMode="clear"
            size="none">
            <span
              [parentElement]="parentFullscreenTag.element"
              venioSvgLoader
              hoverColor="#FFBB12"
              [svgUrl]="'assets/svg/icon-review-fullscreen.svg'"
              height="1.1rem"
              width="1.1rem"></span>
          </button>
        </div>
      </div>

      <div class="t-flex">
        <div
          class="t-flex t-flex-1 t-flex-col t-overflow-y-auto t-h-[calc(100vh-8rem)]">
          <div class="t-flex">
            <div
              class="t-flex t-flex-wrap t-gap-3 t-self-start t-p-3 t-basis-auto t-place-items-center">
              <div class="t-block t-flex-none t-w-7/12">
                <kendo-textbox
                  class="!t-border-[#ccc]"
                  placeholder="Search For Tags"
                  [clearButton]="true"
                  (valueChange)="filterTerm = $event">
                  <ng-template kendoTextBoxSuffixTemplate>
                    <kendo-textbox-separator></kendo-textbox-separator>
                    <button kendoButton fillMode="clear">
                      <span
                        venioSvgLoader
                        svgUrl="assets/svg/icon-search.svg"
                        height="1rem"
                        width="1rem"></span>
                    </button>
                  </ng-template>
                </kendo-textbox>
              </div>

              <div
                class="t-flex t-flex-1 t-gap-0 t-w-fit t-border t-rounded t-overflow-hidden">
                <button
                  kendoButton
                  #parentElTag
                  *ngFor="let icon of svgIconForTagControls"
                  class="!t-p-[0.3rem] t-h-fit t-w-1/5 t-shadow t-shadow-slate-200"
                  (click)="browseActionClicked(icon.actionType)"
                  size="none"
                  rounded="none">
                  <span
                    [parentElement]="parentElTag.element"
                    venioSvgLoader
                    [svgUrl]="icon.iconPath"
                    height="0.85rem"
                    hoverColor="#979797"
                    color="#979797"
                    width="1rem"></span>
                </button>
              </div>
            </div>
          </div>

          <div class="t-flex t-p-3 t-w-full">
            <div class="t-flex t-w-full">
              <kendo-treeview
                [nodes]="data"
                textField="text"
                kendoTreeViewHierarchyBinding
                childrenField="items"
                [filter]="filterTerm"
                kendoTreeViewExpandable
                [expandedKeys]="expandedKeys"
                kendoTreeViewCheckable
                [(checkedKeys)]="checkedKeys"
                class="v-custom-treeview !t-max-h-60 !t-w-full">
              </kendo-treeview>
            </div>
          </div>

          <div class="t-flex">
            <div class="t-flex t-p-3 t-flex-col t-w-full">
              <div class="t-flex-none t-font-bold t-text-base">Coding</div>

              <div class="t-flex t-mt-3">
                <form class="t-flex t-w-full t-flex-col t-gap-3">
                  <div showHints="always">
                    <kendo-label
                      for="address"
                      class="t-text-xs t-uppercase t-mb-3 t-tracking-widest">
                      Address <span class="t-text-error">*</span>
                    </kendo-label>

                    <kendo-textbox
                      formControlName="address"
                      placeholder="Input Value"
                      #address></kendo-textbox>
                  </div>

                  <div showHints="always">
                    <kendo-label
                      for="phone"
                      class="t-text-xs t-uppercase t-mb-3 t-tracking-widest">
                      Phone <span class="t-text-error">*</span>
                    </kendo-label>

                    <kendo-textbox
                      formControlName="phone"
                      placeholder="Input Value"
                      #phone
                      class="t-w-full"></kendo-textbox>
                  </div>
                </form>
              </div>

              <div class="t-flex t-mt-3">
                <kendo-tabstrip class="t-w-full t-h-full">
                  <kendo-tabstrip-tab title="Metadata" [selected]="true">
                    <ng-template kendoTabContent>
                      <div class="content">
                        <div class="t-flex t-p-2 t-mt-2">
                          <div class="t-w-1/3">
                            <kendo-label class="t-font-bold">
                              Internal file ID
                            </kendo-label>
                            <div
                              class="t-text-sm t-text-[#7e7e7e] t-pl-1 t-mt-1"
                              formControlName="phone">
                              161616
                            </div>
                          </div>
                          <div class="t-w-1/3">
                            <kendo-label class="t-font-bold">
                              Custodian Name
                            </kendo-label>
                            <div
                              class="t-text-sm t-text-[#7e7e7e] t-pl-1 t-mt-1">
                              Custodian Name
                            </div>
                          </div>
                          <div class="t-w-1/3">
                            <kendo-label class="t-font-bold">
                              Size
                            </kendo-label>
                            <div
                              class="t-text-sm t-text-[#7e7e7e] t-pl-1 t-mt-1">
                              21405
                            </div>
                          </div>
                        </div>

                        <div class="t-flex t-p-2 t-mt-2">
                          <div class="t-w-1/2">
                            <kendo-label class="t-font-bold">
                              File Extension
                            </kendo-label>
                            <div
                              class="t-text-sm t-text-[#7e7e7e] t-pl-1 t-mt-1">
                              mse
                            </div>
                          </div>
                          <div class="t-w-1/2">
                            <kendo-label class="t-font-bold">
                              Is Parent
                            </kendo-label>
                            <div
                              class="t-text-sm t-text-[#7e7e7e] t-pl-1 t-mt-1">
                              Yes
                            </div>
                          </div>
                        </div>

                        <div class="t-flex t-p-2 t-mt-2">
                          <div class="t-w-full">
                            <kendo-label class="t-font-bold">
                              Group Hash Value
                            </kendo-label>
                            <div
                              class="t-text-sm t-text-[#7e7e7e] t-pl-1 t-mt-1">
                              D6F1BBD5073F9A556CED1F6B790541D2
                            </div>
                          </div>
                        </div>

                        <div class="t-flex t-p-2 t-mt-2">
                          <div class="t-w-full">
                            <kendo-label class="t-font-bold">
                              Relative File Folder Path
                            </kendo-label>
                            <div
                              class="t-text-sm t-text-[#7e7e7e] t-pl-1 t-mt-1">
                              \benjamin_rogers\benjamin_rogers_000_1_1.pst\Personal
                              folders\rogers-b\Benjamin_Rogers_Mar2002\Rogers,
                              Benjamin
                            </div>
                          </div>
                        </div>
                      </div>
                    </ng-template>
                  </kendo-tabstrip-tab>
                  <kendo-tabstrip-tab title="Coding History">
                    <ng-template kendoTabContent>
                      <div class="content">
                        <span>Dummy content #### - part 1</span>
                      </div>
                    </ng-template>
                  </kendo-tabstrip-tab>
                </kendo-tabstrip>
              </div>
            </div>
          </div>
        </div>

        <div
          class="t-flex t-flex-col t-border t-border-l-1 t-border-r-0 t-border-b-0 t-border-t-0 t-border-[#dbdbdb]">
          <button
            kendoButton
            #parentEl
            *ngFor="let icon of svgIconForTagsToolbar"
            class="!t-p-[0.85rem] !t-border !t-border-t-1 !t-border-l-0 !t-border-r-0 !t-border-b-1 !t-border-[#dbdbdb]"
            (click)="browseActionClicked(icon.actionType)"
            fillMode="clear"
            size="none">
            <span
              [parentElement]="parentEl.element"
              venioSvgLoader
              hoverColor="#FFBB12"
              [svgUrl]="icon.iconPath"
              height="1.3rem"
              width="1.3rem"></span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
