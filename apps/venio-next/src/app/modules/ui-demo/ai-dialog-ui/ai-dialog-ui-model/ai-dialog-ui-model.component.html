<kendo-dialog-titlebar (close)="onClose()">
  <div class="t-flex t-w-[65%] t-justify-between">
    <div class="t-block">
      <!-- Image is used to avoid active,focus and unnecessary effects on button -->
      <img
        src="assets/svg/icon-ai-smooth-handmade-stars.svg"
        alt="AI Venio"
        class="t-w-16 t-h-[51px]" />
    </div>
  </div>
</kendo-dialog-titlebar>

<div
  class="t-flex"
  [ngClass]="{
    't-mb-8': !isAiResult,
    'v-custom-magic-ai-box': isFocused,
    'v-custom-magic-ai-box-default': !isFocused
  }">
  <kendo-textarea
    placeholder="Search"
    resizable="auto"
    flow="horizontal"
    (focus)="onFocus()"
    (valueChange)="searchTermValue($event)"
    (blur)="onBlur()"
    (keydown)="handleKeydown($event)"
    [ngClass]="{ 'v-empty-textarea': aiSearchTerm === '' }">
    class="t-w-full t-overflow-x-hidden v-hide-scrollbar">
    <kendo-textarea-suffix>
      <button
        kendoButton
        (click)="onAiSearch()"
        class="t-p-0 t-px-2 t-m-0 v-custom-ai-search-btn"
        [disabled]="aiSearchTerm.length === 0"
        fillMode="link">
        <span
          venioSvgLoader
          applyEffectsTo="stroke"
          color="#1EBADC"
          svgUrl="assets/svg/icon-enter-key-down.svg"
          title="Press Enter"
          height="1.5rem"
          width="1.5rem"></span>
      </button>
    </kendo-textarea-suffix>
  </kendo-textarea>
</div>

<div
  class="t-flex t-w-full t-mt-5 t-transition-all t-ease-in t-delay-300 t-gap-[2%] v-hide-scrollbar t-overflow-x-hidden t-relative"
  [ngClass]="{
    't-h-0 t-opacity-0 t-overflow-y-hidden': !isAiResult,
    't-h-[27rem] t-opacity-100': isAiResult
  }">
  <div
    class="t-flex t-flex-col t-w-[49%] t-rounded-md t-border-[1px] t-border-[#cccccc]">
    <div class="t-block t-p-2">
      <kendo-tabstrip (tabSelect)="onSelect($event)">
        <kendo-tabstrip-tab title="Progress" [selected]="true">
          <ng-template kendoTabContent>
            <div class="t-flex t-p-4 t-mt-4 t-h-[336px] v-hide-scrollbar">
              "Show the conversation between the user and the AI"
            </div>
          </ng-template>
        </kendo-tabstrip-tab>
        <kendo-tabstrip-tab title="Doc Summary">
          <ng-template kendoTabContent>
            <div
              class="t-flex t-max-h-[360px] t-overflow-x-hidden v-hide-scrollbar t-flex-col t-w-full">
              <div class="t-flex t-flex-col t-w-full t-p-3">
                <div
                  class="t-block t-text-[#1EBADC] t-font-semibold t-sticky t-top-0 t-py-2 t-bg-white">
                  <kendo-popover
                    #searchTermPopover
                    [body]="aiSearchTerm"
                    [width]="360">
                  </kendo-popover>
                  <div
                    class="t-truncate t-inline-block t-max-w-full t-cursor-pointer"
                    kendoPopoverAnchor
                    [popover]="searchTermPopover"
                    showOn="hover"
                    (click)="checkSynopsis()">
                    {{ aiSearchTerm }}
                  </div>
                </div>

                <div class="t-flex t-flex-col t-gap-3 t-w-full t-mt-2 t-pr-2.5">
                  <div class="t-flex t-gap-5 t-w-full">
                    <div class="t-flex t-gap-2 t-flex-col t-min-w-[150px]">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        Internal file ID
                      </div>
                      <div class="t-block">6811</div>
                    </div>

                    <div class="t-flex t-flex-1 t-gap-2 t-flex-col">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        Original File Name
                      </div>
                      <div class="t-block">
                        Trade Counts by Country, Commodity, Category for January
                        11, 2001.msg
                      </div>
                    </div>
                  </div>
                  <div class="t-flex t-gap-5 t-w-full">
                    <div class="t-flex t-gap-2 t-flex-col t-min-w-[150px]">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        Internal Extension
                      </div>
                      <div class="t-block">msg</div>
                    </div>

                    <div class="t-flex t-flex-1 t-gap-2 t-flex-col">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        Original File Path
                      </div>
                      <div class="t-block">
                        \\EZCREMA\P123ASD-123\POS\ROBIT\K09\andy-zippe\
                      </div>
                    </div>
                  </div>
                  <div class="t-flex t-gap-5 t-w-full">
                    <div class="t-flex t-gap-2 t-flex-col t-min-w-[150px]">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        Internal file ID
                      </div>
                      <div class="t-block">946501</div>
                    </div>

                    <div class="t-flex t-flex-1 t-gap-2 t-flex-col">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        Original File Name
                      </div>
                      <div class="t-block">~~DLNKS.URL</div>
                    </div>
                  </div>
                  <div class="t-flex t-gap-5 t-w-full">
                    <div class="t-flex t-gap-2 t-flex-col t-min-w-[150px]">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        File Extension
                      </div>
                      <div class="t-block">txt</div>
                    </div>

                    <div class="t-flex t-flex-1 t-gap-2 t-flex-col">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        Original File Path
                      </div>
                      <div class="t-block">
                        \\EZCREMA\P123ASD-123\POS\ROBIT\K09\andy-zippe\posk-pqp\asspp-eeq\001
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Cloned div can be removed-->
              <div class="t-flex t-flex-col t-w-full t-p-3">
                <div
                  class="t-block t-text-[#1EBADC] t-font-semibold t-sticky t-top-0 t-py-2 t-bg-white">
                  <kendo-popover
                    #searchTermPopover
                    [body]="aiSearchTerm"
                    [width]="360">
                  </kendo-popover>
                  <div
                    class="t-truncate t-inline-block t-max-w-full t-cursor-pointer"
                    kendoPopoverAnchor
                    [popover]="searchTermPopover"
                    showOn="hover"
                    (click)="checkSynopsis()">
                    All files with extension msg and txt
                  </div>
                </div>

                <div class="t-flex t-flex-col t-gap-3 t-w-full t-mt-2 t-pr-2.5">
                  <div class="t-flex t-gap-5 t-w-full">
                    <div class="t-flex t-gap-2 t-flex-col t-min-w-[150px]">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        Internal file ID
                      </div>
                      <div class="t-block">6811</div>
                    </div>

                    <div class="t-flex t-flex-1 t-gap-2 t-flex-col">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        Original File Name
                      </div>
                      <div class="t-block">
                        Trade Counts by Country, Commodity, Category for January
                        11, 2001.msg
                      </div>
                    </div>
                  </div>
                  <div class="t-flex t-gap-5 t-w-full">
                    <div class="t-flex t-gap-2 t-flex-col t-min-w-[150px]">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        Internal Extension
                      </div>
                      <div class="t-block">msg</div>
                    </div>

                    <div class="t-flex t-flex-1 t-gap-2 t-flex-col">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        Original File Path
                      </div>
                      <div class="t-block">
                        \\EZCREMA\P123ASD-123\POS\ROBIT\K09\andy-zippe\
                      </div>
                    </div>
                  </div>
                  <div class="t-flex t-gap-5 t-w-full">
                    <div class="t-flex t-gap-2 t-flex-col t-min-w-[150px]">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        Internal file ID
                      </div>
                      <div class="t-block">946501</div>
                    </div>

                    <div class="t-flex t-flex-1 t-gap-2 t-flex-col">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        Original File Name
                      </div>
                      <div class="t-block">~~DLNKS.URL</div>
                    </div>
                  </div>
                  <div class="t-flex t-gap-5 t-w-full">
                    <div class="t-flex t-gap-2 t-flex-col t-min-w-[150px]">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        File Extension
                      </div>
                      <div class="t-block">txt</div>
                    </div>

                    <div class="t-flex t-flex-1 t-gap-2 t-flex-col">
                      <div class="t-text-primary t-uppercase t-font-semibold">
                        Original File Path
                      </div>
                      <div class="t-block">
                        \\EZCREMA\P123ASD-123\POS\ROBIT\K09\andy-zippe\posk-pqp\asspp-eeq\001
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-template>
        </kendo-tabstrip-tab>
      </kendo-tabstrip>
    </div>
  </div>

  <div class="t-flex t-flex-col t-w-[49%]">
    <div
      class="t-block t-p-2 t-w-full t-h-full t-rounded-md t-border-[1px] t-border-[#cccccc]"
      *ngIf="docSynopsis">
      <div class="t-flex t-flex-col t-w-full t-p-3">
        <div class="t-flex t-text-xl t-font-medium t-w-full t-mb-2 t-pl-1.5">
          Document Synopsis
        </div>

        <div
          class="t-flex t-max-h-[360px] t-overflow-x-hidden v-hide-scrollbar t-flex-col t-gap-2 t-w-full t-pl-1.5">
          <div class="t-flex t-flex-col t-gap-3 t-w-full t-pr-[5px]">
            <div class="t-flex t-w-full t-flex-col t-gap-3">
              <div
                class="t-sticky t-top-0 t-text-[#1EBADC] t-font-semibold t-mt-3 t-bg-white t-pr-2.5">
                <kendo-popover
                  #searchTermPopoverDocument
                  [body]="aiSearchTerm"
                  [width]="360">
                </kendo-popover>
                <div
                  class="t-truncate t-inline-block t-max-w-full t-cursor-pointer"
                  kendoPopoverAnchor
                  [popover]="searchTermPopoverDocument"
                  showOn="hover">
                  {{ aiSearchTerm }}
                </div>
              </div>

              <div class="t-w-full t-pr-2.5">
                In this comprehensive document, we delve into the intricacies of
                sustainable development practices in urban environments.
                Beginning with an exploration of historical precedents, we
                analyze current trends and propose innovative solutions for
                future challenges. Key topics include renewable energy
                integration, green infrastructure development, and community
                engagement strategies. Through case studies and empirical
                research, we highlight successful initiatives from around the
                globe, providing actionable insights for policymakers, urban
                planners, and environmental advocates alike. This document aims
                to inspire transformative change towards more resilient and
                environmentally conscious cities.
              </div>
            </div>
          </div>
          <!-- cloned div, can be removed-->
          <div class="t-flex t-flex-col t-gap-3 t-w-full t-pr-[5px]">
            <div class="t-flex t-w-full t-flex-col t-gap-3">
              <div
                class="t-sticky t-top-0 t-text-[#1EBADC] t-font-semibold t-mt-3 t-bg-white t-pr-2.5">
                <kendo-popover
                  #searchTermPopoverDocument
                  [body]="aiSearchTerm"
                  [width]="360">
                </kendo-popover>
                <div
                  class="t-truncate t-inline-block t-max-w-full t-cursor-pointer"
                  kendoPopoverAnchor
                  [popover]="searchTermPopoverDocument"
                  showOn="hover">
                  Search term here
                </div>
              </div>

              <div class="t-w-full t-pr-2.5">
                This document explores the evolution of artificial intelligence
                (AI) in healthcare, from its inception to cutting-edge
                applications today. We trace the development of AI algorithms in
                medical diagnostics, personalized treatment plans, and patient
                care management. Through case studies and expert interviews, we
                examine the ethical implications and regulatory challenges
                surrounding AI adoption in healthcare settings. Our findings
                highlight the transformative potential of AI in improving
                patient outcomes and operational efficiencies across healthcare
                systems worldwide. This comprehensive overview aims to inform
                healthcare professionals, policymakers, and technology
                developers about the current landscape and future directions of
                AI in healthcare.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<kendo-dialog-actions
  class="t-transition-all t-ease-in t-delay-300 v-hide-scrollbar"
  [ngClass]="{
    't-h-0 t-opacity-0 t-overflow-y-hidden': !isAiResult,
    't-h-[70.4px] t-opacity-100': isAiResult
  }">
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="onClose()"
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      data-qa="save-button">
      DONE
    </button>
  </div>
</kendo-dialog-actions>
