import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Output,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { UiPaginationModule } from '@venio/ui/pagination'

@Component({
  selector: 'venio-ai-dialog-ui-model',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    InputsModule,
    TooltipsModule,
    IconsModule,
    LayoutModule,
    GridModule,
    UiPaginationModule,
    DropDownsModule,
    SvgLoaderDirective,
    TooltipsModule,
  ],
  templateUrl: './ai-dialog-ui-model.component.html',
  styleUrl: './ai-dialog-ui-model.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AiDialogUiModelComponent {
  @Output() public readonly searchEventClicked = new EventEmitter<void>()

  public tabStatus = 0

  public isAiResult = false

  public isFocused = false

  public aiSearchTerm = ''

  public docSynopsis = false

  constructor(private dialogRef: DialogRef) {}

  public onClose(): void {
    this.dialogRef.close()
  }

  public onSelect(e: any): void {
    this.tabStatus = e.index
    this.docSynopsis = this.tabStatus === 0 ? true : false
  }

  public onAiSearch(): void {
    if (this.aiSearchTerm.length > 0) {
      this.isAiResult = true
      this.searchEventClicked.emit()
    }
  }

  public onFocus(): void {
    this.isFocused = true
  }

  public onBlur(): void {
    this.isFocused = false
  }

  public searchTermValue(term: string): void {
    this.aiSearchTerm = term
  }

  public checkSynopsis(): void {
    this.docSynopsis = !this.docSynopsis
  }

  public handleKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      if (this.aiSearchTerm.length > 0) {
        event.preventDefault() // Prevent the default action (new line)
        this.onAiSearch()
      }
    }
  }
}
