<div class="t-flex t-h-screen v-login-bg">
  <!-- Left Pane: Logo -->
  <div class="t-w-3/6 t-relative t-flex t-items-center t-justify-center">
    <div class="t-absolute t-inset-0 t-bg-[#b6c66a] t-opacity-20"></div>

    <div class="t-relative t-z-10">
      <img
        src="assets/img/venio-logo-full-vertical.png"
        alt="Venio"
        class="t-h-[9rem] t-w-[10.75rem] t-object-contain" />
    </div>
  </div>

  <!-- Right Pane: Login Form -->
  <div class="t-w-3/6 t-flex t-items-center t-items-start t-p-7 t-bg-white">
    @if(forgotPwd){
    <div class="t-bg-white t-p-9 t-mx-3 t-w-full">
      <venio-login-page-forgot-pwd></venio-login-page-forgot-pwd>
    </div>
    } @else {
    <div class="t-bg-white t-p-9 t-mx-3 t-w-[22rem]">
      <div>
        <h1 class="t-text-[#030303] t-text-4xl t-font-black t-mb-3 t-text-left">
          LOGIN
        </h1>

        <p class="t-text-[#FFBB12] t-text-2xl t-font-black t-mb-6 t-text-left">
          Please enter your details.
        </p>
      </div>

      <form [formGroup]="loginForm" (ngSubmit)="onLogin()">
        <div class="t-mb-4 t-flex t-flex-col t-items-start">
          <kendo-textbox
            formControlName="email"
            placeholder="Email"
            class="t-w-full t-rounded"
            style="
              box-shadow: 0 1px 2px 0 rgba(55, 65, 81, 0.08);
            "></kendo-textbox>
          <kendo-formerror
            *ngIf="
              loginForm.controls.email.touched &&
              loginForm.controls.email.invalid
            "
            >Please enter a valid email address</kendo-formerror
          >
        </div>

        <div class="t-mb-4">
          <kendo-textbox
            formControlName="password"
            placeholder="Password"
            [type]="PasswordValid ? 'text' : 'password'"
            class="t-w-full t-rounded v-input-shadow">
            <ng-template kendoTextBoxSuffixTemplate>
              <button
                kendoButton
                class="t-pr-2"
                look="clear"
                (click)="PasswordValid = !PasswordValid">
                @if(PasswordValid) {
                <kendo-svgicon [icon]="icons.eyeIcon"></kendo-svgicon>
                } @else {
                <kendo-svgicon [icon]="icons.slashIcon"></kendo-svgicon>
                }
              </button>
            </ng-template>
          </kendo-textbox>
          <kendo-formerror
            *ngIf="
              loginForm.controls.password.touched &&
              loginForm.controls.password.errors
            "
            >Please enter a valid password</kendo-formerror
          >
        </div>

        <div class="t-flex t-items-center t-justify-between t-mb-4">
          <div class="t-flex t-items-center">
            <input
              #loggedin
              type="checkbox"
              formControlName="rememberMe"
              class="t-mr-2 t-rounded-xl" />
            <kendo-label
              for="loggedin"
              class="t-text-xs t-text-[#181818] t-font-medium"
              text="Remember me" />
          </div>
          <button
            kendoButton
            (click)="openDialog()"
            class="t-text-xs t-text-[#1EBADC] t-font-medium">
            Forgot Password?
          </button>
          <kendo-dialog
            *ngIf="forgotPassword"
            (close)="close()"
            [maxHeight]="416"
            [width]="'398px'">
            <kendo-dialog-titlebar>
              <button class="t-flex t-items-center" (click)="close()">
                <kendo-svgicon
                  [icon]="icons.leftIcon"
                  class="t-text-[#9BD2A7] t-w-8 t-h-7"></kendo-svgicon>
                <kendo-label class="t-text-sm t-font-black t-text-[#000000]">{{
                  dialogTitle
                }}</kendo-label>
              </button>
            </kendo-dialog-titlebar>

            <div class="t-flex t-flex-col t-items-center t-w-full t-p-2">
              <div class="t-flex t-w-full t-flex-col t-mt-4">
                <div class="t-flex t-flex-row t-items-start">
                  <div
                    class="t-w-[37px] t-h-[37px] t-flex t-items-center t-justify-center t-rounded-full t-bg-[#F1EFEF] t-mr-2">
                    <img
                      src="assets/svg/icon-forgot-password.svg"
                      alt="Forgot Password Icon" />
                  </div>
                  <div class="t-flex t-flex-col t-items-start t-justify-center">
                    <p
                      class="t-text-[#030303] t-text-[1.65rem] t-font-black t-mb-2">
                      FORGOT PASSWORD
                    </p>
                    <p
                      class="t-text-[#FFBB12] t-text-lg/5 t-font-black t-mb-6 t-align-left t-text-left t-mr-4">
                      Please enter your user name.
                    </p>
                  </div>
                </div>
              </div>
              <kendo-textbox
                class="t-w-full t-align-left t-mb-8 v-input-shadow"
                formControlName="forgotPasswordUsername"
                id="username"
                placeholder="User Name">
              </kendo-textbox>
              <button
                kendoButton
                class="t-bg-[#9BD2A7] t-text-white t-w-full t-py-2 t-px-4 t-mb-3 t-rounded-xl t-drop-shadow-md t-drop-shadow-[#9BD2A7] t-font-sans t-text-sm"
                (click)="onSubmitUsername()">
                Submit
              </button>
            </div>
          </kendo-dialog>
          <kendo-dialog
            *ngIf="emailSent"
            (close)="close()"
            [maxHeight]="416"
            [width]="'398px'">
            <kendo-dialog-titlebar>
              <button class="t-flex t-items-center" (click)="close()">
                <kendo-svgicon
                  [icon]="icons.leftIcon"
                  class="t-text-[#9BD2A7] t-w-8 t-h-7"></kendo-svgicon>
                <kendo-label class="t-text-xs t-font-black t-text-[#000000]">{{
                  dialogTitle
                }}</kendo-label>
              </button>
            </kendo-dialog-titlebar>
            <div class="t-flex t-items-start t-space-x-1 t-mt-4">
              <div class="t-flex-shrink-0">
                <div
                  class="t-w-[37px] t-h-[37px] t-flex t-items-center t-justify-center t-rounded-full t-bg-[#F1EFEF] t-mr-2">
                  <img
                    src="assets/svg/icon-email-new.svg"
                    alt="Forgot Password Icon"
                    class="t-items-start" />
                </div>
              </div>

              <div class="t-flex t-flex-col">
                <span
                  class="t-text-[#030303] t-text-[1.65rem] t-font-black t-mb-2"
                  >EMAIL SENT</span
                >
                <span
                  class="t-text-sm/4 t-font-normal t-mt-4 t-mb-10 t-align-left t-text-left"
                  >Email with reset password link was successfully sent to your
                  registered email ID.
                  <br />
                  <br />
                  Please click on the link and follow the instruction</span
                >
              </div>
            </div>
          </kendo-dialog>

          <kendo-dialog
            *ngIf="termsOfUse"
            (close)="close()"
            [maxHeight]="568"
            [height]="'62vh'"
            [minWidth]="250"
            [maxWidth]="1130"
            [width]="'60%'">
            <kendo-dialog-titlebar>
              <button class="t-flex t-items-center" (click)="close()">
                <kendo-svgicon
                  [icon]="icons.leftIcon"
                  class="t-text-[#9BD2A7] t-w-8 t-h-7"></kendo-svgicon>
                <kendo-label class="t-text-xs t-font-black t-text-[#000000]"
                  >BACK</kendo-label
                >
              </button>
            </kendo-dialog-titlebar>

            <div
              class="t-flex t-flex-col t-items-center t-justify-center t-p-4">
              <div class="t-flex t-flex-row t-items-start">
                <div class="t-flex t-flex-col t-items-start t-justify-center">
                  <p
                    class="t-text-[#030303] t-text-[1.75rem]/8 t-font-black t-mb-2">
                    TERMS OF USE
                  </p>
                  <p
                    class="t-text-xs/4 t-mt-4 t-font-normal t-mb-4 t-align-left t-text-left t-text-[#000000]">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                    Praesent et magna justo. Ut lacinia ante euismod, semper
                    diam at, maximus massa. Morbi consectetur, felis vel
                    hendrerit ultricies, neque massa eleifend nunc, nec
                    condimentum purus nunc et ex. Morbi sit amet luctus lacus.
                    Pellentesque dignissim eget arcu quis molestie. Maecenas
                    purus nulla, venenatis ac gravida non, auctor in odio. Morbi
                    placerat hendrerit quam et accumsan.
                    <br />
                    <br />
                    In finibus, nibh id feugiat malesuada, quam velit pretium
                    sem, et sodales ante lacus at urna. Nunc non est feugiat,
                    facilisis tortor ut, iaculis dui. Duis nisi mi, fermentum
                    placerat diam ut, facilisis varius odio. Phasellus
                    scelerisque dapibus eros et tristique. Vestibulum rhoncus,
                    ipsum et ultrices tempus, ante mi iaculis sem, at volutpat
                    ex ipsum sed augue. Phasellus id feugiat metus, ac iaculis
                    turpis. Duis tempus massa quis tincidunt tempor. Nunc a
                    sodales turpis, a faucibus libero. Fusce facilisis rutrum
                    enim, id sollicitudin sem suscipit eu. Vestibulum ut purus
                    sem. Vivamus tempus rutrum diam ornare dapibus. Nunc nisl
                    ante, venenatis ac interdum sit amet, lobortis sit amet
                    mauris.
                    <br />
                    <br />
                    Vivamus ultricies urna sed ante placerat aliquam. Sed ornare
                    sem nulla, eget dignissim nisi hendrerit id. Aliquam feugiat
                    erat elit. Curabitur consequat auctor erat dignissim
                    volutpat. Quisque augue dui, mattis sit amet lacus in,
                    viverra consequat ante. Mauris ultrices elit eros, vitae
                    lobortis turpis molestie ut. Duis hendrerit dignissim quam
                    et interdum.
                    <br />
                    <br />
                    Aliquam interdum sollicitudin orci nec consequat. Curabitur
                    sodales lorem sapien, vitae sagittis nisl vehicula in.
                    Vestibulum ac tincidunt diam. Curabitur sit amet ex libero.
                    Cras ac lorem eu enim aliquam convallis. Suspendisse augue
                    arcu, mattis a sem in, tristique lacinia ex. Vestibulum
                    consequat ultricies lacus sed tristique.
                    <br />
                    <br />
                  </p>
                </div>
              </div>
            </div>
          </kendo-dialog>
        </div>

        <div>
          <button
            kendoButton
            class="t-bg-[#9BD2A7] t-text-white t-w-full t-py-2 t-px-4 t-mb-3 t-rounded-lg t-drop-shadow-md t-drop-shadow-[##9BD2A7] t-font-sans t-text-sm"
            (click)="onLogin()"
            [disabled]="
              loginForm.controls.password.errors ||
              loginForm.controls.email.errors ||
              isSubmitting
            ">
            Login
          </button>
        </div>

        <div class="t-flex t-items-center t-justify-between t-mb-6 t-mt-4">
          <label class="t-flex t-items-center">
            <span class="t-text-xs t-font-sans t-font-medium">v10.17.0.0</span>
          </label>
          <button
            kendoButton
            class="t-text-xs t-text-[#2F3080] t-font-medium"
            (click)="onResetPassword()">
            Reset Password
          </button>
          <kendo-dialog
            *ngIf="resetPassword"
            (close)="close()"
            [minWidth]="250"
            [maxWidth]="398"
            [width]="'60%'">
            <kendo-dialog-titlebar>
              <button class="t-flex t-items-center" (click)="close()">
                <kendo-svgicon
                  [icon]="icons.leftIcon"
                  class="t-text-[#9BD2A7] t-w-8 t-h-7"></kendo-svgicon>
                <kendo-label class="t-text-xs t-font-black t-text-[#000000]">{{
                  dialogTitle
                }}</kendo-label>
              </button>
            </kendo-dialog-titlebar>

            <div
              class="t-flex t-flex-col t-items-center t-justify-center t-p-4">
              <div class="t-flex t-w-full t-flex-col">
                <div class="t-flex t-flex-row t-items-start">
                  <div
                    class="t-w-[37px] t-h-[37px] t-flex t-items-center t-justify-center t-rounded-full t-bg-[#F1EFEF] t-mr-2">
                    <img
                      src="assets/svg/icon-reset-password.svg"
                      alt="Reset Password Icon" />
                  </div>
                  <div class="t-flex t-flex-col t-items-start t-justify-center">
                    <p
                      class="t-text-[#030303] t-text-[1.65rem] t-font-black t-mb-2">
                      RESET PASSWORD
                    </p>
                    <p
                      class="t-text-[#FFBB12] t-text-lg/5 t-font-black t-mb-6 t-align-left t-text-left t-mr-4">
                      Please enter new password.
                    </p>
                  </div>
                </div>
              </div>
              <kendo-formfield class="t-w-full t-align-left t-mb-2">
                <kendo-textbox
                  class="t-w-full t-align-left v-input-shadow"
                  formControlName="password"
                  [type]="!PasswordValid ? 'text' : 'password'"
                  id="password"
                  placeholder="Current Password">
                  <ng-template kendoTextBoxSuffixTemplate>
                    <button
                      kendoButton
                      look="clear"
                      class="t-pr-2"
                      (click)="PasswordValid = !PasswordValid">
                      @if(!PasswordValid) {
                      <kendo-svgicon [icon]="icons.eyeIcon"></kendo-svgicon>
                      } @else {
                      <kendo-svgicon [icon]="icons.slashIcon"></kendo-svgicon>
                      }
                    </button>
                  </ng-template>
                </kendo-textbox>
                <kendo-formerror
                  *ngIf="
                    loginForm.controls.password.touched &&
                    loginForm.controls.password.errors?.required
                  "
                  class="t-m-1"
                  >Please enter a valid password</kendo-formerror
                >
              </kendo-formfield>
              <kendo-formfield class="t-w-full t-align-left t-mb-2">
                <kendo-textbox
                  class="t-w-full t-align-left v-input-shadow t-mr-4"
                  formControlName="newPassword"
                  [type]="newPasswordValid ? 'text' : 'password'"
                  id="newPassword"
                  placeholder="New Password">
                  <ng-template kendoTextBoxSuffixTemplate>
                    <button
                      kendoButton
                      class="t-pr-2"
                      look="clear"
                      (click)="newPasswordValid = !newPasswordValid">
                      @if(newPasswordValid) {
                      <kendo-svgicon [icon]="icons.eyeIcon"></kendo-svgicon>
                      } @else {
                      <kendo-svgicon [icon]="icons.slashIcon"></kendo-svgicon>
                      }
                    </button>
                  </ng-template>
                </kendo-textbox>
                <kendo-formerror
                  *ngIf="
                    loginForm.controls.newPassword.touched &&
                    loginForm.controls.newPassword.errors?.required
                  "
                  class="t-m-1"
                  >Please enter a valid password</kendo-formerror
                >
              </kendo-formfield>
              <kendo-formfield class="t-w-full t-align-left t-mb-2">
                <kendo-textbox
                  class="t-w-full t-align-left v-input-shadow t-mr-4"
                  formControlName="retypeNewPassword"
                  [type]="retypeNewPasswordValid ? 'text' : 'password'"
                  id="confirmNewPassword"
                  placeholder="Retype New Password">
                  <ng-template kendoTextBoxSuffixTemplate>
                    <button
                      kendoButton
                      class="t-pr-2"
                      look="clear"
                      (click)="
                        retypeNewPasswordValid = !retypeNewPasswordValid
                      ">
                      @if(retypeNewPasswordValid) {
                      <kendo-svgicon [icon]="icons.eyeIcon"></kendo-svgicon>
                      } @else {
                      <kendo-svgicon [icon]="icons.slashIcon"></kendo-svgicon>
                      }
                    </button>
                  </ng-template>
                </kendo-textbox>
                <kendo-formerror
                  *ngIf="
                    loginForm.controls.retypeNewPassword.touched &&
                    loginForm.controls.retypeNewPassword.errors?.required
                  "
                  >Please enter a valid password</kendo-formerror
                >
              </kendo-formfield>
              <div
                *ngIf="passwordDontMatch"
                class="t-text-[##ED7425] t-text-[#ED7425] t-mb-1">
                Password does not match
              </div>
              <button
                kendoButton
                class="t-bg-[#9BD2A7] t-text-white t-w-full t-py-2 t-px-4 t-mb-3 t-mt-2 t-h-[2.5rem] t-rounded-xl t-drop-shadow-md t-drop-shadow-[#9BD2A7] t-font-sans t-text-sm"
                (click)="onReset()">
                Reset
              </button>
            </div>
          </kendo-dialog>
          <kendo-dialog
            *ngIf="successResetPassword"
            (close)="close()"
            [maxHeight]="208"
            [height]="'32vh'"
            [minWidth]="250"
            [maxWidth]="398"
            [width]="'60%'">
            <kendo-dialog-titlebar>
              <button class="t-flex t-items-center" (click)="close()">
                <kendo-svgicon
                  [icon]="icons.leftIcon"
                  class="t-text-[#9BD2A7] t-w-8 t-h-7"></kendo-svgicon>
                <kendo-label class="t-text-xs t-font-black t-text-[#000000]">{{
                  dialogTitle
                }}</kendo-label>
              </button>
            </kendo-dialog-titlebar>
            <p
              class="t-m-[3rem] t-text-center t-text-[#030303] t-text-lg/5 t-font-normal">
              {{ passwordChangeStatus }}
            </p>
          </kendo-dialog>
        </div>
      </form>
      <p class="t-mt-6 t-text-xs t-w-full t-font-normal">
        By Logging in you agree to the
        <button kendoButton class="t-text-[#1E97E1]" (click)="onTermsOfUse()">
          Terms of Service
        </button>
        and
        <button kendoButton class="t-text-[#1E97E1]" (click)="onTermsOfUse()">
          Privacy Policy</button
        >.
      </p>
    </div>
    }
  </div>
</div>
