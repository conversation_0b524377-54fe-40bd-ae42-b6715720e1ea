import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LoginPageForgotPwdComponent } from './login-page-forgot-pwd.component'
import { HttpClientTestingModule } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('LoginPageForgotPwdComponent', () => {
  let component: LoginPageForgotPwdComponent
  let fixture: ComponentFixture<LoginPageForgotPwdComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        LoginPageForgotPwdComponent,
        HttpClientTestingModule,
        NoopAnimationsModule,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(LoginPageForgotPwdComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
