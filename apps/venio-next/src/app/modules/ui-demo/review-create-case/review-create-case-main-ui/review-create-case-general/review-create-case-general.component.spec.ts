import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewCreateCaseGeneralComponent } from './review-create-case-general.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('ReviewCreateCaseGeneralComponent', () => {
  let component: ReviewCreateCaseGeneralComponent
  let fixture: ComponentFixture<ReviewCreateCaseGeneralComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewCreateCaseGeneralComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewCreateCaseGeneralComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
