import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewCreateCaseSourceComponent } from './review-create-case-source.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('ReviewCreateCaseSourceComponent', () => {
  let component: ReviewCreateCaseSourceComponent
  let fixture: ComponentFixture<ReviewCreateCaseSourceComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewCreateCaseSourceComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewCreateCaseSourceComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
