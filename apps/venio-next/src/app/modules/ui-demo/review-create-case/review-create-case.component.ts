import {
  ChangeDetectionStrategy,
  Component,
  OnInit,
  TemplateRef,
  viewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { WindowModule, WindowService } from '@progress/kendo-angular-dialog'
import { ButtonsModule } from '@progress/kendo-angular-buttons'

@Component({
  selector: 'venio-review-create-case',
  standalone: true,
  imports: [CommonModule, ButtonsModule, WindowModule],
  templateUrl: './review-create-case.component.html',
  styleUrl: './review-create-case.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewCreateCaseComponent implements OnInit {
  public readonly windowTitleBar =
    viewChild<TemplateRef<unknown>>('windowTitleBar')

  constructor(public windowService: WindowService) {}

  public ngOnInit(): void {
    this.reprocess()
  }

  private reprocess(): void {
    import(
      './review-create-case-main-ui/review-create-case-main-ui.component'
    ).then((td) => {
      this.windowService.open({
        content: td.ReviewCreateCaseMainUiComponent, // Dynamically load main content
        left: 0,
        state: 'maximized',
        top: 100,
        cssClass: 'v-custom-window',
        titleBarContent: this.windowTitleBar(),
        resizable: false,
        draggable: false,
      })
    })
  }
}
