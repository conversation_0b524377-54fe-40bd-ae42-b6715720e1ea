import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewCreateCaseComponent } from './review-create-case.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('ReviewCreateCaseComponent', () => {
  let component: ReviewCreateCaseComponent
  let fixture: ComponentFixture<ReviewCreateCaseComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewCreateCaseComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewCreateCaseComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
