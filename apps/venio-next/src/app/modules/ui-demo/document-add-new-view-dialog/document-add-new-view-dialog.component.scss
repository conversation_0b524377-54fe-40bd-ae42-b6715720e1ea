@layer {
  .v-custom-label {
    background: transparent linear-gradient(270deg, #1ebadc 0%, #73c8bf 100%) 0
      0 no-repeat;
    writing-mode: vertical-rl;
    @apply t-rotate-180 t-text-white #{!important};
  }
  .v-custom-condition-block {
    &__hover {
      background-color: rgb(255 255 255 / 59%);
      // reason for not adding this line in the html is because this can be modified easily in the scss
      // for e.g changing the hover block position or styling
      @apply t-flex-wrap t-gap-2 t-absolute t-invisible t-rounded t-p-1 t-top-1 t-right-[30px] #{!important};
    }
    .v-content-wrapper {
      &:hover {
        .v-custom-condition-block__hover {
          // this is important the kendodoprodnmenu popover is not working with the display none on hover so i used the visibility instead
          @apply t-visible  #{!important};
        }
      }
    }
  }
}
