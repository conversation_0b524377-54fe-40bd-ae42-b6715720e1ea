<div class="t-flex t-p-8">
  <button kendoButton (click)="openDialog()">Document Add New View</button>
</div>

<kendo-dialog
  *ngIf="opened"
  (close)="close('cancel')"
  [maxHeight]="580"
  [height]="'70vh'"
  [minWidth]="250"
  [width]="'80%'">
  <kendo-dialog-titlebar>
    <div>
      {{ dialogTitle }}
    </div>
  </kendo-dialog-titlebar>

  <div class="t-flex t-flex-col">
    <div class="t-flex t-gap-5">
      <div class="t-flex t-flex-1 t-flex-col t-w-full">
        <!-- new content -->
        <div class="t-flex t-w-full">
          <div
            class="t-flex t-w-[50%] t-border t-border-t-0 t-border-b-0 t-border-l-0 !t-border-r-[#ebebeb] t-pr-2">
            <kendo-tabstrip class="t-w-full t-h-full">
              <kendo-tabstrip-tab title="Information" [selected]="true">
                <ng-template kendoTabContent>
                  <div class="t-flex t-p-4 t-w-full v-custom-grey-bg t-mt-3">
                    <form class="t-flex t-flex-1 t-flex-wrap t-w-full t-gap-3">
                      <div
                        showHints="always"
                        class="t-flex t-flex-0 t-basis-[48.8%] t-gap-1 t-flex-col">
                        <kendo-label
                          for="systemFields"
                          class="t-text-xs t-uppercase t-tracking-widest">
                          System Fields <span class="t-text-error">*</span>
                        </kendo-label>

                        <kendo-dropdownlist
                          #systemFields
                          [defaultItem]="['Document']"
                          data-qa="systemFields"
                          [data]="[]"
                          textField="text"
                          valueField="value">
                        </kendo-dropdownlist>
                      </div>

                      <div
                        showHints="always"
                        class="t-flex t-flex-0 t-basis-[48.8%] t-gap-1 t-flex-col">
                        <kendo-label
                          for="viewName"
                          class="t-text-xs t-uppercase t-tracking-widest">
                          NAME <span class="t-text-error">*</span>
                        </kendo-label>

                        <kendo-textbox
                          data-qa="viewName"
                          placeholder="View Name"
                          #viewName></kendo-textbox>
                      </div>

                      <div
                        showHints="always"
                        class="t-flex t-flex-0 t-basis-[48.8%] t-gap-1 t-flex-col">
                        <kendo-label
                          for="tagGroup"
                          class="t-text-xs t-uppercase t-tracking-widest">
                          OWNER <span class="t-text-error">*</span>
                        </kendo-label>

                        <kendo-dropdownlist
                          data-qa="tagGroup"
                          #tagGroup
                          [defaultItem]="['Public']"
                          [data]="[]"
                          textField="text"
                          valueField="value">
                        </kendo-dropdownlist>
                      </div>

                      <div
                        showHints="always"
                        class="t-flex t-flex-0 t-basis-[48.8%] t-gap-1 t-flex-col">
                        <kendo-label
                          class="t-text-xs t-uppercase t-tracking-widest">
                        </kendo-label>

                        <button kendoButton class="t-w-3" data-qa="me">
                          ME
                        </button>
                      </div>
                    </form>
                  </div>
                </ng-template>
              </kendo-tabstrip-tab>
            </kendo-tabstrip>
          </div>
          <div class="t-flex t-w-[50%] t-pl-2">
            <kendo-tabstrip class="t-w-full t-h-full">
              <kendo-tabstrip-tab title="Fields">
                <ng-template kendoTabContent>
                  <div class="t-flex t-mt-3 t-overflow-hidden">
                    <div class="t-flex t-w-[46%] t-flex-col t-gap-2">
                      <div
                        showHints="always"
                        class="t-flex t-flex-0 t-w-full t-flex-col">
                        <kendo-textbox
                          placeholder="Search"
                          data-qa="searchUnselected"
                          #viewName></kendo-textbox>
                      </div>

                      <!-- enable fiterable true to see the filter search, disbaled for custom searchbox above-->
                      <kendo-treelist
                        [kendoTreeListFlatBinding]="fakeData"
                        [navigatable]="true"
                        idField="id"
                        parentIdField="managerId"
                        kendoTreeListSelectable
                        [selectable]="settings"
                        [(selectedItems)]="selected"
                        kendoTreeListExpandable
                        [initiallyExpanded]="true"
                        [filterable]="false"
                        [sortable]="true"
                        [height]="280"
                        class="v-custom-view-tree"
                        data-qa="unselectedlist">
                        <kendo-treelist-checkbox-column
                          [width]="25"
                          [showSelectAll]="true">
                          select
                        </kendo-treelist-checkbox-column>
                        <kendo-treelist-column
                          [expandable]="true"
                          field="name"
                          title="UNSELECTED">
                          <ng-template
                            kendoTreeListFilterCellTemplate
                            let-filter
                            let-column="column">
                            <kendo-treelist-string-filter-cell
                              [column]="column"
                              [filter]="filter"
                              [showOperators]="false">
                            </kendo-treelist-string-filter-cell>
                          </ng-template>
                        </kendo-treelist-column>
                      </kendo-treelist>
                    </div>

                    <div
                      class="t-flex t-w-[8%] t-flex-col t-justify-center t-items-center">
                      <ul
                        class="t-flex t-flex-col t-gap-3 t-justify-center t-mt-20">
                        <li>
                          <button
                            kendoButton
                            fillMode="outline"
                            size="none"
                            class="!t-p-1 t-rotate-90"
                            data-qa="iconLeft"
                            #iconeNext>
                            <span
                              venioSvgLoader
                              [parentElement]="iconeNext.element"
                              svgUrl="assets/svg/icon-e-next-svg.svg"
                              hoverColor="#FFFFFF"
                              height=".95rem"
                              width=".95rem"></span>
                          </button>
                        </li>
                        <li>
                          <button
                            kendoButton
                            fillMode="outline"
                            size="none"
                            class="!t-p-1"
                            data-qa="iconRight"
                            #iconExchange>
                            <span
                              venioSvgLoader
                              [parentElement]="iconExchange.element"
                              svgUrl="assets/svg/icon-exchange-svg.svg"
                              hoverColor="#FFFFFF"
                              height=".95rem"
                              width=".95rem"></span>
                          </button>
                        </li>
                        <li>
                          <button
                            kendoButton
                            fillMode="outline"
                            size="none"
                            data-qa="iconRightArrow"
                            class="!t-p-1"
                            #iconRightArrow>
                            <span
                              venioSvgLoader
                              [parentElement]="iconRightArrow.element"
                              svgUrl="assets/svg/icon-right-arrow-svg.svg"
                              hoverColor="#FFFFFF"
                              height=".95rem"
                              width=".95rem"></span>
                          </button>
                        </li>
                        <li>
                          <button
                            kendoButton
                            fillMode="outline"
                            size="none"
                            class="!t-p-1"
                            data-qa="iconLeftArrow"
                            #iconLeft>
                            <span
                              venioSvgLoader
                              [parentElement]="iconLeft.element"
                              svgUrl="assets/svg/icon-arrow-left-5-svg.svg"
                              hoverColor="#FFFFFF"
                              height=".95rem"
                              width=".95rem"></span>
                          </button>
                        </li>
                        <li>
                          <button
                            kendoButton
                            fillMode="outline"
                            size="none"
                            data-qa="iconLeftArrow"
                            class="!t-p-1"
                            #iconElement>
                            <span
                              venioSvgLoader
                              [parentElement]="iconElement.element"
                              svgUrl="assets/svg/icon-e-next-svg.svg"
                              hoverColor="#FFFFFF"
                              height=".95rem"
                              width=".95rem"></span>
                          </button>
                        </li>
                      </ul>
                    </div>
                    <div class="t-flex t-w-[46%] t-flex-col t-gap-2">
                      <div
                        showHints="always"
                        class="t-flex t-flex-0 t-w-full t-flex-col">
                        <kendo-textbox
                          data-qa="searchSelected"
                          placeholder="Search"
                          #viewName></kendo-textbox>
                      </div>

                      <!-- enable fiterable true to see the filter search, disbaled for custom searchbox above-->
                      <kendo-treelist
                        [kendoTreeListFlatBinding]="fakeData"
                        [navigatable]="true"
                        idField="id"
                        parentIdField="managerId"
                        kendoTreeListSelectable
                        [selectable]="settings"
                        [(selectedItems)]="selected"
                        kendoTreeListExpandable
                        [initiallyExpanded]="true"
                        [filterable]="false"
                        [sortable]="true"
                        [height]="280"
                        class="v-custom-view-tree"
                        data-qa="selectedTree">
                        <kendo-treelist-checkbox-column
                          [width]="25"
                          [showSelectAll]="true">
                          select
                        </kendo-treelist-checkbox-column>
                        <kendo-treelist-column
                          [expandable]="true"
                          field="name"
                          title="SELECTED">
                          <ng-template
                            kendoTreeListFilterCellTemplate
                            let-filter
                            let-column="column">
                            <kendo-treelist-string-filter-cell
                              [column]="column"
                              [filter]="filter"
                              [showOperators]="false">
                            </kendo-treelist-string-filter-cell>
                          </ng-template>
                        </kendo-treelist-column>
                      </kendo-treelist>
                    </div>
                  </div>
                </ng-template>
              </kendo-tabstrip-tab>
              <kendo-tabstrip-tab title="Conditions" [selected]="true">
                <ng-template kendoTabContent>
                  <div class="t-flex t-flex-col">
                    <div
                      class="t-flex t-border t-border-[#2F3080] t-min-h-[62px] t-rounded t-mt-5 t-relative v-custom-condition-block">
                      <div
                        class="t-flex v-custom-label t-items-center t-justify-center">
                        <kendo-dropdownbutton
                          data-qa="andLabel1"
                          [data]="menuData"
                          fillMode="none"
                          class="v-custom-operator-menu">
                          AND
                        </kendo-dropdownbutton>
                      </div>
                      <div
                        class="t-flex t-flex-col t-w-full t-justify-center t-items-center"
                        *ngIf="block === 0">
                        <div class="t-flex t-items-center">
                          <span
                            (click)="block = 1"
                            class="t-flex t-items-center t-cursor-pointer v-custom-secondary-button t-text-[--tb-kendo-custom-secondary-100]">
                            <kendo-svg-icon
                              [icon]="icons.plusIconSvg"></kendo-svg-icon>
                            Click here </span
                          ><span class="t-pl-1"> to add field selection</span>
                        </div>
                      </div>

                      <div
                        class="t-flex t-px-5 t-py-6 t-w-full v-content-wrapper"
                        *ngIf="block === 1">
                        <!-- hover buttons-->
                        <div class="t-flex v-custom-condition-block__hover">
                          <div class="t-flex">
                            <button
                              kendoButton
                              fillMode="clear"
                              data-qa="iconAddNew1"
                              size="none"
                              #iconAddNew>
                              <span
                                venioSvgLoader
                                [parentElement]="iconAddNew.element"
                                svgUrl="assets/svg/icon-plus-large-svg.svg"
                                hoverColor="#FFBB12"
                                color="#979797"
                                height="1rem"
                                width="1rem"></span>
                            </button>
                          </div>
                          <div class="t-flex">
                            <button
                              kendoButton
                              fillMode="clear"
                              size="none"
                              #iconEdit
                              data-qa="iconEdit1">
                              <span
                                venioSvgLoader
                                [parentElement]="iconEdit.element"
                                svgUrl="assets/svg/icon-pencil-svg.svg"
                                hoverColor="#FFBB12"
                                color="#979797"
                                height="1rem"
                                width="1rem"></span>
                            </button>
                          </div>
                          <div class="t-flex">
                            <button
                              kendoButton
                              fillMode="clear"
                              size="none"
                              data-qa="iconRemove1"
                              #iconRemove>
                              <span
                                venioSvgLoader
                                [parentElement]="iconRemove.element"
                                svgUrl="assets/svg/icon-trash-bin-minimalistic-svg.svg"
                                hoverColor="#ED7425"
                                color="#979797"
                                height=".9rem"
                                width=".9rem"></span>
                            </button>
                          </div>

                          <div class="t-flex">
                            <kendo-dropdownbutton
                              [data]="menuData"
                              data-qa="iconDots1"
                              fillMode="none"
                              buttonClass="!t-border-0 !t-w-4 !t-p-0 !t-relative">
                              <span
                                class="t-relative"
                                venioSvgLoader
                                svgUrl="assets/svg/icon-dots-3-vertical-svg.svg"
                                hoverColor="#FFBB12"
                                color="#979797"
                                height="1rem"
                                width="1rem"></span>
                            </kendo-dropdownbutton>
                          </div>
                        </div>
                        <!-- content data-->
                        <div class="t-flex">dummy content</div>
                      </div>
                    </div>

                    <div
                      class="t-flex t-border t-border-[#2F3080] t-min-h-[62px] t-rounded t-mt-5 t-relative v-custom-condition-block">
                      <button
                        kendoButton
                        data-qa="iconClose2"
                        fillMode="clear"
                        class="v-dialog-close t-absolute t--right-0 t-top-0 !t-flex t-items-start t-rounded-bl-lg t-w-6 t-h-6 t-p-0 t-pt-0.5 !t-pl-0.5">
                        <kendo-svg-icon
                          [icon]="icons.closeIcon"
                          [size]="'small'"></kendo-svg-icon>
                      </button>

                      <div
                        class="t-flex v-custom-label t-items-center t-justify-center">
                        <kendo-dropdownbutton
                          data-qa="andLabel1"
                          [data]="menuData"
                          fillMode="none"
                          class="v-custom-operator-menu">
                          OR
                        </kendo-dropdownbutton>
                      </div>
                      <div
                        class="t-flex t-flex-col t-w-full t-justify-center t-items-center"
                        *ngIf="block === 0">
                        <div class="t-flex t-items-center">
                          <span
                            (click)="block = 1"
                            class="t-flex t-items-center t-cursor-pointer v-custom-secondary-button t-text-[--tb-kendo-custom-secondary-100]">
                            <kendo-svg-icon
                              [icon]="icons.plusIconSvg"></kendo-svg-icon>
                            Click here </span
                          ><span class="t-pl-1"> to add field selection</span>
                        </div>
                      </div>

                      <div
                        class="t-flex t-px-5 t-py-6 t-w-full v-content-wrapper"
                        *ngIf="block === 1">
                        <!-- hover buttons-->
                        <div class="t-flex v-custom-condition-block__hover">
                          <div class="t-flex">
                            <button
                              kendoButton
                              fillMode="clear"
                              data-qa="iconAddNew1"
                              size="none"
                              #iconAddNew>
                              <span
                                venioSvgLoader
                                [parentElement]="iconAddNew.element"
                                svgUrl="assets/svg/icon-plus-large-svg.svg"
                                hoverColor="#FFBB12"
                                color="#979797"
                                height="1rem"
                                width="1rem"></span>
                            </button>
                          </div>
                          <div class="t-flex">
                            <button
                              kendoButton
                              fillMode="clear"
                              size="none"
                              #iconEdit
                              data-qa="iconEdit1">
                              <span
                                venioSvgLoader
                                [parentElement]="iconEdit.element"
                                svgUrl="assets/svg/icon-pencil-svg.svg"
                                hoverColor="#FFBB12"
                                color="#979797"
                                height="1rem"
                                width="1rem"></span>
                            </button>
                          </div>
                          <div class="t-flex">
                            <button
                              kendoButton
                              fillMode="clear"
                              size="none"
                              data-qa="iconRemove1"
                              #iconRemove>
                              <span
                                venioSvgLoader
                                [parentElement]="iconRemove.element"
                                svgUrl="assets/svg/icon-trash-bin-minimalistic-svg.svg"
                                hoverColor="#ED7425"
                                color="#979797"
                                height=".9rem"
                                width=".9rem"></span>
                            </button>
                          </div>

                          <div class="t-flex">
                            <kendo-dropdownbutton
                              [data]="menuData"
                              data-qa="iconDots1"
                              fillMode="none"
                              buttonClass="!t-border-0 !t-w-4 !t-p-0 !t-relative">
                              <span
                                class="t-relative"
                                venioSvgLoader
                                svgUrl="assets/svg/icon-dots-3-vertical-svg.svg"
                                hoverColor="#FFBB12"
                                color="#979797"
                                height="1rem"
                                width="1rem"></span>
                            </kendo-dropdownbutton>
                          </div>
                        </div>
                        <!-- content data-->
                        <div class="t-flex">dummy content</div>
                      </div>
                    </div>

                    <div
                      class="t-flex t-border t-border-[#2F3080] t-min-h-[62px] t-rounded t-mt-5 t-relative v-custom-condition-block">
                      <button
                        kendoButton
                        data-qa="iconClose2"
                        fillMode="clear"
                        class="v-dialog-close t-absolute t--right-0 t-top-0 !t-flex t-items-start t-rounded-bl-lg t-w-6 t-h-6 t-p-0 t-pt-0.5 !t-pl-0.5">
                        <kendo-svg-icon
                          [icon]="icons.closeIcon"
                          [size]="'small'"></kendo-svg-icon>
                      </button>

                      <div
                        class="t-flex v-custom-label t-items-center t-justify-center">
                        <kendo-dropdownbutton
                          data-qa="andLabel1"
                          [data]="menuData"
                          fillMode="none"
                          class="v-custom-operator-menu">
                          NOT
                        </kendo-dropdownbutton>
                      </div>
                      <div
                        class="t-flex t-flex-col t-w-full t-justify-center t-items-center"
                        *ngIf="block === 0">
                        <div class="t-flex t-items-center">
                          <span
                            (click)="block = 1"
                            class="t-flex t-items-center t-cursor-pointer v-custom-secondary-button t-text-[--tb-kendo-custom-secondary-100]">
                            <kendo-svg-icon
                              [icon]="icons.plusIconSvg"></kendo-svg-icon>
                            Click here </span
                          ><span class="t-pl-1"> to add field selection</span>
                        </div>
                      </div>

                      <div
                        class="t-flex t-px-5 t-py-6 t-w-full v-content-wrapper"
                        *ngIf="block === 1">
                        <!-- hover buttons-->
                        <div class="t-flex v-custom-condition-block__hover">
                          <div class="t-flex">
                            <button
                              kendoButton
                              fillMode="clear"
                              data-qa="iconAddNew1"
                              size="none"
                              #iconAddNew>
                              <span
                                venioSvgLoader
                                [parentElement]="iconAddNew.element"
                                svgUrl="assets/svg/icon-plus-large-svg.svg"
                                hoverColor="#FFBB12"
                                color="#979797"
                                height="1rem"
                                width="1rem"></span>
                            </button>
                          </div>
                          <div class="t-flex">
                            <button
                              kendoButton
                              fillMode="clear"
                              size="none"
                              #iconEdit
                              data-qa="iconEdit1">
                              <span
                                venioSvgLoader
                                [parentElement]="iconEdit.element"
                                svgUrl="assets/svg/icon-pencil-svg.svg"
                                hoverColor="#FFBB12"
                                color="#979797"
                                height="1rem"
                                width="1rem"></span>
                            </button>
                          </div>
                          <div class="t-flex">
                            <button
                              kendoButton
                              fillMode="clear"
                              size="none"
                              data-qa="iconRemove1"
                              #iconRemove>
                              <span
                                venioSvgLoader
                                [parentElement]="iconRemove.element"
                                svgUrl="assets/svg/icon-trash-bin-minimalistic-svg.svg"
                                hoverColor="#ED7425"
                                color="#979797"
                                height=".9rem"
                                width=".9rem"></span>
                            </button>
                          </div>

                          <div class="t-flex">
                            <kendo-dropdownbutton
                              [data]="menuData"
                              data-qa="iconDots1"
                              fillMode="none"
                              buttonClass="!t-border-0 !t-w-4 !t-p-0 !t-relative">
                              <span
                                class="t-relative"
                                venioSvgLoader
                                svgUrl="assets/svg/icon-dots-3-vertical-svg.svg"
                                hoverColor="#FFBB12"
                                color="#979797"
                                height="1rem"
                                width="1rem"></span>
                            </kendo-dropdownbutton>
                          </div>
                        </div>
                        <!-- content data-->
                        <div class="t-flex">dummy content</div>
                      </div>
                    </div>

                    <div
                      class="t-flex t-border t-border-[#2F3080] t-min-h-[62px] t-rounded t-mt-5 t-relative v-custom-condition-block">
                      <button
                        kendoButton
                        data-qa="iconClose2"
                        fillMode="clear"
                        class="v-dialog-close t-absolute t--right-0 t-top-0 !t-flex t-items-start t-rounded-bl-lg t-w-6 t-h-6 t-p-0 t-pt-0.5 !t-pl-0.5">
                        <kendo-svg-icon
                          [icon]="icons.closeIcon"
                          [size]="'small'"></kendo-svg-icon>
                      </button>

                      <div
                        class="t-flex v-custom-label t-items-center t-justify-center">
                        <kendo-dropdownbutton
                          data-qa="andLabel1"
                          [data]="menuData"
                          fillMode="none"
                          class="v-custom-operator-menu">
                          NOT
                        </kendo-dropdownbutton>
                      </div>

                      <div
                        class="t-flex v-custom-label t-items-center t-justify-center t-ml-1">
                        <kendo-dropdownbutton
                          data-qa="andLabel1"
                          [data]="menuData"
                          fillMode="none"
                          class="v-custom-operator-menu">
                          OR
                        </kendo-dropdownbutton>
                      </div>
                      <div
                        class="t-flex t-flex-col t-w-full t-justify-center t-items-center"
                        *ngIf="block === 0">
                        <div class="t-flex t-items-center">
                          <span
                            (click)="block = 1"
                            class="t-flex t-items-center t-cursor-pointer v-custom-secondary-button t-text-[--tb-kendo-custom-secondary-100]">
                            <kendo-svg-icon
                              [icon]="icons.plusIconSvg"></kendo-svg-icon>
                            Click here </span
                          ><span class="t-pl-1"> to add field selection</span>
                        </div>
                      </div>

                      <div
                        class="t-flex t-px-5 t-py-6 t-w-full v-content-wrapper"
                        *ngIf="block === 1">
                        <!-- hover buttons-->
                        <div class="t-flex v-custom-condition-block__hover">
                          <div class="t-flex">
                            <button
                              kendoButton
                              fillMode="clear"
                              data-qa="iconAddNew1"
                              size="none"
                              #iconAddNew>
                              <span
                                venioSvgLoader
                                [parentElement]="iconAddNew.element"
                                svgUrl="assets/svg/icon-plus-large-svg.svg"
                                hoverColor="#FFBB12"
                                color="#979797"
                                height="1rem"
                                width="1rem"></span>
                            </button>
                          </div>
                          <div class="t-flex">
                            <button
                              kendoButton
                              fillMode="clear"
                              size="none"
                              #iconEdit
                              data-qa="iconEdit1">
                              <span
                                venioSvgLoader
                                [parentElement]="iconEdit.element"
                                svgUrl="assets/svg/icon-pencil-svg.svg"
                                hoverColor="#FFBB12"
                                color="#979797"
                                height="1rem"
                                width="1rem"></span>
                            </button>
                          </div>
                          <div class="t-flex">
                            <button
                              kendoButton
                              fillMode="clear"
                              size="none"
                              data-qa="iconRemove1"
                              #iconRemove>
                              <span
                                venioSvgLoader
                                [parentElement]="iconRemove.element"
                                svgUrl="assets/svg/icon-trash-bin-minimalistic-svg.svg"
                                hoverColor="#ED7425"
                                color="#979797"
                                height=".9rem"
                                width=".9rem"></span>
                            </button>
                          </div>

                          <div class="t-flex">
                            <kendo-dropdownbutton
                              [data]="menuData"
                              data-qa="iconDots1"
                              fillMode="none"
                              buttonClass="!t-border-0 !t-w-4 !t-p-0 !t-relative">
                              <span
                                class="t-relative"
                                venioSvgLoader
                                svgUrl="assets/svg/icon-dots-3-vertical-svg.svg"
                                hoverColor="#FFBB12"
                                color="#979797"
                                height="1rem"
                                width="1rem"></span>
                            </kendo-dropdownbutton>
                          </div>
                        </div>
                        <!-- content data-->
                        <div class="t-flex">dummy content</div>
                      </div>
                    </div>
                  </div>
                </ng-template>
              </kendo-tabstrip-tab>
              <kendo-tabstrip-tab title="Sort">
                <ng-template kendoTabContent>
                  <div class="t-flex t-flex-col t-mt-4">
                    <kendo-grid
                      class="v-customgrid-newview"
                      filterable="menu"
                      [data]="sampleData"
                      [rowReorderable]="true">
                      <kendo-grid-rowreorder-column
                        [width]="40"></kendo-grid-rowreorder-column>
                      <kendo-grid-column
                        headerClass="t-text-primary"
                        field="CompanyName"
                        title="Role">
                        <ng-template kendoGridCellTemplate let-dataItem>
                          <kendo-dropdownlist
                            data-qa="role"
                            [defaultItem]="[]"
                            [data]="[]"
                            textField="text"
                            valueField="value">
                          </kendo-dropdownlist>
                        </ng-template>
                      </kendo-grid-column>
                      <kendo-grid-column
                        field="ContactTitle"
                        headerClass="t-text-primary"
                        title="Permission"
                        [width]="140">
                        <ng-template kendoGridCellTemplate let-dataItem>
                          <kendo-dropdownlist
                            data-qa="permission"
                            [defaultItem]="defaultItemCategories"
                            [data]="[]"
                            textField="text"
                            valueField="value">
                          </kendo-dropdownlist>
                        </ng-template>
                      </kendo-grid-column>
                    </kendo-grid>
                  </div>
                </ng-template>
              </kendo-tabstrip-tab>
            </kendo-tabstrip>
          </div>
        </div>
        <!-- end -->
      </div>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="close('no')"
        class="v-custom-secondary-button"
        themeColor="secondary"
        fillMode="outline"
        data-qa="save">
        SAVE
      </button>
      <button
        kendoButton
        (click)="close('yes')"
        themeColor="dark"
        fillMode="outline"
        data-qa="cancel">
        CANCEL
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>
