import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentAddNewViewDialogComponent } from './document-add-new-view-dialog.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('DocumentAddNewViewDialogComponent', () => {
  let component: DocumentAddNewViewDialogComponent
  let fixture: ComponentFixture<DocumentAddNewViewDialogComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentAddNewViewDialogComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentAddNewViewDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
