import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { InputsModule } from '@progress/kendo-angular-inputs'

import { FormsModule } from '@angular/forms'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import { LabelModule } from '@progress/kendo-angular-label'
import { PageControlActionType } from '../shared/enum/page-control-action-type.enum'
import { ExpansionPanelModule } from '@progress/kendo-angular-layout'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-bulk-edit-ui-dialog',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    InputsModule,
    FormsModule,
    ButtonsModule,
    TreeViewModule,
    LabelModule,
    SvgLoaderDirective,
    ExpansionPanelModule,
    GridModule,
    IconsModule,
    DropDownListModule,
  ],
  templateUrl: './bulk-edit-ui-dialog.component.html',
  styleUrls: ['./bulk-edit-ui-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BulkEditUiDialogComponent {
  public opened = true

  public sampleCustomers = [
    {
      Id: 'ALFKI',
      CompanyName: 'Demo_master-Site Admin Group',
      ContactName: 'Maria Anders',
      ContactTitle: 'Sales Representative',
      City: 'Berlin',
    },
    {
      Id: 'ANATR',
      CompanyName: 'Demo_master-Project Admin Group',
      ContactName: 'Ana Trujillo',
      ContactTitle: 'Owner',
      City: 'México D.F.',
    },
    {
      Id: 'ANTON',
      CompanyName: 'Demo_master-User Group',
      ContactName: 'Antonio Moreno',
      ContactTitle: 'Owner',
      City: 'México D.F.',
    },
    {
      Id: 'AROUT',
      CompanyName: 'Demo_master-Viewer Group',
      ContactName: 'Thomas Hardy',
      ContactTitle: 'Sales Representative',
      City: 'London',
    },
  ]

  public svgIconForTagControls = [
    {
      actionType: PageControlActionType.FIRST_PAGE,
      iconPath: 'assets/svg/icon-pagecontrol-first-page.svg',
    },
    {
      actionType: PageControlActionType.NEXT_PAGE,
      iconPath: 'assets/svg/icon-pagecontrol-next-page.svg',
    },
    {
      actionType: PageControlActionType.VIEW_DETAIL,
      iconPath: 'assets/svg/icon-review-eye.svg',
    },
    {
      actionType: PageControlActionType.SAVE_DETAIL,
      iconPath: 'assets/svg/icon-review-save.svg',
    },
    {
      actionType: PageControlActionType.ADD_DETAIL,
      iconPath: 'assets/svg/icon-review-plus.svg',
    },
  ]

  // color picker value
  public selectedColor = '#E21E36'

  // for dropdown module
  public defaultItemCategories: { text: string; value: number } = {
    text: 'Read/Write',
    value: null,
  }

  public listItems: Array<{ text: string; value: number }> = [
    { text: 'Read', value: 1 },
    { text: 'Write', value: 2 },
  ]

  // for tree view of tags
  public expandedKeys: any[] = ['0', '1']

  public checkedKeys: any[] = ['0_1']

  public filterTerm = ''

  public data: any[] = [
    {
      text: 'Furniture',
      items: [
        { text: 'Tables & Chairs' },
        { text: 'Sofas' },
        { text: 'Occasional Furniture' },
      ],
    },
    {
      text: 'Decor',
      items: [
        { text: 'Bed Linen' },
        { text: 'Curtains & Blinds' },
        { text: 'Carpets' },
      ],
    },
  ]

  public close(status: string): void {
    console.log(`Dialog result: ${status}`)
    this.opened = false
  }

  public open(): void {
    this.opened = true
  }

  public onTabSelect(): void {
    console.log('clicked')
  }

  public browseActionClicked(actionType: PageControlActionType): void {
    switch (actionType) {
      case 'FIRST_PAGE':
        // Invoke methods
        break
      case 'NEXT_PAGE':
        // Invoke methods
        break
      case 'PREV_PAGE':
        // Invoke methods
        break
      case 'LAST_PAGE':
        // Invoke methods
        break
    }
  }

  public onColorChange(color: string): void {
    this.selectedColor = color
  }
}
