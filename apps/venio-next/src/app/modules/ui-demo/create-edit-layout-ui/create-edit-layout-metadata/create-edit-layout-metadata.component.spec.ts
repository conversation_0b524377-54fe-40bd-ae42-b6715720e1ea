import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CreateEditLayoutMetadataComponent } from './create-edit-layout-metadata.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { DialogRef } from '@progress/kendo-angular-dialog'

describe('CreateEditLayoutMetadataComponent', () => {
  let component: CreateEditLayoutMetadataComponent
  let fixture: ComponentFixture<CreateEditLayoutMetadataComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CreateEditLayoutMetadataComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: DialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(CreateEditLayoutMetadataComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
