import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CreateEditLayoutUiComponent } from './create-edit-layout-ui.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('CreateEditLayoutUiComponent', () => {
  let component: CreateEditLayoutUiComponent
  let fixture: ComponentFixture<CreateEditLayoutUiComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CreateEditLayoutUiComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(CreateEditLayoutUiComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
