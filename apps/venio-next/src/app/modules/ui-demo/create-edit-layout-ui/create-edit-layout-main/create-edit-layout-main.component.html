<div class="t-flex t-flex-col t-gap-4 t-flex-1">
  <kendo-grid
    *ngIf="!ifCreate"
    class="t-flex t-w-full t-h-full t-border-b-1 t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto"
    [kendoGridBinding]="cases"
    venioDynamicHeight
    [sortable]="true"
    [groupable]="false"
    [reorderable]="true"
    [pageable]="{ type: 'numeric', position: 'top' }"
    [resizable]="true"
    kendoGridSelectBy="id"
    [filterable]="'menu'">
    <ng-template kendoPagerTemplate>
      <div class="t-flex t-gap-2">
        <kendo-textbox
          class="!t-border-[#ccc] !t-w-[25rem]"
          placeholder="Search"
          [clearButton]="true">
          <ng-template kendoTextBoxSuffixTemplate>
            <button
              kendoButton
              fillMode="clear"
              class="t-text-[#1EBADC]"
              imageUrl="assets/svg/icon-updated-search.svg"></button>
          </ng-template>
        </kendo-textbox>
      </div>
      <kendo-grid-spacer></kendo-grid-spacer>

      <div class="t-flex t-gap-2">
        <button
          kendoButton
          fillMode="outline"
          #deleteBtn
          rounded="medium"
          class="t-w-8 t-px-6 hover:t-border-[#ED7425] hover:!t-bg-[#ED7425]">
          <span
            venioSvgLoader
            applyEffectsTo="fill"
            hoverColor="#FFFFFF"
            color="#979797"
            [parentElement]="deleteBtn.element"
            svgUrl="assets/svg/Icon-material-delete.svg"
            height="1rem"
            width="1rem">
            <kendo-loader size="small"></kendo-loader>
          </span>
        </button>

        <button
          kendoButton
          class="v-custom-secondary-button t-uppercase"
          themeColor="secondary"
          fillMode="outline"
          (click)="openCreateLayout()"
          data-qa="create-new">
          Create New
        </button>
      </div>
    </ng-template>
    <kendo-grid-column
      field="id"
      [width]="30"
      title="#"
      headerClass="t-text-primary"
      [filterable]="false">
    </kendo-grid-column>

    <kendo-grid-checkbox-column
      [showSelectAll]="false"
      [width]="26"></kendo-grid-checkbox-column>

    <kendo-grid-column
      field="name"
      title="Name"
      [width]="200"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        <span
          (click)="openCreateLayout()"
          class="t-text-[var(--v-custom-sky-blue)] t-cursor-pointer"
          >{{ dataItem.name }}</span
        >
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      field="createdBy"
      [width]="200"
      title="Created By & On"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Created By & On"
          >Created By & On</span
        >
      </ng-template>

      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem.createdBy }}
        <span class="t-text-xs t-text-[#999999]">
          {{ dataItem.createdOn | date : 'MM dd yyyy hh:mm:ss a' }}</span
        >
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      [width]="400"
      title="Actions"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        <!-- NOTE:  Unique ID is required here which needs to be passed on setHoverstate() & caseActionControls otherwise it wont work also combining wont work with these btns-->

        <kendo-buttongroup>
          <button
            kendoButton
            #actionGrid1
            class="!t-py-[0.38rem] !t-px-[0.5rem] t-rounded-tr-none t-rounded-br-none t-w-1/2 hover:t-border-[#FFBB12] hover:t-bg-[#FFBB12]"
            (click)="
              caseActionControls(dataItem.id, commonActionTypes.FAVOURITE)
            "
            kendoTooltip
            [title]="capitalizeTitle(commonActionTypes.FAVOURITE)"
            size="none"
            [ngClass]="{
              'v-heart-selected-btn t-bg-[#fdf6f3] t-border-[#FFBB12]':
                toggledButtons[dataItem.id]
            }"
            *ngIf="dataItem.name !== 'Default'">
            <span
              [parentElement]="actionGrid1.element"
              venioSvgLoader
              [hoverColor]="toggledButtons[dataItem.id] ? '#FFBB12' : '#FFFFFF'"
              [color]="toggledButtons[dataItem.id] ? '#FFBB12' : '#979797'"
              svgUrl="assets/svg/icon-favourite-star.svg"
              height="1rem"
              width="1rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>

          <button
            kendoButton
            #actionGrid2
            class="!t-py-[0.38rem] !t-px-[0.5rem] t-rounded-tr-none t-rounded-br-none t-w-1/2 hover:t-border-[#9BD2A7] hover:t-bg-[#9BD2A7]"
            (click)="caseActionControls(dataItem, commonActionTypes.EDIT)"
            kendoTooltip
            [title]="capitalizeTitle(commonActionTypes.EDIT)"
            size="none"
            *ngIf="dataItem.name !== 'Default'">
            <span
              [parentElement]="actionGrid2.element"
              venioSvgLoader
              hoverColor="#FFFFFF"
              color="#979797"
              svgUrl="assets/svg/icon-pencil-svg.svg"
              height="0.85rem"
              width="0.85rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>

          <button
            kendoButton
            #actionGrid3
            class="!t-py-[0.38rem] !t-px-[0.5rem] t-rounded-tr-none t-rounded-br-none t-w-1/2 hover:t-border-[#2F3080] hover:t-bg-[#2F3080]"
            [ngClass]="{
              't-w-[33px]': dataItem.name === 'Default'
            }"
            (click)="caseActionControls(dataItem, commonActionTypes.CLONE)"
            kendoTooltip
            [title]="capitalizeTitle(commonActionTypes.CLONE)"
            size="none">
            <span
              [parentElement]="actionGrid3.element"
              venioSvgLoader
              applyEffectsTo="fill"
              hoverColor="#FFFFFF"
              color="#979797"
              svgUrl="assets/svg/icon-add-clone.svg"
              height="0.75rem"
              width="0.8rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>
          <button
            kendoButton
            #actionGrid4
            class="!t-py-[0.38rem] !t-px-[0.5rem] t-rounded-tr-sm t-rounded-br-sm t-w-1/2 hover:t-border-[#ED7425] hover:t-bg-[#ED7425]"
            (click)="caseActionControls(dataItem, commonActionTypes.DELETE)"
            kendoTooltip
            [title]="capitalizeTitle(commonActionTypes.DELETE)"
            size="none"
            *ngIf="dataItem.name !== 'Default'">
            <span
              [parentElement]="actionGrid4.element"
              venioSvgLoader
              applyEffectsTo="fill"
              hoverColor="#FFFFFF"
              color="#979797"
              svgUrl="assets/svg/Icon-material-delete.svg"
              height="0.75rem"
              width="0.8rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>
        </kendo-buttongroup>
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>

  <venio-create-edit-layout-create-content
    *ngIf="ifCreate"
    (mainLayout)="
      handleMainLayout($event)
    "></venio-create-edit-layout-create-content>
</div>
