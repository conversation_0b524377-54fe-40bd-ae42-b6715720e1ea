import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CreateEditLayoutMainComponent } from './create-edit-layout-main.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('CreateEditLayoutMainComponent', () => {
  let component: CreateEditLayoutMainComponent
  let fixture: ComponentFixture<CreateEditLayoutMainComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CreateEditLayoutMainComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(CreateEditLayoutMainComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
