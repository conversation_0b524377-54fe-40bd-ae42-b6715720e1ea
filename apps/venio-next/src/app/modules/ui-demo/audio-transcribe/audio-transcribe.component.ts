import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { GridModule } from '@progress/kendo-angular-grid'
import { UiPaginationModule } from '@venio/ui/pagination'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { FormsModule } from '@angular/forms'
import { SVGIcon, arrowRotateCwIcon } from '@progress/kendo-svg-icons'

@Component({
  selector: 'venio-audio-transcribe',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    InputsModule,
    TooltipsModule,
    IconsModule,
    LayoutModule,
    GridModule,
    UiPaginationModule,
    DropDownsModule,
    FormsModule,
  ],
  templateUrl: './audio-transcribe.component.html',
  styleUrl: './audio-transcribe.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AudioTranscribeComponent implements OnInit {
  public dialogTitle = 'Audio Transcribe'

  public opened = false

  public tabStatus = 0

  public arrowRotateCwIcon: SVGIcon = arrowRotateCwIcon

  public listItems: Array<string> = ['Item 1', 'Item 2', 'Item 3']

  // Grid 1 Data

  public gridData: Array<{
    selected: boolean
    id: number
    fileType: string
    count: number
  }> = []

  private fileTypes: string[] = ['xls', 'doc', 'txt', 'pdf', 'csv']

  // Grid 2 Data
  public pageSize = 10

  public sizes = [5, 10, 20, 50]

  public skip = 0

  public historyData: Array<{
    jobId: number
    noDocuments: number
    requestedby: string
    startTime: Date
    endTime: Date
    totalTime: string
    status: string
  }> = []

  public ngOnInit(): void {
    this.openDialog()
    this.generateDummyData()
    this.generateHistoryDummyData()
  }

  private generateDummyData(): void {
    for (let i = 0; i < 10; i++) {
      this.gridData.push({
        selected: false,
        id: i + 1,
        fileType: this.fileTypes[i % this.fileTypes.length],
        count: i + 1,
      })
    }
  }

  private generateHistoryDummyData(): void {
    const users = ['User A', 'User B', 'User C', 'User D']
    const statuses = ['Completed', 'Pending', 'Failed']

    for (let i = 0; i < 100; i++) {
      const startTime = new Date()
      startTime.setHours(startTime.getHours() - i)
      const endTime = new Date(startTime)
      endTime.setMinutes(
        endTime.getMinutes() + Math.floor(Math.random() * 60) + 1
      )
      const totalTime = this.calculateTotalTime(startTime, endTime)

      this.historyData.push({
        jobId: i + 1,
        noDocuments: Math.floor(Math.random() * 1000),
        requestedby: users[i % users.length],
        startTime,
        endTime,
        totalTime,
        status: statuses[Math.floor(Math.random() * statuses.length)],
      })
    }
  }

  public toggleAll(event: Event): void {
    const checked = (event.target as HTMLInputElement).checked
    this.gridData.forEach((item) => (item.selected = checked))
  }

  private calculateTotalTime(startTime: Date, endTime: Date): string {
    const diff = endTime.getTime() - startTime.getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((diff % (1000 * 60)) / 1000)

    return `${hours}h ${minutes}m ${seconds}s`
  }

  public openDialog(): void {
    this.opened = true
  }

  public close(status: string): void {
    this.opened = false
  }

  public onSelect(e: any): void {
    this.tabStatus = e.index
  }
}
