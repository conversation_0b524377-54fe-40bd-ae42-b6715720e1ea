<div class="t-flex t-flex-col">
  <kendo-dialog
    *ngIf="opened"
    (close)="close('cancel')"
    [height]="'90vh'"
    [minWidth]="250"
    [maxWidth]="1600"
    [width]="'80%'">
    <kendo-dialog-titlebar>
      <div class="t-flex t-w-[65%] t-justify-between">
        <div class="t-block">{{ dialogTitle }}</div>
      </div>
    </kendo-dialog-titlebar>

    <kendo-tabstrip (tabSelect)="onSelect($event)">
      <kendo-tabstrip-tab title="Transcribe" [selected]="true">
        <ng-template kendoTabContent>
          <div class="t-flex t-flex-col t-w-full t-mt-3">
            <div
              class="t-flex t-p-4 t-flex-col t-w-full t-gap-1 t-bg-[#f6f6f6]">
              <div
                class="t-block t-uppercase t-text-[#1EBADC] t-font-semibold t-border-b-[1px] t-border-b-[#cccccc] t-pb-2">
                Summary
              </div>
              <ul class="t-list-none t-capitalize">
                <li
                  class="t-flex t-p-2 t-gap-3 t-border-b-[1px] t-border-b-[#cccccc]">
                  <div class="t-flex t-w-1/3">Total Search Result</div>
                  <div class="t-flex t-w-2/3 flex-1">245412</div>
                </li>
                <li
                  class="t-flex t-p-2 t-gap-3 t-border-b-[1px] t-border-b-[#cccccc]">
                  <div class="t-flex t-w-1/3">
                    Documents available for transcribing queue
                  </div>
                  <div class="t-flex t-w-2/3 flex-1">0</div>
                </li>
                <li
                  class="t-flex t-p-2 t-gap-3 t-border-b-[1px] t-border-b-[#cccccc]">
                  <div class="t-flex t-w-1/3">
                    Previously added to transcribing queue
                  </div>
                  <div class="t-flex t-w-2/3 flex-1">0</div>
                </li>
                <li
                  class="t-flex t-p-2 t-gap-3 t-border-b-[1px] t-border-b-[#cccccc]">
                  <div class="t-flex t-w-1/3">Remaining to be queued</div>
                  <div class="t-flex t-w-2/3 flex-1">0</div>
                </li>

                <li class="t-flex t-px-2 t-gap-3">
                  <label
                    class="t-flex t-items-center t-min-w-[8.5rem] t-min-h-[2.25rem]">
                    <input
                      type="checkbox"
                      kendoCheckBox
                      rounded="small"
                      size="small" />
                    <span class="t-pl-2 t-tracking-tight"
                      >Show all files for transcribing</span
                    >
                  </label>
                </li>
              </ul>
            </div>

            <div class="t-flex t-flex-col t-gap-3 t-mt-3">
              <div class="t-block t-uppercase t-text-[#1EBADC] t-font-semibold">
                DETAIL INFORMATION FOR DOCUMENTS REMAINING TO BE QUEUED
              </div>
              <div class="t-block">
                <kendo-grid
                  class="t-w-full t-max-h-72 v-hide-scrollbar"
                  [data]="gridData"
                  [resizable]="true"
                  *ngIf="gridData.length">
                  <kendo-grid-column field="selected" title="" [width]="50">
                    <ng-template kendoGridHeaderTemplate let-column>
                      <input
                        kendoCheckBox
                        rounded="small"
                        size="small"
                        type="checkbox"
                        (change)="toggleAll($event)" />
                    </ng-template>
                    <ng-template kendoGridCellTemplate let-dataItem>
                      <input
                        kendoCheckBox
                        rounded="small"
                        size="small"
                        type="checkbox"
                        [(ngModel)]="dataItem.selected" />
                    </ng-template>
                  </kendo-grid-column>
                  <kendo-grid-column
                    field="id"
                    title="#"
                    headerClass="t-text-primary"
                    [width]="90"
                    [minResizableWidth]="70">
                    <ng-template kendoGridHeaderTemplate>
                      <span kendoTooltip title="ID">#</span>
                    </ng-template>
                  </kendo-grid-column>
                  <kendo-grid-column
                    field="fileType"
                    title="File Type"
                    headerClass="t-text-primary"
                    [width]="150"
                    [minResizableWidth]="100">
                    <ng-template kendoGridHeaderTemplate>
                      <span kendoTooltip title="File Type">File Type</span>
                    </ng-template>
                    <ng-template kendoGridCellTemplate let-dataItem>
                      <span kendoTooltip title="{{ dataItem.fileType }}">{{
                        dataItem.fileType
                      }}</span>
                    </ng-template>
                  </kendo-grid-column>
                  <kendo-grid-column
                    field="count"
                    title="Count"
                    headerClass="t-text-primary"
                    [minResizableWidth]="300">
                    <ng-template kendoGridHeaderTemplate>
                      <span kendoTooltip title="Count">Count</span>
                    </ng-template>
                    <ng-template kendoGridCellTemplate let-dataItem>
                      <span kendoTooltip title="{{ dataItem.count }}">{{
                        dataItem.count
                      }}</span>
                    </ng-template>
                  </kendo-grid-column>
                </kendo-grid>

                <!-- No Record template -->
                <div
                  class="t-grid t-h-28 t-w-full t-place-content-center"
                  *ngIf="!gridData.length">
                  <div class="t-text-center t-text-[#979797]">
                    No records available.
                  </div>
                </div>
              </div>

              <div class="t-flex t-gap-4">
                <label class="t-flex t-items-center t-min-h-[2.25rem]">
                  <input
                    type="checkbox"
                    kendoCheckBox
                    rounded="small"
                    name="status"
                    size="small" />
                  <span class="t-pl-2 t-tracking-tight">Launch</span>
                </label>

                <label class="t-flex t-items-center t-min-h-[2.25rem]">
                  <input
                    type="checkbox"
                    kendoCheckBox
                    rounded="small"
                    name="status"
                    size="small" />
                  <span class="t-pl-2 t-tracking-tight">Relaunch</span>
                </label>
              </div>
            </div>
          </div>
        </ng-template>
      </kendo-tabstrip-tab>
      <kendo-tabstrip-tab title="History">
        <ng-template kendoTabContent>
          <div class="t-block t-w-full t-mt-5 t-mb-4">
            <kendo-dropdownlist
              defaultItem="Requested Users"
              [data]="listItems"
              [valuePrimitive]="true"
              class="t-w-64">
            </kendo-dropdownlist>
          </div>
          <div class="t-block">
            <kendo-grid
              [kendoGridBinding]="historyData"
              kendoGridSelectBy="id"
              [pageSize]="pageSize"
              [pageable]="{ type: 'numeric', position: 'top' }"
              [sortable]="true"
              [groupable]="false"
              [reorderable]="true"
              [resizable]="true"
              [skip]="skip">
              <ng-template kendoPagerTemplate>
                <div
                  class="t-flex t-w-full t-justify-start t-items-center t-pl-2">
                  <button
                    kendoButton
                    fillMode="outline"
                    class="t-p-1"
                    size="small"
                    kendoTooltip
                    title="Refresh">
                    <kendo-svgicon
                      [icon]="arrowRotateCwIcon"
                      [size]="'large'"></kendo-svgicon>
                  </button>
                </div>
                <kendo-grid-spacer></kendo-grid-spacer>

                <venio-pagination
                  [disabled]="gridData?.length === 0"
                  [totalRecords]="gridData?.length"
                  [pageSize]="pageSize"
                  [showPageJumper]="false"
                  [showPageSize]="true"
                  [showRowNumberInputBox]="true"
                  class="t-px-5 t-block t-py-2">
                </venio-pagination>
              </ng-template>
              <kendo-grid-column
                field="jobId"
                title="Job ID"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span kendoTooltip title="Job ID">Job ID</span>
                </ng-template>
              </kendo-grid-column>

              <kendo-grid-column
                field="noDocuments"
                [width]="190"
                title="No. of Documents"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span kendoTooltip title="No. of Documents"
                    >No. of Documents</span
                  >
                </ng-template>
              </kendo-grid-column>

              <kendo-grid-column
                field="requestedby"
                title="Requested By"
                [width]="170"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span kendoTooltip title="Requested By">Requested By</span>
                </ng-template>
                <ng-template kendoGridCellTemplate let-dataItem>
                  <span kendoTooltip [title]="dataItem.requestedby">
                    {{ dataItem.requestedby }}
                  </span>
                </ng-template>
              </kendo-grid-column>

              <kendo-grid-column
                field="startTime"
                title="Start Time"
                [width]="200"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span kendoTooltip title="Start Time">Start Time</span>
                </ng-template>
                <ng-template kendoGridCellTemplate let-dataItem>
                  <span
                    kendoTooltip
                    [title]="dataItem.startTime | date : 'HH:mm:ss a'">
                    <span>{{ dataItem.startTime | date : 'HH:mm:ss a' }}</span>
                  </span>
                </ng-template>
              </kendo-grid-column>

              <kendo-grid-column
                field="endTime"
                title="End Time"
                [width]="200"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span kendoTooltip title="End Time">End Time</span>
                </ng-template>
                <ng-template kendoGridCellTemplate let-dataItem>
                  <span
                    kendoTooltip
                    [title]="dataItem.endTime | date : 'HH:mm:ss a'">
                    <span>{{ dataItem.endTime | date : 'HH:mm:ss a' }}</span>
                  </span>
                </ng-template>
              </kendo-grid-column>

              <kendo-grid-column
                field="totalTime"
                title="Total Time"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span kendoTooltip title="Total Time">Total Time</span>
                </ng-template>
                <ng-template kendoGridCellTemplate let-dataItem>
                  <span kendoTooltip [title]="dataItem.totalTime">
                    {{ dataItem.totalTime }}
                  </span>
                </ng-template>
              </kendo-grid-column>

              <kendo-grid-column
                field="status"
                title="Status"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span kendoTooltip title="Status">Status</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                  <span
                    [ngClass]="{
                      't-text-error': dataItem.status === 'Failed',
                      't-text-[#FFBB12]': dataItem.status === 'Pending',
                      't-text-secondary': dataItem.status === 'Completed'
                    }">
                    {{ dataItem.status }}
                  </span>
                </ng-template>
              </kendo-grid-column>
            </kendo-grid>
          </div>
        </ng-template>
      </kendo-tabstrip-tab>
    </kendo-tabstrip>

    <kendo-dialog-actions>
      <div class="t-flex t-text-xs" *ngIf="tabStatus === 0">
        <div class="t-block t-text-error t-pr-2">Note:</div>
        <div class="t-flex t-flex-col t-gap-1">
          <p>
            Password Protected, Corrupted, Zero Byte, System and Denist files
            will not be queued.
          </p>
        </div>
      </div>
      <div class="t-flex t-gap-4 t-justify-end">
        @if(this.tabStatus === 1){
        <button
          kendoButton
          (click)="close('yes')"
          themeColor="dark"
          fillMode="outline"
          data-qa="cancel-button">
          CLOSE
        </button>
        } @else {
        <button
          kendoButton
          (click)="close('no')"
          class="v-custom-secondary-button"
          themeColor="secondary"
          fillMode="outline"
          data-qa="save-button">
          START
        </button>
        <button
          kendoButton
          (click)="close('yes')"
          themeColor="dark"
          fillMode="outline"
          data-qa="cancel-button">
          CANCEL
        </button>
        }
      </div>
    </kendo-dialog-actions>
  </kendo-dialog>
</div>
