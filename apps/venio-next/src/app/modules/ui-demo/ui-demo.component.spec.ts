import { ComponentFixture, TestBed } from '@angular/core/testing'
import { UiDemoComponent } from './ui-demo.component'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import { NotificationModule } from '@progress/kendo-angular-notification'
import { DialogsModule } from '@progress/kendo-angular-dialog'

describe('UiDemoComponent', () => {
  let component: UiDemoComponent
  let fixture: ComponentFixture<UiDemoComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [UiDemoComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      imports: [NotificationModule, DialogsModule],
    }).compileComponents()

    fixture = TestBed.createComponent(UiDemoComponent)
    component = fixture.componentInstance
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
