import {
  ChangeDetectionStrategy,
  Component,
  QueryList,
  TemplateRef,
  Type,
  ViewChild,
  ViewChildren,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  LayoutModule,
  ExpansionPanelComponent,
} from '@progress/kendo-angular-layout'
import { ListViewModule } from '@progress/kendo-angular-listview'
import { MenusModule } from '@progress/kendo-angular-menu'
import {
  borderColorIcon,
  fileTxtIcon,
  linkIcon,
  chevronRightIcon,
} from '@progress/kendo-svg-icons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'

@Component({
  selector: 'venio-transcript-ui',
  standalone: true,
  imports: [
    CommonModule,
    LayoutModule,
    ButtonsModule,
    IconsModule,
    ListViewModule,
    InputsModule,
    MenusModule,
    DialogsModule,
    SvgLoaderDirective,
    DropDownsModule,
  ],
  templateUrl: './transcript-ui.component.html',
  styleUrl: './transcript-ui.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TranscriptUiComponent {
  // Transcript UI
  public menuState = false

  @ViewChildren(ExpansionPanelComponent)
  public readonlypanels: QueryList<ExpansionPanelComponent>

  @ViewChild('transcriptContent', { static: true })
  private transcriptContent: TemplateRef<any>

  @ViewChild('addNote', { static: true })
  private addNotes: TemplateRef<any>

  @ViewChild('linkedDocuments', { static: true })
  private linkedDocuments: TemplateRef<any>

  @ViewChild('wordWheel', { static: true })
  private wordWheel: TemplateRef<any>

  @ViewChild('highlightDocs', { static: true })
  private highlightDocs: TemplateRef<any>

  public items = [
    { text: 'Transcript', expanded: false, templateName: 'transcriptContent' },
    { text: 'Notes', expanded: false, templateName: 'addNotes' },
    {
      text: 'Linked Documents',
      expanded: false,
      templateName: 'linkedDocuments',
    },
    { text: 'WordWheel', expanded: false, templateName: 'wordWheel' },
    { text: 'Highlights', expanded: false, templateName: 'highlightDocs' },
  ]

  public btnDropdown = [
    { text: 'All' },
    { text: 'Notes' },
    { text: 'Highlight' },
  ]

  public listItems: Array<string> = ['Item 1', 'Item 2', 'Item 3']

  public sizeItems: Array<string> = ['Item 1', 'Item 2', 'Item 3']

  public weightItems: Array<string> = ['Item 1', 'Item 2', 'Item 3']

  public dialogTitle = 'Notes'

  public noteDialogOpenStatus = false

  public contextMenuItems: any = [
    {
      text: 'Highlight',
      svgIcon: borderColorIcon,
      action: () => this.addNote(),
    },
    { text: 'Add note', svgIcon: fileTxtIcon, action: () => this.addNote() },
    { text: 'Link document', svgIcon: linkIcon, action: () => this.addNote() },
  ]

  public noteItems = []

  public notesViewerComponent: Promise<Type<unknown>>

  //generate 10 wheel items

  public wheelItems: any = [
    { label: '113:13', id: 1 },
    { label: '411:14', id: 2 },
    { label: '55:15', id: 3 },
    { label: '11:16', id: 4 },
    { label: '12:17', id: 5 },
    { label: '325:18', id: 6 },
    { label: '91:19', id: 7 },
    { label: '11:20', id: 8 },
    { label: '11:21', id: 9 },
    { label: '11:22', id: 10 },
  ]

  public icons = { chevronRightIcon: chevronRightIcon }

  //***** Transcript UI */

  public getTemplate(templateName: string): TemplateRef<any> {
    return this[templateName]
  }

  public onAction(index: number): void {
    this.items[index].expanded = !this.items[index].expanded
  }

  public onItemNoteClick(item: any): void {
    // Handle the click event here
    console.log('Open note in the right panel and add new note')
  }

  public onContextMenuSelect(event: any): void {
    const selectedItem = event.item
    if (selectedItem && selectedItem.action) {
      selectedItem.action()
    }
  }

  public addNote(): void {
    this.notesViewerComponent = import('@venio/feature/document-notes').then(
      ({ DocumentNotesComponent }) => DocumentNotesComponent
    )
    this.noteDialogOpenStatus = true
  }

  public close(status: string): void {
    this.noteDialogOpenStatus = false
  }

  public toggleMenuState(): void {
    this.menuState = !this.menuState
  }

  public trackById(index: number, item: any): number {
    return item.id
  }
}
