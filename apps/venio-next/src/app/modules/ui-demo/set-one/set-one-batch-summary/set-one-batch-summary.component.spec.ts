import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SetOneBatchSummaryComponent } from './set-one-batch-summary.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('SetOneBatchSummaryComponent', () => {
  let component: SetOneBatchSummaryComponent
  let fixture: ComponentFixture<SetOneBatchSummaryComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SetOneBatchSummaryComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(SetOneBatchSummaryComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
