<div class="t-mt-4 t-flex t-gap-2">
  <div class="t-w-full">
    <div
      class="t-flex t-items-center t-gap-2 t-justify-between t-w-64 t-pl-[26px]">
      <span class="t-inline-flex t-items-center t-gap-2">
        <input type="checkbox" #allFiles kendoCheckBox [checked]="true" />
        <kendo-label
          class="k-radio-label"
          [for]="allFiles"
          text="Select All"></kendo-label>
      </span>

      <span>
        <button
          kendoButton
          #deleteBtn
          kendoTooltip
          title="Delete"
          fillMode="clear"
          size="medium">
          <span
            [parentElement]="deleteBtn.element"
            venioSvgLoader
            applyEffectsTo="fill"
            hoverColor="#ED7425"
            color="#979797"
            svgUrl="assets/svg/Icon-material-delete.svg"
            height="0.75rem"
            width="0.8rem">
            <kendo-loader size="small"></kendo-loader>
          </span>
        </button>
      </span>
    </div>
    <kendo-treeview
      [nodes]="treeViewData"
      textField="text"
      kendoTreeViewExpandable
      kendoTreeViewCheckable
      [(checkedKeys)]="checkedKeys"
      [hasChildren]="hasChildren"
      class="v-hide-scrollbar"
      [children]="getChildren"
      [expandedKeys]="expandedKeys"
      [extraSpacing]="70"
      venioDynamicHeight>
      <ng-template kendoTreeViewNodeTemplate let-dataItem let-id="id">
        <div class="t-flex t-items-center t-gap-2">
          @if(dataItem.items){
          <span class="t-font-semibold">
            {{ dataItem.text }}
          </span>

          <span>
            <div
              class="t-font-medium t-text-xs"
              [ngClass]="{
        't-text-success': dataItem.status === 'COMPLETED',
        't-text-error': dataItem.status === 'FAILED',
        't-text-[#FFBB12]': dataItem.status === 'IN PROGRESS',
        't-text-[#718792]': dataItem.status === 'NOT STARTED',
      }">
              {{ dataItem.status }}
            </div>
          </span>

          <span class="t-text-[#656565] t-text-xs t-font-normal">{{
            dataItem.name
          }}</span>
          } @else {
          <span class="t-inline-block">
            {{ dataItem.text }}
            <span class="t-text-[#656565] t-text-xs t-font-normal"
              >Outlook Msg</span
            >
          </span>
          }
        </div>
      </ng-template>
    </kendo-treeview>
  </div>
</div>
