<div class="t-flex t-flex-col t-w-full t-mt-3">
  <div class="t-bg-[#FAFDF6] t-p-4 t-rounded t-flex t-items-center t-gap-10">
    <div class="t-flex t-flex-col t-gap-4">
      <div class="t-text-[var(--v-custom-sky-blue)] t-font-medium t-text-base">
        Layout
      </div>
      <div class="t-text-base t-text-gray-600">Reviewer layout one</div>
    </div>

    <div class="t-flex t-flex-col t-gap-4">
      <div class="t-text-[var(--v-custom-sky-blue)] t-font-medium t-text-base">
        Highlight Group
      </div>
      <div class="t-text-base t-text-gray-600">Default</div>
    </div>

    <div class="t-flex t-items-end t-gap-4">
      <div class="t-flex t-flex-col t-gap-1.5">
        <label
          for="searchBy"
          class="t-text-[var(--v-custom-sky-blue)] t-font-medium t-text-base"
          >Search By</label
        >

        <kendo-dropdownlist
          class="t-w-56"
          [data]="listItems"
          [(ngModel)]="selectedValue"></kendo-dropdownlist>
      </div>

      <div class="t-flex t-flex-col t-gap-4">
        <kendo-multiselecttree
          #multiselecttree
          [kendoMultiSelectTreeHierarchyBinding]="tagDropdowndata"
          [filterable]="true"
          [tagMapper]="tagMapper"
          kendoMultiSelectTreeExpandable
          [expandOnFilter]="filterExpandSettings"
          [checkAll]="true"
          childrenField="items"
          textField="text"
          valueField="text"
          class="!t-w-56"
          *ngIf="selectedValue === 'Tags'"
          placeholder="Select Tags"
          [popupSettings]="{ popupClass: 'v-tags-multiselect' }">
          <ng-template kendoSuffixTemplate>
            <button
              kendoButton
              [svgIcon]="downIcon"
              fillMode="link"
              class="t-absolute t-right-0 t-select-none t-cursor-pointer t-bg-white"></button>
          </ng-template>
        </kendo-multiselecttree>

        <input
          *ngIf="selectedValue === 'Query'"
          kendoTextBox
          placeholder="Input Query"
          class="t-w-56" />
      </div>

      <button
        kendoButton
        class="t-w-4"
        themeColor="info"
        fillMode="outline"
        (click)="batchResponsive = !batchResponsive"
        #searchBtn>
        <span
          venioSvgLoader
          applyEffectsTo="both"
          [parentElement]="searchBtn.element"
          hoverColor="#ffffff"
          svgUrl="assets/svg/icon-updated-search.svg"
          width="16px"
          height="16px"></span>
      </button>
    </div>
  </div>

  <div class="t-flex t-flex-col t-gap-4 t-flex-1 t-mt-4">
    <div
      class="t-flex t-gap-2 t-font-semibold t-text-base t-items-center"
      [ngClass]="{
        't-border-[#ececec] t-border-b-[1px] t-border-l-0 t-border-r-0 t-pb-2 ':
          batchResponsive
      }">
      Batch Summary

      <button
        kendoButton
        themeColor="secondary"
        class="v-custom-secondary-button t-p-0 t-w-[37px] hover:t-text-[#FFFFFF] hover:t-bg-[#9BD2A7]"
        fillMode="outline"
        *ngIf="batchResponsive"
        #actionGrid1>
        <span
          venioSvgLoader
          [parentElement]="actionGrid1.element"
          applyEffectsTo="both"
          hoverColor="#FFFFFF"
          color="#9AD3A6"
          svgUrl="assets/svg/icon-refresh-twoway.svg"
          height="1.1rem"
          width="1.1rem">
          <kendo-loader size="small"></kendo-loader>
        </span>
      </button>
    </div>
    <kendo-grid
      *ngIf="!batchResponsive"
      class="t-flex t-w-full t-h-full t-border-b-1 t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto"
      [kendoGridBinding]="cases"
      venioDynamicHeight
      [sortable]="true"
      [groupable]="false"
      [reorderable]="true"
      [resizable]="true"
      kendoGridSelectBy="id"
      [pageable]="{ type: 'numeric', position: 'top' }"
      [filterable]="'menu'"
      [selectable]="{ checkboxOnly: true, mode: 'multiple' }">
      <ng-template kendoPagerTemplate>
        <kendo-grid-spacer></kendo-grid-spacer>

        <kendo-buttongroup>
          <button
            kendoButton
            #actionGrid5
            class="!t-py-[0.38rem] !t-px-[0.5rem] t-rounded-tr-none t-rounded-br-none t-w-[33px] t-h-[33px] hover:t-border-[#2F3080] hover:t-bg-[#2F3080]"
            kendoTooltip
            [title]="capitalizeTitle(commonActionTypes.REBATCH)"
            size="none">
            <span
              [parentElement]="actionGrid5.element"
              venioSvgLoader
              hoverColor="#FFFFFF"
              color="#979797"
              svgUrl="assets/svg/icon-fast-forward-arrow.svg"
              height="0.85rem"
              width="0.85rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>

          <button
            kendoButton
            #actionGrid6
            class="!t-py-[0.38rem] !t-px-[0.5rem] t-rounded-tr-sm t-rounded-br-sm t-w-[33px] t-h-[33px] hover:t-border-[#ED7425] hover:t-bg-[#ED7425]"
            kendoTooltip
            [title]="capitalizeTitle(commonActionTypes.REBATCH)"
            size="none">
            <span
              [parentElement]="actionGrid6.element"
              venioSvgLoader
              applyEffectsTo="fill"
              hoverColor="#FFFFFF"
              color="#979797"
              svgUrl="assets/svg/Icon-material-delete.svg"
              height="0.75rem"
              width="0.8rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>
        </kendo-buttongroup>
        <venio-pagination
          [disabled]="cases?.length === 0"
          [totalRecords]="cases?.length"
          [pageSize]="pageSize"
          [showPageJumper]="false"
          [showPageSize]="true"
          [showRowNumberInputBox]="true"
          class="t-px-5 t-block t-py-2">
        </venio-pagination>
      </ng-template>

      <kendo-grid-column
        field="id"
        [width]="35"
        title="#"
        headerClass="t-text-primary"
        [filterable]="false">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem.id }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-checkbox-column
        [showSelectAll]="true"
        [width]="35"></kendo-grid-checkbox-column>

      <kendo-grid-column
        field="name"
        title="Name"
        [width]="200"
        headerClass="t-text-primary"
        [filterable]="false">
        <ng-template kendoGridCellTemplate let-dataItem>
          <span>
            {{ dataItem.name }}
          </span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column
        field="totalDocuments"
        title="Total Documents"
        [width]="150"
        headerClass="t-text-primary"
        [filterable]="false">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem.totalDocuments | number }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column
        field="remaining"
        title="Remaining"
        [width]="150"
        headerClass="t-text-primary"
        [filterable]="false">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem.remaining | number }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column
        field="reviewer"
        title="Reviewer"
        [width]="150"
        headerClass="t-text-primary"
        [filterable]="false">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem.reviewer }}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column
        field="status"
        title="Status"
        [width]="150"
        headerClass="t-text-primary"
        [filterable]="false">
        <ng-template kendoGridCellTemplate let-dataItem>
          <div
            class="t-font-medium"
            [ngClass]="{
        't-text-success': dataItem.status === 'COMPLETED',
        't-text-error': dataItem.status === 'FAILED',
        't-text-[#FFBB12]': dataItem.status === 'IN PROGRESS',
      }">
            {{ dataItem.status }}
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column
        title="Action"
        [width]="200"
        headerClass="t-text-primary"
        [filterable]="false">
        <ng-template kendoGridCellTemplate let-dataItem>
          <kendo-buttongroup>
            <button
              kendoButton
              #actionGrid2
              class="!t-py-[0.38rem] !t-px-[0.5rem] t-rounded-tr-none t-rounded-br-none t-w-[33px] hover:t-border-[#9BD2A7] hover:t-bg-[#9BD2A7]"
              kendoTooltip
              (click)="caseActionControls(dataItem, commonActionTypes.REASSIGN)"
              [title]="capitalizeTitle(commonActionTypes.REASSIGN)"
              size="none">
              <span
                [parentElement]="actionGrid2.element"
                venioSvgLoader
                hoverColor="#FFFFFF"
                color="#979797"
                svgUrl="assets/svg/icon-shuffle-communicate.svg"
                height="0.85rem"
                width="0.85rem">
                <kendo-loader size="small"></kendo-loader>
              </span>
            </button>

            <button
              kendoButton
              #actionGrid3
              class="!t-py-[0.38rem] !t-px-[0.5rem] t-rounded-tr-none t-rounded-br-none t-w-[33px] hover:t-border-[#2F3080] hover:t-bg-[#2F3080]"
              kendoTooltip
              [title]="capitalizeTitle(commonActionTypes.REBATCH)"
              size="none">
              <span
                [parentElement]="actionGrid3.element"
                venioSvgLoader
                applyEffectsTo="fill"
                hoverColor="#FFFFFF"
                color="#979797"
                svgUrl="assets/svg/icon-fast-forward-arrow.svg"
                height="0.75rem"
                width="0.8rem">
                <kendo-loader size="small"></kendo-loader>
              </span>
            </button>
            <button
              kendoButton
              #actionGrid4
              class="!t-py-[0.38rem] !t-px-[0.5rem] t-rounded-tr-sm t-rounded-br-sm t-w-[33px] hover:t-border-[#ED7425] hover:t-bg-[#ED7425]"
              (click)="caseActionControls(dataItem, commonActionTypes.DELETE)"
              kendoTooltip
              [title]="capitalizeTitle(commonActionTypes.DELETE)"
              size="none"
              *ngIf="dataItem.name !== 'Default'">
              <span
                [parentElement]="actionGrid4.element"
                venioSvgLoader
                applyEffectsTo="fill"
                hoverColor="#FFFFFF"
                color="#979797"
                svgUrl="assets/svg/Icon-material-delete.svg"
                height="0.75rem"
                width="0.8rem">
                <kendo-loader size="small"></kendo-loader>
              </span>
            </button>
          </kendo-buttongroup>
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>

    <venio-set-one-batch-reponsive
      *ngIf="batchResponsive"></venio-set-one-batch-reponsive>
  </div>
</div>

<div
  class="t-fixed t-top-[1px] t-left-0 t-w-full t-h-full t-bg-[#212121] t-opacity-10 t-z-[1999]"
  *ngIf="isOverlayActive"
  (click)="isOverlayActive = !isOverlayActive"></div>
<div
  class="t-fixed t-top-[1px] t-w-[56%] t-h-full t-bg-white t-overflow-hidden t-shadow-[0px_20px_16px_6px_rgba(0,0,0,0.212)] t-z-[2000] t-transition-all t-duration-400 t-p-5"
  [ngClass]="{
    't-right-0': isOverlayActive,
    't-right-[-56%]': !isOverlayActive
  }">
  <div class="t-flex t-justify-between t-items-center t-w-full">
    <span
      class="t-inline-flex t-items-center t-gap-3 t-text-primary t-text-lg t-font-semibold">
      <button
        class="t-w-[35px] t-p-0 t-bg-[#F3F3F3] t-rounded-full t-h-[35px] t-flex t-items-center"
        fillMode="clear"
        kendoButton
        [imageUrl]="overlayIconUrl"></button>
      {{ overlayTitle }}
      <span class="t-text-[#263238] t-pl-1 t-text-[#263238]">{{
        selectedId
      }}</span>
    </span>
    <button
      (click)="closeOverlay()"
      class="v-dialog-action-button v-dialog-action-button-cancel v-dialog-close t-rounded-full t-text-white t-w-6 t-h-6 t-p-0 t-bg-[#ED7425] t-leading-none">
      <kendo-svg-icon [icon]="icons.closeIcon"></kendo-svg-icon>
    </button>
  </div>

  <venio-set-one-batch-reassign
    *ngIf="activeComponent === 'reassign'"></venio-set-one-batch-reassign>
</div>
