import { ComponentFixture, TestBed } from '@angular/core/testing'
import { UserAccountCreationReportComponent } from './user-account-creation-report.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('UserAccountCreationReportComponent', () => {
  let component: UserAccountCreationReportComponent
  let fixture: ComponentFixture<UserAccountCreationReportComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UserAccountCreationReportComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(UserAccountCreationReportComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
