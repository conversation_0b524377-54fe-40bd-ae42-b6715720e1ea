<div class="t-flex t-p-8">
  <button kendoButton (click)="openDialog()">Send/Remove folder dialog</button>
</div>

<kendo-dialog
  *ngIf="opened"
  (close)="close('cancel')"
  [maxHeight]="550"
  [height]="'90vh'"
  [minWidth]="250"
  [width]="'45%'">
  <kendo-dialog-titlebar>
    <div>
      {{ dialogTitle }}
    </div>
  </kendo-dialog-titlebar>

  <div class="t-flex t-flex-col t-h-full t-overflow-hidden">
    <div class="t-flex t-w-full t-mt-4 t-flex-col t-gap-3 v-custom-grey-bg">
      <div class="t-flex t-w-full t-flex-wrap t-gap-3">
        <div
          class="t-flex t-flex-row-reverse t-gap-2 t-justify-end t-mt-3 t-items-center">
          <label class="k-checkbox-label" for="terms">Document families</label>
          <input type="checkbox" id="terms" kendoCheckBox />
        </div>

        <div
          class="t-flex t-flex-row-reverse t-gap-2 t-justify-end t-mt-3 t-items-center">
          <label class="k-checkbox-label" for="terms">Discussion threads</label>
          <input type="checkbox" id="terms" kendoCheckBox />
        </div>
      </div>

      <div class="t-flex mt-2">
        <div
          showHints="always"
          class="t-flex t-flex-0 t-basis-[100%] t-gap-1 t-flex-col">
          <kendo-label
            for="Description"
            class="t-text-xs t-uppercase t-tracking-widest">
            Comments
          </kendo-label>
          <kendo-textarea
            #Description
            placeholder="Textarea"
            [rows]="3"
            resizable="vertical"></kendo-textarea>
        </div>
      </div>

      <div class="t-flex t-gap-2">
        <div
          showHints="always"
          class="t-flex t-flex-0 t-basis-1/2 t-gap-1 t-flex-col">
          <kendo-dropdownlist
            [defaultItem]="defaultItemCategories"
            [data]="['Copy to Folder', 'Remove from Folder']"
            textField="text"
            valueField="value">
          </kendo-dropdownlist>
        </div>
        <div
          showHints="always"
          class="t-flex t-flex-0 t-basis-1/2 t-gap-1 t-flex-col">
          <kendo-textbox
            placeholder="Folder name"
            [readonly]="true"></kendo-textbox>
        </div>

        <div
          showHints="always"
          class="t-flex t-flex-0 t-basis-1/2 t-gap-1 t-flex-col">
          <kendo-textbox
            placeholder="To Folder"
            [readonly]="true"></kendo-textbox>
        </div>
      </div>

      <div class="t-flex">
        <div
          class="t-flex t-flex-row-reverse t-gap-2 t-justify-end t-mt-3 t-items-center">
          <label class="k-checkbox-label" for="includeSubFolders"
            >Include sub Folders</label
          >
          <input type="checkbox" id="includeSubFolders" kendoCheckBox />
        </div>
      </div>

      <div class="t-flex">
        <!-- tree list -->

        <kendo-treelist
          [kendoTreeListFlatBinding]="data"
          idField="id"
          parentIdField="managerId"
          [height]="140"
          kendoTreeListExpandable
          [initiallyExpanded]="false"
          kendoTreeListSelectable
          [selectable]="settings"
          [(selectedItems)]="selected"
          [ngClass]="'v-custom-folder-tree-list v-custom-tagtree t-mb-5'"
          [columnMenu]="false"
          [sortable]="true"
          [resizable]="true"
          [navigable]="true"
          [pageable]="false">
          <kendo-treelist-checkbox-column
            headerClass="t-text-primary v-custom-tagheader"
            [expandable]="true"
            field="name"
            title="Tags"
            [width]="350"
            [checkChildren]="true"
            [showSelectAll]="true">
            <ng-template kendoTreeListCellTemplate let-dataItem>
              <span
                class="t-inline-block t-absolute t-ml-7 t-mt-[2px]"
                venioSvgLoader
                svgUrl="assets/svg/icon-folder-outline.svg"
                height="1.1rem"
                width="1.1rem"></span>

              <div class="t-inline-block t-absolute t-ml-12">
                {{ dataItem.name }}
              </div>
            </ng-template>
          </kendo-treelist-checkbox-column>
        </kendo-treelist>
      </div>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="close('no')"
        class="v-custom-secondary-button"
        themeColor="secondary"
        fillMode="outline"
        data-qa="save-button">
        SEND
      </button>
      <button
        kendoButton
        (click)="close('yes')"
        themeColor="dark"
        fillMode="outline"
        data-qa="cancel-button">
        CANCEL
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>
