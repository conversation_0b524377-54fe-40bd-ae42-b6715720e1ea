import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LaunchpadCaseuiComponent } from './launchpad-caseui.component'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('LaunchpadCaseuiComponent', () => {
  let component: LaunchpadCaseuiComponent
  let fixture: ComponentFixture<LaunchpadCaseuiComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LaunchpadCaseuiComponent, BrowserAnimationsModule],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(LaunchpadCaseuiComponent)
    component = fixture.componentInstance
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
