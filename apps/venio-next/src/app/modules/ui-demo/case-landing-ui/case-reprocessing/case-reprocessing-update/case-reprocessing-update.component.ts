import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'

import { FormsModule } from '@angular/forms'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { GridModule } from '@progress/kendo-angular-grid'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { IconsModule } from '@progress/kendo-angular-icons'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { State, FilterDescriptor } from '@progress/kendo-data-query'

@Component({
  selector: 'venio-case-reprocessing-update',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    LabelModule,
    FormsModule,
    InputsModule,
    ButtonsModule,
    LoaderModule,
    SvgLoaderDirective,
    TooltipModule,
    TreeListModule,
    DynamicHeightDirective,
    GridModule,
    TreeViewModule,
    DropDownsModule,
  ],
  templateUrl: './case-reprocessing-update.component.html',
  styleUrl: './case-reprocessing-update.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseReprocessingUpdateComponent implements OnInit {
  public setTimeout = false

  public edocMeta: Array<{ id: number; text: string }> = []

  public emailMeta: Array<{ id: number; text: string }> = []

  public dropdownOptions = ['ReExtract all Child', 'Option 2', 'Option 3']

  public selectedDropdownOption = 'ReExtract all Child'

  public filteredEdocMeta: any[] = []

  public searchValue = ''

  public filterOptions: Array<string> = [
    'Author',
    'Company',
    'Title',
    'CreateDateTime',
  ]

  public state: State = {
    skip: 0,
    take: 10,
    filter: null,
  }

  public ngOnInit(): void {
    this.generateEdocMeta()
    this.generateEmailMeta()
  }

  public generateEdocMeta(): void {
    const metaFields = [
      'Company',
      'Title',
      'Author',
      'CreateDateTime',
      'LastSavedDateTime',
      'Sheet Hidden',
      'Row Hidden',
      'File Size',
      'Page Count',
      'File Extension',
    ]

    this.edocMeta = []

    for (let i = 0; i < 50; i++) {
      const field = metaFields[i % metaFields.length]
      this.edocMeta.push({
        id: i + 1,
        text: `${field} ${i + 1}`,
      })
    }

    this.filteredEdocMeta = [...this.edocMeta]
  }

  public generateEmailMeta(): void {
    const emailFields = [
      'Relative Path',
      'MessageID',
      'Creation Time',
      'Last Modified Time',
      'Date Set',
      'Date Received',
      'From',
    ]

    for (let i = 0; i < 50; i++) {
      const field = emailFields[i % emailFields.length]
      this.emailMeta.push({
        id: i + 1,
        text: `${field} ${i + 1}`,
      })
    }
  }

  public applyFilter(): void {
    const filterValue = this.searchValue.toLowerCase()
    this.filteredEdocMeta = this.edocMeta.filter((item) =>
      item.text.toLowerCase().includes(filterValue)
    )
  }

  public onFilterChange(value: string, filterService: any, column: any): void {
    const filter: FilterDescriptor = {
      field: column.field,
      operator: 'contains',
      value: value,
    }

    filterService.filter({
      filters: value ? [filter] : [],
      logic: 'and',
    })
  }
}
