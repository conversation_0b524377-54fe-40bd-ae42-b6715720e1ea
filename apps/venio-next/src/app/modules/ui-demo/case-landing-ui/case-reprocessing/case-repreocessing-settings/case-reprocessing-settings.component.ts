import {
  ChangeDetectionStrategy,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  ChangeDetectorRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Observable, of } from 'rxjs'

import { FormsModule } from '@angular/forms'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { GridModule } from '@progress/kendo-angular-grid'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { IconsModule } from '@progress/kendo-angular-icons'
import {
  xIcon,
  moreVerticalIcon,
  fileZipIcon,
  fileImageIcon,
  filePdfIcon,
  fileCsvIcon,
  fileAudioIcon,
  fileExcelIcon,
  fileIcon,
} from '@progress/kendo-svg-icons'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { LayoutModule } from '@progress/kendo-angular-layout'

export interface Password {
  NsfUserId: string
  password: string
}
@Component({
  selector: 'venio-case-reprocessing-settings',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    LabelModule,
    FormsModule,
    InputsModule,
    ButtonsModule,
    LoaderModule,
    SvgLoaderDirective,
    TooltipModule,
    TreeListModule,
    DynamicHeightDirective,
    GridModule,
    TreeViewModule,
    DropDownsModule,
    LayoutModule,
  ],
  templateUrl: './case-reprocessing-settings.component.html',
  styleUrl: './case-reprocessing-settings.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseReprocessingSettingsComponent implements OnInit, OnDestroy {
  public sourceData: any[] = []

  public icons = {
    closeIcon: xIcon,
    moreVerticalIcon: moreVerticalIcon,
    fileZipIcon: fileZipIcon,
    fileImageIcon: fileImageIcon,
    filePdfIcon: filePdfIcon,
    fileCsvIcon: fileCsvIcon,
    fileAudioIcon: fileAudioIcon,
    fileExcelIcon: fileExcelIcon,
    fileIcon: fileIcon,
  }

  public passwordOptions = ['None', 'Password1', 'Password2']

  public defaultPassword = 'Password'

  public timeoutOptions = ['5 mins', '10 mins', '15 mins']

  public metaExtractionOptions = ['Option1', 'Option2', 'Option3']

  public childExtractionOptions = ['Child 1', 'Child 2', 'Child 3']

  public testExtractionOptions = ['Child 1', 'Child 2', 'Child 3']

  public defaultMetaExtraction = 'Meta Extraction'

  public defaultTestExtraction = 'Test Extraction'

  public defaultChildExtraction = 'Child Extraction'

  public repositoryHierarchy = [
    {
      name: 'Source',
      items: [
        { name: '25498261-mp4_h264_aac_512kb.' },
        { name: 'David_Hutchins_Repair.pst' },
        { name: 'Email_W_Attachments.zip' },
      ],
    },
  ]

  // for tree
  public systemDynamicFolders: any[] = [] // Data source for the TreeList

  public expandedFolderIds: any[] = [] // Tracks expanded folder IDs

  public selected: any[] = [] // Tracks selected items

  // for sliding animation for tab content
  public isExpanded = false

  public isTransitioning = false

  public isVisible = false

  private animationTimeout: any = null

  private resetTimeout: any = null

  public isAnimating = false

  public passwords: Password[] = []

  public filteredPasswordData: Password[] = []

  public selectedPassword: Password = null

  constructor(private cdr: ChangeDetectorRef) {}

  public ngOnInit(): void {
    this.generateDummyData()
    this.generateTreeData()
    this.generatePasswordData()
  }

  public hasChildren = (item: any): boolean => {
    return item.items && item.items.length > 0
  }

  public getChildren(node: any): Observable<any[]> {
    return of(node.items)
  }

  public onSelect(e: any): void {
    console.log('Selected item: ', e)
  }

  public generateDummyData(): void {
    this.sourceData = [] // Initialize the array

    for (let i = 1; i <= 50; i++) {
      this.sourceData.push({
        id: i,
        name: i === 1 ? 'Select All' : i % 2 === 0 ? 'Source' : 'Venio',
      })
    }
  }

  public generateTreeData(): void {
    const rootFolders = 1 // Number of root folders
    const filesPerFolder = 15 // Number of files per folder

    let folderId = 1

    for (let i = 1; i <= rootFolders; i++) {
      // Create a root folder
      const rootFolder = {
        folderId: folderId++,
        parentFolderId: null,
        folderName: `Source Folder ${i}`,
        isFolder: true,
      }
      this.systemDynamicFolders.push(rootFolder)

      // Generate file types
      const fileTypes = ['jpg', 'zip', 'pdf', 'csv', 'aac', 'xls']

      // Generate 15 files for the folder
      for (let j = 0; j < filesPerFolder; j++) {
        const fileType = fileTypes[j % fileTypes.length] // Cycle through file types
        const fileName = `File_${Math.random()
          .toString(36)
          .substring(2, 8)}.${fileType}`

        // Create a child file
        const childFile = {
          folderId: folderId++,
          parentFolderId: rootFolder.folderId,
          folderName: fileName,
          fileType: fileType,
          isFolder: false,
        }
        this.systemDynamicFolders.push(childFile)
      }
    }
    // Expand all folders initially
    this.expandedFolderIds = this.systemDynamicFolders.map(
      (item) => item.folderId
    )
  }

  public generatePasswordData(): void {
    this.filteredPasswordData = [] // Initialize the array

    const commonNsfIds = [
      'John',
      'Marlin',
      'Freddy',
      'cupper',
      'mary',
      'Antony',
    ]

    for (let i = 1; i <= 50; i++) {
      this.filteredPasswordData.push({
        NsfUserId: commonNsfIds[i % commonNsfIds.length],
        password:
          commonNsfIds[i % commonNsfIds.length] + '@' + ((i * 407) % 1000),
      })
    }
    this.passwords = this.filteredPasswordData
  }

  public getFileIcon(fileType: string): any {
    const fileTypeIcons = {
      jpg: this.icons.fileImageIcon,
      zip: this.icons.fileZipIcon,
      pdf: this.icons.filePdfIcon,
      csv: this.icons.fileCsvIcon,
      aac: this.icons.fileAudioIcon,
      xls: this.icons.fileExcelIcon,
    }

    // Return the matching icon or the default file icon
    return fileTypeIcons[fileType] || this.icons.fileIcon
  }

  /**
   * Filters the TreeList based on the search input.
   * @param value The search value entered by the user
   */
  public onFilter(value: string): void {
    this.systemDynamicFolders = this.systemDynamicFolders.filter((folder) =>
      folder.folderName.toLowerCase().includes(value.toLowerCase())
    )
  }

  public toggleSlide(): void {
    // Prevent toggling while an animation is already in progress
    if (this.isAnimating) return

    this.isAnimating = true // Mark animation as in progress

    // Clear any existing timeouts to avoid memory leaks or duplicate execution
    if (this.animationTimeout) {
      clearTimeout(this.animationTimeout)
    }

    if (!this.isExpanded) {
      // On expanding, make the element visible first
      this.isVisible = true // Add element to DOM
      this.cdr.detectChanges() // Force Angular to update the DOM immediately

      this.animationTimeout = setTimeout(() => {
        this.isExpanded = true // Trigger the animation
        this.cdr.detectChanges() // Force DOM update
      }, 10) // Ensure a small delay for DOM to apply initial styles
    } else {
      // On collapsing, reverse the animation first
      this.isExpanded = false
      this.cdr.detectChanges() // Force DOM update for collapsing

      this.animationTimeout = setTimeout(() => {
        this.isVisible = false // Remove the element from the DOM after animation
        this.cdr.detectChanges() // Force DOM update
      }, 500) // Match the transition duration
    }

    // Reset `isAnimating` flag after the animation completes
    if (this.resetTimeout) {
      clearTimeout(this.resetTimeout)
    }
    this.resetTimeout = setTimeout(() => {
      this.isAnimating = false
    }, 500) // Match the total animation duration
  }

  // Clean up any pending timeouts when the component is destroyed
  public ngOnDestroy(): void {
    if (this.animationTimeout) {
      clearTimeout(this.animationTimeout)
    }
    if (this.resetTimeout) {
      clearTimeout(this.resetTimeout)
    }
  }

  public onPasswordFilter(value: string): void {
    const searchValue = value.toLowerCase()
    this.filteredPasswordData = this.passwords.filter(
      (item) =>
        item.NsfUserId.toLowerCase().includes(searchValue) ||
        item.password.toLowerCase().includes(searchValue)
    )
  }

  public onPasswordSelectionChange(value: Password): void {
    this.selectedPassword = value
    // console.log(this.selectedPassword.NsfUserId, this.selectedPassword.password)
  }
}
