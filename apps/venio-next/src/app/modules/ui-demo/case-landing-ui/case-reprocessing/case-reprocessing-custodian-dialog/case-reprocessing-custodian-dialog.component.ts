import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { Observable, of } from 'rxjs'

import { FormsModule } from '@angular/forms'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { IconsModule } from '@progress/kendo-angular-icons'
import { xIcon } from '@progress/kendo-svg-icons'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import { TreeViewModule } from '@progress/kendo-angular-treeview'

@Component({
  selector: 'venio-case-reprocessing-custodian-dialog',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    LabelModule,
    FormsModule,
    InputsModule,
    ButtonsModule,
    LoaderModule,
    SvgLoaderDirective,
    TooltipModule,
    TreeListModule,
    DynamicHeightDirective,
    TreeViewModule,
  ],
  templateUrl: './case-reprocessing-custodian-dialog.component.html',
  styleUrl: './case-reprocessing-custodian-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseReprocessingCustodianDialogComponent implements OnInit {
  public treeViewData: any[] = [
    {
      text: 'Custodian Name - A Social Media',
      id: '0',
      items: [
        { text: 'Facebook few', id: '1' },
        { text: 'Excel', id: '2' },
        { text: 'Slack Message', id: '3' },
        { text: 'Cellebrite', id: '4' },
      ],
    },
    {
      text: 'Custodian Name - Abricto',
      id: '5',
      items: [{ text: 'M001', id: '6' }],
    },
  ]

  public checkedKeys: any[] = ['1', '3', '6']

  public expandedKeys: any[] = []

  public treeViewData2: any[] = [
    {
      text: 'Media Name',
      id: '0',
      items: [
        { text: 'Facebook few', id: '1' },
        { text: 'Excel', id: '2' },
        { text: 'Slack Message', id: '3' },
        { text: 'Cellebrite', id: '4' },
      ],
    },
  ]

  public checkedKeys2: any[] = ['0']

  public expandedKeys2: any[] = []

  constructor() {}

  public ngOnInit(): void {
    this.expandedKeys = this.getAllNodeIds(this.treeViewData)
  }

  public icons = {
    closeIcon: xIcon,
  }

  public hasChildren = (item: any): boolean => {
    return item.items && item.items.length > 0
  }

  public getChildren(node: any): Observable<any[]> {
    return of(node.items)
  }

  private getAllNodeIds(nodes: any[]): string[] {
    let keys: string[] = []

    for (const node of nodes) {
      if (node.id) {
        keys.push(node.id) // Add current node ID
      }

      if (node.items) {
        keys = keys.concat(this.getAllNodeIds(node.items)) // Add children IDs recursively
      }
    }

    return keys
  }
}
