import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { ExpansionPanelModule } from '@progress/kendo-angular-layout'
import { SortableModule } from '@progress/kendo-angular-sortable'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { IconsModule } from '@progress/kendo-angular-icons'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import { GridModule } from '@progress/kendo-angular-grid'
import { KENDO_TREELIST } from '@progress/kendo-angular-treelist'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import {
  DateInputsModule,
  DatePickerModule,
} from '@progress/kendo-angular-dateinputs'
import { ProcessingComponent } from './processing/processing.component'
import { GeneralSettingComponent } from './general-setting/general-setting.component'
import { FileFiltersComponent } from './file-filters/file-filters.component'
import { ImageSettingsComponent } from './image-settings/image-settings.component'

@Component({
  selector: 'venio-case-creation',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    LabelModule,
    DropDownListModule,
    InputsModule,
    ExpansionPanelModule,
    SortableModule,
    SvgLoaderDirective,
    IconsModule,
    TooltipDirective,
    IndicatorsModule,
    ButtonsModule,
    TreeViewModule,
    ReactiveFormsModule,
    GridModule,
    DateInputsModule,
    DatePickerModule,
    KENDO_TREELIST,
    ProcessingComponent,
    GeneralSettingComponent,
    FileFiltersComponent,
    ImageSettingsComponent,
  ],
  templateUrl: './case-creation.component.html',
  styleUrl: './case-creation.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseCreationComponent {
  public currentExpandedPanel: string | null = ''

  public onPanelToggle(panelId: string, isExpanded: boolean): void {
    this.currentExpandedPanel = isExpanded ? panelId : null
  }

  public defaultTemplate = { id: null, name: 'Select Template' }

  public defaultTimezone = { id: null, name: 'Time Zone' }

  public defaultScope = {
    id: null,
    name: 'Show only one instance in the selected scope (DynamicDeDupeTM)',
  }
}
