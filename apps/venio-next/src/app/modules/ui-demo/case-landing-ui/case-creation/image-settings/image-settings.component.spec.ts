import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ImageSettingsComponent } from './image-settings.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('ImageSettingsComponent', () => {
  let component: ImageSettingsComponent
  let fixture: ComponentFixture<ImageSettingsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ImageSettingsComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(ImageSettingsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
