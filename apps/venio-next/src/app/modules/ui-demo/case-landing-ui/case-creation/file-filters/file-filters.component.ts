import { ChangeDetectionStrategy, Component, ViewChild } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  CellClickEvent,
  CellCloseEvent,
  KENDO_TREELIST,
  TreeListComponent,
} from '@progress/kendo-angular-treelist'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { DatePickerModule } from '@progress/kendo-angular-dateinputs'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-file-filters',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    LabelModule,
    DropDownListModule,
    InputsModule,
    ButtonsModule,
    TreeViewModule,
    GridModule,
    KENDO_TREELIST,
    ReactiveFormsModule,
    DatePickerModule,
    SvgLoaderDirective,
  ],
  templateUrl: './file-filters.component.html',
  styleUrl: './file-filters.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FileFiltersComponent {
  @ViewChild(TreeListComponent) public treelist: TreeListComponent

  public advance_hash = [
    { id: 1, name: 'Attachment Name' },
    { id: 2, name: 'BCC' },
    { id: 3, name: 'CC' },
    { id: 4, name: 'From' },
    { id: 5, name: 'Subject' },
    { id: 6, name: 'To' },
    { id: 7, name: 'Send Date' },
  ]

  public source = [
    {
      fileType: 'Archive',
      description: '',
      extension: '',
      items: [
        { fileType: '7Z', description: '7Z Archive File', extension: '*' },
        {
          fileType: '7ZEXE',
          description: 'Self Extracting 7Z Archive File',
          extension: '*',
        },
        { fileType: 'ACE', description: 'ACE Compressed File', extension: '*' },
        {
          fileType: 'ALZ',
          description: 'Alzip Compressed File',
          extension: '*',
        },
        { fileType: 'ARC', description: '.ARC File', extension: '*' },
        {
          fileType: 'ARJ',
          description: 'Archived By Robert Jung',
          extension: '*',
        },
      ],
    },
  ]

  public selectedKeys: string[] = []

  public treeListView: any[] = this.source

  public hasChildren = (item: any): boolean => {
    return item.items && item.items.length > 0
  }

  public fetchChildren = (item: any): any[] => {
    return item.items || []
  }

  public setFileTypeFilters(): void {
    // Handle selected rows
  }

  public onExtensionChange(value: string, item: any): void {
    item.Extensions = value.split(',').map((ext) => ext.trim())
  }

  public onCellClick(event: CellClickEvent): void {
    // Prevent editing if the row has children (is a parent)
    if (event.dataItem.items?.length > 0 || event.dataItem.hasChildren) {
      return
    }

    // Allow editing only for child rows
    if (event.column.field === 'extension' && !event.isEdited) {
      this.treelist.editCell(
        event.dataItem,
        event.columnIndex,
        this.createFormGroup(event.dataItem)
      )
    }
  }

  public onCellClose(event: CellCloseEvent): void {
    if (event.formGroup.valid && event.formGroup.dirty) {
      // Update your data immediately
      Object.assign(event.dataItem, event.formGroup.value)
      this.onSave({ dataItem: event.dataItem })
    }
  }

  private createFormGroup(dataItem: any): FormGroup {
    return new FormGroup({
      extension: new FormControl(
        dataItem.extension,
        Validators.required // Add any validators you need
      ),
    })
  }

  public onSave(event: any): void {
    // Your save logic here
    console.log('Saved:', event.dataItem)
  }

  public filterList = [
    { text: 'EMAIL WITH GROUP DATE BETWEEN 02/27/2025 AND 02/27/2025' },
    { text: 'EMAIL WITH GROUP DATE BETWEEN 02/27/2025 AND 02/27/2025' },
  ]

  public deleteFilter(index: number): void {
    this.filterList.splice(index, 1)
  }
}
