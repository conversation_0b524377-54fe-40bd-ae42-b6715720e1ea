import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DialogRef,
  DialogCloseResult,
  DialogService,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { ProgressBarModule } from '@progress/kendo-angular-progressbar'
import { TooltipModule, PopoverModule } from '@progress/kendo-angular-tooltip'
import {
  SvgLoaderDirective,
  DynamicHeightDirective,
} from '@venio/feature/shared/directives'
import { UiPaginationModule } from '@venio/ui/pagination'
import { CaseLandingUiGraphComponent } from '../case-landing-ui-graph/case-landing-ui-graph.component'
import { CasemulticheckFilterComponent } from '../case-multicheck-filter/casemulticheck-filter.component'
import { checkIcon, chevronDownIcon } from '@progress/kendo-svg-icons'
import { CommonActionTypes } from '@venio/shared/models/constants'

export interface Batch {
  id: number
  reference: string
  documents: string
  expires: string
  instruction: string
  createdBy: string
  createdOn: string
}

@Component({
  selector: 'venio-case-landing-document-shared',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    InputsModule,
    IconsModule,
    LayoutModule,
    GridModule,
    TooltipModule,
    PopoverModule,
    UiPaginationModule,
    DropDownsModule,
    SvgLoaderDirective,
    ProgressBarModule,
    DynamicHeightDirective,
    CaseLandingUiGraphComponent,
    LoaderModule,
    CasemulticheckFilterComponent,
    DialogsModule,
  ],
  templateUrl: './case-landing-document-shared.component.html',
  styleUrl: './case-landing-document-shared.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseLandingDocumentSharedComponent implements OnInit {
  public pageSize = 10

  public sizes = [5, 10, 20, 50]

  public skip = 0

  public range = { start: null, end: null }

  public batches: Batch[] = []

  public dummyDropdownData = [
    { text: 'Item 1' },
    { text: 'Item 2' },
    { text: 'Item 3' },
  ]

  public icons = {
    chevronDownIcon: chevronDownIcon,
    tickIcon: checkIcon,
  }

  public listItems: Array<{ text: string; value: number }> = [
    { text: 'Shared By Me', value: 1 },
    { text: 'Shared With Me', value: 2 },
  ]

  public placeholderItem: { text: string; value: null } = {
    text: 'All Shared Document',
    value: null,
  }

  public commonActionTypes = CommonActionTypes

  constructor(private dialogService: DialogService) {}

  public ngOnInit(): void {
    this.documentShareDialog()
    this.generateDummyData()
  }

  private generateDummyData(): void {
    const references = [
      '98738E7',
      '61523512',
      '12345ABC',
      '789XYZ12',
      '56789EFG',
    ]
    const instructions = [
      'Lorem Ipsum Dolor Sit Amet, Consectetur',
      'Etiam Ullamcorper Nullam',
      'Aliquam Nec Vulputate Eros',
      'Suspendisse Vehicula Vitae',
      'Curabitur Egestas Leo',
    ]
    const users = [
      'Admin',
      'John Doe',
      'Jane Smith',
      'Emily Clark',
      'Michael Johnson',
    ]

    for (let i = 1; i <= 45; i++) {
      const randomIndex = (array: any[]): number =>
        Math.floor(Math.random() * array.length)
      const randomDate = new Date(
        2024,
        Math.floor(Math.random() * 12),
        Math.floor(Math.random() * 28) + 1,
        Math.floor(Math.random() * 24),
        Math.floor(Math.random() * 60),
        Math.floor(Math.random() * 60)
      )
      this.batches.push({
        id: i,
        reference: references[randomIndex(references)],
        documents: `${Math.floor(Math.random() * 100 + 1)}K`,
        expires:
          Math.random() > 0.5
            ? `${Math.floor(Math.random() * 30 + 1)} Days`
            : randomDate.toISOString(),
        instruction: instructions[randomIndex(instructions)],
        createdBy: users[randomIndex(users)],
        createdOn: randomDate.toISOString(),
      })
    }
  }

  private documentShareDialog(): void {
    import(
      './case-landing-document-shared-document-dialog/case-landing-document-shared-document-dialog.component'
    ).then((td) => {
      const dialog: DialogRef = this.dialogService.open({
        content: td.CaseLandingDocumentSharedDocumentDialogComponent,
        maxWidth: '1200px',
        maxHeight: '710px',
        width: '90%',
        height: '60vh',
      })

      dialog.result.subscribe((result) => {
        if (result instanceof DialogCloseResult) {
          //close
        }
      })
    })
  }

  public caseActionControls(id: number, actionType: CommonActionTypes): void {
    console.log('caseActionControls', id, actionType)
  }

  public capitalizeTitle(title: string): string {
    return title.replace(
      /\w\S*/g,
      (text) => text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
    )
  }
}
