<kendo-dialog-titlebar (close)="close('cancel')">
  <div class="t-flex t-justify-between t-w-full t-items-center">
    <div class="t-block">
      <span
        class="t-w-10 t-h-10 t-p-3 t-mr-3 t-bg-[#F2F2F2] t-rounded-full t-inline-flex t-justify-center">
        <span
          #shareBtn
          venioSvgLoader
          applyEffectsTo="fill"
          color="#B8B8B8"
          svgUrl="assets/svg/icon-document-share-sharing.svg"
          title="Download"
          height="1rem"
          width="1rem"></span>
      </span>

      {{ dialogTitle }}

      <span
        class="t-inline-block t-rounded t-px-3 t-py-1 t-text-uppercase t-text-xs t-bg-[#9BD2A7] t-ml-2">
        <span class="t-text-[#0F4B1B] t-tracking-widest">
          LINK EXPIRES ON
        </span>

        <span class="t-text-[#FFFFFF] t-tracking-wide"> 10 29 2025 </span>
      </span>

      <span class="t-text-[#263238] t-text-base t-ml-2">
        Number of documents 100K
      </span>
    </div>
  </div>
</kendo-dialog-titlebar>

<div class="t-flex t-mt-3">
  <div
    class="t-p-4 t-bg-[#F9FBF4] t-rounded-lg t-shadow-sm t-max-w-7xl t-mx-auto t-font-sans">
    <div class="t-grid t-grid-cols-6 t-gap-8 t-items-start">
      <div class="t-col-span-1">
        <h3
          class="t-font-bold t-text-[var(--v-custom-sky-blue)] t-text-sm t-mb-4">
          Internal Users
        </h3>
        <p class="t-text-gray-700 t-text-sm t-mb-1">john.doe&#64;llb.com</p>
        <p class="t-text-gray-700 t-text-sm t-mb-1">melissa.ro&#64;jsc.com</p>
        <p class="t-text-gray-700 t-text-sm">joeshp.joe&#64;sda.com</p>
      </div>

      <div class="t-col-span-1">
        <h3
          class="t-font-bold t-text-[var(--v-custom-sky-blue)] t-text-sm t-mb-4">
          External Users
        </h3>
        <p class="t-text-gray-700 t-text-sm t-mb-1">john.doe&#64;llb.com</p>
        <p class="t-text-gray-700 t-text-sm">joeshp.joe&#64;asd.com</p>
      </div>

      <div class="t-col-span-2">
        <h3
          class="t-font-bold t-text-[var(--v-custom-sky-blue)] t-text-sm t-mb-4">
          Permission for external users
        </h3>
        <ul class="t-space-y-2">
          <li class="t-flex t-items-center !t-items-start t-flex-col t-w-full">
            <span class="t-font-bold t-text-sm t-mr-1">
              All to create document notes visible to all users
            </span>

            <kendo-svg-icon
              [icon]="icons.tickIcon"
              class="t-text-[var(--kendo-success-100)]"></kendo-svg-icon>
          </li>
          <li class="t-flex t-items-center !t-items-start t-flex-col t-w-full">
            <span class="t-font-bold t-text-sm t-mr-1"> Allow Redaction </span>
            <kendo-svg-icon
              [icon]="icons.tickIcon"
              class="t-text-[var(--kendo-success-100)]"></kendo-svg-icon>
          </li>
          <li class="t-flex t-items-center !t-items-start t-flex-col t-w-full">
            <span class="t-font-bold t-text-sm t-mr-1">
              Allow Tag / Untag
            </span>
            <kendo-svg-icon
              [icon]="icons.tickIcon"
              class="t-text-[var(--kendo-success-100)]"></kendo-svg-icon>
          </li>
          <li class="t-flex t-items-center !t-items-start t-flex-col t-w-full">
            <span class="t-font-bold t-text-sm t-mr-1">
              View Analyze Page
            </span>
            <kendo-svg-icon
              [icon]="icons.tickIcon"
              class="t-text-[var(--kendo-success-100)]"></kendo-svg-icon>
          </li>
        </ul>
      </div>

      <div class="t-col-span-2">
        <h3
          class="t-font-bold t-text-[var(--v-custom-sky-blue)] t-text-sm t-mb-4">
          Description
        </h3>
        <p class="t-text-[#666666] t-text-sm t-leading-6">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus
          mauris ipsum, tincidunt at placerat nec, molestie sed eros. In eget
          porta lacus, non mattis odio. Nunc leo erat, egestas ut felis ac,
          molestie bibendum libero.
        </p>
      </div>
    </div>
  </div>
</div>

<!-- <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="close('no')"
        class="t-text-[var(--kendo-success-100)]"
        themeColor="secondary"
        fillMode="outline"
        data-qa="save-button">
        SEND INVITE
      </button>
      <button
        kendoButton
        (click)="close('yes')"
        themeColor="dark"
        fillMode="outline"
        data-qa="cancel-button">
        CANCEL
      </button>
    </div>
  </kendo-dialog-actions> -->
