import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  CheckBoxModule,
  InputsModule,
  RadioButtonModule,
  TextBoxModule,
} from '@progress/kendo-angular-inputs'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { IconsModule } from '@progress/kendo-angular-icons'
import {
  DropDownListModule,
  KENDO_DROPDOWNS,
} from '@progress/kendo-angular-dropdowns'
import { LabelModule } from '@progress/kendo-angular-label'
import { GridModule } from '@progress/kendo-angular-grid'
import { FormsModule } from '@angular/forms'

@Component({
  selector: 'venio-map-file-path',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    InputsModule,
    TextBoxModule,
    DropDownListModule,
    SvgLoaderDirective,
    IconsModule,
    LabelModule,
    RadioButtonModule,
    CheckBoxModule,
    GridModule,
    FormsModule,
    KENDO_DROPDOWNS,
  ],
  templateUrl: './map-file-path.component.html',
  styleUrl: './map-file-path.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MapFilePathComponent {
  public checked = true

  public defaultItem = {
    text: 'Image Mapping Filed',
    value: -1,
  }

  public treeData = [
    {
      text: 'Documents',
      value: 1,
      items: [
        { text: 'PDF', value: 2 },
        { text: 'Word', value: 3 },
      ],
    },
    {
      text: 'Media',
      value: 4,
      items: [
        { text: 'Images', value: 5 },
        { text: 'Videos', value: 6 },
      ],
    },
  ]
}
