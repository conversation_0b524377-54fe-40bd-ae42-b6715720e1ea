<div class="t-flex t-flex-1 t-flex-col t-h-[calc(100%_-_1.2rem)]">
  <kendo-dialog-titlebar (close)="close('cancel')">
    <div class="t-flex t-justify-between t-w-full t-items-center">
      <div class="t-block">
        <span
          class="t-w-10 t-h-10 t-p-3 t-mr-3 t-bg-[#F2F2F2] t-rounded-full t-inline-flex t-justify-center">
          <img src="assets/svg/icon-import.svg" alt="upload icon" />
        </span>

        Import Data
      </div>
    </div>
  </kendo-dialog-titlebar>

  <div class="t-flex t-flex-row t-gap-[30px] t-my-[10px] t-flex-1">
    <div class="t-flex t-h-[300px]">
      <div class="t-flex t-self-stretch t-flex-1">
        <kendo-stepper
          class="v-custom-import-stepper"
          [style.width.px]="150"
          [steps]="steps"
          [currentStep]="currentStepIndex"
          stepType="full"
          orientation="vertical"
          (currentStepChange)="goToStep($event)">
          <ng-template kendoStepperStepTemplate let-step let-index="index">
            <div
              class="t-flex t-items-center"
              (click)="activateStep(step.label)">
              <!-- Icon Container -->
              <div
                class="k-step-indicator t-w-8 t-h-8 t-rounded-full t-flex t-items-center t-justify-center">
                <kendo-svg-icon
                  *ngIf="index === currentStepIndex"
                  [icon]="checkIcon"
                  class="t-text-white"></kendo-svg-icon>

                <kendo-svg-icon
                  *ngIf="index < currentStepIndex"
                  [icon]="pencilIcon"
                  class="t-text-white"></kendo-svg-icon>
              </div>

              <!-- Step Label -->
              <span
                class="t-ml-1"
                [ngClass]="{
                  't-text-[#9BD2A7]': index === currentStepIndex,
                  't-text-[#1DBADC]': index < currentStepIndex,
                  't-text-[#C7C7C7]': index > currentStepIndex
                }">
                {{ step.label }}
              </span>
            </div>
          </ng-template>
        </kendo-stepper>
      </div>
    </div>

    <venio-select-load-file
      class="t-flex t-flex-1 t-px-[16px] t-py-0"
      [ngClass]="{ 't-hidden': currentStepIndex !== 0 }" />

    <venio-map-file-path
      class="t-flex t-flex-1 t-px-[16px] t-py-0"
      [ngClass]="{ 't-hidden': currentStepIndex !== 1 }" />

    <venio-file-mapping
      class="t-flex t-flex-1 t-px-[16px] t-py-0"
      [ngClass]="{ 't-hidden': currentStepIndex !== 2 }" />
  </div>
</div>
