<div class="t-flex t-flex-col t-gap-5 t-flex-1">
  <div
    class="t-flex t-flex-row t-flex-1 t-gap-5 t-border t-border-b-1 t-border-t-1 t-border-l-0 t-border-r-0 t-border-[#0000001A] t-border-solid t-py-[10px]">
    <p class="t-font-medium t-text-[16px] t-text-[#000000DE] t-self-center">
      Unstructured Data
    </p>

    <div
      class="t-flex t-relative t-w-[370px] before:t-inset-0 before:t-absolute before:t-bg-[#ED7425] before:t-opacity-25 before:t-rounded-[4px] t-mx-auto t-px-5 t-py-[2px] t-text-center t-text-wrap">
      <p class="t-relative t-text-[#000000]">
        We found
        <span class="t-font-bold t-text-[13px] t-text-[#ED7428]">900</span>
        files which are not processed
        <span
          class="t-font-bold t-text-[13px] t-text-[#ED7428] t-cursor-pointer"
          >click here</span
        >
        to view and reprocess
      </p>
    </div>
  </div>
  <div class="t-flex t-flex-1 t-flex-row t-gap-5">
    <div
      class="t-flex t-flex-col t-gap-2 t-p-5 t-w-1/3 t-border t-border-[#E0E0E0] t-self-start t-border-1 t-rounded-[4px] t-w-[450px] t-shadow-[0px_12px_20px_rgba(0,0,0,0.16)]">
      <p class="t-text-[#4A4B90DE] t-text-[16px]">Overall Upload</p>
      <p class="t-text-[#9F9F9F] t-text-[12px]">
        Uploaded By
        <span class="t-text-[#000000] t-font-medium">Admin</span>
      </p>
      <div class="t-flex t-flex-row t-gap-2 t-align-end">
        <kendo-progressbar
          [animation]="true"
          [min]="0"
          [max]="100"
          [value]="90"
          [progressCssStyle]="getProgressBarStyle(progressValue2)"
          [label]="{
            visible: false,
          }"
          class="v-custom-progress-bar t-h-[9px]">
        </kendo-progressbar>
        <span
          class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative t-top-[-4px]"
          >2/2</span
        >
      </div>
      <div class="t-flex t-flex-row t-justify-between">
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Date <span class="t-text-[#000000] t-font-medium">12 08 2025</span>
        </p>
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Last Updated
          <span class="t-text-[#000000] t-font-medium">Today</span>
        </p>
      </div>
    </div>
    <div
      class="t-flex t-flex-col t-gap-2 t-p-5 t-w-1/3 t-border t-border-[#E0E0E0] t-self-start t-border-1 t-rounded-[4px] t-w-[450px] t-shadow-[0px_12px_20px_rgba(0,0,0,0.16)]">
      <p class="t-text-[#4A4B90DE] t-text-[16px]">Overall Processing</p>
      <p class="t-text-[#9F9F9F] t-text-[12px]">
        Uploaded By
        <span class="t-text-[#000000] t-font-medium">Admin</span>
      </p>
      <div class="t-flex t-flex-row t-gap-2 t-align-end">
        <kendo-progressbar
          [animation]="true"
          [min]="0"
          [max]="100"
          [value]="80"
          [progressCssStyle]="{
            background: '#FFBB12'
          }"
          [label]="{
            visible: false,
          }"
          class="v-custom-progress-bar t-h-[9px]">
        </kendo-progressbar>
        <span
          class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative t-top-[-4px]"
          >2/2</span
        >
      </div>
      <div class="t-flex t-flex-row t-justify-between">
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Date <span class="t-text-[#000000] t-font-medium">12 08 2025</span>
        </p>
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Last Updated
          <span class="t-text-[#000000] t-font-medium">Today</span>
        </p>
      </div>
    </div>

    <div
      class="t-flex t-flex-col t-gap-2 t-p-5 t-w-1/3 t-border t-border-[#E0E0E0] t-border-1 t-rounded-[4px] t-w-[450px] t-shadow-[0px_12px_20px_rgba(0,0,0,0.16)]">
      <p class="t-text-[#4A4B90DE] t-text-[16px]">Overall Load</p>
      <p class="t-text-[#9F9F9F] t-text-[12px]">
        Uploaded By
        <span class="t-text-[#000000] t-font-medium">Admin</span>
      </p>
      <div class="t-flex t-flex-row t-gap-2 t-align-end">
        <kendo-progressbar
          [animation]="{ duration: 2000 }"
          [min]="0"
          [max]="100"
          [value]="70"
          [progressCssStyle]="getProgressBarStyle(progressValue2)"
          [label]="{
            visible: false,
          }"
          class="v-custom-progress-bar t-h-[9px]">
        </kendo-progressbar>
        <span
          class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative t-top-[-4px]"
          >2/3</span
        >
      </div>
      <div class="t-flex t-flex-row t-justify-between">
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Date <span class="t-text-[#000000] t-font-medium">12 08 2025</span>
        </p>
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Last Updated
          <span class="t-text-[#000000] t-font-medium">Today</span>
        </p>
      </div>

      <div class="t-flex t-justify-end t-w-full">
        <button
          kendoButton
          class="t-bg-[#ffffff] t-p-0 t-w-[12px] t-h-[12px] t-ease-linear"
          rounded="full"
          fillMode="clear"
          title="View Detail"
          [ngClass]="{ 't-rotate-180 t-ease-out': !showCharts }"
          (click)="this.showCharts = !this.showCharts">
          <span
            venioSvgLoader
            class="t-w-[20px] t-h-[20px] t-rounded-full t-bg-[#ffffff]"
            svgUrl="assets/svg/icon-ios-sky-blue-chevrondown.svg"></span>
        </button>
      </div>
      <div
        class="t-flex t-flex-col t-overflow-hidden t-transition-all t-duration-500"
        [ngClass]="{
          't-h-0 t-opacity-0': !showCharts,
          't-opacity-100': showCharts
        }">
        <div class="t-flex t-flex-col">
          <p class="t-text-[#9F9F9F] t-text-[12px]">DRM Public Download.Zip</p>
          <div class="t-flex t-flex-row t-gap-2 t-items-center">
            <kendo-progressbar
              [animation]="true"
              [min]="0"
              [max]="100"
              [value]="65"
              [progressCssStyle]="getProgressBarStyle(65)"
              [label]="{
            visible: false,
          }"
              class="v-custom-progress-bar t-h-[9px]">
            </kendo-progressbar>
            <span
              class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative"
              >65%</span
            ><span class="t-cursor-pointer t-relative"
              ><kendo-svg-icon
                [icon]="closeIcon"
                class="t-text-[#ED7428] t-text-sm"></kendo-svg-icon
            ></span>
          </div>
        </div>
      </div>
      <div
        class="t-flex t-flex-col t-overflow-hidden t-transition-all t-duration-500"
        [ngClass]="{
          't-h-0 t-opacity-0': !showCharts,
          't-opacity-100': showCharts
        }">
        <div class="t-flex t-flex-col">
          <p class="t-text-[#9F9F9F] t-text-[12px]">DRM Public Download.Zip</p>
          <div class="t-flex t-flex-row t-gap-2 t-items-center">
            <kendo-progressbar
              [animation]="true"
              [min]="0"
              [max]="100"
              [value]="65"
              [progressCssStyle]="getProgressBarStyle(65)"
              [label]="{
          visible: false,
        }"
              class="v-custom-progress-bar t-h-[9px]">
            </kendo-progressbar>
            <span
              class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative"
              >65%</span
            ><span class="t-cursor-pointer t-relative"
              ><kendo-svg-icon
                [icon]="closeIcon"
                class="t-text-[#ED7428] t-text-sm"></kendo-svg-icon
            ></span>
          </div>
        </div>
      </div>
      <div
        class="t-flex t-flex-col t-overflow-hidden t-transition-all t-duration-500"
        [ngClass]="{
          't-h-0 t-opacity-0': !showCharts,
          't-opacity-100': showCharts
        }">
        <div class="t-flex t-flex-col">
          <p class="t-text-[#9F9F9F] t-text-[12px]">DRM Public Download.Zip</p>
          <div class="t-flex t-flex-row t-gap-2 t-items-center">
            <kendo-progressbar
              [animation]="true"
              [min]="0"
              [max]="100"
              [value]="65"
              [progressCssStyle]="getProgressBarStyle(65)"
              [label]="{
        visible: false,
      }"
              class="v-custom-progress-bar t-h-[9px]">
            </kendo-progressbar>
            <span
              class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative"
              >65%</span
            ><span class="t-cursor-pointer t-relative"
              ><kendo-svg-icon
                [icon]="closeIcon"
                class="t-text-[#ED7428] t-text-sm"></kendo-svg-icon
            ></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="t-my-4 t-flex t-items-center t-gap-4">
    <h1 class="t-text-lg t-font-semibold">Processing Details</h1>
    <div class="t-flex t-items-center t-gap-4 t-text-xs t-font-medium">
      <div class="t-flex t-items-center t-gap-1">
        <span class="t-w-4 t-h-4 t-rounded-sm t-bg-[#9BD2A7]"></span> COMPLETED
      </div>
      <div class="t-flex t-items-center t-gap-1">
        <span class="t-w-4 t-h-4 t-rounded-sm t-bg-[#FFBC3E]"></span> INPROGRESS
      </div>
      <div class="t-flex t-items-center t-gap-1">
        <span class="t-w-4 t-h-4 t-rounded-sm t-bg-[#EDEBE9]"></span> NOT
        STARTED
      </div>
    </div>
  </div>

  <div class="t-flex t-flex-1 t-flex-row t-gap-5 t-flex-wrap">
    <div
      class="t-flex t-flex-col t-gap-2 t-p-5 t-border t-border-[#E0E0E0] t-border-1 t-rounded-[4px] t-w-[450px] t-self-start t-shadow-[0px_12px_20px_rgba(0,0,0,0.16)]">
      <p class="t-text-[#4A4B90DE] t-text-[16px]">Test</p>

      <p class="t-text-[#9F9F9F] t-text-[12px]">
        Custodian Name <span class="t-text-[#000000] t-font-medium">Admin</span>
      </p>

      <div class="t-flex t-flex-row t-gap-2 t-align-end">
        <kendo-progressbar
          [animation]="{ duration: 3400 }"
          [min]="0"
          [max]="100"
          [value]="100"
          [progressCssStyle]="getProgressBarStyle(progressValue2)"
          [label]="{
          visible: false,
        }"
          class="v-custom-progress-bar t-h-[9px]">
        </kendo-progressbar>
        <span
          class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative t-top-[-4px]"
          >2/2</span
        >
      </div>
      <div class="t-flex t-flex-row t-justify-between">
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Date
          <span class="t-text-[#000000] t-font-medium">12 08 2025</span>
        </p>
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Last Updated
          <span class="t-text-[#000000] t-font-medium">2 days ago</span>
        </p>
      </div>
    </div>

    <div
      class="t-flex t-flex-col t-gap-2 t-p-5 t-border t-border-[#E0E0E0] t-border-1 t-rounded-[4px] t-w-[450px] t-self-start t-shadow-[0px_12px_20px_rgba(0,0,0,0.16)]">
      <p class="t-text-[#4A4B90DE] t-text-[16px]">Test</p>

      <p class="t-text-[#9F9F9F] t-text-[12px]">
        Custodian Name <span class="t-text-[#000000] t-font-medium">Admin</span>
      </p>

      <div class="t-flex t-flex-row t-gap-2 t-align-end">
        <kendo-progressbar
          [animation]="{ duration: 3400 }"
          [min]="0"
          [max]="100"
          [value]="100"
          [progressCssStyle]="getProgressBarStyle(progressValue2)"
          [label]="{
          visible: false,
        }"
          class="v-custom-progress-bar t-h-[9px]">
        </kendo-progressbar>
        <span
          class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative t-top-[-4px]"
          >2/2</span
        >
      </div>
      <div class="t-flex t-flex-row t-justify-between">
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Date
          <span class="t-text-[#000000] t-font-medium">12 08 2025</span>
        </p>
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Last Updated
          <span class="t-text-[#000000] t-font-medium">2 days ago</span>
        </p>
      </div>

      <button
        (click)="openImportDataDialog($event)"
        kendoButton
        size="small"
        class="v-custom-secondary-button !t-rounded-none t-self-start !t-h-[24px]"
        themeColor="secondary"
        fillMode="outline"
        data-qa="upload-button">
        <span class="t-text-[12px]">IMPORT DATA</span>
      </button>
      <button
        kendoButton
        class="t-bg-[#ffffff] t-p-0 t-w-[12px] t-h-[12px] t-ease-linear t-self-end"
        rounded="full"
        fillMode="clear"
        title="View Detail"
        [ngClass]="{ 't-rotate-180 t-ease-out': !showCharts }"
        (click)="this.showCharts = !this.showCharts">
        <span
          venioSvgLoader
          class="t-w-[20px] t-h-[20px] t-rounded-full t-bg-[#ffffff]"
          svgUrl="assets/svg/icon-ios-sky-blue-chevrondown.svg"></span>
      </button>
      <div
        class="t-flex t-flex-row t-gap-5 t-overflow-hidden t-transition-all t-duration-500"
        [ngClass]="{
          't-h-0 t-opacity-0': !showCharts,
          't-h-[140px] t-opacity-100': showCharts
        }">
        <ng-container *ngIf="showCharts">
          <div class="t-flex t-justify-around">
            <div
              *ngFor="let progress of progressData"
              class="t-flex t-flex-col t-items-center">
              <kendo-circularprogressbar
                style="width: 80px; height: 80px"
                class="v-custom-progressbar"
                [animation]="true"
                [value]="progress.value"
                [progressColor]="colors">
                <ng-template
                  kendoCircularProgressbarCenterTemplate
                  let-color="color"
                  let-value="value">
                  <span class="t-text-[#050505] t-text-[10px]">100%</span>
                </ng-template>
              </kendo-circularprogressbar>
              <span class="t-text-sm t-mt-2 t-font-medium t-w-24 t-text-center">
                {{ progress.label }}
              </span>
            </div>
          </div>
        </ng-container>
      </div>
    </div>

    <div
      class="t-flex t-flex-col t-gap-2 t-p-5 t-border t-border-[#E0E0E0] t-border-1 t-rounded-[4px] t-w-[450px] t-self-start t-shadow-[0px_12px_20px_rgba(0,0,0,0.16)]">
      <p class="t-text-[#4A4B90DE] t-text-[16px]">Test</p>

      <p class="t-text-[#9F9F9F] t-text-[12px]">
        Custodian Name <span class="t-text-[#000000] t-font-medium">Admin</span>
      </p>

      <div class="t-flex t-flex-row t-gap-2 t-align-end">
        <kendo-progressbar
          [animation]="{ duration: 3400 }"
          [min]="0"
          [max]="100"
          [value]="100"
          [progressCssStyle]="getProgressBarStyle(progressValue2)"
          [label]="{
          visible: false,
        }"
          class="v-custom-progress-bar t-h-[9px]">
        </kendo-progressbar>
        <span
          class="t-font-medium t-text-[12px] t-text-[#4A4B90] t-opacity-100 t-relative t-top-[-4px]"
          >2/2</span
        >
      </div>
      <div class="t-flex t-flex-row t-justify-between">
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Date
          <span class="t-text-[#000000] t-font-medium">12 08 2025</span>
        </p>
        <p class="t-text-[#9F9F9F] t-text-[12px]">
          Last Updated
          <span class="t-text-[#000000] t-font-medium">2 days ago</span>
        </p>
      </div>

      <div class="t-flex t-justify-end t-w-full">
        <button
          kendoButton
          class="t-bg-[#ffffff] t-p-0 t-w-[12px] t-h-[12px] t-ease-linear"
          rounded="full"
          fillMode="clear"
          title="View Detail"
          [ngClass]="{ 't-rotate-180 t-ease-out': !showCharts }"
          (click)="this.showCharts = !this.showCharts">
          <span
            venioSvgLoader
            class="t-w-[20px] t-h-[20px] t-rounded-full t-bg-[#ffffff]"
            svgUrl="assets/svg/icon-ios-sky-blue-chevrondown.svg"></span>
        </button>
      </div>
      <div
        class="t-flex t-flex-row t-gap-5 t-overflow-hidden t-transition-all t-duration-500"
        [ngClass]="{
          't-h-0 t-opacity-0': !showCharts,
          't-h-[140px] t-opacity-100': showCharts
        }">
        <ng-container *ngIf="showCharts">
          <div class="t-flex t-justify-around">
            <div
              *ngFor="let progress of progressData"
              class="t-flex t-flex-col t-items-center">
              <kendo-circularprogressbar
                style="width: 80px; height: 80px"
                class="v-custom-progressbar"
                [animation]="true"
                [value]="progress.value"
                [progressColor]="colors">
                <ng-template
                  kendoCircularProgressbarCenterTemplate
                  let-color="color"
                  let-value="value">
                  <span class="t-text-[#050505] t-text-[10px]">100%</span>
                </ng-template>
              </kendo-circularprogressbar>
              <span class="t-text-sm t-mt-2 t-font-medium t-w-24 t-text-center">
                {{ progress.label }}
              </span>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</div>
<div kendoDialogContainer></div>
