import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SaveImportTemplateComponent } from './save-import-template.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('SaveImportTemplateComponent', () => {
  let component: SaveImportTemplateComponent
  let fixture: ComponentFixture<SaveImportTemplateComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SaveImportTemplateComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(SaveImportTemplateComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
