import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ImportDataContainerComponent } from './import-data-container.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { DialogRef } from '@progress/kendo-angular-dialog'

class MockDialogRef {
  public close(): void {}
}

describe('ImportDataContainerComponent', () => {
  let component: ImportDataContainerComponent
  let fixture: ComponentFixture<ImportDataContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ImportDataContainerComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: DialogRef, useClass: MockDialogRef },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ImportDataContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
