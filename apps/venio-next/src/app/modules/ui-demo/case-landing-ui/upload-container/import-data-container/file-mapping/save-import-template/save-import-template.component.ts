import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { InputsModule, TextBoxModule } from '@progress/kendo-angular-inputs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'

@Component({
  selector: 'venio-save-import-template',
  standalone: true,
  imports: [CommonModule, InputsModule, TextBoxModule, ButtonsModule],
  templateUrl: './save-import-template.component.html',
  styleUrl: './save-import-template.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SaveImportTemplateComponent {}
