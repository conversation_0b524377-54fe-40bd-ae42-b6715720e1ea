import { ComponentFixture, TestBed } from '@angular/core/testing'

import { UploadContainerComponent } from './upload-container.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('UploadContainerComponent', () => {
  let component: UploadContainerComponent
  let fixture: ComponentFixture<UploadContainerComponent>

  beforeEach(async () => {
    TestBed.configureTestingModule({
      imports: [NoopAnimationsModule, UploadContainerComponent],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    })

    await TestBed.compileComponents()
  })

  beforeEach(() => {
    fixture = TestBed.createComponent(UploadContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
