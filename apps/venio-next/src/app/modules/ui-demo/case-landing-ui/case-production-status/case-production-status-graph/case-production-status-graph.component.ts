import { ChangeDetectionStrategy, Component, Input } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ChartsModule } from '@progress/kendo-angular-charts'

@Component({
  selector: 'venio-case-production-status-graph',
  standalone: true,
  imports: [CommonModule, ChartsModule],
  templateUrl: './case-production-status-graph.component.html',
  styleUrl: './case-production-status-graph.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CaseProductionStatusGraphComponent {
  @Input() public graphId?: number

  @Input() public theme = 'default'

  @Input() public chartData: any[] = [
    { value: 1, color: '#ED7425' },
    { value: 1, color: '#718792' },
    { value: 5, color: '#FFB300' },
    { value: 1, color: '#9BD2A7' },
  ] // Default for demo purposes

  public categories: string[] = this.chartData.map((item) =>
    item.value.toString()
  )

  public getBadgeClass(status: string): string {
    const badgeColors: { [key: string]: string } = {
      FAILED: '#ED7425',
      COMPLETED: '#588107',
      INPROGRESS: '#FFB300',
      'NOT STARTED': '#718792',
    }
    return badgeColors[status] || 't-bg-gray-300' // Default color if status is unknown
  }

  public getDynamicClass(status: string): { [klass: string]: boolean } {
    const colorClass = `t-text-[${this.getBadgeClass(status)}]`

    return {
      [colorClass]: true,
    }
  }

  public getGraphTitle(graphId: number): string {
    switch (graphId) {
      case 1:
        return 'Image'
      case 2:
        return 'Fulltext'
      case 3:
        return 'Native'
      case 4:
        return 'Load file'
      default:
        return 'Unknown'
    }
  }

  public getTotalValue(): number {
    return this.chartData.reduce((sum, item) => sum + item.value, 0)
  }
}
