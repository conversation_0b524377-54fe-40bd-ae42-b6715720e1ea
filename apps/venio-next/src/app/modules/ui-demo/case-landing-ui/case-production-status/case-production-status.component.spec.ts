import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CaseProductionStatusComponent } from './case-production-status.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { DialogRef } from '@progress/kendo-angular-dialog'

describe('CaseProductionStatusComponent', () => {
  let component: CaseProductionStatusComponent
  let fixture: ComponentFixture<CaseProductionStatusComponent>

  class MockDialogRef {
    public close(): void {}
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CaseProductionStatusComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: DialogRef, useClass: MockDialogRef },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(CaseProductionStatusComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
