<div class="t-flex t-gap-3 t-border-l-[1px] t-border-l-[#ccc]">
  <div class="t-flex t-flex-col t-gap-4 t-flex-1">
    <div class="t-flex t-justify-between t-p-2 t-mr-[28px] t-items-center">
      <div class="t-flex"></div>
      <div class="t-flex t-gap-3">
        <button
          kendoButton
          class="v-custom-secondary-button t-uppercase"
          themeColor="secondary"
          fillMode="outline"
          data-qa="create-new">
          Create New
        </button>
      </div>
    </div>

    <kendo-grid
      class="t-grid t-w-full t-h-full t-border-b-1 t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto"
      [kendoGridBinding]="batches"
      venioDynamicHeight
      [sortable]="true"
      [groupable]="false"
      [reorderable]="true"
      [resizable]="true"
      kendoGridSelectBy="id"
      [pageable]="{ type: 'numeric', position: 'top' }">
      <ng-template kendoPagerTemplate>
        <div class="t-flex t-gap-2">
          <kendo-textbox
            class="!t-border-[#ccc] !t-w-[25rem]"
            placeholder="Search"
            [clearButton]="true">
            <ng-template kendoTextBoxSuffixTemplate>
              <button
                kendoButton
                fillMode="clear"
                class="t-text-[#1EBADC]"
                imageUrl="assets/svg/icon-updated-search.svg"></button>
            </ng-template>
          </kendo-textbox>
        </div>
        <kendo-grid-spacer></kendo-grid-spacer>

        <venio-pagination
          [disabled]="batches?.length === 0"
          [totalRecords]="batches?.length"
          [pageSize]="pageSize"
          [showPageJumper]="false"
          [showPageSize]="true"
          [showRowNumberInputBox]="true"
          class="t-px-5 t-block t-py-2">
        </venio-pagination>
      </ng-template>
      <kendo-grid-column
        field="id"
        [width]="45"
        title="#"
        headerClass="t-text-primary">
      </kendo-grid-column>

      <kendo-grid-checkbox-column
        [showSelectAll]="false"
        [width]="40"></kendo-grid-checkbox-column>
      <kendo-grid-column
        field="name"
        title="Name"
        [width]="170"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem.name }}
          <span
            *ngIf="dataItem.name === 'Set 3'"
            class="t-bg-[#9D9EC3] t-rounded-md t-px-2 t-py-1 t-text-white t-text-xs t-cursor-default t-ml-2"
            >CAL</span
          >
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column
        field="client"
        title="Client"
        [width]="200"
        headerClass="t-text-primary"></kendo-grid-column>
      <kendo-grid-column
        field="batchName"
        title="Batch Name"
        [width]="150"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate let-dataItem>
          <div class="t-flex t-gap-1 t-items-center">
            <span
              *ngIf="dataItem.batchName === 'Batch 0005'"
              venioSvgLoader
              hoverColor="#FB9E97"
              color="#FB9E97"
              svgUrl="assets/svg/icon-lock-style.svg"
              height="0.75rem"
              width="0.8rem">
              <kendo-loader size="small"></kendo-loader>
            </span>

            {{ dataItem.batchName }}

            <span>
              <kendo-dropdownbutton
                [data]="batchDropdown"
                [svgIcon]="icons.chevronDownIcon"
                buttonClass="t-text-[#000000] "
                fillMode="none"
                size="small"
                class="v-custom-clear-dropdown-btn t-flex !t-h-[27px] t-items-center">
                <ng-template kendoDropDownButtonItemTemplate let-dataItem>
                  <div class="t-flex t-gap-2 t-items-center">
                    <img
                      [src]="dataItem.svgPath"
                      alt="{{ dataItem.text }}"
                      class="t-w-4 t-h-4" />
                    <span>{{ dataItem.text }}</span>
                  </div>
                </ng-template>
              </kendo-dropdownbutton>
            </span>
          </div>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column
        field="status"
        title="Status"
        [width]="200"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate let-dataItem>
          <div class="progress-container">
            <kendo-progressbar
              *ngIf="dataItem.status === 'INPROGRESS'"
              [value]="dataItem.progress"
              [label]="label"
              [progressCssStyle]="{
                background: dataItem.progress < 20 ? '#FFBB12' : '#9BD2A7'
              }"
              [animation]="{ duration: 600 }"
              class="v-custom-progress-bar t-h-[12px] t-w-full">
            </kendo-progressbar>
          </div>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column
        field="createdBy"
        title="Created By & On"
        [width]="200"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{ dataItem.createdBy }}
          <span class="t-text-[#a3a3a3] t-text-sm">{{
            dataItem.createdOn | date : 'MM dd yyyy HH:mm:ss'
          }}</span>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column
        title="Actions"
        [width]="100"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate let-dataItem>
          <!-- Add action buttons or icons here -->
          <button
            kendoButton
            imageUrl="assets/svg/icon-rate-review-note.svg"></button>
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>
  </div>
</div>
