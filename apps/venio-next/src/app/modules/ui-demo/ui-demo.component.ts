import { ChangeDetectionStrategy, Component, ViewChild } from '@angular/core'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { DialogService } from '@progress/kendo-angular-dialog'
import { ConfirmationDialogUiComponent } from './confirmation-dialog-ui/confirmation-dialog-ui.component'
import { Router } from '@angular/router'

@Component({
  selector: 'venio-ui-demo',
  templateUrl: './ui-demo.component.html',
  styleUrls: ['./ui-demo.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UiDemoComponent {
  @ViewChild('btnAnchor', { static: true }) public btnAnchor: HTMLElement

  public cardData = [
    {
      id: 0,
      link: '/ui-demo/misc-elements',
      name: 'Misc UI Elements',
    },
    {
      id: 1,
      link: '',
      name: 'Notification / Toaster',
    },
    {
      id: 2,
      link: '',
      name: 'Confirmation Dialog',
    },
    {
      id: 4,
      link: '/ui-demo/launchpad-ui',
      name: 'Launchpad',
    },
    {
      id: 5,
      link: '/ui-demo/review-page',
      name: 'Review',
    },
    {
      id: 6,
      link: '/ui-demo/tag-summary',
      name: 'Tag Summary/History',
    },
    {
      id: 7,
      link: '/ui-demo/grid-filter',
      name: 'Grid Filter',
    },
    {
      id: 8,
      link: '/ui-demo/folder-ui-dialog',
      name: 'Folder UI',
    },
    {
      id: 9,
      link: '/ui-demo/edit-tags-coding',
      name: 'Edit & Add Tags/Coding',
    },
    {
      id: 10,
      link: '/ui-demo/tally-ui-dialog',
      name: 'Tally UI',
    },
    {
      id: 11,
      link: '/ui-demo/document-addnew-view',
      name: 'Document Add New View',
    },
    {
      id: 12,
      link: '/ui-demo/search-history-dialog',
      name: 'Search History Dialog',
    },
    {
      id: 13,
      link: '/ui-demo/send-folder-dialog',
      name: 'Send Folder Dialog',
    },
    {
      id: 14,
      link: '/ui-demo/delete-document-dialog',
      name: 'Delete Document',
    },
    {
      id: 15,
      link: '/ui-demo/save-search-dialog',
      name: 'Save Search Dialog',
    },
    {
      id: 16,
      link: '/ui-demo/breadcrumb-ui',
      name: 'Breadcrumb UI',
    },
    {
      id: 17,
      link: '/ui-demo/document-history-ui',
      name: 'Document History UI',
    },
    {
      id: 18,
      link: '/ui-demo/notes-panel-ui',
      name: 'Notes Panel UI',
    },
    {
      id: 19,
      link: '/ui-demo/similar-document-ui',
      name: 'Similar Document UI',
    },
    {
      id: 20,
      link: '/ui-demo/login-report-caselaunchpad',
      name: 'Login Report Case Launchpad',
    },
    {
      id: 21,
      link: '/ui-demo/locked-user-report',
      name: 'Locked User Report',
    },
    {
      id: 22,
      link: '/ui-demo/user-account-creation-report',
      name: 'User Account Creation Report',
    },
    {
      id: 23,
      link: '/ui-demo/data-export-download',
      name: 'Data Export & download',
    },
    {
      id: 24,
      link: '/ui-demo/transcript-ui',
      name: 'Transcript UI',
    },
    {
      id: 25,
      link: '/ui-demo/near-duplicate-ui',
      name: 'Near Duplicate UI',
    },
    {
      id: 26,
      link: '/ui-demo/pst-status-ui',
      name: 'PST Status UI',
    },
    {
      id: 27,
      link: '/ui-demo/social-media-viewer',
      name: 'Social Media Viewer',
    },
    {
      id: 28,
      link: '/ui-demo/audio-transcribe',
      name: 'Audio Transcribe',
    },
    {
      id: 29,
      link: '/ui-demo/transcript-report-ui',
      name: 'Transcript Report UI',
    },
    {
      id: 30,
      link: '/ui-demo/tag-coding-summary',
      name: 'Tag Coding Summary',
    },
    {
      id: 31,
      link: '/ui-demo/bulk-redaction-ui',
      name: 'Bulk Redaction UI',
    },
    {
      id: 32,
      link: '/ui-demo/ai-dialog-ui',
      name: 'AI Dialog UI',
    },
    {
      id: 33,
      link: '/ui-demo/login-ui',
      name: 'Login UI',
    },
    {
      id: 34,
      link: '/ui-demo/metadata-show-hide-dialog',
      name: 'Metadata Show Hide Dialog',
    },
    {
      id: 35,
      link: '/ui-demo/epoc-ui',
      name: 'Epoc UI',
    },
    {
      id: 36,
      link: '/ui-demo/case-landing-ui',
      name: 'Case Landing UI',
    },
    {
      id: 37,
      link: '/ui-demo/login-ui?action=forgot-password',
      name: 'Forgot Password',
    },
    {
      id: 38,
      link: '/ui-demo/case-landing-ui?ind=default',
      name: 'Case Landing UI- Reprocess',
    },
    {
      id: 39,
      link: '/ui-demo/case-landing-ui?ind=update',
      name: 'Case Landing UI- Update',
    },
    {
      id: 40,
      link: '/ui-demo/case-landing-ui?ind=settings',
      name: 'Case Landing UI- Settings',
    },
    {
      id: 41,
      link: '/ui-demo/case-landing-ui?ind=custodian',
      name: 'Case Landing UI- Custodian',
    },
    {
      id: 42,
      link: '/ui-demo/create-edit-layout',
      name: 'Create Edit Layout',
    },
    {
      id: 43,
      link: '/ui-demo/ediscovery-ai-review',
      name: 'Discovery AI Review',
    },
    {
      id: 44,
      link: '/ui-demo/case-landing-ui?tab=2',
      name: 'Case Landing - Share Document',
    },
    {
      id: 45,
      link: '/ui-demo/reprocess-status',
      name: 'Reprocess Status',
    },
    {
      id: 46,
      link: '/ui-demo/set-one',
      name: 'Set One',
    },
    {
      id: 47,
      link: '/ui-demo/review-create-case',
      name: 'Review Create Case',
    },
  ]

  constructor(
    private notificationService: NotificationService,
    private dialogService: DialogService,
    private router: Router
  ) {}

  // notification ui
  public showNotification(): void {
    ;['success', 'none', 'warning', 'error', 'info'].forEach((style) => {
      this.notificationService.show({
        content:
          'Your apple has been saved. Time for tea! Lorem Ipsum Lorem ipsum',
        type: { style } as Type,
        animation: { type: 'fade', duration: 300 },
        closable: true,
      })
    })
  }

  // trigger dialog modal and config
  public openSuccessDialog(): void {
    this.dialogService.open({
      content: ConfirmationDialogUiComponent,
      cssClass: 'v-confirmation-dialog v-dialog-save',
    })
  }

  //navigate to components
  public navigateToLink(link: string): void {
    this.router.navigateByUrl(link)
  }
}
