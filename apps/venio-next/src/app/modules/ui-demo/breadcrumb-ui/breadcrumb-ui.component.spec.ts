import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BreadcrumbUiComponent } from './breadcrumb-ui.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { CommonModule } from '@angular/common'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('BreadcrumbUiComponent', () => {
  let component: BreadcrumbUiComponent
  let fixture: ComponentFixture<BreadcrumbUiComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BreadcrumbUiComponent, CommonModule, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(BreadcrumbUiComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
