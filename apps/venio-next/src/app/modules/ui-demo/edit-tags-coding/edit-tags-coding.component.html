<div class="t-flex t-p-8">
  <button kendoButton (click)="openDialog()">Edit & Add Tag Coding</button>
</div>
<kendo-dialog
  *ngIf="opened"
  (close)="close('cancel')"
  [maxHeight]="580"
  [height]="'70vh'"
  [minWidth]="250"
  [maxWidth]="980"
  [width]="'70%'">
  <kendo-dialog-titlebar>
    <div>
      {{ dialogTitle }}
    </div>
  </kendo-dialog-titlebar>

  <div class="t-flex t-flex-col">
    <div class="t-flex t-gap-5">
      <div class="t-flex t-flex-1 t-flex-col t-w-full">
        <kendo-tabstrip class="t-w-full t-h-full">
          <kendo-tabstrip-tab title="Tag" [selected]="true">
            <ng-template kendoTabContent>
              <div class="content">
                <div class="t-flex t-flex-col">
                  <div class="t-flex t-justify-end" *ngIf="!addStatus">
                    <button
                      kendoButton
                      (click)="openAdd()"
                      class="v-custom-secondary-button t-my-6"
                      themeColor="secondary">
                      ADD NEW TAG
                    </button>
                  </div>

                  <!-- edit tag & manage group -->
                  <div class="t-flex t-flex-col" *ngIf="addStatus">
                    <div class="t-flex t-mt-4">
                      <button
                        kendoButton
                        (click)="openEdit()"
                        class="!t-p-0 !t-m-0 t-font-bold"
                        [svgIcon]="leftSvg"
                        fillMode="clear">
                        ALL TAGS
                      </button>
                    </div>

                    <!-- add new tag -->
                    <div class="t-flex t-w-full t-flex-col t-gap-2">
                      <div
                        class="t-flex t-flex-1 t-mt-3 t-w-full v-custom-grey-bg">
                        <form class="t-flex t-flex-1 t-w-full t-gap-3">
                          <div
                            showHints="always"
                            class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                            <kendo-label
                              for="name"
                              class="t-text-xs t-uppercase t-tracking-widest">
                              NAME <span class="t-text-error">*</span>
                            </kendo-label>

                            <kendo-textbox
                              placeholder="Tag Name"
                              #name></kendo-textbox>
                          </div>

                          <div
                            showHints="always"
                            class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                            <kendo-label
                              for="tagGroup"
                              class="t-text-xs t-uppercase t-tracking-widest">
                              TAG GROUP <span class="t-text-error">*</span>
                            </kendo-label>

                            <kendo-dropdownlist
                              #tagGroup
                              [defaultItem]="defaultItemCategories"
                              [data]="[]"
                              textField="text"
                              valueField="value">
                            </kendo-dropdownlist>
                          </div>
                        </form>
                      </div>
                    </div>

                    <!-- manage group tab-->

                    <div class="t-flex t-mb-4">
                      <kendo-tabstrip
                        class="t-w-full t-mt-3"
                        (tabSelect)="onTabSelect($event)">
                        <kendo-tabstrip-tab
                          title="Manage Tag Group"
                          [selected]="true">
                          <ng-template kendoTabContent>
                            <div class="content">
                              <div class="t-flex">
                                <form
                                  class="t-flex t-w-full v-custom-grey-bg t-flex-col t-mt-3">
                                  <div
                                    showHints="always"
                                    class="t-flex t-mt-3 t-flex-0 t-w-[32.5%]">
                                    <kendo-textbox placeholder="Name">
                                      <ng-template kendoTextBoxSuffixTemplate>
                                        <kendo-textbox-separator></kendo-textbox-separator>

                                        <button
                                          [themeColor]="'secondary'"
                                          kendoButton
                                          fillMode="clear"
                                          [svgIcon]="plusSvg"></button>
                                        <button
                                          [themeColor]="'secondary'"
                                          kendoButton
                                          fillMode="clear"
                                          [svgIcon]="refreshSvg"></button>
                                      </ng-template>
                                    </kendo-textbox>
                                  </div>

                                  <div
                                    class="t-flex t-flex-row-reverse t-gap-2 t-justify-end t-mt-3 t-items-center">
                                    <label class="k-checkbox-label" for="terms"
                                      >Apply exclusive tag</label
                                    >
                                    <input
                                      type="checkbox"
                                      id="terms"
                                      [size]="'small'"
                                      kendoCheckBox />
                                  </div>
                                </form>
                              </div>
                            </div>
                          </ng-template>
                        </kendo-tabstrip-tab>

                        <kendo-tabstrip-tab title="Advance Options">
                          <ng-template kendoTabContent>
                            <div class="content">
                              <div
                                class="t-flex t-w-full t-mt-4 t-flex-col t-gap-3 v-custom-grey-bg">
                                <div
                                  class="t-flex t-w-full t-flex-wrap t-gap-[1.24%] t-gap-y-3">
                                  <div
                                    showHints="always"
                                    class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-gap-1 t-flex-col">
                                    <kendo-label
                                      for="Parent"
                                      class="t-text-xs t-uppercase t-tracking-widest">
                                      Parent Tag
                                    </kendo-label>

                                    <kendo-textbox
                                      placeholder="Input Value"
                                      #Parent></kendo-textbox>
                                  </div>

                                  <div
                                    showHints="always"
                                    class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                                    <kendo-label
                                      for="Label"
                                      class="t-text-xs t-uppercase t-tracking-widest">
                                      Label
                                    </kendo-label>

                                    <kendo-textbox
                                      placeholder="Input Value"
                                      #Label></kendo-textbox>
                                  </div>

                                  <div
                                    showHints="always"
                                    class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                                    <kendo-label
                                      for="Reviewer"
                                      class="t-text-xs t-uppercase t-tracking-widest">
                                      Reviewer Comment
                                    </kendo-label>

                                    <kendo-dropdownlist
                                      #Reviewer
                                      [defaultItem]="defaultItemCategories"
                                      [data]="[]"
                                      textField="text"
                                      valueField="value">
                                    </kendo-dropdownlist>
                                  </div>

                                  <div
                                    showHints="always"
                                    class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                                    <kendo-label
                                      for="Label"
                                      class="t-text-xs t-uppercase t-tracking-widest">
                                      Label
                                    </kendo-label>

                                    <kendo-textbox
                                      placeholder="Input Value"
                                      #Label></kendo-textbox>
                                  </div>

                                  <div
                                    showHints="always"
                                    class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                                    <kendo-label
                                      for="Color"
                                      class="t-text-xs t-uppercase t-tracking-widest">
                                      Color
                                    </kendo-label>
                                    <div class="t-flex !t-relative">
                                      <kendo-textbox
                                        placeholder="Select Color"
                                        class="t-border-r-0 t-rounded-tr-none t-rounded-br-none"
                                        #color
                                        [value]="selectedColor"></kendo-textbox>
                                      <kendo-colorpicker
                                        class="t-relative t-w-20 t-border-l-0 t-rounded-tl-none t-rounded-bl-none"
                                        [value]="selectedColor"
                                        (valueChange)="
                                          onColorChange($event)
                                        "></kendo-colorpicker>
                                    </div>
                                  </div>
                                </div>

                                <div
                                  class="t-flex t-w-full t-flex-wrap t-gap-3">
                                  <div
                                    showHints="always"
                                    class="t-flex t-flex-0 t-basis-[100%] t-gap-1 t-flex-col">
                                    <kendo-label
                                      for="Description"
                                      class="t-text-xs t-uppercase t-tracking-widest">
                                      Description
                                    </kendo-label>
                                    <kendo-textarea
                                      #Description
                                      placeholder="Tell us a little bit about yourself..."
                                      [rows]="3"
                                      resizable="vertical"></kendo-textarea>
                                  </div>
                                </div>
                              </div>

                              <div
                                class="t-flex t-flex-col t-w-full t-gap-2 t-mt-0 v-custom-grey-bg">
                                <!-- security grid-->

                                <div
                                  class="t-flex-none t-font-bold t-text-base t-w-3/5">
                                  <span class="t-text-primary">Security</span>
                                </div>
                                <kendo-grid
                                  filterable="menu"
                                  [data]="sampleData">
                                  <kendo-grid-column
                                    headerClass="t-text-primary"
                                    field="CompanyName"
                                    title="Role">
                                  </kendo-grid-column>
                                  <kendo-grid-column
                                    field="ContactTitle"
                                    headerClass="t-text-primary"
                                    title="Permission">
                                    <ng-template
                                      kendoGridCellTemplate
                                      let-dataItem>
                                      <kendo-dropdownlist
                                        class="t-w-56"
                                        [defaultItem]="defaultItemCategories"
                                        [data]="[]"
                                        textField="text"
                                        valueField="value">
                                      </kendo-dropdownlist>
                                    </ng-template>
                                  </kendo-grid-column>
                                </kendo-grid>
                              </div>

                              <div class="t-flex t-flex-col t-gap-3 t-mt-3">
                                <div
                                  class="t-flex-none t-font-bold t-text-base t-border t-border-t-0 t-border-l-0 t-border-r-0 t-border-b-1 v-custom-border t-pb-3 t-w-3/5">
                                  <span class="t-text-primary"
                                    >Tag Propogation Settings</span
                                  >
                                </div>

                                <div
                                  class="t-flex-none t-font-bold t-text-base t-text-primary">
                                  Duplicate Propogation Settings
                                </div>

                                <div
                                  class="t-flex t-flex-row-reverse t-justify-end t-gap-2">
                                  <kendo-label
                                    class="k-radio-label"
                                    [for]="propogation"
                                    text="Propogate tags to all duplicate in the case"></kendo-label>

                                  <input
                                    type="radio"
                                    #propogation
                                    kendoRadioButton />
                                </div>
                              </div>
                            </div>
                          </ng-template>
                        </kendo-tabstrip-tab>
                      </kendo-tabstrip>
                    </div>
                  </div>

                  <div class="t-flex t-w-full t-flex-col" *ngIf="tagGridStatus">
                    <kendo-treelist
                      [kendoTreeListFlatBinding]="data"
                      idField="id"
                      parentIdField="managerId"
                      [height]="410"
                      kendoTreeListExpandable
                      [initiallyExpanded]="true"
                      kendoTreeListSelectable
                      [selectable]="settings"
                      [(selectedItems)]="selected"
                      [ngClass]="'v-custom-tagtree t-mb-5'"
                      [columnMenu]="false"
                      [sortable]="true"
                      [resizable]="true"
                      [reorderable]="true"
                      [navigable]="true"
                      [pageable]="false"
                      [pageSize]="100"
                      [rowReorderable]="true">
                      <kendo-treelist-rowreorder-column
                        [columnMenu]="false"
                        [width]="40"></kendo-treelist-rowreorder-column>

                      <kendo-treelist-checkbox-column
                        headerClass="t-text-primary v-custom-tagheader"
                        [expandable]="true"
                        field="name"
                        title="Tags"
                        [width]="350"
                        [checkChildren]="true"
                        [showSelectAll]="true">
                        <ng-template kendoTreeListCellTemplate let-dataItem>
                          <div
                            class="t-inline-block t-absolute t-ml-7 t-pointer-events-none t-select-none">
                            {{ dataItem.name }}
                          </div>
                        </ng-template>
                      </kendo-treelist-checkbox-column>

                      <kendo-treelist-command-column
                        title="Action"
                        [columnMenu]="false"
                        headerClass="t-text-primary">
                        <ng-template kendoTreeListCellTemplate let-dataItem>
                          <div class="t-flex">
                            <kendo-buttongroup>
                              <button
                                kendoButton
                                #actionGrid
                                *ngFor="let icon of svgIconForTagControls"
                                class="!t-p-[0.3rem] t-w-1/3"
                                (click)="browseActionClicked(icon.actionType)"
                                fillMode="clear"
                                size="none">
                                <span
                                  [parentElement]="actionGrid.element"
                                  venioSvgLoader
                                  hoverColor="#FFBB12"
                                  color="#979797"
                                  [svgUrl]="icon.iconPath"
                                  height="0.9rem"
                                  width="1rem"></span>
                              </button>
                            </kendo-buttongroup>
                          </div>
                        </ng-template>
                      </kendo-treelist-command-column>
                    </kendo-treelist>

                    <kendo-grid [data]="sampleCustomers" class="!t-max-h-64">
                      <kendo-grid-column
                        field="ProductName"
                        headerClass="t-text-primary"
                        title="Tags">
                      </kendo-grid-column>
                      <kendo-grid-column
                        field="SupplierID"
                        headerClass="t-text-primary"
                        title="Action"
                        [width]="130">
                        <ng-template kendoGridCellTemplate let-dataItem>
                          <div class="t-flex">
                            <kendo-buttongroup>
                              <button
                                kendoButton
                                #actionGrid
                                *ngFor="let icon of svgIconForTagControls"
                                class="!t-p-[0.3rem] t-w-1/3"
                                (click)="browseActionClicked(icon.actionType)"
                                fillMode="clear"
                                size="none">
                                <span
                                  [parentElement]="actionGrid.element"
                                  venioSvgLoader
                                  hoverColor="#FFBB12"
                                  color="#979797"
                                  [svgUrl]="icon.iconPath"
                                  height="0.9rem"
                                  width="1rem"></span>
                              </button>
                            </kendo-buttongroup>
                          </div>
                        </ng-template>
                      </kendo-grid-column>
                    </kendo-grid>
                  </div>
                </div>
              </div>
            </ng-template>
          </kendo-tabstrip-tab>
          <kendo-tabstrip-tab title="Coding">
            <ng-template kendoTabContent>
              <div class="content">
                <div class="t-flex t-flex-col t-w-full">
                  <div
                    class="t-flex t-justify-end t-mt-3"
                    *ngIf="!customFields">
                    <button
                      kendoButton
                      class="v-custom-secondary-button"
                      data-qa="add-new-coding-field-button"
                      themeColor="secondary"
                      fillMode="outline"
                      (click)="addNewCodingField()">
                      Add new coding field
                    </button>
                  </div>

                  <div class="t-flex t-mt-4" *ngIf="customFields">
                    <button
                      kendoButton
                      class="!t-p-0 !t-m-0 t-font-bold"
                      [svgIcon]="leftSvg"
                      fillMode="clear"
                      (click)="addNewCodingField()">
                      BACK
                    </button>
                  </div>

                  <div
                    *ngIf="!customFields"
                    class="t-flex t-flex-col t-mt-4 t-gap-3 t-w-full">
                    <!-- show grid by default -->
                    <div class="t-flex t-flex-col t-mt-4 t-gap-3 t-w-full">
                      <kendo-grid
                        class="!t-max-h-96"
                        filterable="menu"
                        [data]="gridData"
                        [selectable]="{ mode: 'multiple', drag: true }">
                        <kendo-grid-checkbox-column
                          [showSelectAll]="true"
                          [width]="50"
                          class="!t-py-[0.6rem]"></kendo-grid-checkbox-column>
                        <kendo-grid-column
                          field="full_name"
                          headerClass="t-text-primary"
                          title="Field Name">
                        </kendo-grid-column>
                        <kendo-grid-column
                          field="full_name"
                          headerClass="t-text-primary"
                          title="Display Name">
                        </kendo-grid-column>
                        <kendo-grid-column
                          field="full_name"
                          headerClass="t-text-primary"
                          title="Field Type">
                        </kendo-grid-column>
                        <kendo-grid-column
                          field="full_name"
                          headerClass="t-text-primary"
                          title="Description">
                        </kendo-grid-column>

                        <kendo-grid-column
                          title="Action"
                          headerClass="t-text-primary"
                          [width]="140"
                          [class]="{ 'text-center': true }"
                          [resizable]="false">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div class="t-flex">
                              <!-- To avoid the cyclomatic complexity error, seperate component has been created-->
                              <venio-common-action-buttons
                                [icons]="svgIconForGridControls"
                                (buttonClick)="
                                  browseActionClicked($event)
                                "></venio-common-action-buttons>
                            </div>
                          </ng-template>
                        </kendo-grid-column>
                      </kendo-grid>
                    </div>
                  </div>

                  <div *ngIf="customFields" class="other-section">
                    <!-- Your new section content goes here -->
                    <div class="t-flex">
                      <div
                        class="t-flex t-w-full t-mt-4 t-flex-col t-gap-3 v-custom-grey-bg">
                        <div
                          class="t-flex t-w-full t-flex-wrap t-gap-[1.25%] t-gap-y-3">
                          <div
                            showHints="always"
                            class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-gap-1 t-flex-col">
                            <kendo-label
                              for="Parent"
                              class="t-text-xs t-uppercase t-tracking-widest">
                              Field Name <span class="t-text-error">*</span>
                            </kendo-label>

                            <kendo-textbox
                              placeholder="Custom Field Name"
                              #Parent></kendo-textbox>
                          </div>

                          <div
                            showHints="always"
                            class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                            <kendo-label
                              for="type"
                              class="t-text-xs t-uppercase t-tracking-widest">
                              Type <span class="t-text-error">*</span>
                            </kendo-label>

                            <kendo-dropdownlist
                              #type
                              [defaultItem]="defaultItemCategories"
                              [data]="[]"
                              data-qa="customType"
                              textField="text"
                              valueField="value">
                            </kendo-dropdownlist>
                          </div>

                          <!-- numberic type-->

                          <div
                            showHints="always"
                            class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                            <kendo-label
                              for="type"
                              class="t-text-xs t-uppercase t-tracking-widest">
                              Precision <span class="t-text-error">*</span>
                            </kendo-label>

                            <kendo-textbox
                              placeholder="Precision"
                              data-qa="precision"
                              #Parent></kendo-textbox>
                          </div>

                          <div
                            showHints="always"
                            class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                            <kendo-label
                              for="scaleType"
                              class="t-text-xs t-uppercase t-tracking-widest">
                              Scale <span class="t-text-error">*</span>
                            </kendo-label>

                            <kendo-numerictextbox
                              placeholder="Scale"
                              data-qa="scaleType"
                              format="#"
                              #scaleTYpe></kendo-numerictextbox>
                          </div>

                          <!-- text & unicode text type-->
                          <div
                            showHints="always"
                            class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                            <kendo-label
                              for="typeLength"
                              class="t-text-xs t-uppercase t-tracking-widest">
                              Length <span class="t-text-error">*</span>
                            </kendo-label>

                            <kendo-numerictextbox
                              placeholder="Length"
                              data-qa="TypeLength"
                              format="#"
                              #TypeLength></kendo-numerictextbox>
                          </div>
                        </div>

                        <div class="t-flex t-w-full t-flex-wrap t-gap-3">
                          <div
                            showHints="always"
                            class="t-flex t-flex-0 t-basis-[100%] t-gap-1 t-flex-col">
                            <kendo-label
                              for="Description"
                              class="t-text-xs t-uppercase t-tracking-widest">
                              Description
                            </kendo-label>
                            <kendo-textarea
                              #Description
                              placeholder="Description"
                              [rows]="3"
                              resizable="vertical"></kendo-textarea>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="t-flex t-mb-4">
                  <kendo-tabstrip
                    class="t-w-full t-mt-3"
                    (tabSelect)="onTabSelect($event)">
                    <kendo-tabstrip-tab
                      title="Advance Options"
                      [selected]="true">
                      <ng-template kendoTabContent>
                        <div class="content" [formGroup]="form">
                          <div
                            class="t-flex t-w-full t-mt-4 t-flex-col t-gap-3 v-custom-grey-bg">
                            <div
                              class="t-flex t-w-full t-flex-wrap t-gap-[1.24%] t-gap-y-3">
                              <div
                                showHints="always"
                                class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-gap-1 t-flex-col">
                                <kendo-label
                                  for="Parent"
                                  class="t-text-xs t-uppercase t-tracking-widest">
                                  Allow Empty Value
                                </kendo-label>

                                <div class="t-flex t-gap-3 t-mt-2">
                                  <input
                                    type="radio"
                                    #Yes
                                    value="Yes"
                                    formControlName="yesNo"
                                    kendoRadioButton
                                    name="yesNo"
                                    checked />
                                  <kendo-label
                                    [for]="Yes"
                                    text="Yes"></kendo-label>

                                  <input
                                    type="radio"
                                    formControlName="yesNo"
                                    #No
                                    value="No"
                                    kendoRadioButton
                                    name="yesNo" />
                                  <kendo-label
                                    [for]="No"
                                    text="No"></kendo-label>
                                </div>
                              </div>

                              <div
                                *ngIf="isNoSelected()"
                                showHints="always"
                                class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-gap-1 t-flex-col">
                                <kendo-label
                                  for="defaultValue"
                                  class="t-text-xs t-uppercase t-tracking-widest">
                                  Default Value
                                  <span class="t-text-error">*</span>
                                </kendo-label>

                                <kendo-textbox
                                  formControlName="defaultValue"
                                  placeholder="Default value"
                                  #defaultValue></kendo-textbox>
                              </div>
                            </div>

                            <div class="t-flex t-w-full t-mt-2">
                              <div
                                class="t-flex-none t-font-bold t-text-base t-w-3/5">
                                <span class="t-text-primary">Coding</span>
                              </div>
                            </div>
                            <div
                              class="t-flex t-w-full t-flex-wrap t-gap-[1.24%] t-gap-y-3">
                              <div
                                showHints="always"
                                class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                                <kendo-label
                                  for="Label"
                                  class="t-text-xs t-uppercase t-tracking-widest">
                                  Enable Coding
                                </kendo-label>

                                <div class="t-flex t-gap-3 t-mt-2">
                                  <input
                                    type="radio"
                                    #enableYes
                                    value="Yes"
                                    kendoRadioButton
                                    formControlName="enableYesNo"
                                    name="enableYesNo"
                                    checked />
                                  <kendo-label
                                    [for]="enableYes"
                                    text="Yes"></kendo-label>

                                  <input
                                    type="radio"
                                    #enableNo
                                    value="No"
                                    kendoRadioButton
                                    formControlName="enableYesNo"
                                    name="enableYesNo" />
                                  <kendo-label
                                    [for]="enableNo"
                                    text="No"></kendo-label>
                                </div>
                              </div>

                              <div
                                *ngIf="!isEnableCodingSelected()"
                                showHints="always"
                                class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                                <kendo-label
                                  for="Reviewer"
                                  class="t-text-xs t-uppercase t-tracking-widest">
                                  Enable Multiple Values
                                </kendo-label>

                                <div class="t-flex t-gap-3 t-mt-2">
                                  <input
                                    type="radio"
                                    #multipleYes
                                    value="Yes"
                                    formControlName="multipleYesNo"
                                    kendoRadioButton
                                    name="multipleYesNo" />
                                  <kendo-label
                                    [for]="multipleYes"
                                    text="Yes"></kendo-label>

                                  <input
                                    type="radio"
                                    #multipleNo
                                    formControlName="multipleYesNo"
                                    value="No"
                                    kendoRadioButton
                                    checked
                                    name="multipleYesNo" />
                                  <kendo-label
                                    [for]="multipleNo"
                                    text="No"></kendo-label>
                                </div>
                              </div>

                              <div
                                *ngIf="!isNoDelimiter()"
                                showHints="always"
                                class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-gap-1 t-flex-col">
                                <kendo-label
                                  for="delimiterValue"
                                  class="t-text-xs t-uppercase t-tracking-widest">
                                  Delimiter <span class="t-text-error">*</span>
                                </kendo-label>

                                <kendo-dropdownlist
                                  #delimiterValue
                                  formControlName="delimiterValue"
                                  [data]="['ab,cd', 'ab|cd', 'ab;cd']"
                                  [defaultItem]="{
                                    text: 'Select',
                                    value: null
                                  }"
                                  textField="text"
                                  valueField="value"
                                  [valuePrimitive]="true"
                                  required>
                                </kendo-dropdownlist>
                              </div>
                            </div>

                            <div
                              class="t-flex t-w-full t-flex-wrap t-gap-3 t-mt-1">
                              <div
                                *ngIf="!isEnableCodingSelected()"
                                showHints="always"
                                class="t-flex t-flex-0 t-basis-[100%] t-gap-1 t-flex-col">
                                <kendo-label
                                  for="codingValues"
                                  class="t-text-xs t-uppercase t-tracking-widest">
                                  Coding Values
                                </kendo-label>
                                <kendo-textarea
                                  #codingValues
                                  placeholder="Coding Values"
                                  data-qa="codingValues"
                                  [rows]="3"
                                  resizable="vertical"></kendo-textarea>
                              </div>
                            </div>
                          </div>

                          <div
                            class="t-flex t-flex-col t-w-full t-gap-2 t-mt-0 v-custom-grey-bg">
                            <!-- security grid-->

                            <div class="t-flex t-justify-between">
                              <div
                                class="t-flex-none t-font-bold t-text-base t-w-3/5">
                                <span class="t-text-primary">Security</span>
                              </div>

                              <div class="t-flex-0">
                                <button
                                  kendoButton
                                  (click)="close('no')"
                                  class="v-custom-secondary-button"
                                  themeColor="secondary"
                                  [svgIcon]="refreshSvg"
                                  data-qa="refersh-button"
                                  fillMode="outline"></button>
                              </div>
                            </div>
                            <kendo-grid filterable="menu" [data]="sampleData">
                              <kendo-grid-column
                                headerClass="t-text-primary"
                                field="CompanyName"
                                title="Role">
                              </kendo-grid-column>
                              <kendo-grid-column
                                field="ContactTitle"
                                headerClass="t-text-primary"
                                title="Permission">
                                <ng-template kendoGridCellTemplate let-dataItem>
                                  <kendo-dropdownlist
                                    class="t-w-56"
                                    [defaultItem]="defaultItemCategories"
                                    [data]="[]"
                                    textField="text"
                                    valueField="value">
                                  </kendo-dropdownlist>
                                </ng-template>
                              </kendo-grid-column>
                            </kendo-grid>
                          </div>
                        </div>
                      </ng-template>
                    </kendo-tabstrip-tab>
                  </kendo-tabstrip>
                </div>
              </div>
            </ng-template>
          </kendo-tabstrip-tab>
        </kendo-tabstrip>
      </div>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="close('no')"
        class="v-custom-secondary-button"
        themeColor="secondary"
        fillMode="outline"
        data-qa="save-button">
        SAVE
      </button>
      <button
        kendoButton
        (click)="close('yes')"
        themeColor="dark"
        fillMode="outline"
        data-qa="cancel-button">
        CANCEL
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>
