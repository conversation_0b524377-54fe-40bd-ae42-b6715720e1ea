import { ComponentFixture, TestBed } from '@angular/core/testing'
import { EditTagsCodingComponent } from './edit-tags-coding.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('EditTagsCodingComponent', () => {
  let component: EditTagsCodingComponent
  let fixture: ComponentFixture<EditTagsCodingComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EditTagsCodingComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(EditTagsCodingComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
