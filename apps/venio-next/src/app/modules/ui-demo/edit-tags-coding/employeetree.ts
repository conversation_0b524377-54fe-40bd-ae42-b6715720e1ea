export interface Employee {
  id: number
  managerId?: number | null
  name: string
  title: string
  phone: string
  hireDate?: Date
  imgId?: number
  gender?: string
}

export const employeestree: Employee[] = [
  {
    id: 1,
    name: '<PERSON>',
    title: 'Chief Executive Officer',
    phone: '(*************',
    managerId: null,
    hireDate: new Date('2019-01-15'),
    imgId: 2,
    gender: 'M',
  },
  {
    id: 2,
    name: '<PERSON>',
    title: 'Chief Technical Officer',
    phone: '(*************',
    managerId: 1,
    hireDate: new Date('2019-02-19'),
    imgId: 8,
    gender: 'M',
  },
  {
    id: 111,
    name: '<PERSON>',
    title: 'Chief Executive Officer',
    phone: '(*************',
    managerId: null,
    hireDate: new Date('2019-01-15'),
    imgId: 2,
    gender: 'M',
  },
  {
    id: 211,
    name: '<PERSON>',
    title: 'Chief Technical Officer',
    phone: '(*************',
    managerId: 111,
    hireDate: new Date('2019-02-19'),
    imgId: 8,
    gender: 'M',
  },
  {
    id: 32,
    name: '<PERSON>',
    title: 'VP, Engineering',
    phone: '(*************',
    managerId: 2,
    hireDate: new Date('2019-04-13'),
    imgId: 4,
    gender: 'F',
  },
  {
    id: 11,
    name: 'Hyacinth Hood',
    title: 'Team Lead',
    phone: '(*************',
    managerId: 32,
    hireDate: new Date('2018-01-17'),
    imgId: 1,
    gender: 'M',
  },
  {
    id: 60,
    name: 'Akeem Carr',
    title: 'Junior Software Developer',
    phone: '(*************',
    managerId: 11,
    hireDate: new Date('2018-01-18'),
    imgId: 5,
    gender: 'M',
  },
  {
    id: 78,
    name: 'Rinah Simon',
    title: 'Software Developer',
    phone: '(*************',
    managerId: 11,
    hireDate: new Date('2018-03-17'),
    imgId: 9,
    gender: 'F',
  },
  {
    id: 42,
    name: 'Gage Daniels',
    title: 'Software Architect',
    phone: '(*************',
    managerId: 32,
    hireDate: new Date('2019-03-14'),
    imgId: 8,
    gender: 'M',
  },
  {
    id: 43,
    name: 'Constance Vazquez',
    title: 'Director, Engineering',
    phone: '(*************',
    managerId: 32,
    hireDate: new Date('2018-03-18'),
    imgId: 6,
    gender: 'F',
  },
  {
    id: 46,
    name: 'Darrel Solis',
    title: 'Team Lead',
    phone: '(*************',
    managerId: 43,
    hireDate: new Date('2019-04-15'),
    imgId: 1,
    gender: 'M',
  },
  {
    id: 47,
    name: 'Brian Yang',
    title: 'Senior Software Developer',
    phone: '(*************',
    managerId: 46,
    hireDate: new Date('2019-02-21'),
    imgId: 10,
    gender: 'M',
  },
  {
    id: 50,
    name: 'Lillian Bradshaw',
    title: 'Software Developer',
    phone: '(*************',
    managerId: 46,
    hireDate: new Date('2019-05-23'),
    imgId: 3,
    gender: 'F',
  },
  {
    id: 51,
    name: 'Christian Palmer',
    title: 'Technical Lead',
    phone: '(*************',
    managerId: 46,
    hireDate: new Date('2019-04-16'),
    imgId: 5,
    gender: 'M',
  },
  {
    id: 55,
    name: 'Summer Mosley',
    title: 'QA Engineer',
    phone: '(*************',
    managerId: 46,
    hireDate: new Date('2019-09-21'),
    imgId: 10,
    gender: 'F',
  },
  {
    id: 56,
    name: 'Barry Ayers',
    title: 'Software Developer',
    phone: '(*************',
    managerId: 46,
    hireDate: new Date('2018-04-16'),
    imgId: 1,
    gender: 'M',
  },
  {
    id: 59,
    name: 'Keiko Espinoza',
    title: 'Junior QA Engineer',
    phone: '(*************',
    managerId: 46,
    hireDate: new Date('2018-01-22'),
    imgId: 9,
    gender: 'M',
  },
  {
    id: 61,
    name: 'Candace Pickett',
    title: 'Support Officer',
    phone: '(*************',
    managerId: 46,
    hireDate: new Date('2018-09-18'),
    imgId: 9,
    gender: 'F',
  },
  {
    id: 63,
    name: 'Mia Caldwell',
    title: 'Team Lead',
    phone: '(*************',
    managerId: 43,
    hireDate: new Date('2018-07-17'),
    imgId: 2,
    gender: 'F',
  },
  {
    id: 65,
    name: 'Thomas Terry',
    title: 'Senior Enterprise Support Officer',
    phone: '(*************',
    managerId: 63,
    hireDate: new Date('2018-07-14'),
    imgId: 3,
    gender: 'M',
  },
  {
    id: 67,
    name: 'Ruth Downs',
    title: 'Senior Software Developer',
    phone: '(*************',
    managerId: 63,
    hireDate: new Date('2018-08-14'),
    imgId: 8,
    gender: 'F',
  },
  {
    id: 70,
    name: 'Yasir Wilder',
    title: 'Senior QA Engineer',
    phone: '(*************',
    managerId: 63,
    hireDate: new Date('2019-08-17'),
    imgId: 5,
    gender: 'M',
  },
  {
    id: 71,
    name: 'Flavia Short',
    title: 'Support Officer',
    phone: '(*************',
    managerId: 63,
    hireDate: new Date('2018-06-15'),
    imgId: 9,
    gender: 'F',
  },
  {
    id: 74,
    name: 'Aaron Roach',
    title: 'Junior Software Developer',
    phone: '(*************',
    managerId: 63,
    hireDate: new Date('2018-09-18'),
    imgId: 1,
    gender: 'M',
  },
  {
    id: 75,
    name: 'Eric Russell',
    title: 'Software Developer',
    phone: '(*************',
    managerId: 63,
    hireDate: new Date('2019-09-13'),
    imgId: 2,
    gender: 'M',
  },
  {
    id: 76,
    name: 'Cheyenne Olson',
    title: 'Software Developer',
    phone: '(*************',
    managerId: 63,
    hireDate: new Date('2018-09-18'),
    imgId: 4,
    gender: 'M',
  },
  {
    id: 77,
    name: 'Shaine Avila',
    title: 'UI Designer',
    phone: '(*************',
    managerId: 63,
    hireDate: new Date('2018-01-22'),
    imgId: 2,
    gender: 'M',
  },
  {
    id: 81,
    name: 'Chantale Long',
    title: 'Senior QA Engineer',
    phone: '(*************',
    managerId: 63,
    hireDate: new Date('2018-09-14'),
    imgId: 2,
    gender: 'F',
  },
  {
    id: 83,
    name: 'Dane Cruz',
    title: 'Junior Software Developer',
    phone: '(*************',
    managerId: 63,
    hireDate: new Date('2019-03-15'),
    imgId: 9,
    gender: 'M',
  },
  {
    id: 84,
    name: 'Regan Patterson',
    title: 'Technical Writer',
    phone: '(*************',
    managerId: 63,
    hireDate: new Date('2019-05-17'),
    imgId: 1,
    gender: 'M',
  },
  {
    id: 85,
    name: 'Drew Mckay',
    title: 'Senior Software Developer',
    phone: '(*************',
    managerId: 63,
    hireDate: new Date('2019-05-21'),
    imgId: 3,
    gender: 'M',
  },
  {
    id: 88,
    name: 'Bevis Miller',
    title: 'Senior Software Developer',
    phone: '(*************',
    managerId: 63,
    hireDate: new Date('2018-08-15'),
    imgId: 4,
    gender: 'M',
  },
  {
    id: 89,
    name: 'Bruce Mccarty',
    title: 'Support Officer',
    phone: '(*************',
    managerId: 63,
    hireDate: new Date('2019-10-21'),
    imgId: 9,
    gender: 'M',
  },
  {
    id: 90,
    name: 'Ocean Blair',
    title: 'Team Lead',
    phone: '(*************',
    managerId: 43,
    hireDate: new Date('2018-06-20'),
    imgId: 8,
    gender: 'F',
  },
  {
    id: 91,
    name: 'Guinevere Osborn',
    title: 'Software Developer',
    phone: '(*************',
    managerId: 90,
    hireDate: new Date('2019-06-17'),
    imgId: 8,
    gender: 'M',
  },
  {
    id: 92,
    name: 'Olga Strong',
    title: 'Graphic Designer',
    phone: '(*************',
    managerId: 90,
    hireDate: new Date('2018-06-15'),
    imgId: 8,
    gender: 'F',
  },
  {
    id: 93,
    name: 'Robert Orr',
    title: 'Support Officer',
    phone: '(*************',
    managerId: 90,
    hireDate: new Date('2018-06-22'),
    imgId: 6,
    gender: 'M',
  },
  {
    id: 95,
    name: 'Odette Sears',
    title: 'Senior Software Developer',
    phone: '(*************',
    managerId: 90,
    hireDate: new Date('2019-05-20'),
    imgId: 7,
    gender: 'F',
  },
  {
    id: 45,
    name: 'Zelda Medina',
    title: 'QA Architect',
    phone: '(*************',
    managerId: 32,
    hireDate: new Date('2018-08-16'),
    imgId: 5,
    gender: 'F',
  },
  {
    id: 3,
    name: 'Priscilla Frank',
    title: 'Chief Product Officer',
    phone: '(*************',
    managerId: 1,
    hireDate: new Date('2019-04-22'),
    imgId: 9,
    gender: 'F',
  },
  {
    id: 4,
    name: 'Ursula Holmes',
    title: 'EVP, Product Strategy',
    phone: '(*************',
    managerId: 3,
    hireDate: new Date('2018-01-15'),
    imgId: 7,
    gender: 'F',
  },
  {
    id: 24,
    name: 'Melvin Carrillo',
    title: 'Director, Developer Relations',
    phone: '(*************',
    managerId: 3,
    hireDate: new Date('2018-01-17'),
    imgId: 8,
    gender: 'M',
  },
  {
    id: 29,
    name: 'Martha Chavez',
    title: 'Developer Advocate',
    phone: '(*************',
    managerId: 24,
    hireDate: new Date('2018-05-14'),
    imgId: 5,
    gender: 'F',
  },
  {
    id: 30,
    name: 'Oren Fox',
    title: 'Developer Advocate',
    phone: '(*************',
    managerId: 24,
    hireDate: new Date('2018-07-19'),
    imgId: 8,
    gender: 'M',
  },
  {
    id: 41,
    name: 'Amos Barr',
    title: 'Developer Advocate',
    phone: '(*************',
    managerId: 24,
    hireDate: new Date('2019-01-16'),
    imgId: 9,
    gender: 'M',
  },
]
