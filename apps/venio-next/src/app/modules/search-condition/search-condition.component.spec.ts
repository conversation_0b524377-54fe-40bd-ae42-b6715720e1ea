import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SearchConditionComponent } from './search-condition.component'
import { FieldFacade, SearchFacade } from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('SearchConditionComponent', () => {
  let component: SearchConditionComponent
  let fixture: ComponentFixture<SearchConditionComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SearchConditionComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        SearchFacade,
        FieldFacade,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(SearchConditionComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
