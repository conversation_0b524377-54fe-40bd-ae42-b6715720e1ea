import {
  ChangeDetectionStrategy,
  Component,
  inject,
  input,
  output,
  computed,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  ButtonGroupComponent,
  ButtonComponent,
} from '@progress/kendo-angular-buttons'
import { LoaderComponent } from '@progress/kendo-angular-indicators'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'
import { ProjectFacade } from '@venio/data-access/common'
import { StartupsFacade, UserRights } from '@venio/data-access/review'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { ReviewSetBatchModel } from '@venio/shared/models/interfaces'
import { map } from 'rxjs'
import { isEqual } from 'lodash'

@Component({
  selector: 'venio-reviewset-detail-view-batches-grid-actions',
  standalone: true,
  imports: [
    CommonModule,
    ButtonGroupComponent,
    ButtonComponent,
    SvgLoaderDirective,
    LoaderComponent,
    TooltipDirective,
  ],
  templateUrl: './reviewset-detail-view-batches-grid-actions.component.html',
  styleUrl: './reviewset-detail-view-batches-grid-actions.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsetDetailViewBatchesGridActionsComponent {
  private readonly startupsFacade = inject(StartupsFacade)

  private readonly projectFacade = inject(ProjectFacade)

  /**
   * Signal representing the current review set entry data.
   */
  public readonly rowDataItem = input<ReviewSetBatchModel>()

  /**
   * Emits whenever a user triggers an action. When user clicks on icon actions, an event with payload is emitted.
   */
  public readonly actionInvoked = output<CommonActionTypes>()

  /**
   * Common action types reference.
   */
  public readonly commonActionTypes = CommonActionTypes

  public readonly canRebatch = toSignal(
    this.startupsFacade.hasGroupRight$(UserRights.ALLOW_TO_PURGE_REVIEW_BATCH),
    {
      initialValue: false,
    }
  )

  public readonly selectedBatchStatus = toSignal(
    this.projectFacade.selecteSelectedBatchDetail$.pipe(
      map(
        (selected: ReviewSetBatchModel[]) =>
          selected?.map((item) => item.batchStatus?.toLowerCase()) || []
      )
    ),
    { initialValue: [] }
  )

  public readonly rowData = computed<ReviewSetBatchModel>(
    () => this.rowDataItem(),
    {
      equal: isEqual,
    }
  )

  public readonly isBatchInProgress = computed(() => {
    return this.rowData().batchStatus.toLowerCase() === 'in progress'
  })

  public readonly batchStatus = computed(() => {
    return this.rowData().batchStatus.toLowerCase()
  })

  public readonly shouldDisableRebatch = computed(() => {
    return this.batchStatus() !== 'in progress'
  })

  public readonly rebatchTitle = computed(() => {
    if (this.batchStatus() === 'in progress') {
      return 'Rebatch'
    }
    return `Batch is ${this.batchStatus()}. Cannot rebatch.`
  })

  public readonly shouldDisableDelete = computed(() => {
    return this.batchStatus() === 'in progress'
  })

  public readonly deleteTitle = computed(() => {
    if (this.batchStatus() === 'in progress') {
      return 'Batch is in progress. Cannot delete.'
    }
    return 'Delete'
  })

  /**
   * Emits an action when action icon buttons are clicked.
   * @param {CommonActionTypes} actionType - The action type.
   * @returns {void}
   */
  public actionButtonClick(actionType: CommonActionTypes): void {
    this.actionInvoked.emit(actionType)
  }
}
