import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  output,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  ButtonComponent,
  ButtonGroupComponent,
} from '@progress/kendo-angular-buttons'
import { ProjectFacade } from '@venio/data-access/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { PageArgs, UiPaginationModule } from '@venio/ui/pagination'
import { LoaderComponent } from '@progress/kendo-angular-indicators'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { ReviewSetBatchBulkAction } from '@venio/shared/models/interfaces'
import { CommonActionTypes } from '@venio/shared/models/constants'

@Component({
  selector: 'venio-reviewset-detail-view-batches-toolbar',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    ButtonGroupComponent,
    SvgLoaderDirective,
    LoaderComponent,
    TooltipDirective,
    UiPaginationModule,
  ],
  templateUrl: './reviewset-detail-view-batches-toolbar.component.html',
  styleUrl: './reviewset-detail-view-batches-toolbar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsetDetailViewBatchesToolbarComponent {
  private readonly projectFacade = inject(ProjectFacade)

  /** Output event for the paging. */
  public readonly pagingChange = output<PageArgs>()

  /** Output event for the action invoked.
   * When the actions of grid e.g., rebatch and delete. are clicked, this event is emitted with type `ReviewSetBatchAction`
   */
  public readonly bulkActionInvoked = output<ReviewSetBatchBulkAction>()

  /** Static common action types */
  public readonly commonActionTypes = CommonActionTypes

  /** Signal for the review batch info */
  private readonly currentPagingInfo = toSignal(
    this.projectFacade.selectReviewSetBatchRequestInfo$
  )

  /** Signal for the review set batch loading state */
  public isReviewSetBatchLoading = toSignal(
    this.projectFacade.selectIsReviewSetBatchLoading$,
    { initialValue: true }
  )

  private readonly selectedBatches = toSignal(
    this.projectFacade.selecteSelectedBatchDetail$
  )

  private readonly selectedBatchStatus = computed(() => {
    const batches = this.selectedBatches() || []
    return batches.map((item) => item.batchStatus?.toLowerCase())
  })

  private readonly selectedBatchIds = computed(() => {
    const batches = this.selectedBatches() || []
    return batches.map((item) => item.batchId) || []
  })

  public readonly isBatchInProgress = computed(() => {
    return this.selectedBatchStatus().includes('in progress')
  })

  public readonly shouldDisableRebatch = computed(() => {
    return !this.isBatchInProgress() || !this.selectedBatchStatus()?.[0]
  })

  public readonly shouldDisableDelete = computed(() => {
    return this.isBatchInProgress() || !this.selectedBatchStatus()?.[0]
  })

  public readonly rebatchTitle = computed(() => {
    return !this.selectedBatchStatus()?.[0]
      ? 'No batches selected'
      : !this.isBatchInProgress()
      ? 'Some of the selected batches are not currently in progress. Cannot rebatch.'
      : 'Rebatch'
  })

  public readonly deleteTitle = computed(() => {
    return !this.selectedBatchStatus()?.[0]
      ? 'No batches selected'
      : this.isBatchInProgress()
      ? 'Some of the selected batches are currently in progress. Cannot delete.'
      : 'Delete'
  })

  public readonly totalRecords = computed(
    () => this.currentPagingInfo()?.totalReviewSetBatchCount || 0
  )

  public readonly pageSize = computed(
    () => this.currentPagingInfo()?.pageSize || 0
  )

  public pagingControlChange(arg: PageArgs): void {
    this.pagingChange.emit(arg)
  }

  /**
   * Emits an action when action icon buttons are clicked.
   * @param {CommonActionTypes} actionType - The action type.
   * @returns {void}
   */
  public actionButtonClick(actionType: CommonActionTypes): void {
    this.bulkActionInvoked.emit({
      actionType,
      batchIds: this.selectedBatchIds(),
    })
  }
}
