import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetDetailViewBatchesComponent } from './reviewset-detail-view-batches.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideAnimations } from '@angular/platform-browser/animations'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideMockStore } from '@ngrx/store/testing'
import { ProjectFacade } from '@venio/data-access/common'
import { ResponseModel, ReviewSetEntry } from '@venio/shared/models/interfaces'
import { of } from 'rxjs'

describe('ReviewsetDetailViewBatchesComponent', () => {
  let component: ReviewsetDetailViewBatchesComponent
  let fixture: ComponentFixture<ReviewsetDetailViewBatchesComponent>

  const mockReviewSetEntry: ReviewSetEntry = {
    sn: 1,
    uuid: '123e4567-e89b-12d3-a456-************',
    reviewSetId: 1001,
    projectId: 5001,
    reviewSetName: 'Sample Review Set',
    isCqlReviewSet: false,
    projectName: 'Project Alpha',
    batchCount: 5,
    totalDocCount: 1000,
    reviewedDocCount: 600,
    reviewerCount: 3,
    reviewSetStatus: 'In Progress',
    isReviewSetAssignedToCurrentUser: true,
    assignedReviewers: 'John Doe, Jane Smith',
    createdBy: 'Admin User',
    createdDate: '2024-02-07',
    createdTime: '10:30 AM',
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReviewsetDetailViewBatchesComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      providers: [
        provideAnimations(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ProjectFacade,
          useValue: {
            selectReviewSetRebatchSuccess$: of({} as ResponseModel),
            selectReviewSetReBatchError$: of({} as ResponseModel),
            selectReviewSetDeleteBatchSuccess$: of({} as ResponseModel),
            selectReviewSetDeleteBatchError$: of({} as ResponseModel),
            selectReviewSetReassignSuccess$: of({} as ResponseModel),
            selectReviewSetReassignError$: of({} as ResponseModel),
            fetchReviewSetBatch: jest.fn(),
            updateReviewSetBatchRequestInfo: jest.fn(),
            rebatchReviewSetBatch: jest.fn(),
            deleteReviewSetBatch: jest.fn(),
            reassignReviewSetBatch: jest.fn(),
            resetProjectState: jest.fn(),
          } satisfies Partial<ProjectFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetDetailViewBatchesComponent)
    fixture.componentRef.setInput('selectedReviewSetEntry', mockReviewSetEntry)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
