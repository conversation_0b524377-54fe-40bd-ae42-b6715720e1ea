@defer{ @switch(selectedViewType()){
@case(reviewSetViewDetailViewTypes.TREE_VIEW){
<venio-reviewset-detail-view-documents
  (batchDeletionStatus)="forwardBatchDeletionStatus($event)"
  [selectedReviewSetEntry]="selectedReviewSetEntry()" />
} @case(reviewSetViewDetailViewTypes.BATCH_VIEW){
<venio-reviewset-detail-view-batches
  [selectedReviewSetEntry]="selectedReviewSetEntry()" />
} } } @placeholder {
<kendo-skeleton width="100%" height="60px" />
}
