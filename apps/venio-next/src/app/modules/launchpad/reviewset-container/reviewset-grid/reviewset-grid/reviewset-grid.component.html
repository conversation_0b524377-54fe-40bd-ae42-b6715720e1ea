<div #containerElement class="t-flex-1 t-relative">
  <kendo-grid
    venioDynamicHeight
    [extraSpacing]="20"
    class="t-w-full t-min-h-[20rem] t-grid t-border-b-1 t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto v-case-launchpad-grid"
    [data]="gridView()"
    [loading]="isReviewSetSummaryDetailLoading()"
    [skip]="skip"
    [pageSize]="pageSize"
    [rowHeight]="34"
    [sortable]="true"
    [sort]="sort"
    [groupable]="false"
    [reorderable]="false"
    [resizable]="true"
    [trackBy]="caseTrackByFn"
    [selectable]="{ mode: 'multiple', cell: false, checkboxOnly: true }"
    [selectedKeys]="selectedProjectIds()"
    (pageChange)="handlePagingForVirtualScroll($event)"
    (filterChange)="reviewSetFilterChange($event)"
    (selectedKeysChange)="selectReviewSet($event)"
    (sortChange)="caseSortOrderChange($event)"
    scrollable="virtual"
    kendoGridSelectBy="uuid"
    filterable="menu">
    <ng-template kendoGridNoRecordsTemplate>
      <p *ngIf="!isReviewSetSummaryDetailLoading()">No records found</p>
    </ng-template>
    <kendo-grid-column
      field="sn"
      [width]="45"
      title="#"
      headerClass="t-text-primary"
      [filterable]="false" />
    <kendo-grid-checkbox-column [showSelectAll]="false" [width]="40" />
    <kendo-grid-column
      field="reviewSetName"
      title="Name"
      [sortable]="true"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        {{ dataItem.reviewSetName }}
        @if(dataItem.isCalReviewSet){
        <span
          class="t-bg-[#9D9EC3] t-rounded-md t-px-2 t-py-1 t-text-white t-text-xs t-cursor-default t-ml-2 ng-star-inserted"
          >CAL</span
        >
        }
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      field="projectName"
      title="Case Name"
      [sortable]="true"
      headerClass="t-text-primary"
      [filterable]="false" />

    <kendo-grid-column
      [sortable]="false"
      [filterable]="false"
      field="batchCount"
      [width]="178"
      title="Number Of Batches"
      class="!t-text-right !t-pr-4"
      headerClass="t-text-primary">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="t-flex t-flex-grow t-justify-end t-gap-2">
          <span>{{ dataItem.batchCount }}</span>
          @if(canManageReviewSet(dataItem)) {
          <span
            (click)="openReviewSetViewDetailDialog(dataItem)"
            class="t-cursor-pointer"
            venioSvgLoader
            color="#1DBADC"
            svgUrl="assets/svg/eye.svg"
            height="1rem"
            width="1rem"></span>
          }
        </div>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      [sortable]="false"
      [filterable]="false"
      field="reviewSetStatus"
      title="Status"
      headerClass="t-text-primary">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="progress-container t-cursor-pointer">
          <kendo-progressbar
            [value]="
              (dataItem.reviewedDocCount / dataItem.totalDocCount) * 100 || 0
            "
            [label]="{
              visible: true,
              format: 'percent',
              position: 'end'
            }"
            [progressCssStyle]="{
              background:
                (dataItem.reviewedDocCount / dataItem.totalDocCount) * 100 < 20
                  ? '#FFBB12'
                  : '#9BD2A7'
            }"
            [animation]="{ duration: 600 }"
            (click)="openReviewerDashboardDialog(dataItem)"
            class="v-custom-progress-bar t-h-[12px] t-w-full t-cursor-pointer">
          </kendo-progressbar>
        </div>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      [sortable]="false"
      field="projectId"
      [width]="240"
      title="Created By & On"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Created By & On"
          >Created By & On</span
        >
      </ng-template>

      <ng-template kendoGridCellTemplate let-dataItem>
        <p
          class="t-inline-block t-truncate t-overflow-hidden"
          kendoTooltip
          [title]="dataItem.createdBy">
          {{ dataItem.createdBy }}
        </p>
        <p class="t-text-xs t-text-[#999999]">
          {{ dataItem.createdDate }} {{ dataItem.createdTime }}
        </p>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      title="Actions"
      [width]="200"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        <ng-template #actionPlaceholder>
          <div class="t-flex t-flex-row t-gap-2">
            <kendo-skeleton
              *ngFor="let n of [1, 2, 3, 4]"
              height="25px"
              width="25px"
              shape="rectangle"
              class="t-rounded-md" />
          </div>
        </ng-template>
        @defer {
        <venio-reviewset-grid-actions
          *ngIf="dataItem"
          [rowDataItem]="dataItem"
          (actionInvoked)="forwardActionControlClick($event, dataItem)" />
        } @placeholder {
        <ng-container *ngTemplateOutlet="actionPlaceholder" />
        }
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
</div>

@if(reviewSetDetailViewDialogVisibility()){
<kendo-window
  [top]="100"
  [left]="0"
  state="maximized"
  class="v-custom-window"
  [resizable]="false"
  [draggable]="false">
  @defer{
  <kendo-window-titlebar [draggable]="false" class="t-p-0">
    <venio-reviewset-detail-view-header
      [selectedReviewSetEntry]="selectedReviewSetEntry()"
      (dialogCloseAction)="closeReviewSetViewDetailDialog()" />
  </kendo-window-titlebar>
  <venio-reviewset-detail-view-dialog-container
    [selectedReviewSetEntry]="selectedReviewSetEntry()" />
  }
</kendo-window>
} @if(reviewerDashboardDialogVisibility()){
<kendo-window
  [top]="100"
  [left]="0"
  state="maximized"
  class="v-custom-window"
  [resizable]="false"
  [draggable]="false">
  @defer{
  <kendo-window-titlebar [draggable]="false" class="t-p-0">
    <venio-reviewset-batches-dashboard-header
      [selectedReviewSetEntry]="selectedReviewSetEntry()"
      (dialogCloseAction)="closeReviewerDashboardDialog()" />
  </kendo-window-titlebar>
  <venio-reviewset-batches-view-dashboard
    [selectedReviewSetEntry]="selectedReviewSetEntry()" />
  }
</kendo-window>
}
