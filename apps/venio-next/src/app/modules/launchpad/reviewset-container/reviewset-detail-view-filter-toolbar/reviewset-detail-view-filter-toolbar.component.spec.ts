import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetDetailViewFilterToolbarComponent } from './reviewset-detail-view-filter-toolbar.component'
import { StoreModule } from '@ngrx/store'
import { EffectsModule } from '@ngrx/effects'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { ProjectFacade, UserFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import { ResponseModel, UserModel } from '@venio/shared/models/interfaces'
import { SearchService } from '@venio/data-access/review'
import { NotificationService } from '@progress/kendo-angular-notification'
import { provideNoopAnimations } from '@angular/platform-browser/animations'

describe('ReviewsetDetailViewFilterToolbarComponent', () => {
  let component: ReviewsetDetailViewFilterToolbarComponent
  let fixture: ComponentFixture<ReviewsetDetailViewFilterToolbarComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReviewsetDetailViewFilterToolbarComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ProjectFacade,
          useValue: {
            selectIsReviewSetDocumentViewLoading$: of(undefined),
            selectTaggedDocumentTagsSuccess$: of({} as ResponseModel),
            resetProjectState: jest.fn(),
            fetchReviewSetDocumentView: jest.fn(),
            updateReviewSetDocumentViewLoader: jest.fn(),
            fetchTaggedDocumentTags: jest.fn(),
          } satisfies Partial<ProjectFacade>,
        },
        {
          provide: NotificationService,
          useValue: {
            show: jest.fn(),
          } satisfies Partial<NotificationService>,
        },
        {
          provide: UserFacade,
          useValue: {
            selectCurrentUserDetails$: of({} as UserModel),
          } satisfies Partial<UserFacade>,
        },
        {
          provide: SearchService,
          useValue: {
            search$: jest.fn(),
          } satisfies Partial<SearchService>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetDetailViewFilterToolbarComponent)
    component = fixture.componentInstance
    fixture.componentRef.setInput('selectedReviewSetEntry', {})
    fixture.componentRef.setInput('batchDeletionStatus', false)
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
