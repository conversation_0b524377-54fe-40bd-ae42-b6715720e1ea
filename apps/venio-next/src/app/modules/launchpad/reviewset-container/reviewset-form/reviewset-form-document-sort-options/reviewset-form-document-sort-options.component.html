<div class="t-flex t-flex-row t-gap-4" [formGroup]="reviewSetForm()">
  <div>
    <div class="t-flex t-flex-col t-gap-2">
      <div class="t-flex t-items-center t-mb-1 t-w-[142px]">
        <input
          type="checkbox"
          formControlName="sortByCustodian"
          kendoCheckBox
          #cust />
        <kendo-label
          class="k-checkbox-label"
          [for]="cust"
          text="Sort by Custodian" />
      </div>
      <div class="t-flex">
        <kendo-dropdownlist
          formControlName="custodianSortOrder"
          class="t-w-32 t-uppercase"
          [data]="['asc', 'desc']">
          <ng-template kendoDropDownListItemTemplate let-dataItem>
            <span class="t-uppercase"> {{ dataItem }}</span>
          </ng-template>
        </kendo-dropdownlist>
      </div>
    </div>
  </div>
  <div class="t-flex t-w-[2px] v-custom-dashed-border t-mx-4"></div>
  <div class="t-basis-2/3 t-flex t-flex-col t-gap-3">
    <div class="t-w-full t-flex t-flex-col t-gap-2">
      <div class="t-flex t-items-center t-mb-1">
        <kendo-label
          class="t-font-medium t-text-[#263238]"
          text="Document Sort Order" />
      </div>
      <div class="t-flex t-gap-3 t-w-full">
        <div class="t-flex t-basis-1/2">
          <kendo-dropdownlist
            [loading]="isCustomFieldsLoading()"
            formControlName="orderByField"
            [filterable]="true"
            (filterChange)="
              reviewSetFormService.configureFilterTerm($event, 'SORT_FIELD')
            "
            [data]="reviewSetFormService.filteredSortFields()" />
        </div>
        <div class="t-flex t-basis-1/4">
          <kendo-dropdownlist
            formControlName="sortOrder"
            class="t-w-32 t-uppercase"
            [data]="['asc', 'desc']">
            <ng-template kendoDropDownListItemTemplate let-dataItem>
              <span class="t-uppercase"> {{ dataItem }}</span>
            </ng-template>
          </kendo-dropdownlist>
        </div>
      </div>
    </div>
  </div>
</div>
