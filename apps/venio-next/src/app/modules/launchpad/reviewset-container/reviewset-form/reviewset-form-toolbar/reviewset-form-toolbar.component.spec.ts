import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetFormToolbarComponent } from './reviewset-form-toolbar.component'
import { ReviewsetFormService } from '../reviewset-form.service'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { FormBuilder } from '@angular/forms'
import { createMockReviewSetForm } from '../review-set-form.mock'
import { ProjectFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import { CaseDetailResponseModel } from '@venio/shared/models/interfaces'
import { ReviewSetPayloadService } from '../reviewset-payload.service'

describe('ReviewsetFormToolbarComponent', () => {
  let component: ReviewsetFormToolbarComponent
  let fixture: ComponentFixture<ReviewsetFormToolbarComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewsetFormToolbarComponent],
      providers: [
        provideMockStore(),
        provideNoopAnimations(),
        ReviewsetFormService,
        ReviewSetPayloadService,
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: ProjectFacade,
          useValue: {
            selectIsCaseDetailLoading$: of(false),
            selectCaseDetail$: of(undefined as CaseDetailResponseModel),
            resetProjectState: jest.fn(),
            updateCaseDetailRequestInfo: jest.fn(),
            fetchCaseDetail: jest.fn(),
          } satisfies Partial<ProjectFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetFormToolbarComponent)
    component = fixture.componentInstance
    const reviewSetFormService = TestBed.inject(ReviewsetFormService)

    const formBuilder = TestBed.inject(FormBuilder)
    const mockForm = createMockReviewSetForm(formBuilder)
    reviewSetFormService.reviewSetForm = mockForm
    fixture.componentRef.setInput('reviewSetForm', mockForm)
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
