import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewsetFormAdvancedOptionsComponent } from './reviewset-form-advanced-options.component'
import { ReviewsetFormService } from '../reviewset-form.service'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { FormBuilder } from '@angular/forms'
import { createMockReviewSetForm } from '../review-set-form.mock'
import { provideMockStore } from '@ngrx/store/testing'
import { ReviewSetPayloadService } from '../reviewset-payload.service'

describe('ReviewsetFormAdvancedOptionsComponent', () => {
  let component: ReviewsetFormAdvancedOptionsComponent
  let fixture: ComponentFixture<ReviewsetFormAdvancedOptionsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewsetFormAdvancedOptionsComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
      providers: [
        provideMockStore(),
        ReviewsetFormService,
        ReviewSetPayloadService,
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewsetFormAdvancedOptionsComponent)
    component = fixture.componentInstance
    const formBuilder = TestBed.inject(FormBuilder)
    fixture.componentRef.setInput(
      'reviewSetForm',
      createMockReviewSetForm(formBuilder)
    )
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
