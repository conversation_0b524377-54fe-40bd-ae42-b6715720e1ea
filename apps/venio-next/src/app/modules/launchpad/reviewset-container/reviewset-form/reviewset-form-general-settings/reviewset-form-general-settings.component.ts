import { Component, computed, inject, input, signal } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import { ReviewSetForm } from '@venio/shared/models/interfaces'
import {
  NumericTextBoxComponent,
  TextAreaComponent,
  TextBoxComponent,
  TextBoxSuffixTemplateDirective,
} from '@progress/kendo-angular-inputs'
import { toObservable, toSignal } from '@angular/core/rxjs-interop'
import { combineLatest, Observable, switchMap } from 'rxjs'
import { map } from 'rxjs/operators'
import { ReviewsetFormService } from '../reviewset-form.service'
import { LoaderComponent } from '@progress/kendo-angular-indicators'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'

@Component({
  selector: 'venio-reviewset-form-general-settings',
  standalone: true,
  imports: [
    CommonModule,
    NumericTextBoxComponent,
    TextAreaComponent,
    TextBoxComponent,
    ReactiveFormsModule,
    TextBoxSuffixTemplateDirective,
    LoaderComponent,
    TooltipDirective,
  ],
  templateUrl: './reviewset-form-general-settings.component.html',
  styleUrl: './reviewset-form-general-settings.component.scss',
})
export class ReviewsetFormGeneralSettingsComponent {
  public readonly reviewSetForm = input.required<FormGroup<ReviewSetForm>>()

  private readonly reviewSetFormService = inject(ReviewsetFormService)

  public readonly nameInvalid = toSignal(
    this.#createControlInvalidSignal('name')
  )

  public readonly nameAlreadyExists = toSignal(
    toObservable(signal(false)).pipe(
      switchMap(() =>
        combineLatest([
          this.reviewSetForm().controls.name.valueChanges,
          this.reviewSetForm().controls.name.statusChanges,
        ])
      ),
      map((x) => {
        const error = this.reviewSetForm().controls.name.errors
        return error?.['nameExists'] as boolean
      })
    )
  )

  public readonly batchPrefixInvalid = toSignal(
    this.#createControlInvalidSignal('batchPrefix')
  )

  public readonly batchStartNumberInvalid = toSignal(
    this.#createControlInvalidSignal('batchStartNumber')
  )

  public readonly batchPadLenInvalid = toSignal(
    this.#createControlInvalidSignal('batchPaddingLength')
  )

  public readonly batchSizeInvalid = toSignal(
    this.#createControlInvalidSignal('batchSize')
  )

  public readonly isReviewSetsLoading = computed(
    () => this.reviewSetFormService.loadingStatus().isReviewSetsLoading
  )

  public readonly isReviewSetNameChecking = computed(
    () => this.reviewSetFormService.loadingStatus().isReviewSetNameChecking
  )

  #createControlInvalidSignal(
    controlName: keyof ReviewSetForm
  ): Observable<boolean> {
    return toObservable(signal(false)).pipe(
      switchMap(() =>
        combineLatest([
          this.reviewSetForm().controls[controlName].valueChanges,
          this.reviewSetForm().controls[controlName].statusChanges,
        ])
      ),
      map((x) => {
        const control = this.reviewSetForm().controls[controlName]
        const isNumber = typeof control.value === 'number'
        const isString = typeof control.value === 'string'

        const invalidValue = isNumber
          ? control.value <= 0
          : isString
          ? control.value.trim() === ''
          : typeof control.value === 'undefined' || control.value === null
        return invalidValue && (control.touched || control.dirty)
      })
    )
  }
}
