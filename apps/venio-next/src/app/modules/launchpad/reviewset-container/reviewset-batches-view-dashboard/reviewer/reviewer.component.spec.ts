import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReviewerComponent } from './reviewer.component'
import { ReviewSetFacade } from '@venio/data-access/common'
import { of } from 'rxjs'

jest.mock('@venio/util/utilities', () => {
  return {
    ReviewSetBatchChartWorkerService: jest.fn().mockImplementation(() => {
      return {
        generateReviewerChartData: jest.fn().mockResolvedValue([]),
        terminate: jest.fn(),
      }
    }),
  }
})

describe('ReviewerComponent', () => {
  let component: ReviewerComponent
  let fixture: ComponentFixture<ReviewerComponent>

  const mockReviewSetFacade = {
    selectReviewSetReviewerSuccess$: of([]),
    isReviewSetReviewerLoading$: of(false),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReviewerComponent],
      providers: [{ provide: ReviewSetFacade, useValue: mockReviewSetFacade }],
    }).compileComponents()

    fixture = TestBed.createComponent(ReviewerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
