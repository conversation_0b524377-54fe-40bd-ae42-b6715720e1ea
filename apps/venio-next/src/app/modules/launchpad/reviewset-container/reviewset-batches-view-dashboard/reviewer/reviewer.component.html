<div class="t-w-full t-h-[260px]">
  <div class="t-relative">
    @if (isReviewSetReviewerLoading()) {
    <div
      class="k-i-loading t-absolute t-h-full t-w-full t-bg-[rgba(255,255,255,0.47)] t-text-[rgba(18,17,17,0.93)] t-top-0 t-left-0 t-right-0 t-bottom-0 t-text-[58px] t-z-10"></div>
    }

    <div class="t-flex">
      <kendo-chart
        class="t-w-full t-h-[260px] !t-border-none v-custom-char-production-bar"
        [chartArea]="COMMON_CHART_OPTIONS.chartArea">
        <kendo-chart-legend
          position="top"
          [visible]="false"
          orientation="horizontal">
        </kendo-chart-legend>
        <kendo-chart-category-axis>
          <kendo-chart-category-axis-item
            [categories]="formattedDates()"
            [line]="{ visible: false }"
            [majorGridLines]="COMMON_CHART_OPTIONS.gridLines"
            [minorGridLines]="COMMON_CHART_OPTIONS.gridLines"
            [majorTicks]="COMMON_CHART_OPTIONS.majorTicks"
            [minorTicks]="COMMON_CHART_OPTIONS.minorTicks"
            [labels]="CATEGORY_AXIS_LABELS">
          </kendo-chart-category-axis-item>
        </kendo-chart-category-axis>
        <kendo-chart-value-axis>
          <kendo-chart-value-axis-item
            [majorGridLines]="COMMON_CHART_OPTIONS.gridLines"
            [minorGridLines]="COMMON_CHART_OPTIONS.gridLines"
            [majorTicks]="COMMON_CHART_OPTIONS.majorTicks"
            [minorTicks]="COMMON_CHART_OPTIONS.minorTicks"
            [labels]="VALUE_AXIS_LABELS">
          </kendo-chart-value-axis-item>
        </kendo-chart-value-axis>
        <kendo-chart-series>
          @for (user of uniqueUsers(); track user) {
          <kendo-chart-series-item
            [stack]="true"
            type="column"
            [data]="stackedData()[user]"
            [name]="user"
            [color]="usersColor()[user]"
            [gap]="12"
            [spacing]="0.5"
            [border]="COMMON_CHART_OPTIONS.border">
          </kendo-chart-series-item>
          }
        </kendo-chart-series>
      </kendo-chart>
    </div>
  </div>
</div>

<!-- TODO: This functionality will be implemented at a later stage -->
<!-- <div class="t-text-center t-text-sm t-font-medium">
  <div class="t-relative t-top-[-5px]">
    <span class="t-tracking-[1.5px] t-font-medium t-text-[10px]">Date</span>
  </div>
</div> -->
