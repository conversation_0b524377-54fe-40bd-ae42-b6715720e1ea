import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  signal,
  Type,
  untracked,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { ReviewSetFacade } from '@venio/data-access/common'
import { DialogService } from '@progress/kendo-angular-dialog'
import { eyeIcon } from '@progress/kendo-svg-icons'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { SVGIconModule } from '@progress/kendo-angular-icons'
import { ChartsModule } from '@progress/kendo-angular-charts'
import { filter } from 'rxjs'
import { ReviewStatusModel } from '@venio/shared/models/interfaces'
import { ReviewSetBatchChartWorkerService } from '@venio/util/utilities'
import { COMMON_CHART_OPTIONS } from '@venio/shared/models/constants'

@Component({
  selector: 'venio-tag-status',
  standalone: true,
  imports: [CommonModule, ChartsModule, SVGIconModule, ButtonsModule],
  templateUrl: './tag-status.component.html',
  styleUrl: './tag-status.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagStatusComponent {
  private readonly reviewSetFacade = inject(ReviewSetFacade)

  private readonly viewContainerRef = inject(ViewContainerRef)

  private dialogService = inject(DialogService)

  public readonly COMMON_CHART_OPTIONS = COMMON_CHART_OPTIONS

  public icons = {
    eyeIcon: eyeIcon,
  }

  private tagStatus = signal<ReviewStatusModel[]>([])

  public shouldDisableTagFilter = computed(() => {
    return Boolean(!this.tagStatus()?.[0])
  })

  /** Signal for the filter fields */
  private readonly filterFields = toSignal(
    this.reviewSetFacade.selectFilterFields$.pipe(
      filter((e) => typeof e !== 'undefined')
    ),
    {
      initialValue: [],
    }
  )

  /** Computed property for the user selected filter fields */
  private readonly selectedFilterFields = computed(() => {
    return this.filterFields() || []
  })

  public readonly isReviewSetTagStatusLoading = toSignal(
    this.reviewSetFacade.isReviewSetTagStatusLoading$,
    {
      initialValue: true,
    }
  )

  /** Signal for the review set progress detail */
  private readonly reviewSetTagStatus = toSignal(
    this.reviewSetFacade.selectReviewSetTagStatusSuccess$.pipe(
      filter((e) => typeof e !== 'undefined')
    ),
    {
      initialValue: [],
    }
  )

  private readonly loadedReviewSetTagStatus = computed(() => {
    return this.reviewSetTagStatus() || []
  })

  /** Computed property for the tag status */
  public readonly loadedTagStatus = computed(() => {
    const reviewSetTagStatus = this.tagStatus()
    const userSelectedFields = this.selectedFilterFields()
    return userSelectedFields?.[0]
      ? reviewSetTagStatus.filter((field) =>
          userSelectedFields.includes(field.tagName)
        )
      : reviewSetTagStatus
  })

  constructor() {
    effect(() => {
      if (this.loadedReviewSetTagStatus()) {
        untracked(() => this.#prepareTagStatusChartData())
      }
    })
  }

  #prepareTagStatusChartData(): void {
    const reviewSetBatchChartWorkerService =
      new ReviewSetBatchChartWorkerService()
    reviewSetBatchChartWorkerService
      .generateTagStatusChartData(this.loadedReviewSetTagStatus())
      .then((tagStatusData: ReviewStatusModel[]) => {
        this.tagStatus.set(tagStatusData)
        reviewSetBatchChartWorkerService.terminate()
      })
  }

  #launchDialogContent(dialogContent: Type<unknown>): void {
    this.dialogService.open({
      appendTo: this.viewContainerRef,
      content: dialogContent,
      maxWidth: '900px',
      maxHeight: '650px',
      width: '40%',
      height: '90vh',
    })
  }

  #handleLazyLoadedDialog(): void {
    import('../tag-status/tag-status-filter/tag-status-filter.component').then(
      (d) => {
        // launch the dialog
        this.#launchDialogContent(d.TagStatusFilterComponent)
      }
    )
  }

  public openTagFilter(): void {
    this.#handleLazyLoadedDialog()
  }
}
