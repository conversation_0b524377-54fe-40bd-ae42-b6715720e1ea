import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  signal,
  untracked,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { ReviewSetFacade } from '@venio/data-access/common'
import { ChartsModule } from '@progress/kendo-angular-charts'
import { ReviewSetBatchChartWorkerService } from '@venio/util/utilities'
import { ReviewerChartDataOutput } from '@venio/shared/models/interfaces'
import {
  COMMON_CHART_OPTIONS,
  CATEGORY_AXIS_LABELS,
  VALUE_AXIS_LABELS,
} from '@venio/shared/models/constants'

@Component({
  selector: 'venio-reviewer',
  standalone: true,
  imports: [CommonModule, ChartsModule],
  templateUrl: './reviewer.component.html',
  styleUrl: './reviewer.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewerComponent {
  private readonly reviewSetFacade = inject(ReviewSetFacade)

  public readonly COMMON_CHART_OPTIONS = COMMON_CHART_OPTIONS

  public readonly CATEGORY_AXIS_LABELS = CATEGORY_AXIS_LABELS

  public readonly VALUE_AXIS_LABELS = VALUE_AXIS_LABELS

  public formattedDates = signal<string[]>([])

  public uniqueUsers = signal<string[]>([])

  public stackedData = signal<Record<string, number[]>>({})

  public usersColor = signal<Record<string, string>>({})

  public readonly isReviewSetReviewerLoading = toSignal(
    this.reviewSetFacade.isReviewSetReviewerLoading$,
    {
      initialValue: true,
    }
  )

  /** Signal for the review set batch summary detail */
  private readonly reviewSetReviewer = toSignal(
    this.reviewSetFacade.selectReviewSetReviewerSuccess$
  )

  /** Computed property for the review set batch summary detail */
  private readonly loadedReviewSetReviewer = computed(() => {
    return this.reviewSetReviewer() || []
  })

  constructor() {
    effect(() => {
      if (this.loadedReviewSetReviewer()) {
        untracked(() => this.#prepareReviewerChartData())
      }
    })
  }

  #prepareReviewerChartData(): void {
    const reviewSetBatchChartWorkerService =
      new ReviewSetBatchChartWorkerService()
    reviewSetBatchChartWorkerService
      .generateReviewerChartData(this.loadedReviewSetReviewer())
      .then((reviewerChartData: ReviewerChartDataOutput) => {
        this.formattedDates.set(reviewerChartData.formattedDates)
        this.uniqueUsers.set(reviewerChartData.uniqueUsers)
        this.stackedData.set(reviewerChartData.stackedData)
        this.usersColor.set(reviewerChartData.usersColor)
        reviewSetBatchChartWorkerService.terminate()
      })
  }
}
