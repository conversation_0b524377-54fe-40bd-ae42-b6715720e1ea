<div class="t-w-full t-flex-col t-gap-8">
  <div class="t-items-center t-w-full">
    @defer{
    <venio-summary-chart />
    } @placeholder {
    <div
      class="t-flex t-flex-col t-flex-grow t-relative t-justify-start t-items-center">
      <kendo-skeleton [height]="220" [width]="220" shape="circle" />
    </div>
    }
  </div>

  <div class="t-w-full">
    <div class="t-p-4 t-space-y-4">
      <div
        class="t-bg-white t-shadow-xl t-mb-8 t-border-[1px] t-border-[#E0E0E0] t-rounded-md t-p-4 t-w-full t-max-w-sm t-mx-auto">
        <div class="t-flex t-justify-between t-items-center t-mb-3">
          <div class="t-flex t-items-center">
            <span
              class="t-inline-block t-w-3 t-h-3 t-bg-[#9BD2A7] t-rounded-full t-mr-2"></span>
            <span class="t-text-[#393939] t-text-sm t-font-medium"
              >Online Reviewer</span
            >
          </div>
          <div class="t-flex t-items-center">
            <span
              class="t-inline-block t-w-3 t-h-3 t-bg-[#A09F9F] t-rounded-full t-mr-2"></span>
            <span class="t-text-[#393939] t-text-sm t-font-medium"
              >Total Reviewer</span
            >
          </div>
        </div>
        <div class="t-flex t-justify-around t-items-center">
          <div class="t-text-4xl t-font-bold t-text-[#9BD2A7]">
            {{ reviewerStats().activeReviewerCount }}
          </div>
          <div class="t-text-4xl t-font-bold t-text-[#A09F9F]">
            {{ reviewerStats().reviewerCount }}
          </div>
        </div>
      </div>
      <div
        class="t-bg-[#F5F7F0] t-rounded-md t-shadow-md t-p-4 t-w-full t-max-w-sm t-mx-auto">
        <div class="t-grid t-grid-cols-2 t-gap-y-4 t-items-center">
          <div class="t-flex t-items-center t-gap-x-3">
            <div
              class="t-inline-block t-w-6 t-h-6 t-bg-gray-300 t-rounded-full t-flex t-items-center t-justify-center">
              <span
                class="t-w-full"
                venioSvgLoader
                svgUrl="assets/svg/icon-summary-set-one-cloud.svg"
                height="28px"
                width="28px">
              </span>
            </div>
            <span class="t-font-medium">Per Day</span>
          </div>
          <div class="t-text-right t-font-medium t-text-lg">
            {{ reviewStats().reviewedPerDay }}
          </div>
          <div class="t-flex t-items-center t-gap-x-3">
            <div
              class="t-inline-block t-w-6 t-h-6 t-bg-gray-300 t-rounded-full t-flex t-items-center t-justify-center">
              <span
                class="t-w-full"
                venioSvgLoader
                svgUrl="assets/svg/icon-summary-set-one-watch.svg"
                height="28px"
                width="28px">
              </span>
            </div>
            <span class="t-font-medium">Per Hour</span>
          </div>
          <div class="t-text-right t-font-medium t-text-lg">
            {{ reviewStats().reviewedPerHour }}
          </div>
          <div class="t-flex t-items-center t-gap-x-3">
            <div
              class="t-inline-block t-w-6 t-h-6 t-bg-gray-300 t-rounded-full t-flex t-items-center t-justify-center">
              <span
                class="t-w-full"
                venioSvgLoader
                svgUrl="assets/svg/icon-summary-set-one-hiererachy.svg"
                height="28px"
                width="28px">
              </span>
            </div>
            <span class="t-font-medium">Per Reviewer</span>
          </div>
          <div class="t-text-right t-font-medium t-text-lg">
            {{ reviewStats().reviewedPerReviewer }}
          </div>
        </div>
      </div>

      <div class="t-flex t-justify-between t-items-center">
        <span class="t-text-[#9F9F9F] t-text-sm">Completion Date</span>
        <span class="t-text-[#127628] t-text-sm t-font-semibold">{{
          completionDate()
        }}</span>
      </div>
    </div>
  </div>
</div>
