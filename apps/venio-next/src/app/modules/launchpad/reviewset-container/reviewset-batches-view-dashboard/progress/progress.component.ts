import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  signal,
  untracked,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { ReviewSetFacade } from '@venio/data-access/common'
import { ChartsModule } from '@progress/kendo-angular-charts'
import {
  ProgressChartDataOutput,
  ProgressGroupedData,
} from '@venio/shared/models/interfaces'
import { ReviewSetBatchChartWorkerService } from '@venio/util/utilities'
import {
  COMMON_CHART_OPTIONS,
  CATEGORY_AXIS_LABELS,
  VALUE_AXIS_LABELS,
} from '@venio/shared/models/constants'

@Component({
  selector: 'venio-progress',
  standalone: true,
  imports: [CommonModule, ChartsModule],
  templateUrl: './progress.component.html',
  styleUrl: './progress.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProgressComponent {
  private readonly reviewSetFacade = inject(ReviewSetFacade)

  public readonly COMMON_CHART_OPTIONS = COMMON_CHART_OPTIONS

  public readonly CATEGORY_AXIS_LABELS = CATEGORY_AXIS_LABELS

  public readonly VALUE_AXIS_LABELS = VALUE_AXIS_LABELS

  public formattedDates = signal<string[]>([])

  public groupedData = signal<ProgressGroupedData[]>([])

  public readonly isReviewSetProgressLoading = toSignal(
    this.reviewSetFacade.isReviewSetProgressLoading$,
    {
      initialValue: true,
    }
  )

  /** Signal for the review set progress detail */
  private readonly reviewSetProgress = toSignal(
    this.reviewSetFacade.selectReviewSetProgressSuccess$
  )

  /** Computed property for the review set progress detail */
  public readonly loadedReviewSetProgress = computed(() => {
    return this.reviewSetProgress() || []
  })

  constructor() {
    effect(() => {
      if (this.loadedReviewSetProgress()) {
        untracked(() => this.#prepareProgressChartData())
      }
    })
  }

  #prepareProgressChartData(): void {
    const reviewSetBatchChartWorkerService =
      new ReviewSetBatchChartWorkerService()
    reviewSetBatchChartWorkerService
      .generateProgressChartData(this.loadedReviewSetProgress())
      .then((progressChartData: ProgressChartDataOutput) => {
        this.formattedDates.set(progressChartData.formattedDates)
        this.groupedData.set(progressChartData.groupedData)
        reviewSetBatchChartWorkerService.terminate()
      })
  }
}
