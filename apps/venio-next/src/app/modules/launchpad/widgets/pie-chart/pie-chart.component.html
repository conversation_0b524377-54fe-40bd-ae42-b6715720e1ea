<div class="t-flex t-items-center t-flex-col t-h-full">
  <kendo-chart class="t-border-0 t-h-64">
    <kendo-chart-tooltip>
      <ng-template
        kendoChartSeriesTooltipTemplate
        let-value="value"
        let-category="category"
        let-series="series">
        {{ category }}: {{ value }}
      </ng-template>
    </kendo-chart-tooltip>
    <kendo-chart-series>
      <kendo-chart-series-item
        type="pie"
        [margin]="0"
        [padding]="10"
        [data]="chartInfo()"
        categoryField="status"
        field="count"
        [colorField]="'color'"
        [autoFit]="true">
      </kendo-chart-series-item>
    </kendo-chart-series>
    <kendo-chart-legend [visible]="false"></kendo-chart-legend>
  </kendo-chart>
  <div class="t-flex t-gap-3 t-justify-between t-flex-wrap">
    @for (item of chartInfo();track item.count) {
    <div class="t-flex t-flex-col t-gap-1">
      <div class="t-text-xs t-font-medium t-text-[#393939]">
        {{ item.status }}
      </div>
      <div class="t-flex t-items-center t-gap-[9px]">
        <span
          class="t-inline-block t-rounded t-w-4 t-h-4"
          [ngStyle]="{ 'background-color': item.color }"></span
        ><span class="t-text-[#004F11]">{{ item.count }}</span>
      </div>
    </div>
    }
  </div>
</div>
