import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  output,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  TextBoxComponent,
  TextBoxSuffixTemplateDirective,
} from '@progress/kendo-angular-inputs'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { PageArgs, UiPaginationModule } from '@venio/ui/pagination'
import { ProjectFacade } from '@venio/data-access/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { DebounceTimer } from '@venio/util/utilities'
@Component({
  selector: 'venio-launchpad-sub-toolbar',
  standalone: true,
  imports: [
    CommonModule,
    TextBoxComponent,
    TextBoxSuffixTemplateDirective,
    ButtonComponent,
    UiPaginationModule,
  ],
  templateUrl: './launchpad-sub-toolbar.component.html',
  styleUrl: './launchpad-sub-toolbar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LaunchpadSubToolbarComponent {
  private readonly projectFacade = inject(ProjectFacade)

  private readonly currentPagingInfo = toSignal(
    this.projectFacade.selectCaseDetailPagingInfo$
  )

  /**
   * Pass any loading state to the sub-toolbar for performing
   * loading actions if required.
   */
  public readonly isLoading = input<boolean>(false)

  public readonly searchChange = output<string>()

  public readonly pagingChange = output<PageArgs>()

  public readonly totalRecords = computed(
    () => this.currentPagingInfo()?.totalCaseCount || 0
  )

  public readonly pageSize = computed(
    () => this.currentPagingInfo()?.pageSize || 0
  )

  @DebounceTimer(800)
  public searchValueChange(value: string): void {
    this.searchChange.emit(value)
  }

  public pagingControlChange(arg: PageArgs): void {
    this.pagingChange.emit(arg)
  }
}
