import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  OnDestroy,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Subject } from 'rxjs'
import { UserFacade } from '@venio/data-access/common'
import { GridModule } from '@progress/kendo-angular-grid'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  UserSelectionModel,
  UsersListModel,
} from '@venio/shared/models/interfaces'
import { GridTypes } from '@venio/shared/models/constants'
import { InviteUploadLocalState } from '../invite-upload-container/invite-upload-local-state'

@Component({
  selector: 'venio-invite-upload-user-grid',
  standalone: true,
  imports: [CommonModule, GridModule],
  templateUrl: './invite-upload-user-grid.component.html',
  styleUrl: './invite-upload-user-grid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InviteUploadUserGridComponent implements OnD<PERSON>roy {
  public gridType = input.required<GridTypes>()

  private readonly userFacade = inject(UserFacade)

  private readonly inviteUploadLocalState = inject(InviteUploadLocalState)

  // This key used to identify and select users in the grid
  public readonly userSelectionKey = 'email'

  public readonly gridTypeIsInternal = computed(
    () => this.gridType() === GridTypes.INTERNAL
  )

  public readonly columns = computed(() => {
    const columnMap = this.inviteUploadLocalState.gridColumnMap()
    return columnMap.get(this.gridType()) || []
  })

  public readonly selectedUsers = computed(() =>
    this.gridTypeIsInternal()
      ? this.inviteUploadLocalState.selectedInternalUsers()
      : this.inviteUploadLocalState.selectedExternalUsers()
  )

  public readonly users = computed(() =>
    this.gridTypeIsInternal()
      ? this.inviteUploadLocalState.filteredInternalUsers()
      : this.inviteUploadLocalState.filteredExternalUsers()
  )

  public readonly isGridDisabled = computed(() => {
    const isInvitationInProgress = this.invitationInProgress()
    const isShareToExternalUserEnabled =
      this.inviteUploadLocalState.shareToExternalUsers()

    return this.gridTypeIsInternal()
      ? isInvitationInProgress
      : !isShareToExternalUserEnabled || isInvitationInProgress
  })

  public readonly invitationInProgress = toSignal(
    this.userFacade.selectIsInvitationInProgress$,
    {
      initialValue: false,
    }
  )

  private toDestroy$: Subject<void> = new Subject<void>()

  public onSelectedKeysChange(event: string[]): void {
    if (this.gridTypeIsInternal()) {
      this.inviteUploadLocalState.addSelectedInternalUser(event)
    } else {
      this.inviteUploadLocalState.addSelectedExternalUser(event)
    }
  }

  public onSelectionChange(event): void {
    const selectedItems: Array<UsersListModel> = event.selectedRows.map(
      (row) => row.dataItem
    )
    const deselectedItems: Array<UsersListModel> = event.deselectedRows.map(
      (row) => row.dataItem
    )
    const userSelectedItem: UserSelectionModel = {
      selectedItems: selectedItems,
      deselectedItems: deselectedItems,
    }
    if (this.gridTypeIsInternal()) {
      this.inviteUploadLocalState.addInternalUserList(userSelectedItem)
    }
    this.inviteUploadLocalState.updatedUserSelectedStatus()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
