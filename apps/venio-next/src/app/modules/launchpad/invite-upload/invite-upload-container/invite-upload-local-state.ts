import { Injectable, WritableSignal, signal } from '@angular/core'
import {
  UsersListModel,
  InviteUploadUserGridModel,
  UserSelectionModel,
} from '@venio/shared/models/interfaces'
import { InviteUploadWorkerService } from '@venio/util/utilities'

@Injectable()
export class InviteUploadLocalState {
  public readonly shareToExternalUsers: WritableSignal<boolean> = signal(false)

  public readonly selectedExternalUsers: WritableSignal<string[]> = signal([])

  public readonly selectedInternalUsers: WritableSignal<string[]> = signal([])

  public readonly externalUsers: WritableSignal<UsersListModel[]> = signal([])

  public readonly filteredInternalUsers: WritableSignal<UsersListModel[]> =
    signal([])

  public readonly filteredExternalUsers: WritableSignal<UsersListModel[]> =
    signal([])

  public readonly internalUsersList: WritableSignal<Map<number, string>> =
    signal(new Map())

  public readonly userMessage: WritableSignal<string> = signal('')

  public readonly hasNoUsersSelected: WritableSignal<boolean> = signal(false)

  public readonly gridColumnMap: WritableSignal<
    Map<string, InviteUploadUserGridModel[]>
  > = signal(new Map())

  public setUserMessage(message: string): void {
    this.userMessage.set(message)
  }

  public setGridColumns(
    gridColumns: Map<string, InviteUploadUserGridModel[]>
  ): void {
    this.gridColumnMap.set(gridColumns)
  }

  public addSelectedInternalUser(user: string[]): void {
    this.selectedInternalUsers.set(user)
  }

  public addSelectedExternalUser(user: string[]): void {
    this.selectedExternalUsers.set(user)
  }

  public setFilteredInternalUsers(users: UsersListModel[]): void {
    this.filteredInternalUsers.set(users)
  }

  public setFilteredExternalUsers(users: UsersListModel[]): void {
    this.filteredExternalUsers.set(users)
  }

  public updatedUserSelectedStatus(): void {
    const hasNoUsersSelected =
      this.internalUsersList().size === 0 &&
      this.selectedExternalUsers().length === 0

    this.hasNoUsersSelected.set(hasNoUsersSelected)
  }

  public setSharedToExternalUsers(value: boolean): void {
    this.shareToExternalUsers.set(value)
  }

  public addFilteredExternalUser(user: UsersListModel): void {
    this.externalUsers.update((prev) => [...prev, user])
    this.filteredExternalUsers.update((prev) => [...prev, user])
  }

  public addInternalUserList(userSelectedItem: UserSelectionModel): void {
    const { selectedItems, deselectedItems } = userSelectedItem
    const inviteUploadWorkerService = new InviteUploadWorkerService()
    inviteUploadWorkerService
      .addInternalUserData(
        selectedItems,
        deselectedItems,
        this.internalUsersList()
      )
      .then((internalUsersList: Map<number, string>) => {
        inviteUploadWorkerService.terminate()
        this.internalUsersList.set(internalUsersList)
        this.updatedUserSelectedStatus()
      })
  }
}
