import { ChangeDetectionStrategy, Component, input } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import { InviteUploadForm } from '@venio/shared/models/interfaces'
import { EditorModule } from '@progress/kendo-angular-editor'
import { LabelModule } from '@progress/kendo-angular-label'

@Component({
  selector: 'venio-invite-upload-editor',
  standalone: true,
  imports: [CommonModule, EditorModule, LabelModule, ReactiveFormsModule],
  templateUrl: './invite-upload-editor.component.html',
  styleUrl: './invite-upload-editor.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InviteUploadEditorComponent {
  public inviteUploadForm = input.required<FormGroup<InviteUploadForm>>()
}
