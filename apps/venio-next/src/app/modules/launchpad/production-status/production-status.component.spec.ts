import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ProductionStatusComponent } from './production-status.component'
import {
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  PLATFORM_ID,
} from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { EffectsModule } from '@ngrx/effects'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { StoreModule } from '@ngrx/store'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { NotificationService } from '@progress/kendo-angular-notification'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MESSAGE_SERVICE_CONFIG,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import {
  ProductionFacade,
  ProductionShareInvitationService,
} from '@venio/data-access/common'
import { environment } from '@venio/shared/environments'
import { of } from 'rxjs'

describe('ProductionStatusComponent', () => {
  let component: ProductionStatusComponent
  let fixture: ComponentFixture<ProductionStatusComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ProductionStatusComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({
          initialState: {
            productionState: {
              isProductionStatusLoading: false,
              productionDetails: [],
            },
          },
        }),
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        {
          provide: MESSAGE_SERVICE_CONFIG,
          useValue: {
            origin: environment.allowedOrigin,
            iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          },
        },
        {
          provide: DialogRef,
          useValue: { close: jest.fn() },
        },
        {
          provide: NotificationService,
          useValue: {
            show: jest.fn().mockReturnValue({
              notification: {
                location: { nativeElement: { onclick: jest.fn() } },
              },
              hide: jest.fn(),
            }),
          },
        },
        {
          provide: ProductionFacade,
          useValue: {
            selectProductionStatusLoading$: of(false),
            selectProductionStatusSuccessResponse$: of([]),
            fetchProductionStatus: jest.fn(),
            downloadRelativityErrorLog: jest.fn(),
            selectRelativirtErrorLogSuccess$: of(null),
            selectRelativirtErrorLogFailure$: of(null),
          },
        },
        {
          provide: ProductionShareInvitationService,
          useValue: {
            productionShareForm: { value: {} },
          },
        },
        {
          provide: IframeMessengerService,
          useValue: {
            sendMessage: jest.fn(),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ProductionStatusComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should call fetchProductionStatus on filter change', () => {
    jest.spyOn(component, 'fetchProductionStatus')
    component.onProductionStatusFilterChange(1)
    expect(component.fetchProductionStatus).toHaveBeenCalled()
  })

  it('should notify parent app on browseActionClicked', () => {
    const mockData = {
      projectId: 1,
      exportId: 1,
      isRelativityImportEnabled: true,
    } as any
    component.browseActionClicked('Share', mockData)
    expect(component.ifProductionShare).toBe(true)
  })
})
