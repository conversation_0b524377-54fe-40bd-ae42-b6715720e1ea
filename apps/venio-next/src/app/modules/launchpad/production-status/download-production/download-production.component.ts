import { xIcon } from '@progress/kendo-svg-icons'
import { CommonModule } from '@angular/common'
import {
  HttpClient,
  HttpEventType,
  HttpHeaders,
  HttpEvent,
} from '@angular/common/http'
import {
  ChangeDetectorRef,
  Component,
  computed,
  inject,
  input,
  OnDestroy,
  OnInit,
  output,
  signal,
} from '@angular/core'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import {
  LabelSettings,
  ProgressBarModule,
} from '@progress/kendo-angular-progressbar'
import { saveAs } from '@progress/kendo-file-saver'
import { DownloadProgressInfoModel } from '@venio/data-access/review'
import { environment } from '@venio/shared/environments'
import { UuidGenerator } from '@venio/util/uuid'
import {
  tap,
  filter,
  throttleTime,
  asyncScheduler,
  Subject,
  takeUntil,
} from 'rxjs'
import { IconsModule } from '@progress/kendo-angular-icons'
import { NotificationService, Type } from '@progress/kendo-angular-notification'

@Component({
  selector: 'venio-download-production',
  standalone: true,
  imports: [ProgressBarModule, ButtonModule, CommonModule, IconsModule],
  templateUrl: './download-production.component.html',
  styleUrl: './download-production.component.scss',
})
export class DownloadProductionComponent implements OnInit, OnDestroy {
  public closeIcon = xIcon

  private destroy$ = new Subject<void>()

  private customFilename!: string

  private readonly httpClient = inject(HttpClient)

  private readonly cdr = inject(ChangeDetectorRef)

  private get _apiUrl(): string {
    return environment.apiUrl.replace(/\/+$/, '')
  }

  public progressStyles: { [key: string]: string } = {
    background: '#FFBB12',
  }

  public label: LabelSettings = {
    visible: false,
  }

  public readonly downloadProgressInfo = signal<DownloadProgressInfoModel>({
    progress: 0,
    downloadSpeed: '0',
    remainingTime: '0',
    isDownloadCompleted: false,
    totalFileSize: '0',
  })

  public readonly isDownloadStarted = signal(false)

  public sourceUrl = input<string, string>('', {
    transform: (url: string | null) => {
      const apiBaseUrl = this._apiUrl
      return url ? `${apiBaseUrl}/${url}` : apiBaseUrl
    },
  })

  public customFileName = input<string | undefined>()

  private readonly completeSourceUrl = computed(() => this.sourceUrl())

  public readonly cancelClick = output<boolean>()

  public readonly downloadProgressChange = output<DownloadProgressInfoModel>()

  private notificationService = inject(NotificationService)

  private count = 0

  public ngOnInit(): void {
    this.initDownloadFile()
  }

  public ngOnDestroy(): void {
    this.destroy$.next()
    this.destroy$.complete()
    this.isDownloadStarted.set(false)
    this.resetInitialInfo()
  }

  public cancelDownload(): void {
    this.cancelClick.emit(true)
    this.resetInitialInfo()
  }

  private resetInitialInfo(): void {
    this.downloadProgressInfo.set({
      progress: 0,
      downloadSpeed: '0',
      remainingTime: '0',
      isDownloadCompleted: false,
      totalFileSize: '0',
    })
  }

  private initDownloadFile(): void {
    if (this.isDownloadStarted()) {
      return
    }
    this.httpClient
      .get(this.completeSourceUrl(), {
        responseType: 'blob',
        observe: 'events',
        reportProgress: true,
      })
      .pipe(
        tap((event) => {
          if (event.type === HttpEventType.ResponseHeader) {
            const httpHeaderEvent = event
            if (httpHeaderEvent.status !== 200) {
              this.#showMessage('Error while downloading the file', {
                style: 'error',
              })
              this.updateProgressStatus(event)
            }
          }
        }),
        tap((e) => this.saveDownloadedFile(e)),
        filter(
          (e: HttpEvent<Blob>) => e.type === HttpEventType.DownloadProgress
        ),
        throttleTime(600, asyncScheduler, { leading: true, trailing: true }),
        takeUntil(this.destroy$)
      )
      .subscribe({
        next: (event) => {
          this.updateProgressStatus(event)
        },
      })
  }

  private getFileNameFromHeaders(h: HttpHeaders): string {
    if (this.customFilename) return this.customFilename
    const disposition = h.get('Content-Disposition')
    const filename = disposition?.split(';').find((s) => s.match(/file/gi))
    return filename
      ? filename.split('=')[1]?.trim().replace(/["']/g, '')
      : UuidGenerator.uuid
  }

  private saveDownloadedFile(e: HttpEvent<Blob>): void {
    if (e.type !== HttpEventType.Response) return

    this.updateProgressInfo({
      progress: 100,
      isDownloadCompleted: true,
      remainingTime: '0',
    })

    const downloadFileName = this.getFileNameFromHeaders(e.headers)
    saveAs(e.body, downloadFileName)
  }

  private notifyDownloadProgressChange(): void {
    this.downloadProgressChange.emit(this.downloadProgressInfo())
  }

  private updateProgressInfo(info: Partial<DownloadProgressInfoModel>): void {
    this.downloadProgressInfo.update((current) => ({
      ...current,
      ...info,
      remainingTime: info.remainingTime?.trim() || '0',
    }))
    this.notifyDownloadProgressChange()
  }

  private updateProgressStatus(event: HttpEvent<Blob>): void {
    if (event.type !== HttpEventType.DownloadProgress) return

    this.isDownloadStarted.set(event.total > 0)

    const progress = Math.round((100 * event.loaded) / event.total)

    const prop: Partial<DownloadProgressInfoModel> = {
      progress: progress,
      isDownloadCompleted: progress === 100,
    }

    this.updateProgressInfo(prop)
    this.cdr.markForCheck()
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }
}
