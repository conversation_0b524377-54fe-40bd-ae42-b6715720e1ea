import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ProductionShareSkeletonComponent } from './production-share-skeleton.component'
import { CommonModule } from '@angular/common'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { LayoutModule } from '@progress/kendo-angular-layout'

describe('ProductionShareSkeletonComponent', () => {
  let component: ProductionShareSkeletonComponent
  let fixture: ComponentFixture<ProductionShareSkeletonComponent>

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        ProductionShareSkeletonComponent,
        CommonModule,
        IndicatorsModule,
        LayoutModule,
      ],
    }).compileComponents()
  })

  beforeEach(() => {
    fixture = TestBed.createComponent(ProductionShareSkeletonComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create the component', () => {
    expect(component).toBeTruthy()
  })
})
