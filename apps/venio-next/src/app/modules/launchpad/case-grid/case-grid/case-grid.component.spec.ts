import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CaseGridComponent } from './case-grid.component'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { ProjectFacade } from '@venio/data-access/common'
import { CaseDetailModel } from '@venio/shared/models/interfaces'
import { of } from 'rxjs'

describe('CaseLaunchpadSubGridComponent', () => {
  let component: CaseGridComponent
  let fixture: ComponentFixture<CaseGridComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CaseGridComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ProjectFacade,
          useValue: {
            selectSelectedCaseDetail$: of([]),
            selectIsCaseDetailLoading$: of(false),
            selectCaseDetail$: of({} as CaseDetailModel),
            resetProjectState: jest.fn(),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(CaseGridComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
