import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LaunchpadContainerComponent } from './launchpad-container.component'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { ProjectFacade, UserFacade } from '@venio/data-access/common'
import { CaseDetailModel } from '@venio/shared/models/interfaces'
import { of } from 'rxjs'
import { DocumentShareFacade } from '@venio/data-access/review'

describe('LaunchpadContainerComponent', () => {
  let component: LaunchpadContainerComponent
  let fixture: ComponentFixture<LaunchpadContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        LaunchpadContainerComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ProjectFacade,
          useValue: {
            selectSelectedCaseDetail$: of({} as CaseDetailModel),
            selectIsCaseDetailLoading$: of(false),
            selectCaseDetail$: of({} as CaseDetailModel),
            selectSelectedReviewSetSummaryDetail$: of({}),
            selectReviewSetSummaryDetail$: of({}),
          },
        },
        {
          provide: DocumentShareFacade,
          useValue: {
            selectSharedDocumentList$: of({}),
            selectSharedDocumentCountDetail$: of({}),
          },
        },
        {
          provide: UserFacade,
          useValue: {
            selectIsCurrentUserLoading$: of({}),
            selectCurrentUserDetails$: of({}),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(LaunchpadContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
