import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  OnDestroy,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DrawerItem,
  DrawerModule,
  DrawerSelectEvent,
} from '@progress/kendo-angular-layout'
import { menuIcon, SVGIcon } from '@progress/kendo-svg-icons'
import { DropDownButtonModule } from '@progress/kendo-angular-buttons'
import { RouterOutlet } from '@angular/router'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import {
  IframeMessengerFacade,
  MessageContent,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { Subject, map, filter } from 'rxjs'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { toSignal } from '@angular/core/rxjs-interop'

@Component({
  selector: 'venio-layout-drawer',
  standalone: true,
  imports: [
    CommonModule,
    DrawerModule,
    DropDownButtonModule,
    RouterOutlet,
    SvgLoaderDirective,
    LoaderModule,
  ],
  templateUrl: './layout-drawer.component.html',
  styleUrls: ['./layout-drawer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LayoutDrawerComponent implements OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  public selected = 'Documents'

  public menuSvg: SVGIcon = menuIcon

  public items: Array<DrawerItem & any> = [
    {
      text: 'Documents',
      selected: true,
      icon: 'icon-document.svg',
      x: '1.875rem',
      y: '1.875rem',
    },
    {
      text: 'Review Batches',
      icon: 'icon-review-batches.svg',
      x: '1.875rem',
      y: '1.875rem',
    },
    { text: 'Reports', icon: 'icon-reports.svg', x: '1.875rem', y: '1.875rem' },
    {
      text: 'FCLV',
      icon: 'icon-folder-fclv.svg',
      x: '1.875rem',
      y: '1.875rem',
    },
    // { text: '', svgIcon: starOutlineIcon },
  ]

  /**
   * Listens for layout change messages from the iframe messenger.
   * Upon receiving a message,
   * it triggers a change detection cycle and sets the toolbar visibility to false.
   *
   * This method is used to adapt the UI in response to layout changes communicated from the
   * parent window, specifically to hide the toolbar when such a message is received.
   */
  public readonly hideLeftBar = toSignal(
    this.iframeMessengerFacade
      .selectIframeMessengerContent$(MessageType.UI_STATE_CHANGE)
      .pipe(
        filter(
          (mc) =>
            (Array.isArray(mc) && mc?.[0]?.content['leftBar']) ||
            (mc as MessageContent)?.content['leftBar']
        ),
        map((mc) =>
          Array.isArray(mc)
            ? mc?.[0]?.content['leftBar'] === 'hide'
            : mc?.content['leftBar'] === 'hide'
        )
      ),
    {
      initialValue: false,
    }
  )

  /**
   * Only necessary until the legacy app depends on the micro app.
   * Once everything is migrated to the micro app, this and `hideToolbar` can be removed.
   */
  public readonly isLeftAsideVisible = computed(() => this.hideLeftBar())

  constructor(
    private changeDetectorRef: ChangeDetectorRef,
    private iframeMessengerFacade: IframeMessengerFacade
  ) {}

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public onSelect(ev: DrawerSelectEvent): void {
    this.changeDetectorRef.markForCheck()
    this.items.forEach((item) => {
      item['selected'] = ev.item.text === item.text
    })
  }
}
