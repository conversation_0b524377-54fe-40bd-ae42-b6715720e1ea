import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LayoutDrawerComponent } from './layout-drawer.component'
import { CUSTOM_ELEMENTS_SCHEMA, PLATFORM_ID } from '@angular/core'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import {
  AppIdentitiesTypes,
  IframeMessengerFacade,
  MESSAGE_SERVICE_CONFIG,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { environment } from '@venio/shared/environments'
import { of } from 'rxjs'

describe('LayoutDrawerComponent', () => {
  let component: LayoutDrawerComponent
  let fixture: ComponentFixture<LayoutDrawerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LayoutDrawerComponent, BrowserAnimationsModule],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        {
          provide: MESSAGE_SERVICE_CONFIG,
          useValue: {
            origin: environment.allowedOrigin, // Adjust the origin accordingly
            iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          },
        },
        {
          provide: IframeMessengerFacade,
          useValue: {
            selectIframeMessengerContent$: jest
              .fn()
              .mockReturnValue(of(undefined)),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(LayoutDrawerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
