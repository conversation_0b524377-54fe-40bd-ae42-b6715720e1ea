import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule, NgOptimizedImage } from '@angular/common'
import { ToolBarModule } from '@progress/kendo-angular-toolbar'
import { StackLayoutModule } from '@progress/kendo-angular-layout'
import {
  DropDownButtonModule,
  SplitButtonModule,
} from '@progress/kendo-angular-buttons'
import { BadgeModule } from '@progress/kendo-angular-indicators'
import { ToolbarLogoComponent } from './toolbar-logo/toolbar-logo.component'
import { ToolbarHistoryLogComponent } from './toolbar-history-log/toolbar-history-log.component'
import { ToolbarNotificationComponent } from './toolbar-notification/toolbar-notification.component'
import { ToolbarUserComponent } from './toolbar-user/toolbar-user.component'

@Component({
  selector: 'venio-layout-toolbar',
  standalone: true,
  imports: [
    CommonModule,
    ToolBarModule,
    StackLayoutModule,
    NgOptimizedImage,
    SplitButtonModule,
    DropDownButtonModule,
    BadgeModule,
    ToolbarLogoComponent,
    ToolbarHistoryLogComponent,
    ToolbarNotificationComponent,
    ToolbarUserComponent,
  ],
  templateUrl: './layout-toolbar.component.html',
  styleUrls: ['./layout-toolbar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LayoutToolbarComponent {
  public navigationProgressBarComp = import(
    '@venio/feature/navigation-progress-bar'
  ).then(({ NavigationProgressBarComponent }) => NavigationProgressBarComponent)
}
