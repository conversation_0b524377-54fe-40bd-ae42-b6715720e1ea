import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ToolbarHistoryLogComponent } from './toolbar-history-log.component'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('ToolbarHistoryLogComponent', () => {
  let component: ToolbarHistoryLogComponent
  let fixture: ComponentFixture<ToolbarHistoryLogComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ToolbarHistoryLogComponent],
      providers: [provideHttpClient(), provideHttpClientTesting()],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents()

    fixture = TestBed.createComponent(ToolbarHistoryLogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
