import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ToolbarLogoComponent } from './toolbar-logo.component'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('ToolbarLogoComponent', () => {
  let component: ToolbarLogoComponent
  let fixture: ComponentFixture<ToolbarLogoComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ToolbarLogoComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(ToolbarLogoComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
