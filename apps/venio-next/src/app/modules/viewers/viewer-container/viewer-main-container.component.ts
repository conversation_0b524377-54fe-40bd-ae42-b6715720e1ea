import {
  ChangeDetectionStrategy,
  Component,
  HostListener,
  inject,
  NgModule,
} from '@angular/core'
import { CommonModule } from '@angular/common'

import { ViewerContainerRoutingModule } from './viewer-container-routing.module'
import { environment } from '@venio/shared/environments'
import { WINDOW } from '@venio/data-access/iframe-messenger'

@Component({
  selector: 'venio-viewer-main-container',
  standalone: false,
  templateUrl: './viewer-main-container.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ViewerMainContainerComponent {
  private windowRef = inject(WINDOW)

  @HostListener('window:beforeunload')
  public readonly beforeUnload = (): boolean => {
    this.windowRef.postMessage(
      { type: 'VIEWER_CLOSED', fileId: null },
      environment.allowedOrigin
    )
    return confirm('Are you sure you want to leave?')
  }
}

@NgModule({
  declarations: [ViewerMainContainerComponent],
  imports: [CommonModule, ViewerContainerRoutingModule],
})
export class ViewerContainerModule {}
