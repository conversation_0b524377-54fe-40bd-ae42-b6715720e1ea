import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  Inject,
  input,
  Input,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core'
import { Shortcuts } from '../model/shortcut-key-dictionary.model'
import { CommonModule } from '@angular/common'

import {
  PopoverContainerDirective,
  PopoverModule,
  TooltipsModule,
} from '@progress/kendo-angular-tooltip'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { WINDOW } from '@venio/data-access/iframe-messenger'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { Callback, ShortcutManager } from '@venio/util/utilities'
import { ShortcutKeyBindings } from '@venio/shared/models/constants'

@Component({
  selector: 'venio-shortcut-key-dictionary',
  templateUrl: './shortcut-key-dictionary.component.html',
  styleUrl: './shortcut-key-dictionary.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    ButtonsModule,
    TooltipsModule,
    SvgLoaderDirective,
    PopoverModule,
  ],
})
export class ShortcutKeyDictionaryComponent implements OnInit, AfterViewInit {
  // Accepts the type of shortcuts to display depending on the page
  private _type = signal<string>('home') // default value

  public readonly showKeyboardShortcuts = input<boolean>(false)

  @Input()
  public set type(value: string) {
    this._type.set(value)
  }

  public get type(): string {
    return this._type()
  }

  @ViewChild('container', { static: false })
  private container?: PopoverContainerDirective

  @ViewChild('popoverAnchor')
  private popoverAnchor?: ElementRef<HTMLElement>

  private shortcutManager: ShortcutManager

  public readonly showShortcutPopover = signal(false)

  public shortcuts = signal<Shortcuts[]>([
    {
      action: 'Undo changes for bulk tag & coding',
      windows: 'Ctrl + Z',
      mac: '⌘ + Z',
      type: 'home',
    },
    {
      action: 'Save & Next Document',
      windows: 'Alt + S',
      mac: 'Option + S',
      type: 'review',
    },
    {
      action: 'Undo Changes For Tag & Coding',
      windows: 'Ctrl + Z',
      mac: '⌘ + Z',
      type: 'review',
    },
    {
      action: 'Navigate To Previous Page',
      windows: 'PgUp',
      mac: 'Fn + ↑',
      type: 'review',
    },
    {
      action: 'Navigate To Next Page',
      windows: 'PgDn',
      mac: 'Fn + ↓',
      type: 'review',
    },
    {
      action: 'Navigate To First Page',
      windows: 'Home',
      mac: 'Fn + Left Arrow',
      type: 'review',
    },
    {
      action: 'Navigate To Last Page',
      windows: 'End',
      mac: 'Fn + Right Arrow',
      type: 'review',
    },
    {
      action: 'Opens Short code info window',
      windows: 'Ctrl + /',
      mac: 'Fn + /',
      type: 'review',
    },
  ])

  public selectedShortcuts = signal<Shortcuts[]>([])

  public userAgent = ''

  constructor(@Inject(WINDOW) private windowReference: Window) {}

  public ngOnInit(): void {
    this.userAgent = this.getOS()
    this.updateShortcuts()
  }

  public ngAfterViewInit(): void {
    this.#initTagCodingShortcutKeys()
  }

  public getOS(): string {
    const userAgent = this.windowReference.navigator.userAgent
    if (/Macintosh|MacIntel|MacPPC|Mac68K/.test(userAgent)) {
      return 'macOS'
    } else if (/Windows/.test(userAgent)) {
      return 'Windows'
    } else if (/Linux/.test(userAgent)) {
      return 'Linux'
    } else if (/Android/.test(userAgent)) {
      return 'Android'
    } else if (/iPhone|iPad|iPod/.test(userAgent)) {
      return 'iOS'
    }
    return 'unknown'
  }

  // Updates the Selected shortcuts based on the type of input
  public updateShortcuts(): void {
    const currentType = this._type()
    this.selectedShortcuts.set(
      this.shortcuts().filter((shortcut) => shortcut.type === currentType)
    )
  }

  private togglePopover(): void {
    const anchor = this.popoverAnchor.nativeElement
    this.container.show(anchor)
  }

  private showShortcutHandler: Callback = (event, combo): void => {
    const actions: { [key: string]: () => void } = {
      [ShortcutKeyBindings.SHOW_KEYBOARD_SHORTCUTS]: () => {
        if (this.showKeyboardShortcuts()) {
          this.togglePopover()
        }
      },
    }

    if (actions[combo]) {
      event.preventDefault()
      event.stopPropagation()
      actions[combo]()
    }
  }

  #initTagCodingShortcutKeys(): void {
    this.shortcutManager = new ShortcutManager()
    this.shortcutManager.bind(
      [ShortcutKeyBindings.SHOW_KEYBOARD_SHORTCUTS],
      this.showShortcutHandler
    )
  }
}
