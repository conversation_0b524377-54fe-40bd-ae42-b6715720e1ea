import {
  OnInit,
  Component,
  input,
  output,
  ChangeDetectionStrategy,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  FilterDescriptor,
  CompositeFilterDescriptor,
} from '@progress/kendo-data-query'
import { FilterService } from '@progress/kendo-angular-grid'
import { FormsModule } from '@angular/forms'
import { InputsModule } from '@progress/kendo-angular-inputs'

/**
 * A reusable multi-check filter component designed for integration with Kendo Grid.
 * Provides advanced filtering functionality, including search, selection, and styling options.
 */

/** Example Usage:
 * 
 <venio-casemulticheck-filter
  [field]="'status'"
  [filterChange]="filterChange"
  [currentFilter]="filter"
  [data]="statusOptions"
  [customClass]="'custom-filter'"
  [containerStyle]="{ 'background-color': '#f9f9f9', 'padding': '10px' }"
  [operator]="'eq'"
  [searchPlaceholder]="'Filter by status...'"
  [labels]="{ noResults: 'No matching statuses found.' }"
  (itemSelected)="onItemSelected($event)"
  (itemDeselected)="onItemDeselected($event)"
  (valueChange)="onValueChange($event)">
</venio-casemulticheck-filter>

 */

@Component({
  selector: 'venio-grid-custom-filter-checklist',
  standalone: true,
  imports: [CommonModule, InputsModule, FormsModule],
  templateUrl: './grid-custom-filter-checklist.component.html',
  styleUrl: './grid-custom-filter-checklist.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GridCustomFilterChecklistComponent implements OnInit {
  /**
   * The current filter descriptor, typically passed from the parent Kendo Grid.
   */
  public readonly currentFilter = input<CompositeFilterDescriptor | undefined>(
    undefined
  )

  /**
   * The dataset to display in the checklist.
   */
  public readonly data = input<string[]>([])

  /**
   * The Kendo Grid filter service for managing filter logic.
   */
  public readonly filterChange = input<FilterService | null>(null)

  /**
   * The field name to which the filter is applied.
   */
  public readonly field = input<string>('')

  /**
   * Additional CSS classes to apply to the root container for customization.
   */
  public readonly customClass = input<string>('')

  /**
   * Inline styles to apply to the root container for further customization.
   */
  public readonly containerStyle = input<Record<string, string>>({})

  /**
   * The operator to use for filtering (e.g., 'eq', 'startswith').
   * Defaults to 'eq' (equals).
   * If it's required, you can use `input.required<string>()`.
   */
  public readonly operator = input<string>('eq')

  /**
   * The placeholder text for the search box.
   * Defaults to 'Search...'.
   */
  public readonly searchPlaceholder = input<string>('Search...')

  /**
   * Localized labels for messages, such as "No results found."
   */
  public readonly labels = input<{ noResults: string }>({
    noResults: 'No items found.',
  })

  /**
   * Emits the list of currently selected values whenever it changes.
   */
  public readonly valueChange = output<string[]>()

  /**
   * Emits the selected item when an item is selected.
   */
  public readonly itemSelected = output<string>()

  /**
   * Emits the deselected item when an item is deselected.
   */
  public readonly itemDeselected = output<string>()

  /**
   * The filtered data displayed in the checklist.
   */
  public currentData: string[] = []

  /**
   * The current list of selected values.
   */
  private value: string[] = []

  /**
   * Initializes the filtered data and selected values after the view is initialized.
   */
  public ngOnInit(): void {
    // Because `this.data()` is a signal, you can directly invoke it to get the value.
    this.currentData = this.data()

    const cf = this.currentFilter() // Or handle it defensively if needed
    if (cf?.filters && Array.isArray(cf.filters)) {
      this.value = cf.filters
        .filter((f: FilterDescriptor) => f.value !== undefined)
        .map((f: FilterDescriptor) => f.value as string)
    } else {
      this.value = []
    }
  }

  /**
   * Checks if an item is currently selected.
   * @param item The item to check.
   * @returns True if the item is selected; false otherwise.
   */
  public isItemSelected(item: string): boolean {
    return this.value.includes(item)
  }

  /**
   * Toggles the selection of an item and updates the filter.
   * Emits `itemSelected` or `itemDeselected` as appropriate.
   * @param item The item to toggle.
   */
  public onSelectionChange(item: string): void {
    if (this.isItemSelected(item)) {
      this.value = this.value.filter((x) => x !== item)
      this.itemDeselected.emit(item)
    } else {
      this.value.push(item)
      this.itemSelected.emit(item)
    }

    // Emit the updated value list via the signal-based output
    this.valueChange.emit(this.value)

    // Check for null because filterChange is input<FilterService | null>
    if (this.filterChange()) {
      this.filterChange().filter({
        filters: this.value.map((value) => ({
          field: this.field(),
          operator: this.operator(),
          value,
        })),
        logic: 'or',
      })
    }
  }

  /**
   * Filters the data based on the input in the search box.
   * @param event The input event from the search box.
   */
  public onInput(event: Event): void {
    const searchText = (event.target as HTMLInputElement).value.toLowerCase()
    this.currentData = this.data().filter((item) =>
      item.toLowerCase().includes(searchText)
    )
  }
}
