{"name": "feature-treelist", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/treelist/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/treelist/ng-package.json", "tailwindConfig": "libs/feature/treelist/tailwind.config.js"}, "configurations": {"production": {"tsConfig": "libs/feature/treelist/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/treelist/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/treelist/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}