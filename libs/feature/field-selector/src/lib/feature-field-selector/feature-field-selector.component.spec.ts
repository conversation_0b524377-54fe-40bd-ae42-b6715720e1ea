import { ComponentFixture, TestBed } from '@angular/core/testing'
import { FeatureFieldSelectorComponent } from './feature-field-selector.component'
import { provideMockStore } from '@ngrx/store/testing'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('FeatureFieldSelectorComponent', () => {
  let component: FeatureFieldSelectorComponent
  let fixture: ComponentFixture<FeatureFieldSelectorComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FeatureFieldSelectorComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        DialogRef,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(FeatureFieldSelectorComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
