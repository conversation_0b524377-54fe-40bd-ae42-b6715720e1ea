{"name": "feature-field-selector", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/field-selector/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/field-selector/ng-package.json", "tailwindConfig": "libs/feature/field-selector/tailwind.config.js"}, "configurations": {"production": {"tsConfig": "libs/feature/field-selector/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/field-selector/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/field-selector/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}