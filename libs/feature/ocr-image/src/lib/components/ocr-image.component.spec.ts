import { ComponentFixture, TestBed } from '@angular/core/testing'
import { OcrImageComponent } from './ocr-image.component'
import { provideMockStore } from '@ngrx/store/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import {
  ConvertDocumentFacade,
  OcrImageFacade,
} from '@venio/data-access/review'
import { CommonModule } from '@angular/common'
import { VenioTreelistModule } from '@venio/shared/treelist'
import { GridModule } from '@progress/kendo-angular-grid'
import { LoaderModule } from '@progress/kendo-angular-indicators'

describe('OcrImageComponent', () => {
  let component: OcrImageComponent
  let fixture: ComponentFixture<OcrImageComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [OcrImageComponent],
      imports: [
        NoopAnimationsModule,
        CommonModule,
        VenioTreelistModule,
        GridModule,
        LoaderModule,
      ],
      providers: [ConvertDocumentFacade, OcrImageFacade, provideMockStore({})],
    }).compileComponents()

    fixture = TestBed.createComponent(OcrImageComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
