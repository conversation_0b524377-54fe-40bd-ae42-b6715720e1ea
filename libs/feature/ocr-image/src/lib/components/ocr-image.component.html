<div class="t-h-full t-flex t-flex-col">
  <div class="t-font-medium t-text-[#263238] t-text-[16px] t-mt-2 t-mb-1">
    Summary
  </div>
  <div class="t-mb-2">
    <kendo-grid
      [kendoGridBinding]="gridData"
      filterable="menu"
      [hideHeader]="true"
      class="t-h-50 t-overflow-y-auto"
      data-qa="delete-document-summary-grid">
      <kendo-grid-column
        field="description"
        [width]="400"
        data-qa="create-slipsheet-summary-description">
      </kendo-grid-column>
      <kendo-grid-column
        field="count"
        class="!k-pr-8"
        data-qa="create-slipsheet-summary-count">
      </kendo-grid-column>
    </kendo-grid>
  </div>
  <div (resize)="onContainerResize($event)" class="t-h-full" #treelistContainer>
    <div class="t-flex t-justify-center">
      <kendo-loader
        *ngIf="showSpinner"
        size="medium"
        type="pulsing"></kendo-loader>
    </div>
    <venio-treelist
      [data]="fileTypeData"
      parentIdField="fileTypeGroupID"
      idField="fileTypeID"
      selectedField="selected"
      [sortable]="true"
      *ngIf="!showSpinner && fileTypeData?.length > 0"
      [height]="fileTypeGridHeight"
      [resizable]="true"
      (venioTreeViewSelectionChange)="selectionChange($event)">
      <venio-treelist-column
        field="fileType"
        title="File Type"
        [sortable]="true"
        [width]="100"
        [resizable]="true"></venio-treelist-column>

      <venio-treelist-column
        field="fileTypeDescription"
        [sortable]="true"
        [width]="100"
        title="Description"
        [resizable]="true"></venio-treelist-column>

      <venio-treelist-column
        field="total"
        title="Count"
        [width]="200"
        [sortable]="true"
        class="!k-text-right !k-pr-8"
        [resizable]="true"></venio-treelist-column>
    </venio-treelist>
  </div>
</div>
