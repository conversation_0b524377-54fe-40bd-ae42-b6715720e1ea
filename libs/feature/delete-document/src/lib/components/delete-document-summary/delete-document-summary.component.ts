import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core'
import { Subject, take, takeUntil } from 'rxjs'
import {
  ReviewParamService,
  DeleteDocumentOptions,
  DeleteDocumentSummary,
  DeleteDocumentFacade,
} from '@venio/data-access/review'
import { DeleteDocumentGridModel } from '../../models/delete-document-grid.model'

@Component({
  selector: 'venio-delete-document-summary',
  templateUrl: './delete-document-summary.component.html',
  styleUrls: ['./delete-document-summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeleteDocumentSummaryComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private deleteSummary: DeleteDocumentSummary[] = null

  //currently selected delete options in the delete option component
  private currentDeleteOption: DeleteDocumentOptions = null

  public selectedDocumentsCount = 0

  public childFileToBeDeleted = 0

  public filesToBeDeleted = 0

  public gridData: DeleteDocumentGridModel[] = []

  constructor(
    private cdr: ChangeDetectorRef,
    private deleteDocumentFacade: DeleteDocumentFacade,
    private reviewParamService: ReviewParamService
  ) {}

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngOnInit(): void {
    this.filesToBeDeleted = 0
    this.childFileToBeDeleted = 0
    this.selectedDocumentsCount = 0
    this.gridData = this.getGridData()

    //get currently selected options
    this.deleteDocumentFacade.getCurrentDeleteOption$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((delOption) => {
        this.currentDeleteOption = delOption
        this.updateDeleteSummary()
        this.cdr.detectChanges()
      })

    //update summary table when summary is loaded
    this.deleteDocumentFacade.getDeleteDocumentSummary$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((summary) => {
        this.deleteSummary = summary
        this.updateDeleteSummary()
        this.cdr.detectChanges()
      })

    //fetch document summary from api
    this.reviewParamService.projectId.pipe(take(1)).subscribe((projectId) => {
      this.deleteDocumentFacade.fetchDeleteDocumentSummary(projectId)
    })
  }

  private getGridData(): DeleteDocumentGridModel[] {
    return [
      {
        description: 'Files selected to be deleted from search hits',
        count: this.selectedDocumentsCount,
      },
      {
        description: 'Child files to be deleted',
        count: this.childFileToBeDeleted,
      },
      {
        description: 'Total files to be deleted',
        count: this.filesToBeDeleted,
      },
    ]
  }

  /*
   * API returns count for all possible delete modes. This method calculates the count for currently selected delete modes.
   */
  private updateDeleteSummary(): void {
    if (this.deleteSummary !== null) {
      this.selectedDocumentsCount = 0
      this.childFileToBeDeleted = 0
      this.filesToBeDeleted = 0

      //count of all selected documents
      this.selectedDocumentsCount = this.deleteSummary.filter(
        (x) => x.deleteMode === 'DELETE_ALL' && x.summaryTitle === 'DELETE_ALL'
      )[0].count

      if (
        this.currentDeleteOption.SelectedDeleteModes?.length === 1 &&
        this.currentDeleteOption.SelectedDeleteModes[0] === 'DELETE_ALL'
      ) {
        this.childFileToBeDeleted = this.deleteSummary.filter(
          (x) => x.summaryTitle === 'ChildCount'
        )[0].count

        if (this.currentDeleteOption.DeleteChildRecords) {
          const childCountWithOption = this.deleteSummary.filter(
            (x) => x.summaryTitle === 'ChildCountWithOption'
          )[0].count

          this.childFileToBeDeleted += childCountWithOption
        }

        this.filesToBeDeleted = this.deleteSummary.filter(
          (summary) => summary.deleteMode === 'DELETE_ALL'
        )[0].count

        // add child files to be deleted to total files to be deleted
        this.filesToBeDeleted += this.childFileToBeDeleted
      } else {
        let deleteCount = 0
        this.currentDeleteOption.SelectedDeleteModes.forEach((mode) => {
          const currentCount = this.deleteSummary.filter(
            (summary) => summary.deleteMode === mode
          )[0].count
          // get the maximum count from all selected delete modes
          deleteCount = currentCount > deleteCount ? currentCount : deleteCount
        })
        this.filesToBeDeleted = deleteCount
      }
      this.deleteDocumentFacade.setDeleteFileCount(this.filesToBeDeleted)
      this.gridData = this.getGridData()
    }
  }
}
