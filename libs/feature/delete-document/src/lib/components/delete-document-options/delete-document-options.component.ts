import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
} from '@angular/core'
import { DeleteDocumentFacade, DeleteMode } from '@venio/data-access/review'
import { Subject } from 'rxjs'

@Component({
  selector: 'venio-delete-document-options',
  templateUrl: './delete-document-options.component.html',
  styleUrls: ['./delete-document-options.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeleteDocumentOptionsComponent
  implements AfterViewInit, OnDestroy
{
  private readonly toDestroy$ = new Subject<void>()

  //flag for disabling checkbox in one group when other radio button is selected
  public deleteAllSelected = true

  //checkbox value for 'Delete child record of all the selected parent documents'
  public deleteChildRecords = false

  //checkbox value for 'Delete media if all files are deleted'
  public deleteMediaIfAllFilesAreDeleted = false

  //checkbox values for 'Only images'
  public deleteImages = false

  //checkbox values for 'Only native'
  public deleteNative = false

  //checkbox values for 'Only html/rtf'
  public deleteHtmlRtf = false

  //checkbox values for image redaction
  public deleteRedaction = false

  constructor(
    private cdr: ChangeDetectorRef,
    private deleteDocumentFacade: DeleteDocumentFacade
  ) {}

  public ngAfterViewInit(): void {
    this.updateCurrentDeleteOption([DeleteMode.DELETE_ALL])
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public onDeleteOptionChange(option: string): void {
    let selectedDeleteOptions: DeleteMode[] = []
    switch (option) {
      case 'DELETE_ALL':
        this.deleteAllSelected = true
        selectedDeleteOptions = [DeleteMode.DELETE_ALL]
        // deselect all other checkboxes under 'Document Type' when 'delete all' is selected
        this.deleteRedaction =
          this.deleteHtmlRtf =
          this.deleteNative =
          this.deleteImages =
            false
        break
      case 'DELETE_DOCUMENT_TYPE':
        this.deleteAllSelected = false
        if (this.deleteImages) {
          selectedDeleteOptions.push(DeleteMode.DELETE_IMAGE)
          // if 'delete images' is selected then automatically select 'delete redactions' option and prevent unchecking it
          this.deleteRedaction = true
        }
        if (this.deleteRedaction) {
          selectedDeleteOptions.push(DeleteMode.DELETE_REDACTION)
        }
        if (this.deleteNative) {
          selectedDeleteOptions.push(DeleteMode.DELETE_NATIVE)
        }
        if (this.deleteHtmlRtf) {
          selectedDeleteOptions.push(DeleteMode.DELETE_HTML_RTF)
        }
        //uncheck checkboxes under 'delete all' radion button
        this.deleteChildRecords = this.deleteMediaIfAllFilesAreDeleted = false
        break
    }
    this.updateCurrentDeleteOption(selectedDeleteOptions)
    this.cdr.detectChanges()
  }

  public validateCheckStatus(e): void {
    //if 'delete images' is selected then prevent unchecking 'delete redactions' option
    if (this.deleteImages && !e.currentTarget.checked) {
      e.preventDefault()
    }
  }

  public updateCurrentDeleteOption(selectedDeleteOptions: DeleteMode[]): void {
    this.deleteDocumentFacade.setCurrentDeleteOption(
      selectedDeleteOptions,
      this.deleteChildRecords,
      this.deleteMediaIfAllFilesAreDeleted
    )
  }
}
