<div class="t-flex t-flex-col t-gap-2 t-mb-3">
  <div class="t-flex">
    <input
      type="radio"
      name="deleteOption"
      id="rdDeleteAll"
      (change)="onDeleteOptionChange('DELETE_ALL')"
      checked
      data-qa="delete-all-radio-button"
      kendoRadioButton />
    <kendo-label
      for="rdDeleteAll"
      class="t-k-radio-label t-ml-2"
      text="Documents and all associated files">
    </kendo-label>
  </div>

  <div class="t-flex t-flex-col t-gap-2 t-ml-4">
    <div class="t-flex">
      <input
        type="checkbox"
        class="k-checkbox"
        [(ngModel)]="deleteChildRecords"
        id="chkDeleteChildRecords"
        (change)="onDeleteOptionChange('DELETE_ALL')"
        [disabled]="!deleteAllSelected"
        data-qa="delete-child-records-checkbox"
        kendoCheckBox />
      <kendo-label
        class="k-checkbox-label"
        for="chkDeleteChildRecords"
        text="Delete child record of all the selected parent documents">
      </kendo-label>
    </div>

    <div class="t-flex">
      <input
        type="checkbox"
        class="k-checkbox"
        [(ngModel)]="deleteMediaIfAllFilesAreDeleted"
        id="chkDeleteMediaIfAllFilesAreDeleted"
        (change)="onDeleteOptionChange('DELETE_ALL')"
        [disabled]="!deleteAllSelected"
        data-qa="delete-media-if-all-files-are-deleted-checkbox"
        kendoCheckBox />
      <kendo-label
        class="k-checkbox-label"
        for="chkDeleteMediaIfAllFilesAreDeleted"
        text="Delete media if all the files are deleted">
      </kendo-label>
    </div>
  </div>
</div>

<div class="t-flex t-flex-col t-gap-2 t-mb-3">
  <div class="t-flex">
    <input
      type="radio"
      name="deleteOption"
      (change)="onDeleteOptionChange('DELETE_DOCUMENT_TYPE')"
      id="rdDeleteDocumentType"
      data-qa="delete-document-type-radio-button"
      kendoRadioButton />
    <kendo-label
      for="rdDeleteDocumentType"
      class="t-k-radio-label t-ml-2"
      text="Document Type">
    </kendo-label>
  </div>

  <div class="t-flex t-flex-col t-gap-2 t-ml-4">
    <div class="t-flex">
      <input
        type="checkbox"
        class="k-checkbox"
        [(ngModel)]="deleteImages"
        id="chkDeleteImages"
        (change)="onDeleteOptionChange('DELETE_DOCUMENT_TYPE')"
        [disabled]="deleteAllSelected"
        data-qa="delete-images-checkbox"
        kendoCheckBox />
      <kendo-label
        class="k-checkbox-label"
        for="chkDeleteImages"
        text="Only images">
      </kendo-label>
    </div>

    <div class="t-flex">
      <input
        type="checkbox"
        class="k-checkbox"
        [(ngModel)]="deleteRedaction"
        id="chkDeleteRedaction"
        (click)="validateCheckStatus($event)"
        (change)="onDeleteOptionChange('DELETE_DOCUMENT_TYPE')"
        [disabled]="deleteAllSelected"
        data-qa="delete-redaction-checkbox"
        kendoCheckBox />
      <kendo-label
        class="k-checkbox-label"
        for="chkDeleteRedaction"
        text="Image redaction objects (Pointers and redacted OCR text)">
      </kendo-label>
    </div>

    <div class="t-flex">
      <input
        type="checkbox"
        class="k-checkbox"
        [(ngModel)]="deleteNative"
        id="chkDeleteNative"
        (change)="onDeleteOptionChange('DELETE_DOCUMENT_TYPE')"
        [disabled]="deleteAllSelected"
        data-qa="delete-native-checkbox"
        kendoCheckBox />
      <kendo-label
        class="k-checkbox-label"
        for="chkDeleteNative"
        text="Only native">
      </kendo-label>
    </div>

    <div class="t-flex">
      <input
        type="checkbox"
        class="k-checkbox"
        [(ngModel)]="deleteHtmlRtf"
        id="chkDeleteHtmlRtf"
        (change)="onDeleteOptionChange('DELETE_DOCUMENT_TYPE')"
        [disabled]="deleteAllSelected"
        data-qa="delete-html-rtf-checkbox"
        kendoCheckBox />
      <kendo-label
        class="k-checkbox-label"
        for="chkDeleteHtmlRtf"
        text="Only HTML and RTF"></kendo-label>
    </div>
  </div>
</div>
