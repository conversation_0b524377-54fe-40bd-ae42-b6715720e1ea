<kendo-dialog-titlebar (close)="dialogRef.close()">
  <div>
    <span>Show & Hide Fields</span>
  </div>
</kendo-dialog-titlebar>
<lib-feature-field-selector
  [availableFields]="availableFields"
  [visibleFields]="visibleFields"
  (fieldSelectionChanged)="
    onFieldSelectionChanged($event)
  "></lib-feature-field-selector>
<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="onActionClick('save')"
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      data-qa="save-button">
      SAVE
    </button>
    <button
      kendoButton
      (click)="onActionClick('cancel')"
      themeColor="dark"
      fillMode="outline"
      data-qa="cancel-button">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
