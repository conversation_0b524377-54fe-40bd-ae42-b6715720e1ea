import {
  ChangeDetectionStrategy,
  ChangeDetector<PERSON><PERSON>,
  Component,
  On<PERSON><PERSON>roy,
} from '@angular/core'

import { Subject } from 'rxjs'

import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import { IconsModule } from '@progress/kendo-angular-icons'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { FeatureFieldSelectorComponent } from '@venio/feature/field-selector'
import {
  FieldSelector,
  FieldSelectorViewModel,
} from '@venio/data-access/review'
@Component({
  imports: [
    ButtonsModule,
    TreeListModule,
    IconsModule,
    DialogsModule,
    FeatureFieldSelectorComponent,
  ],
  selector: 'lib-field-selector',
  standalone: true,
  templateUrl: './field-selector.component.html',
  styleUrls: ['./field-selector.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FieldSelectorComponent implements <PERSON><PERSON><PERSON><PERSON>, FieldSelector {
  private toDestroy$: Subject<void> = new Subject<void>()

  public availableFields: FieldSelectorViewModel[] = []

  public visibleFields: FieldSelectorViewModel[] = []

  constructor(private cdr: ChangeDetectorRef, public dialogRef: DialogRef) {}

  public onFieldSelectionChanged(
    selectedFields: FieldSelectorViewModel[]
  ): void {
    this.cdr.markForCheck()
    this.visibleFields = selectedFields
  }

  public onActionClick(action: string): void {
    switch (action) {
      case 'save':
        this.save()
        break
      case 'cancel':
        this.cancel()
        break
    }
  }

  private save(): void {
    this.dialogRef.close(this.visibleFields || [])
  }

  private cancel(): void {
    this.dialogRef.close([])
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
