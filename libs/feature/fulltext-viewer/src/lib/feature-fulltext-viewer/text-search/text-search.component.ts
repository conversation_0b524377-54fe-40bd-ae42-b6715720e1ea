import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core'

import { Subject } from 'rxjs'

import { FormControl, ReactiveFormsModule } from '@angular/forms'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { IconsModule } from '@progress/kendo-angular-icons'
import {
  ActionModel,
  FulltextAction,
  HighlightTypeDataModel,
  FulltextFacade,
} from '@venio/data-access/review'
import {
  takeUntil,
  debounceTime,
  distinctUntilChanged,
  filter,
} from 'rxjs/operators'
import { xIcon, arrowDownIcon, arrowUpIcon } from '@progress/kendo-svg-icons'
@Component({
  imports: [InputsModule, ButtonsModule, ReactiveFormsModule, IconsModule],
  selector: 'lib-text-search',
  standalone: true,
  templateUrl: './text-search.component.html',
  styleUrls: ['./text-search.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TextSearchComponent implements OnInit, AfterViewInit, OnDestroy {
  public searchTerm: FormControl = new FormControl()

  private toDestroy$: Subject<void> = new Subject<void>()

  public icons: any = { xIcon, arrowDownIcon, arrowUpIcon }

  public searchDetails: HighlightTypeDataModel

  @ViewChild('searchTermCtrl')
  public searchTermElement: ElementRef

  constructor(
    private cdr: ChangeDetectorRef,
    private fulltextFacade: FulltextFacade
  ) {}

  public ngAfterViewInit(): void {
    this.searchTermElement?.nativeElement?.focus()
  }

  public ngOnInit(): void {
    this.handleSearchTermChange()
    this.getSearchTermDetails()
  }

  public onActionClick(action: string): void {
    switch (action) {
      case 'next':
        this.fulltextFacade.textSearchActionHandler.next({
          actionType: FulltextAction.Next,
        })
        break
      case 'previous':
        this.fulltextFacade.textSearchActionHandler.next({
          actionType: FulltextAction.Previous,
        })
        break
      case 'close':
        this.fulltextFacade.textSearchActionHandler.next({
          actionType: FulltextAction.Close,
        })
        break
    }
  }

  private handleSearchTermChange(): void {
    this.searchTerm.valueChanges
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        filter((value) => value.length > 0),
        takeUntil(this.toDestroy$)
      )
      .subscribe((value) => {
        const payload: ActionModel = {
          actionType: FulltextAction.Find,
          data: { searchTerm: value },
        }
        this.fulltextFacade.textSearchActionHandler.next(payload)
      })
  }

  private getSearchTermDetails(): void {
    this.fulltextFacade.textSearchActionHandler
      .pipe(
        filter(
          (action) => action.actionType === FulltextAction.UpdateSearchDetails
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((action: ActionModel) => {
        this.cdr.markForCheck()
        this.searchDetails = action.data
      })
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
