import { ComponentFixture, TestBed } from '@angular/core/testing'
import { HighlightGroupComponent } from './highlight-group.component'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('TextSearchComponent', () => {
  let component: HighlightGroupComponent
  let fixture: ComponentFixture<HighlightGroupComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HighlightGroupComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(HighlightGroupComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
