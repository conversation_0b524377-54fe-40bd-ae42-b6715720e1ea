import { ComponentFixture, TestBed } from '@angular/core/testing'
import { FeatureFulltextViewerComponent } from './feature-fulltext-viewer.component'
import { provideMockStore } from '@ngrx/store/testing'
import { ActivatedRoute } from '@angular/router'
import { of } from 'rxjs'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { VenioNotificationService } from '@venio/feature/notification'
import { NotificationService } from '@progress/kendo-angular-notification'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('FeatureFulltextViewerComponent', () => {
  let component: FeatureFulltextViewerComponent
  let fixture: ComponentFixture<FeatureFulltextViewerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FeatureFulltextViewerComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        VenioNotificationService,
        NotificationService,
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '2' }),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(FeatureFulltextViewerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
