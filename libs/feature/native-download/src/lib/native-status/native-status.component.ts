import {
  On<PERSON><PERSON>roy,
  OnInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ViewChild,
  ElementRef,
  HostListener,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  NativeDownloadFacade,
  NativeDownloadStatusModel,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import { VenioNotificationService } from '@venio/feature/notification'
import { Subject } from 'rxjs'

import { GridModule } from '@progress/kendo-angular-grid'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { debounceTime, filter, take, takeUntil } from 'rxjs/operators'
import { SVGIcon, trashIcon, downloadIcon } from '@progress/kendo-svg-icons'
import { ButtonsModule } from '@progress/kendo-angular-buttons'

@Component({
  selector: 'lib-native-status',
  standalone: true,
  imports: [CommonModule, ButtonsModule, GridModule, LoaderModule],
  templateUrl: './native-status.component.html',
  styleUrl: './native-status.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NativeStatusComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private resizeEvent = new Subject<Event>()

  public statusData: NativeDownloadStatusModel[]

  public isStatusLoading = false

  public enableRefresh = false

  public pageSize = 10

  public deleteSvg: SVGIcon = trashIcon

  public downloadSvg: SVGIcon = downloadIcon

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public fileTypeGridHeight: number

  @ViewChild('gridContainer', { static: true })
  private gridContainerDivRef: ElementRef

  constructor(
    private notificationService: VenioNotificationService,
    private activatedRoute: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private nativeDownloadFacade: NativeDownloadFacade
  ) {
    this.isStatusLoading = true
  }

  public ngOnInit(): void {
    this.getNativeDownloadStatus()
    this.loadJobStatus()
    this.enableRefresh = false
    this.cdr.markForCheck()

    // Listening for resize event to set grid size
    this.resizeEvent
      .pipe(debounceTime(200), takeUntil(this.toDestroy$))
      .subscribe(() => {
        this.resizePrintGrid()
        this.cdr.markForCheck()
      })
  }

  private getNativeDownloadStatus(): void {
    this.nativeDownloadFacade.getNativeDownloadStatus$
      .pipe(
        filter((data) => !!data),
        takeUntil(this.toDestroy$)
      )
      .subscribe((status) => {
        this.cdr.markForCheck()
        this.statusData = status
        this.resizePrintGrid()
        this.isStatusLoading = false
      })
  }

  public loadJobStatus(): void {
    this.nativeDownloadFacade.fetchNativeDownloadStatus(this.projectId)
  }

  public onDeleteClicked(data): void {
    this.nativeDownloadFacade.deleteNativeDownload(
      this.projectId,
      data.NativeDownloadId
    )
  }

  public refreshAll(): void {
    this.loadJobStatus()
  }

  public onDownloadClicked(data): void {
    this.nativeDownloadFacade
      .downloadNativeDownload(this.projectId, data.NativeDownloadId)
      .pipe(take(1))
      .subscribe(
        (res) => {
          //Get filename form content-disposition header
          const contentDisposition = res.headers.get('content-disposition')
          let filename: string = contentDisposition
            .split(';')[1]
            .split('filename')[1]
            .split('=')[1]
            .trim()
          //fiilename can have doublequotes at start and end, which needs to be replaced
          filename = filename.replace(/(^"|"$)/g, '')
          //As response has header, use res body for content
          this.downLoadFile(res.body, filename)
        },
        async () => {
          this.notificationService.showError(
            'Error occured while downloading native.'
          )
        }
      )
  }

  private downLoadFile(data: any, fileName: string): any {
    const blob = new Blob([data], { type: data.type.toString() })
    const downloadLink = document.createElement('a')
    downloadLink.href = window.URL.createObjectURL(blob)
    downloadLink.setAttribute('download', fileName)
    document.body.appendChild(downloadLink)
    downloadLink.click()
    document.body.removeChild(downloadLink)
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Triggers the event handler subject to set kendo grid size
   * @param {Event} event Event object
   * @returns {void}
   */
  @HostListener('window:resize', ['$event'])
  public onContainerResize(event: Event): void {
    this.resizeEvent.next(event)
  }

  /**
   * Sets the kendo grid height to the container height minus 8px or 140px if the height is less than 140px
   * @returns {void}
   */
  private resizePrintGrid(): void {
    // set initial height to 0
    this.fileTypeGridHeight = 0
    // set timeout to wait for the container to finish resizing
    setTimeout(() => {
      const divElement = this.gridContainerDivRef.nativeElement
      if (divElement.offsetHeight - 8 < 140) this.fileTypeGridHeight = 140
      else this.fileTypeGridHeight = divElement.offsetHeight - 8
      this.cdr.markForCheck()
    }, 30)
  }
}
