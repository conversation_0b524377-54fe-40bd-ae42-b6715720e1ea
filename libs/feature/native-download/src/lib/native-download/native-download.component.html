<form [formGroup]="downloadForm" class="t-h-full">
  <fieldset
    class="v-custom-grey-bg t-border-gray-300-temp t-rounded-md t-h-full t-pb-1 t-border-gray-300-temp t-border t-border-solid t-pt-1">
    <div class="t-py-1 t-px-4" #mainComponent>
      <div>
        <div class="t-text-base t-mb-1">
          <kendo-label text="Native Version"></kendo-label>
        </div>

        <div class="t-mt-[0.5rem] t-m-1">
          <kendo-label id="native-name-lbl" text="Native">
            <span class="t-text-error"> *</span>
            <kendo-textbox
              id="native-name"
              placeholder="Native download name"
              formControlName="nativeDownloadName"></kendo-textbox>
          </kendo-label>
          <div class="t-text-error" *ngIf="!hasNativeDownloadName()">
            Please enter valid native download name
          </div>
        </div>

        <div class="t-mt-[0.5rem] t-m-1">
          <input
            id="download-original"
            type="radio"
            class=""
            formControlName="downloadOriginal"
            value="DOWNLOAD_ORIGINAL"
            kendoRadioButton />
          <kendo-label
            id="download-original-lbl"
            class="t-px-2 t-py-1"
            for="download-original"
            text="Download original"
            ><span class="t-text-error"> *</span></kendo-label
          >
        </div>
        <div class="t-mt-[0.5rem] t-m-1 t-ml-6">
          <input
            id="download-with-redaction"
            class=""
            formControlName="downloadWithRedaction"
            type="checkbox"
            kendoCheckBox />
          <kendo-label
            class="t-px-2 t-py-1"
            for="download-with-redaction"
            text="With Redaction"
            ><span class="t-text-error"> (If available)</span></kendo-label
          >
        </div>

        <div class="t-mt-[1rem] t-m-1 t-items-center">
          <input
            id="original-file-name"
            type="radio"
            class=""
            formControlName="downloadFileName"
            value="ORIGINAL_FILE_NAME"
            kendoRadioButton />
          <kendo-label
            id="original-filename-lbl"
            class="t-px-2 t-py-1"
            for="original-file-name"
            text="Original file name"></kendo-label>
        </div>
        <div class="t-mt-[0.5rem] t-m-1 t-items-center">
          <input
            id="internal-file-id"
            type="radio"
            class="t-px-2 t-py-1"
            formControlName="downloadFileName"
            value="INTERNAL_FILE_ID"
            kendoRadioButton />
          <kendo-label
            id="internale-file-id-lbl"
            class="t-px-2 t-py-1"
            for="internal-file-id"
            text="Internal file ID"></kendo-label>
        </div>
        <div class="t-mt-[0.5rem] t-m-1 t-items-center">
          <input
            id="custom-file-name"
            type="radio"
            class="t-px-2 t-py-1"
            formControlName="downloadFileName"
            value="CUSTOM_FILE_NAME"
            kendoRadioButton />
          <kendo-label
            id="custom-filename-lbl"
            class="t-px-2 t-py-1"
            for="custom-file-name"
            text="Custom"></kendo-label>
        </div>

        <div class="t-flex t-ml-6" formGroupName="customFileNameSettings">
          <div class="t-w-1/2 t-m-1">
            <kendo-label id="prefix-label" class="" text="Prefix">
              <kendo-textbox
                id="prefix"
                placeholder="Prefix"
                [required]="isCustomFileNameSelected()"
                formControlName="prefix"></kendo-textbox>
            </kendo-label>
            <div
              class="t-text-error"
              *ngIf="
                downloadForm.get('customFileNameSettings').get('prefix').invalid
              ">
              Please enter valid prefix
            </div>
          </div>
          <div class="t-w-1/4 t-m-1">
            <kendo-label
              id="starting-number-label"
              class=""
              text="Starting Number">
              <kendo-numerictextbox
                id="starting-number"
                placeholder="Starting Number"
                format="#"
                [min]="0"
                [max]="getMaxValue()"
                [maxlength]="getMaxLength()"
                [autoCorrect]="true"
                [spinners]="true"
                [required]="isCustomFileNameSelected()"
                formControlName="startingNumber"></kendo-numerictextbox
            ></kendo-label>
            <div
              class="t-text-error"
              *ngIf="
                downloadForm.get('customFileNameSettings').get('startingNumber')
                  .invalid
              ">
              Please enter valid starting number
            </div>
          </div>
          <div class="t-w-1/4 t-m-1">
            <kendo-label id="padding-label" class="" text="Padding">
              <kendo-numerictextbox
                id="padding"
                placeholder="padding"
                format="#"
                [min]="1"
                [max]="1000"
                [autoCorrect]="true"
                [spinners]="true"
                [required]="isCustomFileNameSelected()"
                (valueChange)="onPaddingChanged($event)"
                formControlName="padding"></kendo-numerictextbox
            ></kendo-label>
            <div
              class="t-text-error"
              *ngIf="
                downloadForm.get('customFileNameSettings').get('padding')
                  .invalid
              ">
              Please enter valid padding
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="t-flex t-flex-row t-justify-end t-mt-[1rem]">
        <div class="t-flex t-flex-row">
          <div class="t-m-1">
            <button
    kendoButton
              id="save"
              class="t-p-1"
              type="submit"
              kendoButton
              [disabled]="downloadForm.invalid || disableSaveButton"
              (click)="onDeleteClicked()">
              <span class="k-icon k-i-save"></span> Download
            </button>
          </div>
        </div>
      </div> -->
    </div>
  </fieldset>
</form>

<ng-template #nativeDownloadActionTemplate>
  <div class="t-flex t-flex-row t-justify-end">
    <button
      kendoButton
      id="save"
      class="t-m-1 v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      type="submit"
      kendoButton
      [disabled]="downloadForm.invalid || disableSaveButton"
      (click)="onDeleteClicked()">
      <span class="k-icon k-i-save"></span> Download
    </button>
  </div>
</ng-template>
