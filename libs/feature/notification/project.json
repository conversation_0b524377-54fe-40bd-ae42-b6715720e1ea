{"name": "feature-notification", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/notification/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/notification/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/feature/notification/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/notification/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/notification/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}