import 'jest-preset-angular/setup-jest'
import '@angular/localize/init'
import 'fake-indexeddb/auto'
/**
 * @link https://stackoverflow.com/a/48750402/4444844
 */
import <PERSON>ie from 'dexie'
import indexedDB from 'fake-indexeddb'
import { TextEncoder, TextDecoder } from 'util'

global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

Dexie.dependencies.indexedDB = indexedDB
;(global as any).URL.createObjectURL = jest.fn(() => 'dummy-url')
;(global as any).URL.revokeObjectURL = jest.fn()
global.encryptStr = jest.fn()
global.decryptStr = jest.fn()
;(global as any).setInterval = jest.fn((fn, time) => {
  fn()
  return { clear: jest.fn() }
})
