import {
  Directive,
  ElementRef,
  Renderer2,
  Input,
  AfterViewInit,
  On<PERSON><PERSON>roy,
  Inject,
  Output,
  EventEmitter,
} from '@angular/core'
import { WINDOW } from '@venio/data-access/iframe-messenger'

@Directive({
  selector: '[venioTextTruncate]',
  standalone: true,
})
export class TextTruncateDirective implements AfterViewInit, OnDestroy {
  private _lineCount: number

  @Input()
  public set lineCount(value: number) {
    this._lineCount = value
    this.#reapplyTruncationIfNeeded()
  }

  public get lineCount(): number {
    return this._lineCount
  }

  @Input()
  public shouldCalculateByPolling = false

  @Input()
  public pollingInterval = 1000

  @Output()
  public readonly isTextTruncated = new EventEmitter<boolean>()

  public originalText: string | null = null

  private truncationInterval = null

  private resizeObserver: ResizeObserver | null = null

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    @Inject(WINDOW) private windowRef: Window
  ) {}

  public ngAfterViewInit(): void {
    this.#storeOriginalText()

    if (this.originalText) {
      this.#truncateText()
      this.#monitorElementSize()
      this.#pollingTruncateCalculation()
    }
  }

  public ngOnDestroy(): void {
    if (this.truncationInterval) {
      clearInterval(this.truncationInterval)
    }

    this.resizeObserver?.disconnect()
  }

  #reapplyTruncationIfNeeded(): void {
    if (this.originalText) {
      this.#truncateText()
    }
  }

  #monitorElementSize(): void {
    this.resizeObserver = new ResizeObserver(() => this.#truncateText())

    this.resizeObserver.observe(this.el.nativeElement)
  }

  #storeOriginalText(): void {
    this.originalText =
      this.el.nativeElement.textContent || this.el.nativeElement.innerText
  }

  #pollingTruncateCalculation(): void {
    if (!this.shouldCalculateByPolling) {
      return
    }
    this.truncationInterval = setInterval(() => {
      this.#truncateText()
    }, Math.min(this.pollingInterval, 500))
  }

  #truncateText(): void {
    // Bypass truncation if lineCount is negative or zero
    if (this._lineCount <= 0) {
      this.isTextTruncated.emit(false)
      return
    }

    const element = this.el.nativeElement
    const style = this.windowRef.getComputedStyle(element)
    const scaleFactor = this.windowRef.devicePixelRatio || 1
    const fontSize = parseFloat(style.fontSize) || 16
    const lineHeight = parseFloat(style.lineHeight) || fontSize
    const maxHeight = lineHeight * this._lineCount * scaleFactor
    const maxWidth = element.clientWidth * scaleFactor

    this.renderer.setProperty(element, 'textContent', this.originalText)

    if (element.scrollWidth <= maxWidth && element.scrollHeight <= maxHeight) {
      this.isTextTruncated.emit(false)
      return
    }

    let truncatedText = this.originalText || ''
    // Calculate the approximate number of characters that could fit
    const approxCharsToFit = Math.floor((maxWidth / fontSize) * this._lineCount)
    truncatedText = truncatedText.slice(0, approxCharsToFit)

    while (
      (element.scrollWidth > maxWidth || element.scrollHeight > maxHeight) &&
      truncatedText.length > 0
    ) {
      truncatedText = truncatedText.slice(0, -1)
      this.renderer.setProperty(element, 'textContent', truncatedText + '...')
      this.isTextTruncated.emit(true)
    }
  }
}
