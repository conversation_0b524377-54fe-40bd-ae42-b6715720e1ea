import {
  Directive,
  Input,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewContainerRef,
} from '@angular/core'
import { Store } from '@ngrx/store'
import {
  UserRights,
  hasGlobalRight,
  hasGroupRight,
} from '@venio/data-access/review'
import { combineLatest, Observable, Subject, take, takeUntil } from 'rxjs'

/**
 * UserGlobalRightCheckDirective
 *
 * This directive is used to conditionally display elements based on the user's global rights.
 * It checks for user permissions and renders the view accordingly.
 *
 * Selector: '[venioHasUserGlobalRights]'
 * - This selector allows you to apply this directive to any element.
 *
 * Inputs:
 * - rightName: UserRights | UserRights[]
 *   The right or rights to check against.
 *   The element will only be displayed if the user has these rights.
 *
 * - anyOfTheGivenPermission: boolean
 *   Determines the mode of permission checking.
 *   If true, displaying the element requires any of the specified permissions.
 *   If false, all specified permissions are required.
 *   Default: false
 *
 * Examples:
 * - Single Permission Check:
 *   `<div *venioHasUserGlobalRights="'canEdit'">...</div>`
 *   Displays the element if the user has the 'canEdit' permission.
 *
 * - Multiple Permissions Check (All required):
 *   `<div *venioHasUserGlobalRights="['canEdit', 'canDelete']">...</div>`
 *   Displays the element if the user has both 'canEdit' and 'canDelete' permissions.
 *
 * - Multiple Permissions Check (Any one is sufficient):
 *   `<div *venioHasUserGlobalRights="['canEdit', 'canDelete']; anyOfTheGivenPermission: true">...</div>`
 *   Displays the element if the user has either 'canEdit' or 'canDelete' permission.
 *
 * Lifecycle Hooks:
 * - ngOnInit: Subscribes to the store to check for user rights.
 * Determines if the view should be created based on the rights.
 * - ngOnDestroy: Unsubscribes from the store and cleans up resources.
 *
 * Internal Logic:
 * - The directive uses Angular's ViewContainerRef to manage the rendering of the template.
 * - It subscribes to a store selector that determines the presence of the required rights.
 * - Based on the rights and the `anyOfTheGivenPermission` input, it decides to render or clear the view.
 */
@Directive({
  selector: '[venioHasUserGlobalRights]',
  standalone: true,
  exportAs: 'appHasUserGlobalRights',
})
export class UserGlobalRightCheckDirective implements OnInit, OnDestroy {
  private hasView = false

  @Input('venioHasUserGlobalRights')
  public rightName: UserRights | UserRights[]

  private _anyOfTheGivenPermission = false

  /**
   * By default, all the given permissions must be present to show the view.
   * If this is set to true, any of the given permissions is enough to show the view.
   * @param {boolean} anyOfTheGivenPermission - if true, any of the given permissions is enough to show the view
   *  @example
   *  <div *venioHasUserGlobalRights="['canEdit',
   *  'canDelete']">...</div> // will show the view if the user has both permissions
   *  <div *venioHasUserGlobalRights="['canEdit', 'canDelete']; anyOfTheGivenPermission:
   *  true">...</div> // will show the view if the user has at least one of the permissions
   */
  @Input()
  public set venioHasUserGlobalRightsAnyOfTheGivenPermission(
    anyOfTheGivenPermission: boolean
  ) {
    this._anyOfTheGivenPermission = anyOfTheGivenPermission
  }

  private toDestroy$: Subject<void> = new Subject<void>()

  constructor(
    private tplRef: TemplateRef<unknown>,
    private vcr: ViewContainerRef,
    private store: Store
  ) {}

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngOnInit(): void {
    // If no rightName is provided, display the view by default and exit early.
    if (
      (!this.rightName ||
        (Array.isArray(this.rightName) && !this.rightName?.[0])) &&
      !this.hasView
    ) {
      this.vcr.createEmbeddedView(this.tplRef)
      this.hasView = true
      return
    }

    this.store
      .select(hasGlobalRight(this.rightName, this._anyOfTheGivenPermission))
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe((hasRight) => {
        if (hasRight && !this.hasView) {
          this.vcr.createEmbeddedView(this.tplRef)
          this.hasView = true
        }

        if (!hasRight && this.hasView) {
          this.vcr.clear()
          this.hasView = false
        }
      })
  }
}

/**
 * UserGroupRightCheckDirective
 *
 * This Angular directive is used to conditionally display content based on the user's group rights.
 * It evaluates the specified group rights and renders the associated content if the conditions are met.
 *
 * Selector: '[venioHasUserGroupRights]'
 * - Apply this directive to any element to check for user group rights.
 *
 * Standalone: true
 * - This directive is standalone and does not require any additional dependencies to be imported.
 *
 * ExportAs: 'venioHasUserGroupRights'
 * - Allows the directive to be referenced by this name in template expressions.
 *
 * Inputs:
 * - `venioHasUserGroupRights` (UserRights | UserRights[]): The group right or rights to be checked.
 * The content will be displayed if the user has these rights.
 *
 * - `anyOfTheGivenPermission` (boolean):
 * Determines whether any or all specified permissions are required to display the content.
 *   Default: false (all specified permissions are required).
 *
 * Examples:
 * - Single Group Right Check:
 *   `<div *venioHasUserGroupRights="'canViewGroup'">Content for users with 'canViewGroup' right</div>`
 *   Displays the content if the user has the 'canViewGroup' permission.
 *
 * - Multiple Group Rights Check (All required):
 *   `<div *venioHasUserGroupRights="['canEditGroup', 'canDeleteGroup']">Content for editing and deleting group</div>`
 *   Displays the content if the user has both 'canEditGroup' and 'canDeleteGroup' permissions.
 *
 * - Multiple Group Rights Check (Any one is sufficient):
 *   `<div *venioHasUserGroupRights="['canEditGroup', 'canDeleteGroup'];
 *   ayOfTheGivenPermission: true">Content for edit or delete group permissions</div>`
 *   Displays the content if the user has either 'canEditGroup' or 'canDeleteGroup' permission.
 *
 * Lifecycle Hooks:
 * - ngOnInit(): Subscribes to the store to evaluate user rights and decides whether to render the content.
 * - ngOnDestroy(): Unsubscribes from the store and cleans up resources to prevent memory leaks.
 *
 * Internal Logic:
 * - Uses Angular's ViewContainerRef for rendering the template.
 * - Subscribes to a store selector to check the presence of the required rights.
 * - Based on the rights and the `anyOfTheGivenPermission` input, it manages the rendering or removal of the view.
 */
@Directive({
  selector: '[venioHasUserGroupRights]',
  standalone: true,
  exportAs: 'venioHasUserGroupRights',
})
export class UserGroupRightCheckDirective implements OnInit, OnDestroy {
  private hasView = false

  @Input('venioHasUserGroupRights') public rightName: UserRights | UserRights[]

  private _anyOfTheGivenPermission = false

  /**
   * By default, all the given permissions must be present to show the view.
   * If this is set to true, any of the given permissions is enough to show the view.
   * @param {boolean} anyOfTheGivenPermission - if true, any of the given permissions is enough to show the view
   *  @example
   *  <div *venioHasUserGlobalRights="['canEdit',
   *  'canDelete']">...</div> // will show the view if the user has both permissions
   *  <div *venioHasUserGlobalRights="['canEdit', 'canDelete']; anyOfTheGivenPermission:
   *  true">...</div> // will show the view if the user has at least one of the permissions
   */
  @Input()
  public set venioHasUserGroupRightsAnyOfTheGivenPermission(
    anyOfTheGivenPermission: boolean
  ) {
    this._anyOfTheGivenPermission = anyOfTheGivenPermission
  }

  private toDestroy$: Subject<void> = new Subject<void>()

  constructor(
    private tplRef: TemplateRef<unknown>,
    private vcr: ViewContainerRef,
    private store: Store
  ) {}

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngOnInit(): void {
    // If no rightName is provided, display the view by default and exit early.
    if (
      (!this.rightName ||
        (Array.isArray(this.rightName) && !this.rightName?.[0])) &&
      !this.hasView
    ) {
      this.vcr.createEmbeddedView(this.tplRef)
      this.hasView = true
      return
    }

    this.store
      .select(hasGroupRight(this.rightName, this._anyOfTheGivenPermission))
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((hasRight) => {
        if (hasRight && !this.hasView) {
          this.vcr.createEmbeddedView(this.tplRef)
          this.hasView = true
        }

        if (!hasRight && this.hasView) {
          this.vcr.clear()
          this.hasView = false
        }
      })
  }
}

/**
 * UserCombinedRightsCheckDirective
 *
 * This Angular directive is used to conditionally display content based on the user's rights,
 * either global, group, or both, according to the specified permission type.
 *
 * Selector: '[venioUserCombinedRights]'
 * - Apply this directive to any element to check for combined user and group rights.
 *
 * Inputs:
 * - `rights`: UserRights | UserRights[]
 *   The right or rights to check against.
 *   The content will only be displayed if the user has these rights according to the permission type.
 *
 * - `permissionType`: 'user' | 'global' | 'both'
 *   Specifies the type of rights to check.
 *
 * - `anyOfTheGivenPermission`: boolean
 *   Determines the mode of permission checking.
 *   If true, displaying the content requires any of the specified permissions.
 *   If false, all specified permissions are required.
 *   Default: false.
 *
 * Examples:
 * - `<div *venioCombinedRights="'canEdit'; permissionType: 'global'">Edit option</div>`
 *   Displays the element if the user has the 'canEdit' global permission.
 *
 * - `<div *venioUserCombinedRights="['canEdit', 'canView']; permissionType: 'both'; anyOfTheGivenPermission: true">Edit or View</div>`
 *   Displays the element if the user has either the 'canEdit' or 'canView' permissions in both global and group contexts.
 *
 * Lifecycle Hooks:
 * - ngOnInit(): Subscribes to the store to evaluate user rights and decides whether to render the content.
 * - ngOnDestroy(): Unsubscribes from the store and cleans up resources to prevent memory leaks.
 *
 * Internal Logic:
 * - Uses Angular's ViewContainerRef for rendering the template.
 * - Subscribes to a store selector to check the presence of the required rights.
 * - Based on the rights, permission type, and the `anyOfTheGivenPermission` input, it manages the rendering or removal of the view.
 */
@Directive({
  selector: '[venioCombinedRights]',
  standalone: true,
  exportAs: 'venioCombinedRights',
})
export class CombinedRightsCheckDirective implements OnInit, OnDestroy {
  private hasView = false

  public toDestroy$ = new Subject<void>()

  @Input('venioCombinedRights')
  public rightName: UserRights | UserRights[]

  @Input()
  public permissionType: 'user' | 'global' | 'both' = 'both'

  private _anyOfTheGivenPermission = false

  private _permissionType: 'user' | 'global' | 'both' = 'both'

  /**
   * By default, all the given permissions must be present to show the view.
   * If this is set to true, any of the given permissions is enough to show the view.
   * @param {boolean} anyOfTheGivenPermission - if true, any of the given permissions is enough to show the view
   *  @example
   *  <div *venioHasUserGlobalRights="['canEdit',
   *  'canDelete']">...</div> // will show the view if the user has both permissions
   *  <div *venioHasUserGlobalRights="['canEdit', 'canDelete']; anyOfTheGivenPermission:
   *  true">...</div> // will show the view if the user has at least one of the permissions
   */
  @Input()
  public set venioCombinedRightsAnyOfTheGivenPermission(
    anyOfTheGivenPermission: boolean
  ) {
    this._anyOfTheGivenPermission = anyOfTheGivenPermission
  }

  @Input()
  public set venioCombinedRightsPermissionType(
    permissionType: 'user' | 'global' | 'both'
  ) {
    this._permissionType = permissionType
  }

  constructor(
    private tplRef: TemplateRef<unknown>,
    private vcr: ViewContainerRef,
    private store: Store
  ) {}

  public ngOnInit(): void {
    // If no rightName is provided, display the view by default and exit early.
    if (
      (!this.rightName ||
        (Array.isArray(this.rightName) && !this.rightName?.[0])) &&
      !this.hasView
    ) {
      this.vcr.createEmbeddedView(this.tplRef)
      this.hasView = true
      return
    }

    const observables: Array<Observable<boolean>> = []

    if (this.permissionType === 'global' || this.permissionType === 'both') {
      observables.push(
        this.store.select(
          hasGlobalRight(this.rightName, this._anyOfTheGivenPermission)
        )
      )
    }
    if (this.permissionType === 'user' || this.permissionType === 'both') {
      observables.push(
        this.store.select(
          hasGroupRight(this.rightName, this._anyOfTheGivenPermission)
        )
      )
    }

    combineLatest(observables)
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((results) => {
        const hasRights = this._anyOfTheGivenPermission
          ? results.some(Boolean)
          : results.every(Boolean)
        if (hasRights && !this.hasView) {
          this.vcr.createEmbeddedView(this.tplRef)
          this.hasView = true
        } else if (!hasRights && this.hasView) {
          this.vcr.clear()
          this.hasView = false
        }
      })
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
