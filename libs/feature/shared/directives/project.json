{"name": "feature-shared-directives", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/shared/directives/src", "prefix": "venio", "projectType": "library", "tags": ["directives"], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/shared/directives/ng-package.json", "tailwindConfig": "libs/feature/shared/directives/tailwind.config.js"}, "configurations": {"production": {"tsConfig": "libs/feature/shared/directives/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/shared/directives/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/shared/directives/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}