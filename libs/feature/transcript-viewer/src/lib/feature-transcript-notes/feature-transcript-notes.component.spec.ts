import { ComponentFixture, TestBed } from '@angular/core/testing'
import { FeatureTranscriptNotesComponent } from './feature-transcript-notes.component'
import { ActivatedRoute } from '@angular/router'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('FeatureTranscriptNotesComponent', () => {
  let component: FeatureTranscriptNotesComponent
  let fixture: ComponentFixture<FeatureTranscriptNotesComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FeatureTranscriptNotesComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: '1',
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(FeatureTranscriptNotesComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
