import { ComponentFixture, TestBed } from '@angular/core/testing'
import { FeatureTranscriptViewerComponent } from './feature-transcript-viewer.component'
import { ActivatedRoute } from '@angular/router'
import {
  VenioNotificationModule,
  VenioNotificationService,
} from '@venio/feature/notification'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import {
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { PLATFORM_ID } from '@angular/core'
import { ReviewPanelFacade } from '@venio/data-access/document-utility'
import {
  SearchFacade,
  DocumentsFacade,
  FieldFacade,
} from '@venio/data-access/review'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'

describe('FeatureTranscriptViewerComponent', () => {
  let component: FeatureTranscriptViewerComponent
  let fixture: ComponentFixture<FeatureTranscriptViewerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        FeatureTranscriptViewerComponent,
        VenioNotificationModule,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
        IframeMessengerModule.forRoot({}),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: '1',
              },
            },
          },
        },
        SearchFacade,
        DocumentsFacade,
        ReviewPanelFacade,
        FieldFacade,
        VenioNotificationService,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(FeatureTranscriptViewerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
