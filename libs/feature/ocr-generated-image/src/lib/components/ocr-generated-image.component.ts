import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core'
import {
  ConvertDocumentFacade,
  ConvertDocumentTab,
  FileTypesResponseModel,
  OcrGeneratedImageFacade,
  SummaryGridModel,
} from '@venio/data-access/review'
import { Subject, combineLatest, debounceTime, filter, takeUntil } from 'rxjs'

@Component({
  selector: 'venio-ocr-generated-image',
  templateUrl: './ocr-generated-image.component.html',
  styleUrls: ['./ocr-generated-image.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OcrGeneratedImageComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private resizeEvent = new Subject<Event>()

  public totalSearchResult = 0

  public previousQueueCount = 0

  public remainingQueueCount = 0

  public totalDocumentWithSlipsheetOnly = 0

  public totalDocumentWithoutImage = 0

  public totalDocumentImaged = 0

  public fileTypeData: FileTypesResponseModel[] = []

  public updatedSelection: FileTypesResponseModel[] = []

  public gridData: SummaryGridModel[] = []

  public showSpinner = false

  public fileTypeGridHeight: number

  @ViewChild('treelistContainer', { static: true })
  private treelistContainerDivRef: ElementRef

  constructor(
    private cdr: ChangeDetectorRef,
    private readonly ocrGenImageFacade: OcrGeneratedImageFacade,
    private readonly convertDocumentFacade: ConvertDocumentFacade
  ) {}

  public ngOnInit(): void {
    this.showSpinner = true
    this.initSlices()
    this.gridData = this.getGridData()
    this.ocrGenImageFacade.getOcrImageSummary()

    // Listening for resize event to set grid size
    this.resizeEvent
      .pipe(debounceTime(200), takeUntil(this.toDestroy$))
      .subscribe(() => {
        this.resizeFileTypeGrid()
        this.cdr.markForCheck()
      })
  }

  private initSlices(): void {
    this.initLaunchOptionChangeEvents()
    this.initOcrImageSummaryLoad()
    this.initOcrImageFileTypeLoad()
    this.initConvertButton()
  }

  private initConvertButton(): void {
    this.convertDocumentFacade.startConvertDocument$
      .pipe(
        filter((tab) => !!tab && tab === ConvertDocumentTab.GeneratedImageOcr),
        takeUntil(this.toDestroy$)
      )
      .subscribe((value) => {
        this.startConvert()
      })
  }

  public ngOnDestroy(): void {
    this.convertDocumentFacade.resetStartConvertDocument()
    this.ocrGenImageFacade.resetOcrState()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Triggers the event handler subject to set tree list size
   * @param {Event} event Event object
   * @returns {void}
   */
  @HostListener('window:resize', ['$event'])
  public onContainerResize(event: Event): void {
    this.resizeEvent.next(event)
  }

  /**
   * Sets the tree list height to the container height minus 8px or 140px if the height is less than 140px
   * @returns {void}
   */
  private resizeFileTypeGrid(): void {
    // set initial height to 0
    this.fileTypeGridHeight = 0
    // set timeout to wait for the container to finish resizing
    setTimeout(() => {
      const divElement = this.treelistContainerDivRef?.nativeElement
      if (divElement?.offsetHeight - 8 < 140) this.fileTypeGridHeight = 140
      else this.fileTypeGridHeight = divElement?.offsetHeight - 8
      this.cdr.markForCheck()
    }, 30)
  }

  public startConvert(): void {
    const selectedItems = this.updatedSelection.filter(
      (item) => item.fileTypeGroupID > 0 && item.selected
    )
    const selectedExtensions = selectedItems
      .map((fileType) => fileType?.extension ?? '')
      .filter((fileType) => !!fileType)

    const selectedFileTypes = selectedItems
      .map((fileType) => fileType?.fileType ?? '')
      .filter((fileType) => !!fileType)

    this.ocrGenImageFacade.queueForOcr(selectedFileTypes, selectedExtensions)
  }

  private initLaunchOptionChangeEvents(): void {
    combineLatest([
      this.convertDocumentFacade.getLaunchOptionState$,
      this.convertDocumentFacade.getRelaunchOptionState$,
    ])
      .pipe(debounceTime(50), takeUntil(this.toDestroy$))
      .subscribe(([launch, relaunch]) => {
        this.showSpinner = true
        this.ocrGenImageFacade.fetchOcrImageFileTypes(launch, relaunch)
        this.cdr.markForCheck()
      })
  }

  private initOcrImageSummaryLoad(): void {
    this.ocrGenImageFacade.getOcrImageSummary$
      .pipe(
        filter((s) => !!s),
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe((summary) => {
        if (summary !== null) {
          this.totalSearchResult = summary.totalSearchHitCount ?? 0
          this.previousQueueCount = summary.previousOCRCountForGenerated ?? 0
          this.remainingQueueCount = summary.remainOCRCountForGenerated ?? 0
          this.totalDocumentWithSlipsheetOnly =
            summary.totalDocumentWithSlipsheetOnly ?? 0
          this.totalDocumentWithoutImage = summary.totalDocumentWithoutTiff ?? 0
          this.totalDocumentImaged = summary.totalDocumentTiffed ?? 0

          const enableRelaunch = this.previousQueueCount > 0
          const enableLaunch = this.remainingQueueCount > 0
          this.convertDocumentFacade.setLaunchOption(enableLaunch, false)
          this.convertDocumentFacade.setLaunchOptionAvailability(enableLaunch)
          this.convertDocumentFacade.setRelaunchOptionAvailability(
            enableRelaunch
          )

          this.gridData = this.getGridData()
        }
        this.cdr.markForCheck()
      })
  }

  private initOcrImageFileTypeLoad(): void {
    this.ocrGenImageFacade.getOcrImageFileTypes$
      .pipe(
        filter((s) => !!s),
        debounceTime(500),
        takeUntil(this.toDestroy$)
      )
      .subscribe((fileType: FileTypesResponseModel[]) => {
        if (fileType !== null) {
          this.fileTypeData = fileType
        }
        this.resizeFileTypeGrid()
        this.showSpinner = false
        this.cdr.markForCheck()
      })
  }

  public selectionChange(selectedItems: FileTypesResponseModel[]): void {
    this.updatedSelection = selectedItems
  }

  private getGridData(): SummaryGridModel[] {
    return [
      {
        description: 'Total Search Result',
        count: this.totalSearchResult,
      },
      {
        description: 'Remaining to be Queued',
        count: this.remainingQueueCount,
      },
      {
        description: 'Previously Added to OCR Queue',
        count: this.previousQueueCount,
      },
      {
        description: 'Total document with slipsheet only',
        count: this.totalDocumentWithSlipsheetOnly,
      },
      {
        description: 'Total documents without image',
        count: this.totalDocumentWithoutImage,
      },
      {
        description: 'Total documents imaged',
        count: this.totalDocumentImaged,
      },
    ]
  }
}
