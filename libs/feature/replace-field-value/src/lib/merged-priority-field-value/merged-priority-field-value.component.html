<ng-container [formGroup]="fieldMergeForm">
  <div
    *ngIf="errorMessage"
    class="t-flex t-justify-between t-items-center t-bg-[#f8d7da] t-p-3">
    <span>{{ errorMessage }}</span>
    <button
      type="button"
      (click)="errorMessage = null"
      class="t-border-0 t-cursor-pointer t-text-error">
      close
    </button>
  </div>
  <div class="t-flex t-flex-col t-mt-0.5">
    <div>
      <kendo-label class="t-font-semibold">{{
        isMergeField ? headerLabel.MERGE_TITLE : headerLabel.PRIORITY_TITLE
      }}</kendo-label>
    </div>

    <div class="t-flex-row t-mt-0.5">
      <kendo-label>Documents Selected:</kendo-label>
      <kendo-label>{{ selectedDocumentcount }}</kendo-label>
    </div>

    <div class="t-flex t-flex-row t-mt-1">
      <div class="t-flex t-flex-col t-w-1/2">
        <div class="t-flex t-items-center">
          <kendo-label>Select Field</kendo-label>
          <kendo-dropdownlist
            id="field-to-update"
            class="!t-w-52 t-ml-4"
            [data]="availableCustomFields"
            [loading]="
              (isCustomFieldsLoading$ | async) || (isFieldsLoading$ | async)
            "
            formControlName="field"
            textField="displayName"
            valueField="fieldName"
            [filterable]="true"
            [virtual]="{ itemHeight: 28 }"
            [kendoDropDownFilter]="{
              caseSensitive: false,
              operator: 'contains'
            }"
            [popupSettings]="{
              width: 350,
              height: 150
            }"
            data-qa="fieldtoupdate">
          </kendo-dropdownlist>
        </div>
      </div>

      <div class="t-flex t-flex-col t-w-1/2" *ngIf="isMergeField">
        <div class="t-flex t-items-center t-pl-4">
          <kendo-label>Delimiter</kendo-label>
          <kendo-dropdownlist
            class="!t-w-28 t-ml-4"
            id="delimiter"
            [data]="delimiterList"
            formControlName="indexDelimiter"
            textField="displayText"
            valueField="indexDelimiter"
            [loading]="
              (isFieldsLoading$ | async) || (isCustomFieldsLoading$ | async)
            "
            [valuePrimitive]="false"
            [filterable]="true"
            [virtual]="{ itemHeight: 28 }"
            [popupSettings]="{
              width: 200,
              height: 150
            }"
            [kendoDropDownFilter]="{
              caseSensitive: true,
              operator: 'contains'
            }"
            data-qa="delimiter">
          </kendo-dropdownlist>
        </div>
      </div>
    </div>

    <div class="t-flex t-flex-row t-mt-1">
      <div class="t-flex t-flex-col t-w-1/2">
        <kendo-label class="t-mb-1 t-font-bold t-text-blue-900-temp"
          >Available Source Field</kendo-label
        >
        <kendo-grid
          [kendoGridBinding]="availableSourceFields"
          kendoGridSelectBy="id"
          (selectionChange)="onSelectionChange($event)"
          [(selectedKeys)]="mySelection"
          [loading]="
            (isFieldsLoading$ | async) || (isCustomFieldsLoading$ | async)
          "
          [ngClass]="{
            'k-disabled t-opacity-50': isReplaceInProgress$ | async,
            't-opacity-100': (isReplaceInProgress$ | async) === false
          }"
          [selectable]="{ checkboxOnly: true }"
          [filterable]="true"
          [pageable]="false"
          [sortable]="true"
          [groupable]="false"
          [reorderable]="true"
          [resizable]="true"
          [height]="360"
          [rowHeight]="36"
          scrollable="virtual"
          data-qa="availableSourceFields">
          <ng-template kendoGridNoRecordsTemplate>
            <p>No available fields</p>
          </ng-template>
          <kendo-grid-checkbox-column
            [width]="50"
            class="!t-py-[0.6rem]"
            [resizable]="false"
            [columnMenu]="false"
            [showSelectAll]="true"></kendo-grid-checkbox-column>
          <kendo-grid-column
            field="venioFieldId"
            title="Id"
            [width]="50"
            [hidden]="true">
          </kendo-grid-column>
          <kendo-grid-column field="displayFieldName" title="">
            <ng-template
              kendoGridFilterCellTemplate
              let-filter
              let-column="column">
              <kendo-grid-string-filter-cell
                class="t-h-4"
                [column]="column"
                [filter]="filter">
              </kendo-grid-string-filter-cell>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="internalFieldName" [hidden]="true">
          </kendo-grid-column>
        </kendo-grid>
      </div>

      <div class="t-flex t-flex-col t-w-1/2 t-pl-4">
        <kendo-label class="t-mb-1 t-font-bold t-text-blue-900-temp"
          >Selected Source Field</kendo-label
        >
        <kendo-grid
          [kendoGridBinding]="selectedSourceFields"
          [rowReorderable]="true"
          [pageable]="false"
          [height]="360"
          [rowHeight]="36"
          [hideHeader]="true"
          scrollable="virtual"
          [ngClass]="{
            'k-disabled t-opacity-50': isReplaceInProgress$ | async,
            't-opacity-100': (isReplaceInProgress$ | async) === false
          }"
          data-qa="selectedSourceFields">
          <ng-template kendoGridNoRecordsTemplate>
            <p>No selected fields</p>
          </ng-template>
          <kendo-grid-rowreorder-column
            [width]="40"></kendo-grid-rowreorder-column>
          <kendo-grid-column
            field="displayFieldName"
            title=""></kendo-grid-column>
          <kendo-grid-column
            field="internalFieldName"
            [hidden]="true"></kendo-grid-column>
        </kendo-grid>
      </div>
    </div>
  </div>
</ng-container>
