import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { LabelModule } from '@progress/kendo-angular-label'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { DataBindingDirective, GridModule } from '@progress/kendo-angular-grid'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogModule, DialogRef } from '@progress/kendo-angular-dialog'
import { State } from '@progress/kendo-data-query'
import { TextBoxModule } from '@progress/kendo-angular-inputs'
import {
  Observable,
  Subject,
  combineLatest,
  debounceTime,
  filter,
  takeUntil,
} from 'rxjs'
import {
  DocumentsFacade,
  Field,
  OverlayCustomFieldsDelimiterModel,
  OverlayCustomFieldsHeaderLabel,
  OverlayCustomFieldsInputTypes,
  OverlayCustomFieldsMergePayloadModel,
  OverlayCustomFieldsUiType,
  ReplaceFieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import {
  CustomFieldsModel,
  DocumentCodingViewModel,
} from '@venio/shared/models/interfaces'
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { VenioNotificationService } from '@venio/feature/notification'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'

@Component({
  selector: 'venio-merged-field-value',
  standalone: true,
  imports: [
    CommonModule,
    LabelModule,
    DropDownsModule,
    GridModule,
    ButtonsModule,
    TextBoxModule,
    FormsModule,
    ReactiveFormsModule,
    IndicatorsModule,
    DialogModule,
  ],
  templateUrl: './merged-priority-field-value.component.html',
  styleUrl: './merged-priority-field-value.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MergedPriorityFieldValueComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  public selectedDocumentcount = 0

  public state: State = { skip: 0, take: 500 }

  public availableCustomFields: DocumentCodingViewModel[] = []

  public mySelection: any[] = []

  private componentFields: Field[] = []

  public availableSourceFields: Field[] = []

  public selectedSourceFields: Field[] = []

  public delimiterList: OverlayCustomFieldsDelimiterModel[] = []

  public isReplaceInProgress$ = this.replaceFieldFacade.isReplaceInProgress$

  public isInvalidForm = true

  public errorMessage: string | null

  public readonly headerLabel = OverlayCustomFieldsHeaderLabel

  public fieldMergeForm: FormGroup

  @ViewChild(DataBindingDirective)
  public dataBinding: DataBindingDirective

  @Input()
  public uiInputType: OverlayCustomFieldsUiType

  public isCustomFieldsLoading$: Observable<boolean>

  public isFieldsLoading$: Observable<boolean>

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public get isMergeField(): boolean {
    return this.uiInputType === OverlayCustomFieldsUiType.MERGE
  }

  constructor(
    public replaceFieldFacade: ReplaceFieldFacade,
    public documentFacade: DocumentsFacade,
    public searchFacade: SearchFacade,
    public notificationService: VenioNotificationService,
    public dialog: DialogRef,
    private cdr: ChangeDetectorRef,
    private activatedRoute: ActivatedRoute,
    private formBuilder: FormBuilder
  ) {}

  public ngOnInit(): void {
    this.initForm()
    this.fieldLoadingHandlers()

    this.refreshInvalidFormFlagWhenChanged()
    this.selectMergeResponse()
    this.handleCustomFieldChange()

    this.getCustomFields()
    this.getComponentFields()
    this.getDelimiters()
    this.getSelectedDocumentCount()
  }

  private initForm(): void {
    this.fieldMergeForm = this.formBuilder.group({
      field: [null, Validators.required],
      indexDelimiter: [null, this.isMergeField ? Validators.required : []],
    })
  }

  private fieldLoadingHandlers(): void {
    this.isCustomFieldsLoading$ = this.replaceFieldFacade.isCustomFieldsLoading$
    this.isFieldsLoading$ = this.replaceFieldFacade.isFieldsLoading$
    this.isReplaceInProgress$ = this.replaceFieldFacade.isReplaceInProgress$

    this.replaceFieldFacade.isReplaceInProgress$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((status) => {
        this.cdr.markForCheck()
        if (!status) {
          this.fieldMergeForm.get('field').enable({ emitEvent: false })
          this.fieldMergeForm.get('indexDelimiter').enable()
        } else {
          this.fieldMergeForm.get('field').disable({ emitEvent: false })
          this.fieldMergeForm.get('indexDelimiter').disable()
        }
      })
  }

  private handleCustomFieldChange(): void {
    this.fieldMergeForm
      .get('field')
      .valueChanges.pipe(debounceTime(100), takeUntil(this.toDestroy$))
      .subscribe((field: DocumentCodingViewModel) => {
        this.cdr.markForCheck()
        this.availableSourceFields = this.handleSourceFieldFilter(
          this.componentFields,
          field,
          this.isMergeField
        )
        this.resetSelection()

        if (
          this.isMergeField &&
          field.allowMultipleCodingValues === true &&
          field.delimiterForCodingValues
        ) {
          const multiValueDelimiters = this.delimiterList.filter(
            (d: OverlayCustomFieldsDelimiterModel) => {
              return d.displayText.includes(field.delimiterForCodingValues)
            }
          )
          const multiValueDelimiter = multiValueDelimiters.length
            ? multiValueDelimiters[0]
            : null
          this.setDelimiterValue(multiValueDelimiter)
        } else {
          this.setDelimiterValue({ displayText: 'select', indexDelimiter: 0 })
        }
      })
  }

  private handleSourceFieldFilter(
    componentFields: Field[],
    selectedCustomField: DocumentCodingViewModel,
    isMergeField: boolean
  ): Field[] {
    const { uiInputType } = selectedCustomField
    const inputType = (
      uiInputType || ''
    ).toLowerCase() as OverlayCustomFieldsInputTypes

    const isText =
      inputType === OverlayCustomFieldsInputTypes.UnicodeText ||
      inputType === OverlayCustomFieldsInputTypes.UnicodeParagraph ||
      inputType === OverlayCustomFieldsInputTypes.Text ||
      inputType === OverlayCustomFieldsInputTypes.Paragraph

    const filteredFields = componentFields.filter(
      (c) => this.getInputType(c.fieldDataType) === inputType
    )

    // if the filtered field is the only selected custom field, show none
    if (
      filteredFields.length === 1 &&
      filteredFields[0].venioFieldId ===
        selectedCustomField.customFieldInfoId &&
      filteredFields[0].isCustomField
    ) {
      return []
    }

    return isMergeField || (!isMergeField && isText)
      ? componentFields
      : filteredFields
  }

  private getInputType = (
    fieldDataType: string
  ): OverlayCustomFieldsInputTypes => {
    let uiInputType = OverlayCustomFieldsInputTypes.UnicodeText
    switch (fieldDataType?.toLowerCase()) {
      case 'int':
      case 'bigint':
      case 'float':
      case 'decimal':
        uiInputType = OverlayCustomFieldsInputTypes.Numeric
        break
      case 'nvarchar':
      case 'varchar':
        uiInputType = OverlayCustomFieldsInputTypes.Text
        break
      case 'bit':
        uiInputType = OverlayCustomFieldsInputTypes.Boolean
        break
      case 'date':
        uiInputType = OverlayCustomFieldsInputTypes.Date
        break
      case 'time':
      case 'datetime':
        uiInputType = OverlayCustomFieldsInputTypes.DateTime
        break
    }

    return uiInputType
  }

  public onSelectionChange($event): void {
    const selectedFields = this.selectedSourceFields.map((x) => x)
    $event.deselectedRows.forEach((row) => {
      if (selectedFields.includes(row.dataItem)) {
        selectedFields.splice(selectedFields.indexOf(row.dataItem), 1)
      }
    })

    $event.selectedRows.forEach((row) => {
      if (selectedFields.includes(row.dataItem)) return
      selectedFields.push(row.dataItem)
    })
    this.selectedSourceFields = selectedFields
    this.checkFormValidity()
  }

  private getCustomFields(): void {
    this.replaceFieldFacade.getCustomFields$
      .pipe(
        debounceTime(100),
        filter((result) => !!result),
        takeUntil(this.toDestroy$)
      )
      .subscribe((fields: DocumentCodingViewModel[]) => {
        this.cdr.markForCheck()
        // for merge field, we need to filter out Boolean, Numeric, DateTime, Date
        if (this.isMergeField) {
          this.availableCustomFields = this.excludeNonTextFields(fields)
        } else {
          this.availableCustomFields = fields
        }
        this.setInitialFieldDropdownValue(this.availableCustomFields)
      })
  }

  private excludeNonTextFields(
    fields: DocumentCodingViewModel[]
  ): DocumentCodingViewModel[] {
    return fields.filter((field) => {
      const inputType = (field.uiInputType || '').toLowerCase()
      return !(
        inputType === OverlayCustomFieldsInputTypes.Boolean ||
        inputType === OverlayCustomFieldsInputTypes.Numeric ||
        inputType === OverlayCustomFieldsInputTypes.DateTime ||
        inputType === OverlayCustomFieldsInputTypes.Date
      )
    })
  }

  private setInitialFieldDropdownValue(
    fields: DocumentCodingViewModel[]
  ): void {
    if (fields.length > 0) {
      this.fieldMergeForm.get('field').setValue(fields[0])
    }
  }

  private getComponentFields(): void {
    this.replaceFieldFacade.getComponentFields$
      .pipe(
        debounceTime(100),
        filter((result) => !!result),
        takeUntil(this.toDestroy$)
      )
      .subscribe((fields: Field[]) => {
        this.cdr.markForCheck()
        this.componentFields = fields.map((item) => Object.assign({}, item))
      })
  }

  private getDelimiters(): void {
    if (!this.isMergeField) return

    this.replaceFieldFacade.getDelimiters$
      .pipe(
        debounceTime(100),
        filter((result) => !!result),
        takeUntil(this.toDestroy$)
      )
      .subscribe((delimiter: string[]) => {
        this.cdr.markForCheck()
        this.delimiterList = delimiter.map(
          (delimiter, index) =>
            ({
              indexDelimiter: index,
              displayText: delimiter,
            } as OverlayCustomFieldsDelimiterModel)
        )
      })
  }

  private setDelimiterValue(
    delimiter: OverlayCustomFieldsDelimiterModel
  ): void {
    this.fieldMergeForm.get('indexDelimiter').setValue(delimiter)
  }

  private getSelectedDocumentCount(): void {
    combineLatest([
      this.documentFacade.getIsBatchSelected$,
      this.documentFacade.getSelectedDocuments$,
      this.documentFacade.getUnselectedDocuments$,
      this.searchFacade.getTotalHitCount$,
    ])
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(
        ([
          isBatchSelected,
          selectedDocuments,
          unselectedDocuments,
          totalHitCount,
        ]) => {
          if (isBatchSelected) {
            this.selectedDocumentcount =
              totalHitCount - unselectedDocuments.length
          } else {
            this.selectedDocumentcount = selectedDocuments.length
          }
        }
      )
  }

  private refreshInvalidFormFlagWhenChanged(): void {
    this.fieldMergeForm.valueChanges
      .pipe(debounceTime(100), takeUntil(this.toDestroy$))
      .subscribe(() => {
        this.checkFormValidity()
      })
  }

  private checkFormValidity(): void {
    this.cdr.markForCheck()
    const values = this.fieldMergeForm.getRawValue()
    this.isInvalidForm =
      Object.keys(values)
        .filter((key) => key !== 'indexDelimiter')
        .some((key) => !values[key]) || this.selectedSourceFields.length === 0
  }

  private selectMergeResponse(): void {
    this.replaceFieldFacade.selectReplacesResponse$
      .pipe(
        debounceTime(100),
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response) => {
        this.cdr.markForCheck()
        if (response.status === 'success') {
          this.notificationService.showSuccess(response.message)

          if (
            (response?.data?.replaceType === 'merged-fields') ===
              this.isMergeField ||
            (response?.data?.replaceType === 'priority-fields') ===
              !this.isMergeField
          ) {
            this.resetSelection()
          }
        } else {
          this.notificationService.showError(response.message)
        }
        this.replaceFieldFacade.clearReplaceReponse()
      })
  }

  public onReplaceClicked(): void {
    this.cdr.markForCheck()

    if (this.selectedSourceFields.length === 0) {
      this.errorMessage = 'Please select at least one field to replace'
      return
    }

    if (this.isMergeField) {
      this.replaceFieldFacade.applyMergeFields(
        this.projectId,
        this.getPayload()
      )
    } else {
      this.replaceFieldFacade.applyPriorityOrderFields(
        this.projectId,
        this.getPayload()
      )
    }
  }

  private getPayload(): OverlayCustomFieldsMergePayloadModel {
    const { field, indexDelimiter } = this.fieldMergeForm.getRawValue()
    if (!field) return

    const { customFieldInfoId, fieldName, uiInputType } =
      field as CustomFieldsModel
    return {
      mergeFieldItems: this.isMergeField
        ? this.selectedSourceFields?.map(
            (field: Field) => field.displayFieldName
          )
        : [],
      priorityFields: !this.isMergeField
        ? this.selectedSourceFields?.map(
            (field: Field) => field.displayFieldName
          )
        : [],
      fieldName,
      customFieldId: customFieldInfoId,
      indexDelimiter: indexDelimiter?.['indexDelimiter'],
      fieldType: uiInputType,
    } as OverlayCustomFieldsMergePayloadModel
  }

  public onCancel(): void {
    this.dialog.close()
  }

  private resetSelection(): void {
    this.mySelection = []
    this.selectedSourceFields = []
    this.fieldMergeForm
      .get('indexDelimiter')
      .setValue({ displayText: 'select', indexDelimiter: 0 })
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
