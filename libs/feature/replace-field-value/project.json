{"name": "replace-field-value", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/replace-field-value/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/replace-field-value/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/feature/replace-field-value/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/replace-field-value/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/replace-field-value/jest.config.ts"}}}}