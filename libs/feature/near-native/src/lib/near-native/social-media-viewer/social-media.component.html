<!-- template for #transcriptViewer, could be moved to separate component -->
<div class="t-flex t-w-full t-h-full">
  <div
    class="t-flex t-flex-col t-w-16 t-transition-all t-border t-border-[#ccc] t-border-l-0 t-border-b-0 t-border-t-0 t-border-r-1 t-h-max t-min-h-full"
    [ngClass]="{ '!t-w-[230px]': menuState }">
    <div class="t-flex t-flex-col t-gap-5 t-p-4 t-self-start">
      <button
        kendoButton
        fillMode="clear"
        class="t-p-0 t-bg-white t-text-[var(--v-custom-sky-blue)]"
        (click)="this.openInfo()">
        <kendo-svgicon [icon]="infoSolidIcon" [size]="'xlarge'"></kendo-svgicon>
      </button>
      <button
        kendoButton
        fillMode="clear"
        class="t-p-0 t-bg-white t-text-[var(--v-custom-sky-blue)]"
        (click)="this.toggleMenuState()">
        <kendo-svgicon [icon]="filterIcon" [size]="'xlarge'"></kendo-svgicon>
      </button>
    </div>

    <div
      class="t-block t-w-full t-p-4 t-pt-2 t-relative t-overflow-hidden"
      *ngIf="menuState">
      <div class="t-block t-max-h-80 t-overflow-y-auto v-hide-scrollbar">
        <form [formGroup]="form">
          <div formArrayName="participants">
            <div
              class="t-flex t-w-full t-flex-col t-gap-5 t-mb-3"
              *ngFor="
                let control of participantsFormArray.controls;
                let i = index
              ">
              <div [formGroupName]="i">
                <label
                  class="t-flex t-items-center t-min-w-[8.5rem] t-min-h-[2.25rem]">
                  <input
                    type="checkbox"
                    kendoCheckBox
                    rounded="small"
                    size="small"
                    formControlName="selected" />
                  <span class="t-pl-1 t-tracking-tight">{{
                    control.value.name
                  }}</span>
                </label>
              </div>
            </div>
          </div>
        </form>
      </div>

      <div class="t-flex t-flex-col t-w-full t-gap-3 t-mt-3">
        <div class="t-text-[var(--v-custom-sky-blue)] t-font-medium">
          Filter By Date & Time
        </div>

        <div class="t-flex t-gap-2 t-flex-col">
          <kendo-datetimepicker
            class="v-custom-daterange"
            [fillMode]="'none'"
            [size]="'medium'"
            [formControl]="dateStart"
            placeholder="From">
          </kendo-datetimepicker>

          <kendo-datetimepicker
            class="v-custom-daterange"
            [fillMode]="'none'"
            [size]="'medium'"
            [formControl]="dateEnd"
            placeholder="To">
          </kendo-datetimepicker>
        </div>
      </div>
    </div>
  </div>

  <div class="t-flex t-flex-1 t-flex-col t-mt-1">
    <div
      class="t-flex t-items-center t-w-full t-border t-border-[#cccccc] t-border-r-0 t-border-l-0 t-border-t-0 t-border-b-1 t-h-[56.5px] t-sticky">
      <div class="t-flex t-w-full t-justify-between t-px-3">
        <div class="t-block">
          <button
            kendoButton
            fillMode="clear"
            class="t-p-0 t-bg-[#1877F2] t-cursor-default t-text-white t-rounded-full t-ml-3">
            <!-- <kendo-svgicon
              [icon]="getSocialMediaIcon()"
              [size]="'xxlarge'"></kendo-svgicon> -->

            <span
              venioSvgLoader
              [svgUrl]="getSocialMediaIcon()"
              [title]="getTitle()"
              class="t-p-[0.35rem] t-grid t-w-[36px] t-h-[36px] t-place-content-center">
            </span>
          </button>
        </div>

        <div class="t-flex t-gap-2">
          <div class="t-flex t-gap-3" kendoTooltip>
            <button
              kendoButton
              title="Zoom In"
              class="!t-p-[0.3rem] t-w-9 t-rounded t-text-[#9BD2A7] hover:t-text-[#ffffff]"
              fillMode="outline"
              (click)="onActionHandler('ZOOM-IN')"
              size="none">
              <kendo-svgicon [icon]="zoomInIcon" [size]="'large'">
              </kendo-svgicon>
            </button>

            <button
              kendoButton
              title="Zoom Out"
              class="!t-p-[0.3rem] t-w-9 t-rounded t-text-[#ED7425] hover:t-text-[#ffffff]"
              fillMode="outline"
              (click)="onActionHandler('ZOOM-OUT')"
              size="none">
              <kendo-svgicon [icon]="zoomOutIcon" [size]="'large'">
              </kendo-svgicon>
            </button>

            <button
              kendoButton
              title="Reset Zoom"
              class="!t-p-[0.3rem] t-w-9 t-rounded t-text-[#979797] hover:t-text-[#ffffff]"
              fillMode="outline"
              (click)="onActionHandler('RESET')"
              size="none">
              <kendo-svgicon [icon]="arrowRotateCwIcon" [size]="'large'">
              </kendo-svgicon>
            </button>
          </div>

          <div class="t-flex" *ngIf="totalPage">
            <kendo-label class="k-form t-mx-1">
              <kendo-numerictextbox
                class="!t-w-[3.5rem] !t-border-[#707070]"
                [spinners]="false"
                [decimals]="0"
                [format]="'n'"
                [formControl]="currentPage"></kendo-numerictextbox>
              <span> /{{ totalPage }} </span>
            </kendo-label>
          </div>
        </div>
      </div>
    </div>

    <!-- FOR DEMO: context menu for add note, highlight & linked documents-->
    <div
      *ngIf="user"
      class="t-flex t-flex-col t-items-center t-justify-center t-w-full t-relative t-overflow-hidden">
      <div
        class="k-chat v-custom-chat t-w-full t-h-full t-max-w-full t-bg-white t-border-0 t-pt-0 t-mt-0"
        *ngIf="messages">
        <cdk-virtual-scroll-viewport
          #cdkviewport
          itemSize="50"
          class="k-message-list k-avatars t-overflow-auto"
          (scrolledIndexChange)="onScrolledIndexChanged($event)"
          style="height: 500px">
          <div
            class="k-message-list-content v-message"
            *cdkVirtualFor="let message of messages; let i = index"
            (click)="toggleTimestamp(i)">
            <div *ngIf="isNewDay(i)" class="k-timestamp">
              {{ message.timestamp | date : 'fullDate' }}
            </div>
            <div
              class="k-message-group"
              [ngClass]="{
                'k-message-group': true,
                'k-alt': message.author.id === user.id
              }">
              <p class="k-author">{{ message.author.name }}</p>
              <div
                class="k-avatar k-avatar-md k-avatar-solid k-avatar-solid-primary k-rounded-full">
                <span class="k-avatar-image">
                  <img alt="Avatar" [src]="message.author.avatarUrl" />
                </span>
              </div>
              <div
                [ngClass]="{ 'k-selected k-focus': selectedMessageIndex === i }"
                class="k-message k-only k-message-content">
                <div class="k-chat-bubble" *ngIf="message.text">
                  {{ message.text }}
                </div>
                <span
                  *ngIf="isTimestampVisible[i]"
                  class="k-message-time"
                  aria-hidden="true"
                  >{{ message.timestamp | date : 'short' }}</span
                >
              </div>

              <ng-container
                [ngTemplateOutlet]="contentTemp"
                [ngTemplateOutletContext]="{ data: message }"></ng-container>
            </div>
          </div>
        </cdk-virtual-scroll-viewport>
      </div>
    </div>
  </div>
</div>

<ng-template #contentTemp let-data="data">
  <div *ngIf="data.attachments.length > 0">
    <!-- <span class="t-flex" *ngIf="data.attachments.length === 1">
      <kendo-svgicon [icon]="paperclipAltIcon" [size]="'large'">
      </kendo-svgicon>
      <p
        class="t-mb-0 t-p-0"
        style="font-size: 10px; overflow-wrap: break-word; overflow: hidden">
        {{ data.attachments[0].name }}
      </p>
    </span> -->

    <div class="t-flex t-flex-col">
      <span *ngFor="let att of data.attachments">
        <a (click)="downloadAttachment(att.name)">
          <div class="t-flex">
            <span>
              <kendo-svgicon [icon]="paperclipAltIcon" [size]="'large'">
              </kendo-svgicon> </span
            >{{ att.name }}
            <kendo-svgicon [icon]="downloadIcon" [size]="'large'">
            </kendo-svgicon>
          </div>
        </a>
      </span>
    </div>
  </div>
</ng-template>
<!-- Info Dialog template-->

<kendo-dialog
  *ngIf="opened"
  (close)="close('cancel')"
  [maxHeight]="550"
  [height]="'90vh'"
  [minWidth]="250"
  [width]="'45%'">
  <kendo-dialog-titlebar>
    <div>
      {{ dialogTitle }}
    </div>
  </kendo-dialog-titlebar>

  <div class="t-flex t-w-full t-flex-col t-gap-3 t-mt-5">
    <div
      class="t-flex t-flex-col t-gap-1 t-bg-[#f7f7f7] t-text-[#979797] t-p-5 t-rounded-sm t-w-full">
      <div class="t-block t-font-semibold">Entire Conversation Metadata</div>
      <div class="t-block t-w-full t-pt-2">
        <ul class="t-flex t-gap-2 t-flex-col t-w-full t-list-none">
          <li>Conversation Initiator: {{ user.name }}</li>
          <li>Participant: {{ getallParticipantsInString() }}</li>
          <li>Total Messages: {{ entireConversationMetaData?.count }}</li>
          <li>
            Total Participants: {{ entireConversationMetaData?.participant }}
          </li>
          <li>
            Start Date: {{ formatDateOnlyForEntireConvo(false) }}
            <span class="t-font-semibold">{{
              formatTimeOnlyForEntireConvo(false)
            }}</span>
            End Date:
            {{ formatDateOnlyForEntireConvo(true) }}
            <span class="t-font-semibold">
              {{ formatTimeOnlyForEntireConvo(true) }}</span
            >
          </li>
        </ul>
      </div>
    </div>

    <div
      class="t-flex t-flex-col t-gap-1 t-bg-[#f7f7f7] t-text-[#979797] t-p-5 t-rounded-sm t-w-full">
      <div class="t-block t-font-semibold">Chunk Conversation Metadata</div>
      <div class="t-block t-w-full t-pt-2">
        <ul class="t-flex t-gap-2 t-flex-col t-w-full t-list-none">
          <li>Conversation Initiator: {{ user.name }}</li>
          <li>Participant: {{ getallParticipantsInString() }}</li>
          <li>Total Messages: {{ chunkConversationMetaData?.count }}</li>
          <li>
            Total Participants: {{ chunkConversationMetaData?.participant }}
          </li>
          <li>
            Start Date:
            {{ formatDateOnlyForChunkMessages(false) }}
            <span class="t-font-semibold">{{
              formatTimeOnlyForChunkMessages(false)
            }}</span>
            End Date:
            {{ formatDateOnlyForChunkMessages(true) }}
            <span class="t-font-semibold">
              {{ formatTimeOnlyForChunkMessages(true) }}</span
            >
          </li>
        </ul>
      </div>
    </div>
  </div>
</kendo-dialog>
