<div
  *ngIf="!errorMessage"
  #container
  style="height: calc(100vh - 5px)"
  id="nearNativeContainer"
  infiniteScroll
  [infiniteScrollDistance]="1"
  [infiniteScrollThrottle]="50"
  (scrolled)="onScroll()"
  [scrollWindow]="false"></div>
<ng-template #placeholder></ng-template>
<div class="v-warning-alert mb-0" *ngIf="errorMessage">
  <div class="d-flex justify-content-between">
    <div class="mr-3">{{ errorMessage }}</div>
    <div *ngIf="showRetryButton">
      <button class="btn btn-sm btn-secondary" (click)="onRetry()">
        Retry
      </button>
    </div>
  </div>
</div>

<kendo-loader *ngIf="showSpinner" size="medium" type="pulsing"></kendo-loader>
