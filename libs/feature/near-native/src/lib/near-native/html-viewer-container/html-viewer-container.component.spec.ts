import { ComponentFixture, TestBed } from '@angular/core/testing'
import { HtmlViewerContainerComponent } from './html-viewer-container.component'
import { DomSanitizer } from '@angular/platform-browser'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('HtmlViewerContainerComponent', () => {
  let component: HtmlViewerContainerComponent
  let fixture: ComponentFixture<HtmlViewerContainerComponent>
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HtmlViewerContainerComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DomSanitizer,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(HtmlViewerContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
