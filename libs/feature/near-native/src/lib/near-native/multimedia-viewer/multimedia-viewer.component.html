<div class="t-flex t-flex-row t-gap-2">
  <div
    [ngClass]="{
      'v-player-error': isPlayerError,
      't-basis-3/4': !isPlayerError
    }">
    <video
      [hidden]="isPlayerError"
      #player
      controls
      preload="metadata"
      class="t-w-full t-outline-none"
      [src]="mediaUrl || 'about:blank'"
      crossorigin="anonymous"></video>
    <span *ngIf="isPlayerError"
      >Media error: Format(s) not supported or source(s) not found</span
    >
  </div>
  <div class="t-flex t-basis-1/4 t-flex-col t-gap-2" *ngIf="!isPlayerError">
    <kendo-grid
      [data]="markedTimes"
      [pageSize]="20"
      [pageable]="{
        buttonCount: 5,
        info: false,
        type: 'input',
        pageSizes: false
      }"
      [sortable]="false"
      [resizable]="true"
      [selectable]="{ enabled: true, checkboxOnly: false, mode: 'single' }"
      [height]="200"
      (cellClick)="cellClickHandler($event)">
      <kendo-grid-column field="displayTime" title="Time" filter="numeric">
      </kendo-grid-column>
      <kendo-grid-column field="note" title="Note"> </kendo-grid-column>
    </kendo-grid>
    <div [formGroup]="markForm" class="t-flex t-flex-col t-gap-2">
      <div>
        <div><label [for]="time">Time (in seconds): </label></div>
        <div>
          <kendo-numerictextbox
            #time
            [step]="1"
            formControlName="startTime"></kendo-numerictextbox>
        </div>
      </div>
      <div>
        <div><label [for]="note">Note: </label></div>
        <div>
          <kendo-textarea
            #note
            [rows]="3"
            resizable="vertical"
            placeholder="Enter a note"
            formControlName="note"></kendo-textarea>
        </div>
      </div>
      <div class="t-flex t-gap-2">
        <button kendoButton (click)="actionClicked('mark')">
          Mark Current Time
        </button>
        <button
          kendoButton
          (click)="actionClicked('save')"
          [disabled]="markForm.invalid">
          Save
        </button>
      </div>
    </div>
  </div>
</div>

<kendo-loader *ngIf="showSpinner" size="medium" type="pulsing"></kendo-loader>
