import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  Type,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  EMPTY,
  Subject,
  catchError,
  filter,
  from,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs'
import {
  DocumentsFacade,
  ReviewParamService,
  UserRights,
  Viewer,
} from '@venio/data-access/review'
import { NearNativeFacade } from '../services/near-native.facade'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { DocumentDetail } from '../models/near-native.model'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  SvgLoaderDirective,
  UserGroupRightCheckDirective,
} from '@venio/feature/shared/directives'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import {
  audioExt,
  socialExt,
  spreadExt,
  videoExt,
} from '../models/near-native-extension-const'
import { HtmlViewerContainerComponent } from './html-viewer-container/html-viewer-container.component'
import { VenioNotificationService } from '@venio/feature/notification'
import { HttpErrorResponse } from '@angular/common/http'

@Component({
  selector: 'lib-near-native',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    SvgLoaderDirective,
    TooltipsModule,
    HtmlViewerContainerComponent,
    UserGroupRightCheckDirective,
  ],
  templateUrl: './near-native.component.html',
  styleUrls: ['./near-native.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NearNativeComponent implements OnInit, AfterViewInit, OnDestroy {
  public currentFileId: number

  private toDestroy$: Subject<void> = new Subject<void>()

  public projectId: number

  private documentDetail: DocumentDetail

  private isRefresh: boolean

  public get viewerType(): string {
    const ext = this.documentDetail?.extension
    const type = [...audioExt, ...videoExt].includes(ext?.trim().toLowerCase())
      ? 'audioVideo'
      : [...spreadExt].includes(ext?.trim().toLowerCase())
      ? 'spreadDocument'
      : [...socialExt].includes(this.documentDetail?.parentFileType)
      ? 'socialMedia'
      : 'htmlViewer'
    return type
  }

  public UserRights = UserRights

  public viewer: Promise<Type<unknown>>

  public spreadSheetViewer: Promise<Type<unknown>>

  public multimediaViewer: Promise<Type<unknown>>

  public htmlViewer: Promise<Type<unknown>>

  public socialMediaViewer: Promise<Type<unknown>>

  public inputs: {
    fileId: number
    projectId: number
    htmlConvertedDateValue: string
  }

  constructor(
    private reviewParamService: ReviewParamService,
    private nearNativeFacade: NearNativeFacade,
    private documentFacade: DocumentsFacade,
    private cdr: ChangeDetectorRef,
    private notificationService: VenioNotificationService
  ) {}

  public ngAfterViewInit(): void {
    this.documentFacade.viewerComponentReady = Viewer.Native
    this.spreadSheetViewer = import(
      './spreadsheet-viewer/spreadsheet-viewer.component'
    ).then((m) => m.SpreadsheetViewerComponent)
    this.multimediaViewer = import(
      './multimedia-viewer/multimedia-viewer.component'
    ).then((m) => m.MultimediaViewerComponent)
    this.htmlViewer = import(
      './html-viewer-container/html-viewer-container.component'
    ).then((m) => m.HtmlViewerContainerComponent)
    this.socialMediaViewer = import(
      './social-media-viewer/social-media.component'
    ).then((m) => m.SocialMediaComponent)
  }

  public ngOnInit(): void {
    this.init()
  }

  public init(): void {
    this.reviewParamService.projectId
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe((projectId) => {
        this.projectId = projectId
        this.initializeNearNativeViewer()
      })
  }

  public initializeNearNativeViewer(): void {
    this.documentFacade.onLoadNearNative
      .pipe(
        filter(
          (fileId) =>
            !!fileId &&
            fileId > 0 &&
            (fileId !== this.currentFileId || this.isRefresh)
        ),
        tap((fileId) => {
          this.currentFileId = fileId
          if (this.isRefresh) this.viewer = null
          this.isRefresh = false
        }),
        switchMap((fileId) => {
          return this.nearNativeFacade.fetchDocumentDetails(
            this.projectId,
            fileId
          )
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        this.documentDetail = response.data
        this.inputs = {
          fileId: this.currentFileId,
          projectId: this.projectId,
          htmlConvertedDateValue: this.documentDetail.htmlConvertedDateValue,
        }

        this.cdr.markForCheck()
        if (this.viewerType === 'htmlViewer') {
          this.viewer = this.htmlViewer
        } else if (this.viewerType === 'audioVideo') {
          this.viewer = this.multimediaViewer
        } else if (this.viewerType === 'spreadDocument') {
          this.viewer = this.spreadSheetViewer
        } else if (this.viewerType === 'socialMedia') {
          this.viewer = this.socialMediaViewer
        }
      })
  }

  public onActionClick(action: string): void {
    switch (action) {
      case 'download':
        this.downloadNativeFile()
        break
      case 'refresh':
        this.isRefresh = true
        this.documentFacade.loadNearNative = this.currentFileId
        break
    }
  }

  private downloadNativeFile(): void {
    this.nearNativeFacade
      .downloadNativeFile(this.projectId, this.currentFileId)
      .pipe(
        catchError((err: unknown) => {
          //err.error is a Blob that we can turn into a Response
          const errResponse = new Response((err as HttpErrorResponse).error)

          // Convert the Promise to an Observable
          return from(errResponse.json()).pipe(
            switchMap((jsonErr) => {
              this.notificationService.showError(jsonErr.message)
              return EMPTY
            }),
            catchError((err: unknown) => {
              return EMPTY
            })
          )
        }),
        take(1)
      )
      .subscribe((response: Blob) => {
        const url = window.URL.createObjectURL(response)
        const a = document.createElement('a')
        a.href = url
        a.download = this.documentDetail.filename
        a.click()
        window.URL.revokeObjectURL(url)
        a.remove()
      })
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
