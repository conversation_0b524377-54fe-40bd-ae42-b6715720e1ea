import { Message } from '@progress/kendo-angular-conversational-ui'
import { SocialMediaParticipant } from '@venio/shared/models/interfaces'
import { expose } from 'comlink'

export function parseSocialMediaData(
  messages,
  participants: { [key: string]: SocialMediaParticipant }
): Message[] {
  return messages.map((m) => ({
    author: participants[m.sender],
    text: m.content,
    timestamp: new Date(m.timeStamp),
    attachments: m.attachments,
  }))
}
expose({ parseSocialMediaData })
