import { Injectable } from '@angular/core'
import { NearNativeService } from './near-native.service'
import { Observable, Subject } from 'rxjs'
import { ChunkDocumentModel } from '@venio/data-access/review'

@Injectable({
  providedIn: 'root',
})
export class NearNativeFacade {
  public refreshMultimediaMarkings$: Subject<void> = new Subject<void>()

  constructor(private nearNativeService: NearNativeService) {}

  public fetchNearNative<T>(
    projectId: number,
    fileId: number,
    payload: ChunkDocumentModel
  ): Observable<T> {
    return this.nearNativeService.fetchNearNative<T>(projectId, fileId, payload)
  }

  public fetchDocumentDetails<T>(
    projectId: number,
    fileId: number
  ): Observable<T> {
    return this.nearNativeService.fetchDocumentDetails<T>(projectId, fileId)
  }

  public downloadNativeFile(
    projectId: number,
    fileId: number
  ): Observable<Blob> {
    return this.nearNativeService.downloadFile(projectId, fileId)
  }

  public fetchMultimediaMarkings = <T>(
    projectId: number,
    fileId: number
  ): Observable<T> =>
    this.nearNativeService.fetchMultimediaMarkings<T>(projectId, fileId)

  public saveMultimediaNote = <T>(
    projectId: number,
    fileId: number,
    startTime: number,
    note: string
  ): Observable<T> =>
    this.nearNativeService.addNote<T>({ projectId, fileId, startTime, note })

  public fetchExcelWorkBook = <T>(
    projectId: number,
    fileId: number
  ): Observable<T> =>
    this.nearNativeService.fetchExcelWorkbook<T>(projectId, fileId)

  public fetchExcelAnnotation = <T>(
    projectId: number,
    fileId: number
  ): Observable<T> =>
    this.nearNativeService.fetchExcelAnnotation<T>(projectId, fileId)

  public saveExcelAnnotation<T>(
    saveSetting: any,
    chunkdocumentInfo: any,
    isRedaction: boolean
  ): Observable<T> {
    return this.nearNativeService.saveExcelAnnotation<T>(
      saveSetting,
      chunkdocumentInfo,
      isRedaction
    )
  }

  public fetchSocialMedia<T>(projectId: number, fileId: number): Observable<T> {
    return this.nearNativeService.fetchSocialMedia(projectId, fileId)
  }

  public getAttachmentLink<T>(
    projectId: number,
    fileId: number,
    fileName: string
  ): Observable<T> {
    return this.nearNativeService.getAttachmentLink(projectId, fileId, fileName)
  }
}
