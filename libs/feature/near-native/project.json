{"name": "feature-near-native", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/near-native/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/near-native/ng-package.json", "tailwindConfig": "libs/feature/near-native/tailwind.config.js"}, "configurations": {"production": {"tsConfig": "libs/feature/near-native/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/near-native/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/near-native/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}