import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  Subject,
  combineLatest,
  map,
  take,
  takeUntil,
  filter,
  debounceTime,
} from 'rxjs'
import {
  AudioTranscribeFacade,
  ConvertDocumentFacade,
  ConvertDocumentTab,
  DocSelectionType,
  DocumentsFacade,
  ResponseMessage,
  SearchFacade,
  SelectableTranscribeQueue,
  TranscribeDetailModel,
  TranscribeRequestModel,
  TranscribingJobDetailModel,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import { VenioNotificationService } from '@venio/feature/notification'
import { ResponseModel } from '@venio/shared/models/interfaces'
import {
  arrowRotateCwIcon,
  chevronLeftIcon,
  SVGIcon,
} from '@progress/kendo-svg-icons'
import { PageArgs, UiPaginationModule } from '@venio/ui/pagination'
import { GridDataResult, GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { FormsModule } from '@angular/forms'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-audio-transcribe',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    InputsModule,
    TooltipsModule,
    IconsModule,
    LayoutModule,
    GridModule,
    UiPaginationModule,
    DropDownsModule,
    FormsModule,
    LoaderModule,
    SvgLoaderDirective,
  ],
  templateUrl: './audio-transcribe.component.html',
  styleUrl: './audio-transcribe.component.scss',
})
export class AudioTranscribeComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public selectionTypeEnum: DocSelectionType

  public fileIdList: number[] = []

  public tempSearchResultTable: string

  public payload: TranscribeRequestModel

  public audioTranscribeFiles: TranscribeDetailModel

  public isHistoryVisible = false

  public isLaunch = true

  public isRelaunch = false

  public isShowAllTranscribeFiles = false

  public disableLaunch = false

  public disableRelaunch = false

  public hasSelectedDocuments = false

  public isGettingFilesForAudioTranscribe =
    this.audioTranscribeFacade.getIsGettingFilesForAudioTranscribe$

  public isQueuingFilesForAudioTranscribe =
    this.audioTranscribeFacade.getIsQueuingFilesForAudioTranscribe$

  public isGettingJobDetailsOfAudioTranscribe =
    this.audioTranscribeFacade.getIsGettingJobDetailsOfAudioTranscribe$

  public jobDetails: TranscribingJobDetailModel[]

  public filteredJobDetails: TranscribingJobDetailModel[] = []

  public userNames: string[] = []

  public tabStatus = 0

  public arrowRotateCwIcon: SVGIcon = arrowRotateCwIcon

  public chevronLeftIcon: SVGIcon = chevronLeftIcon

  public fileTypeDetails: SelectableTranscribeQueue[] = []

  public selectedFileTypes: SelectableTranscribeQueue[] = []

  public isHeaderCheckboxChecked = true

  public selectedUser: string | undefined

  public defaultUser = 'Requested Users'

  public currentPage = 1

  public pageSize = 10

  public skip = 0

  public gridDataJobDetails: GridDataResult = { data: [], total: 0 }

  constructor(
    private activatedRoute: ActivatedRoute,
    private readonly convertDocumentFacade: ConvertDocumentFacade,
    private audioTranscribeFacade: AudioTranscribeFacade,
    private documentsFacade: DocumentsFacade,
    private searchFacade: SearchFacade,
    private cdr: ChangeDetectorRef,
    private notificationService: VenioNotificationService
  ) {}

  public ngOnInit(): void {
    this.initSlices()
    this.initLaunchOptionChangeEvents()
    this.initConvertButton()
    this.#getFilesForAudioTranscribe()
    this.#populateFilesForAudioTranscribe()
    this.#getAudioTranscribeJobDetails()
    this.#populateJobDetailsOfAudioTranscribe()
    this.#handleSuccessResponse()
    this.#handleErrorResponse()
  }

  private initSlices(): void {
    this.convertDocumentFacade.setLaunchOption(false, false)
    this.convertDocumentFacade.setLaunchOptionAvailability(false)
    this.convertDocumentFacade.setRelaunchOptionAvailability(false)
  }

  private initLaunchOptionChangeEvents(): void {
    combineLatest([
      this.convertDocumentFacade.getLaunchOptionState$,
      this.convertDocumentFacade.getRelaunchOptionState$,
    ])
      .pipe(debounceTime(50), takeUntil(this.toDestroy$))
      .subscribe(([launch, relaunch]) => {
        this.isLaunch = launch
        this.isRelaunch = relaunch
        if (this.hasSelectedDocuments) this.getFilesForTranscribe()
        this.cdr.markForCheck()
      })
  }

  private initConvertButton(): void {
    this.convertDocumentFacade.startConvertDocument$
      .pipe(
        filter((tab) => !!tab && tab === ConvertDocumentTab.AudioTranscribe),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.startConvert()
      })
  }

  /**
   * Starts convert process after validation
   * @returns {void}
   */
  public startConvert(): void {
    this.queueFilesForAudioTranscribe()
  }

  #getFilesForAudioTranscribe(): void {
    combineLatest([
      this.documentsFacade.getIsBatchSelected$,
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.getUnselectedDocuments$,
      this.searchFacade.getTotalHitCount$,
      this.searchFacade.getSearchTempTables$,
    ])
      .pipe(
        map(
          ([
            isBatchSelected,
            selectedDocs,
            unselectedDocs,
            totalHitCount,
            searchTempTables,
          ]) => {
            const selectedDocCount = isBatchSelected
              ? totalHitCount - unselectedDocs.length
              : selectedDocs.length

            if (selectedDocCount > 0) {
              this.selectionTypeEnum = DocSelectionType.SelectedFilesOnly

              if (isBatchSelected && unselectedDocs.length > 0) {
                this.selectionTypeEnum = DocSelectionType.AllFilesExceptSelected
                this.fileIdList = unselectedDocs
              } else if (isBatchSelected && unselectedDocs.length <= 0) {
                this.selectionTypeEnum = DocSelectionType.AllFiles
              } else {
                this.selectionTypeEnum = DocSelectionType.SelectedFilesOnly
                this.fileIdList = selectedDocs
              }
              this.tempSearchResultTable =
                searchTempTables.searchResultTempTable
            }

            return selectedDocCount > 0
          }
        ),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((hasSelectedDocs) => {
        this.hasSelectedDocuments = hasSelectedDocs
        if (hasSelectedDocs) {
          this.getFilesForTranscribe()
        }
      })
  }

  public getFilesForTranscribe(): void {
    this.payload = {
      selectionType: this.selectionTypeEnum,
      fileIdList: this.fileIdList,
      globalTempTableName: this.tempSearchResultTable,
      showAllFilesForTranscribing: this.isShowAllTranscribeFiles,
      launchSelected: this.isLaunch,
      relaunchSelected: this.isRelaunch,
    }

    this.audioTranscribeFacade.getFilesForAudioTranscribe(
      this.payload,
      this.projectId
    )
  }

  #populateFilesForAudioTranscribe(): void {
    this.audioTranscribeFacade.getFilesForAudioTranscribeSuccessResponse$
      .pipe(
        filter((response) => !!response),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        this.cdr.markForCheck()
        this.audioTranscribeFiles = response.data

        this.fileTypeDetails =
          this.audioTranscribeFiles.transcribeQueueItems.map((item) => ({
            ...item,
            selected: true, // Default to true
          }))

        this.selectedFileTypes = this.fileTypeDetails

        if (this.audioTranscribeFiles.remainingQueueCount === 0) {
          this.isLaunch = false
          this.disableLaunch = true
        } else {
          this.disableLaunch = false
        }

        if (this.audioTranscribeFiles.previousQueueCount === 0) {
          this.isRelaunch = false
          this.disableRelaunch = true
        } else {
          this.disableRelaunch = false
        }

        const enableRelaunch = !this.disableRelaunch
        const enableLaunch = !this.disableLaunch
        if (!this.isShowAllTranscribeFiles)
          this.convertDocumentFacade.setLaunchOption(false, false)
        this.convertDocumentFacade.setLaunchOptionAvailability(enableLaunch)
        this.convertDocumentFacade.setRelaunchOptionAvailability(enableRelaunch)

        this.#resetAudioTranscribeState()
      })
  }

  public hasNoFileTypeSelected(): boolean {
    return this.selectedFileTypes.length > 0 ? false : true
  }

  public queueFilesForAudioTranscribe(): void {
    if (this.selectedFileTypes.length <= 0) {
      const responseMessage: ResponseMessage = {
        success: false,
        message: 'Please select at least one file type to send to transcribe.',
      }
      this.convertDocumentFacade.setResponseMessage(responseMessage)
      return
    }

    const payload = {
      selectionType: this.selectionTypeEnum,
      fileIdList: this.fileIdList,
      globalTempTableName: this.tempSearchResultTable,
      showAllFilesForTranscribing: this.isShowAllTranscribeFiles,
      launchSelected: this.isLaunch,
      relaunchSelected: this.isRelaunch,
      transcribeQueueItems: this.selectedFileTypes,
    }

    this.audioTranscribeFacade.queueFilesForAudioTranscribe(
      payload,
      this.projectId
    )
  }

  #getAudioTranscribeJobDetails(): void {
    if (this.hasSelectedDocuments)
      this.audioTranscribeFacade.getJobDetailsOfAudioTranscribe(
        this.payload,
        this.projectId
      )
  }

  #populateJobDetailsOfAudioTranscribe(): void {
    this.audioTranscribeFacade.getJobDetailsOfAudioTranscribeSuccessResponse$
      .pipe(
        filter((response) => !!response),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        this.jobDetails = response.data
        if (!this.selectedUser || this.selectedUser === this.defaultUser) {
          this.filteredJobDetails = this.jobDetails
        } else {
          this.filteredJobDetails = this.jobDetails.filter(
            (job) => job.userName === this.selectedUser
          )
        }

        this.userNames = Array.from(
          new Set(this.jobDetails.map((job) => job.userName))
        )
        this.loadJobDetails()
      })
  }

  private loadJobDetails(): void {
    this.cdr.markForCheck()
    this.gridDataJobDetails = {
      data: this.filteredJobDetails.slice(this.skip, this.skip + this.pageSize),
      total: this.filteredJobDetails.length,
    }
  }

  public onUserSelectionChange(selectedUserName: string): void {
    if (selectedUserName === this.defaultUser) {
      this.filteredJobDetails = this.jobDetails
    } else {
      this.filteredJobDetails = this.jobDetails.filter(
        (job) => job.userName === selectedUserName
      )
    }
    this.selectedUser = selectedUserName
    this.currentPage = 1
    this.skip = 0
    this.loadJobDetails()
  }

  public onRefreshClick(): void {
    this.#getAudioTranscribeJobDetails()
  }

  #handleSuccessResponse(): void {
    this.audioTranscribeFacade.getQueueFilesForAudioTranscribeSuccessResponse$
      .pipe(
        filter((success) => !!success),
        takeUntil(this.toDestroy$)
      )
      .subscribe((success) => {
        const responseMessage: ResponseMessage = {
          success: true,
          message: success.message,
        }

        this.convertDocumentFacade.setResponseMessage(responseMessage)

        this.getFilesForTranscribe()
        this.#getAudioTranscribeJobDetails()
        this.#resetAudioTranscribeState()
      })
  }

  #handleErrorResponse(): void {
    this.audioTranscribeFacade.getFilesForAudioTranscribeFailureResponse$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.notificationService.showError(error.message)

        this.#resetAudioTranscribeState()
      })

    this.audioTranscribeFacade.getQueueFilesForAudioTranscribeFailureResponse$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        const responseMessage: ResponseMessage = {
          success: false,
          message: error?.message,
        }
        this.convertDocumentFacade.setResponseMessage(responseMessage)

        this.#resetAudioTranscribeState()
      })

    this.audioTranscribeFacade.getJobDetailsOfAudioTranscribeFailureResponse$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.notificationService.showError(error.message)

        this.#resetAudioTranscribeState()
      })
  }

  public pageChanged(args: PageArgs): void {
    this.currentPage = args.pageNumber
    this.skip = (args.pageNumber - 1) * args.pageSize
    this.loadJobDetails()
  }

  public pageSizeChanged(args: PageArgs): void {
    this.currentPage = args.pageNumber
    this.pageSize = args.pageSize
    this.skip = (args.pageNumber - 1) * args.pageSize
    this.loadJobDetails()
  }

  public formatDuration(value: string): string {
    if (value === 'N/A') {
      return value
    }
    const [hours, minutes, seconds] = value.split(':').map(Number)
    return `${hours}h ${minutes}m ${seconds}s`
  }

  public onSelect(e: any): void {
    this.tabStatus = e.index
  }

  public toggleAll(event: Event): void {
    const isChecked = (event.target as HTMLInputElement).checked
    this.isHeaderCheckboxChecked = isChecked
    this.fileTypeDetails.forEach((item) => (item.selected = isChecked))
    this.selectedFileTypes = isChecked ? [...this.fileTypeDetails] : []
  }

  public onRowCheckboxChange(dataItem: SelectableTranscribeQueue): void {
    this.cdr.markForCheck()
    this.selectedFileTypes = this.fileTypeDetails.filter(
      (item) => item.selected
    )
    this.isHeaderCheckboxChecked = false
    this.isHeaderCheckboxChecked = this.fileTypeDetails.every(
      (item) => item.selected
    )
  }

  #resetAudioTranscribeState(): void {
    this.audioTranscribeFacade.resetAudioTranscribeState([
      'getFilesForAudioTranscribeSuccessResponse',
      'getFilesForAudioTranscribeFailureResponse',
      'queueFilesForAudioTranscribeSuccessResponse',
      'queueFilesForAudioTranscribeFailureResponse',
      'getJobDetailsOfAudioTranscribeSuccessResponse',
      'getJobDetailsOfAudioTranscribeFailureResponse',
    ])
  }

  public ngOnDestroy(): void {
    this.convertDocumentFacade.resetStartConvertDocument()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
