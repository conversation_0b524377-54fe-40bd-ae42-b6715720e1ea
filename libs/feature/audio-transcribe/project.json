{"name": "feature-audio-transcribe", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/audio-transcribe/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/audio-transcribe/ng-package.json", "tailwindConfig": "libs/feature/audio-transcribe/tailwind.config.js"}, "configurations": {"production": {"tsConfig": "libs/feature/audio-transcribe/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/audio-transcribe/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/audio-transcribe/jest.config.ts", "passWithNoTests": true}}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/feature/audio-transcribe/**/*.ts", "libs/feature/audio-transcribe/**/*.html", "libs/feature/audio-transcribe/package.json"]}}}}