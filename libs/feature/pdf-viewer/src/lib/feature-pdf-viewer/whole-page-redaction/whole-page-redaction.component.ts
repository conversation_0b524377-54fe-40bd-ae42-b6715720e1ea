import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import {
  RedactionSet,
  PageSelectionOption,
  WholePageRedaction,
} from '@venio/data-access/review'
import { Subject } from 'rxjs'
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'

@Component({
  selector: 'venio-whole-page-redaction',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    InputsModule,
    LabelModule,
    DropDownsModule,
    ReactiveFormsModule,
    ButtonsModule,
  ],
  templateUrl: './whole-page-redaction.component.html',
  styleUrls: ['./whole-page-redaction.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WholePageRedactionComponent
  implements OnInit, OnDestroy, WholePageRedaction
{
  public solidRectangles: RedactionSet[]

  public highlights: RedactionSet[]

  public currentPageNumber: number

  public pageCount: number

  public pageList: number[]

  private toDestroy$: Subject<void> = new Subject<void>()

  public redactionForm: FormGroup

  public pageSelectionOption = PageSelectionOption

  public errors: { [key: string]: string }

  constructor(
    public dialog: DialogRef,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef
  ) {}

  public ngOnInit(): void {
    this.redactionForm = this.fb.group({
      pageSelection: [PageSelectionOption.CURRENT_PAGE, Validators.required],
      pageFrom: [1, Validators.min(1)],
      pageTo: [this.pageCount, Validators.min(1)],
      exceptPage: [this.currentPageNumber, Validators.min(1)],
      redactionSet: [null, Validators.required],
    })
    this.pageList = this.createPageList()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  private createPageList(): number[] {
    return Array.from({ length: this.pageCount }, (_, index) => index + 1)
  }

  public onSubmit(): void {
    this.cdr.markForCheck()
    this.errors = {}
    if (this.redactionForm.get('redactionSet').invalid)
      this.errors['redactionSet'] = 'Redaction set is required.'
    const pageSelectionControl = this.redactionForm.get('pageSelection')
    const pageFrom = +this.redactionForm.get('pageFrom').value
    const pageTo = +this.redactionForm.get('pageTo').value
    if (
      pageSelectionControl.value === PageSelectionOption.PAGE_RANGE &&
      pageFrom > pageTo
    )
      this.errors['pageFrom'] = 'Range value is invalid'

    if (this.redactionForm.valid) this.dialog.close(this.redactionForm.value)
  }
}
