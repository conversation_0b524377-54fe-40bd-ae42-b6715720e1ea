import { ComponentFixture, TestBed } from '@angular/core/testing'
import { WholePageRedactionComponent } from './whole-page-redaction.component'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { DialogRef } from '@progress/kendo-angular-dialog'

describe('WholePageRedactionComponent', () => {
  let component: WholePageRedactionComponent
  let fixture: ComponentFixture<WholePageRedactionComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [WholePageRedactionComponent],
      providers: [
        provideNoopAnimations(),
        {
          provide: DialogRef,
          useValue: {
            close: (): void => {},
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(WholePageRedactionComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
