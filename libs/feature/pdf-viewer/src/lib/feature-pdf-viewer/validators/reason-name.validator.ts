import { AsyncValidatorFn } from '@angular/forms'
import { PdfViewerFacade } from '@venio/data-access/review'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { catchError, map, of } from 'rxjs'
import { HttpErrorResponse } from '@angular/common/http'

export function reasonNameValidator(
  pdfFacade: PdfViewerFacade,
  projectId: number
): AsyncValidatorFn {
  return (control) => {
    return pdfFacade.lookupRedactionReasonExists(projectId, control.value).pipe(
      catchError((error: unknown) => of((error as HttpErrorResponse)?.error)),
      map((response: ResponseModel) => {
        return response.status === 'Success' ? null : { exist: true }
      })
    )
  }
}
