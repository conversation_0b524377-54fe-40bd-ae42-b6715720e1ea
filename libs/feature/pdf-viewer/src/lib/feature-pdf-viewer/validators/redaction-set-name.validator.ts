import { AsyncValidatorFn } from '@angular/forms'
import { PdfViewerFacade } from '@venio/data-access/review'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { catchError, map, of } from 'rxjs'
import { HttpErrorResponse } from '@angular/common/http'

export function redactionSetNameValidator(
  pdfFacade: PdfViewerFacade,
  projectId: number,
  currentRedactionSetId: number
): AsyncValidatorFn {
  return (control) => {
    return pdfFacade
      .lookupRedactionSet(projectId, control.value, currentRedactionSetId)
      .pipe(
        catchError((error: unknown) => of((error as HttpErrorResponse)?.error)),
        map((response: ResponseModel) => {
          return response.status === 'Success' ? null : { exist: true }
        })
      )
  }
}
