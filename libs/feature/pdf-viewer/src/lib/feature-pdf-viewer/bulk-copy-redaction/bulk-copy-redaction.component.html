<!-- Dialog base should be created in future with refrence with will contain the diaog title and dialog actions, and instance will be used-->
<!-- PLS NOTE: tailwind classes for e.g t-basis-1/3 , t-px-7, t-gap-5 or t-h-32 not working and most of the classes are not working here so i have created a custom classes which will be replaced with tailwind classes once this issue is resolved!-->

<kendo-dialog-titlebar (close)="dialog.close()">
  Copy Redaction
</kendo-dialog-titlebar>

<div class="v-dialog-content t-py-2">
  <div class="v-dialog-content-icon"></div>
  <div class="v-dialog-content-message">
    <div>
      <form [formGroup]="redactionForm" class="v-custom-grey-bg">
        <div class="t-flex t-gap-[4%]">
          <div class="t-w-[48%]">
            <h1
              class="t-w-full t-text-xs t-uppercase t-tracking-widest t-mb-3 t-font-bold">
              Source Criteria
            </h1>
            <div class="t-flex t-gap-3 t-flex-col">
              <div class="t-flex t-flex-col t-gap-1">
                <kendo-label
                  for="redactionSet"
                  class="t-text-xs t-uppercase t-tracking-widest">
                  Redaction set
                </kendo-label>

                <kendo-multiselect
                  #redactionSet
                  [data]="redactionSets"
                  textField="name"
                  valueField="id"
                  formControlName="selectedRedactionSets"
                  placeholder="Select redaction sets"></kendo-multiselect>
                <div
                  *ngIf="displayMessage?.selectedRedactionSets"
                  class="t-text-error">
                  {{ displayMessage?.selectedRedactionSets }}
                </div>
              </div>

              <div class="t-flex t-flex-col t-gap-1">
                <kendo-label
                  for="redactionSetIn"
                  class="t-text-xs t-uppercase t-tracking-widest">
                  Redaction sets in
                </kendo-label>

                <div class="t-flex t-gap-3 t-mt-2">
                  <div class="t-flex t-gap-1">
                    <input
                      #sourceAllPages
                      type="radio"
                      kendoRadioButton
                      [value]="'ALL_PAGES'"
                      formControlName="sourcePageSelection" />
                    <kendo-label
                      [for]="sourceAllPages"
                      text="All Pages"></kendo-label>
                  </div>

                  <div class="t-flex t-gap-1">
                    <input
                      #sourceAllSpecificPages
                      type="radio"
                      kendoRadioButton
                      [value]="'SPECIFIC_PAGES'"
                      formControlName="sourcePageSelection" />
                    <kendo-label
                      [for]="sourceAllSpecificPages"
                      text="Page Number"></kendo-label>
                  </div>
                </div>

                <div
                  class="t-flex t-items-center t-w-full t-mt-2"
                  *ngIf="
                    redactionForm.get('sourcePageSelection').value ===
                    'SPECIFIC_PAGES'
                  ">
                  <div class="t-flex t-flex-1">
                    <kendo-textbox
                      placeholder="Enter page number for e.g 1-3,5,7-9"
                      formControlName="sourcePageList">
                      <ng-template kendoTextBoxSuffixTemplate>
                        <kendo-svg-icon
                          kendoTooltip
                          [icon]="infoCircleIcon"
                          [title]="pageListMessage"></kendo-svg-icon>
                      </ng-template>
                    </kendo-textbox>
                  </div>
                </div>

                <div
                  class="t-flex"
                  *ngIf="
                    redactionForm.get('sourcePageSelection').value ===
                    'SPECIFIC_PAGES'
                  ">
                  <span
                    *ngIf="displayMessage?.sourcePageList"
                    class="!t-text-error">
                    {{ displayMessage?.sourcePageList }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="t-w-[48%]">
            <h1
              class="t-w-full t-text-xs t-uppercase t-tracking-widest t-mb-3 t-font-bold">
              Destination Criteria
            </h1>

            <div class="t-flex t-flex-col t-gap-3">
              <div class="t-flex t-flex-col t-gap-1">
                <kendo-label
                  for="copyRedactionTo"
                  class="t-text-xs t-uppercase t-tracking-widest">
                  Copy redaction to
                </kendo-label>

                <div class="t-flex t-gap-1">
                  <input
                    #destinationCurrentDocument
                    type="radio"
                    kendoRadioButton
                    [value]="'CURRENT_DOCUMENT'"
                    formControlName="destinationDocumentSelection" />
                  <kendo-label
                    [for]="destinationCurrentDocument"
                    text="Current Document"></kendo-label>
                </div>

                <div class="t-flex t-flex-col t-gap-3 t-mt-2">
                  <div class="t-flex t-px-4 t-flex-col t-gap-3">
                    <div class="t-flex">
                      <input
                        #destinationRemainingPages
                        type="radio"
                        kendoRadioButton
                        [value]="'ALL_REMAINING_PAGES'"
                        formControlName="destinationPageSelection" />
                      <kendo-label
                        [for]="destinationRemainingPages"
                        text="All Remaining Pages"
                        class="t-ml-2"></kendo-label>
                    </div>
                    <div class="t-flex t-px-0 t-gap-2">
                      <input
                        #destinationSpecificPages
                        type="radio"
                        kendoRadioButton
                        [value]="'SPECIFIC_PAGES'"
                        formControlName="destinationPageSelection" />
                      <kendo-label
                        [for]="destinationSpecificPages"
                        text="Page Number"></kendo-label>
                    </div>
                  </div>

                  <div
                    class="t-flex t-w-full t-flex-col t-gap-2"
                    *ngIf="
                      redactionForm.get('destinationPageSelection').value ===
                      'SPECIFIC_PAGES'
                    ">
                    <kendo-textbox
                      class="t-w-full"
                      placeholder="Enter page number for e.g 1-3,5,7-9"
                      formControlName="destinationPageList">
                      <ng-template kendoTextBoxSuffixTemplate>
                        <kendo-svg-icon
                          kendoTooltip
                          [icon]="infoCircleIcon"
                          [title]="pageListMessage"></kendo-svg-icon>
                      </ng-template>
                    </kendo-textbox>
                    <span
                      *ngIf="displayMessage?.destinationPageList"
                      class="t-text-error">
                      {{ displayMessage?.destinationPageList }}
                    </span>
                  </div>
                  <div class="t-flex">
                    <input
                      #destinationAllDocuments
                      type="radio"
                      kendoRadioButton
                      [value]="'ALL_DOCUMENTS'"
                      formControlName="destinationDocumentSelection" />
                    <kendo-label
                      [for]="destinationAllDocuments"
                      class="t-ml-2"
                      text="All documents in search result"></kendo-label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="t-block t-mt-3 t-w-full t-text-xs t-text-error">
          <strong>Note: </strong> We copy redaction based on coordinates of
          selected redaction. Any slight variation in image size/orientation
          will lead redaction being copied to incorrect location. As this is
          bulk operation, once redaction is applied it cannot be undone as bulk
          action, it has to be done one document at a time.
        </div>
      </form>
    </div>

    <kendo-dialog-actions>
      <div class="t-flex t-gap-4 t-justify-end">
        <button
          kendoButton
          (click)="onSubmit()"
          class="v-custom-secondary-button"
          themeColor="secondary"
          fillMode="outline"
          data-qa="save-button">
          <span>Apply</span>
        </button>
        <button
          kendoButton
          (click)="dialog.close(null)"
          themeColor="dark"
          fillMode="outline"
          data-qa="cancel-button">
          CANCEL
        </button>
      </div>
    </kendo-dialog-actions>
  </div>
</div>
