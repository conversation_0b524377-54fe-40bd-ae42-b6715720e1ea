import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BulkCopyRedactionComponent } from './bulk-copy-redaction.component'
import { DialogRef } from '@progress/kendo-angular-dialog'
import {
  DocumentsFacade,
  FieldFacade,
  PdfViewerFacade,
  PdfViewerService,
  SearchFacade,
} from '@venio/data-access/review'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('BulkCopyRedactionComponent', () => {
  let component: BulkCopyRedactionComponent
  let fixture: ComponentFixture<BulkCopyRedactionComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BulkCopyRedactionComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),

        PdfViewerFacade,
        PdfViewerService,
        SearchFacade,
        FieldFacade,
        DocumentsFacade,
        provideNoopAnimations(),
        provideMockStore({}),
        {
          provide: DialogRef,
          useValue: {
            close: (): void => {},
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(BulkCopyRedactionComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
