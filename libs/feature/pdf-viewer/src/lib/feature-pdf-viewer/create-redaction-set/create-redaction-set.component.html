<!-- Dialog base should be created in future with refrence with will contain the diaog title and dialog actions, and instance will be used-->
<!-- PLS NOTE: tailwind classes for e.g t-basis-1/3 , t-px-7, t-gap-5 or t-h-32 not working and most of the classes are not working here so i have created a custom classes which will be replaced with tailwind classes once this issue is resolved!-->

<kendo-dialog-titlebar (close)="dialog.close()">
  Create Redaction Set
</kendo-dialog-titlebar>

<div class="v-dialog-content t-py-2">
  <div class="v-dialog-content-icon"></div>
  <div class="v-dialog-content-message">
    <div>
      <form [formGroup]="redactionSetForm" class="v-custom-grey-bg">
        <div class="t-flex t-gap-3 t-flex-col">
          <div class="t-flex t-flex-col t-gap-1">
            <kendo-label
              for="redactionSet"
              class="t-text-xs t-uppercase t-tracking-widest">
              Redaction set
            </kendo-label>

            <kendo-dropdownlist
              #redactionSet
              [data]="redactionTypes"
              textField="redactionTypeName"
              valueField="id"
              formControlName="redactionType"
              [defaultItem]="{
                redactionTypeName: 'Select redaction type',
                id: null
              }"></kendo-dropdownlist>
            <div *ngIf="displayMessage?.redactionType" class="t-text-error">
              {{ displayMessage?.redactionType }}
            </div>
          </div>

          <!-- color options-->

          <ng-container *ngIf="showRectangleFormControls">
            <div class="t-flex t-flex-col t-gap-1">
              <kendo-label
                for="color"
                class="t-text-xs t-uppercase t-tracking-widest"
                >Color</kendo-label
              >
              <kendo-dropdownlist
                [data]="['Black', 'White']"
                formControlName="color"></kendo-dropdownlist>
              <div *ngIf="displayMessage?.color" class="t-text-error">
                {{ displayMessage?.color }}
              </div>
            </div>

            <div class="t-flex t-flex-col t-gap-1">
              <kendo-label
                for="caption"
                class="t-text-xs t-uppercase t-tracking-widest"
                >Caption</kendo-label
              >
              <kendo-textbox formControlName="caption"></kendo-textbox>
              <div *ngIf="displayMessage?.caption" class="t-text-error">
                {{ displayMessage?.caption }}
              </div>
            </div>
          </ng-container>

          <div class="t-flex t-flex-col t-gap-1">
            <kendo-label
              for="reason"
              class="t-text-xs t-uppercase t-tracking-widest">
              Reason
            </kendo-label>
            <kendo-dropdownlist
              #reason
              [data]="reasons"
              textField="reason"
              valueField="id"
              formControlName="reason"
              [defaultItem]="{
                reason: 'Select reason',
                id: null
              }">
            </kendo-dropdownlist>

            <span
              (click)="manageReason()"
              class="v-text-blue t-mt-2 t-cursor-pointer">
              {{
                isManageReasonVisible
                  ? 'Hide Manage Reason'
                  : 'Show Manage Reason'
              }}</span
            >

            <div *ngIf="displayMessage?.reason" class="t-text-error">
              {{ displayMessage?.reason }}
            </div>
          </div>

          <ng-template #manageReasonComponent></ng-template>
          <div class="t-flex t-flex-col t-gap-1">
            <kendo-label
              for="name"
              class="t-text-xs t-uppercase t-tracking-widest">
              Name
            </kendo-label>

            <kendo-textbox #name formControlName="name"></kendo-textbox>
            <div *ngIf="displayMessage?.name" class="t-text-error">
              {{ displayMessage?.name }}
            </div>
          </div>
        </div>
      </form>
    </div>

    <kendo-dialog-actions>
      <div class="t-flex t-gap-4 t-justify-end">
        <button
          kendoButton
          (click)="onSubmit()"
          class="v-custom-secondary-button"
          themeColor="secondary"
          fillMode="outline"
          data-qa="save-button">
          <span>Create</span>
        </button>
        <button
          kendoButton
          (click)="dialog.close(null)"
          themeColor="dark"
          fillMode="outline"
          data-qa="cancel-button">
          CANCEL
        </button>
      </div>
    </kendo-dialog-actions>
  </div>
</div>
