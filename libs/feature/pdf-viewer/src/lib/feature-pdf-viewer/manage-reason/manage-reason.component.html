<div class="t-flex t-w-full t-flex-col t-gap-1">
  <form [formGroup]="reasonForm">
    <div class="t-flex t-flex-col t-gap-1">
      <kendo-label for="reason" class="t-text-xs t-uppercase t-tracking-widest">
        Reason
      </kendo-label>

      <div class="t-flex t-gap-2">
        <kendo-textbox
          placeholder="Enter Reason"
          formControlName="reason"></kendo-textbox>
        <button
          kendoButton
          *ngIf="isEdit"
          fillMode="clear"
          size="none"
          class="!t-p-1"
          data-qa="iconReset"
          (click)="onReset()"
          #reset>
          <span
            venioSvgLoader
            [parentElement]="reset.element"
            svgUrl="assets/svg/icon-grid-action-refresh.svg"
            hoverColor="#FFBB12"
            height=".95rem"
            width=".95rem"></span>
        </button>

        <button
          kendoButton
          (click)="onSubmit()"
          class="v-custom-secondary-button"
          themeColor="secondary"
          fillMode="outline"
          data-qa="btn-submit">
          <span>{{ isEdit ? 'Update' : 'Add' }}</span>
        </button>
      </div>

      <div *ngIf="displayMessage?.reason" class="t-w-full t-text-error">
        {{ displayMessage?.reason }}
      </div>
    </div>

    <div class="t-flex t-w-full t-gap-3 t-py-1 t-mt-2">
      <kendo-grid
        [kendoGridBinding]="reasons"
        [resizable]="true"
        [sortable]="true"
        [pageSize]="10"
        scrollable="virtual"
        [reorderable]="false"
        [rowHeight]="30"
        class="t-w-full v-redaction-table">
        <kendo-grid-column
          [headerStyle]="{
            color: '#2F3080',
            'later-spacing': '0.42px',
            'text-transform': 'capitalize'
          }"
          [field]="'reason'"
          [width]="260"
          [title]="'Reason'"></kendo-grid-column>

        <kendo-grid-column
          [headerStyle]="{
            color: '#2F3080'
          }"
          class="!t-py-[0.6rem]"
          title="Action"
          [width]="85"
          [reorderable]="false">
          <ng-template
            kendoGridCellTemplate
            let-dataItem
            let-rowIndex="rowIndex">
            <div class="t-flex">
              <kendo-buttongroup class="t-flex t-gap-2" kendoTooltip>
                <button
                  kendoButton
                  fillMode="clear"
                  size="none"
                  class="!t-p-[0.3rem] t-w-1/3"
                  data-qa="icon edit"
                  (click)="editReason(dataItem)"
                  title="Edit"
                  #iconedit>
                  <span
                    venioSvgLoader
                    [parentElement]="iconedit.element"
                    svgUrl="assets/svg/icon-action-grid-pencil.svg"
                    height="0.9rem"
                    width="1rem"></span>
                </button>

                <button
                  kendoButton
                  fillMode="clear"
                  size="none"
                  class="!t-p-[0.3rem] t-w-1/3 t-ml-3"
                  data-qa="icon delete"
                  (click)="deleteReason(dataItem)"
                  title="Delete"
                  #iconDelete>
                  <span
                    venioSvgLoader
                    [parentElement]="iconDelete.element"
                    svgUrl="assets/svg/Icon-material-delete.svg"
                    height=".9rem"
                    width="1rem"></span>
                </button>
              </kendo-buttongroup>
            </div>

            <!-- <button (click)="editReason(dataItem)" #editReason>            
              <span
                venioSvgLoader
                [parentElement]="editReason.element"
                hoverColor="#FFFFFF"
                [svgUrl]="'assets/svg/icon-action-grid-pencil.svg'"
                height="1.3rem"
                width="1.3rem"></span>
            </button> -->
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
    </div>
  </form>
</div>
