import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DocumentsFacade,
  SearchFacade,
  TiffViewerPayload,
  Viewer,
} from '@venio/data-access/review'
import { UserFacade } from '@venio/data-access/common'
import { Subject, filter, map, takeUntil } from 'rxjs'
import { SafeResourceUrl } from '@angular/platform-browser'
import { ControlSettingService } from '@venio/data-access/control-settings'
import { ActivatedRoute } from '@angular/router'
import {
  <PERSON><PERSON><PERSON>ceTimer,
  ShortcutManager,
  UrlSanitizer,
} from '@venio/util/utilities'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageContent,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import {
  ReviewPanelFacade,
  UpdateDocumentHistoryPage,
} from '@venio/data-access/document-utility'
import {
  ShortcutKeyBindings,
  ShortcutKeyDescriptions,
} from '@venio/shared/models/constants'
import { LocalStorage } from '@venio/shared/storage'
import { toSignal } from '@angular/core/rxjs-interop'

@Component({
  selector: 'venio-feature-tiff-viewer',
  standalone: true,
  imports: [CommonModule, UrlSanitizer],
  templateUrl: './feature-tiff-viewer.component.html',
  styleUrl: './feature-tiff-viewer.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FeatureTiffViewerComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  public iframeSrc: SafeResourceUrl

  public isViewerWindow = false

  private toDestory$: Subject<void> = new Subject<void>()

  private tiffViewerUrl = ''

  public isImageTypePdf: boolean

  private searchGUID: string

  @ViewChild('tiffViewer') public tiffViewer: ElementRef

  @ViewChild('tiffViewerContainer', { static: false })
  public tiffViewerContainer: ElementRef<HTMLDivElement>

  public get isViewerPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isViewerPanelPopout')
  }

  private get docShareToken(): number {
    return this.activatedRoute.snapshot.queryParams['docShareToken']
  }

  public currentUserDetails = toSignal(
    this.userFacade.selectCurrentUserDetails$
  )

  private shortcutManager: ShortcutManager

  constructor(
    private documentFacade: DocumentsFacade,
    private cdr: ChangeDetectorRef,
    private controlSettingService: ControlSettingService,
    private userFacade: UserFacade,
    private searchFacade: SearchFacade,
    private reviewPanelFacade: ReviewPanelFacade,
    private iframeMessengerService: IframeMessengerService,
    private activatedRoute: ActivatedRoute
  ) {}

  public ngOnInit(): void {
    this.loadTiffViewer()
    this.selectSearchGuid()
    this.#handleLegacyPageTiffNotification()
    this.#hanldeLegacyTiffRedactionNotifcation()
  }

  private selectSearchGuid(): void {
    this.searchFacade.getSearchTempTables$
      .pipe(takeUntil(this.toDestory$))
      .subscribe((tempTableParams) => {
        this.searchGUID = tempTableParams.searchGuid
      })
  }

  private loadTiffViewer(): void {
    this.documentFacade.onLoadTiffViewer
      .pipe(takeUntil(this.toDestory$))
      .subscribe((payload: TiffViewerPayload) => {
        this.cdr.markForCheck()
        this.tiffViewerUrl =
          this.controlSettingService.getControlSetting.WEB_BASE_URL +
          '/Redaction/Redaction.aspx'
        const paramDocShareToken = this.docShareToken
          ? `&docShareToken=${this.docShareToken}`
          : ''
        this.iframeSrc = `${this.tiffViewerUrl}?projectid=${
          payload.projectId
        }&FileId=${payload.fileId}&userid=${
          this.currentUserDetails().userId
        }&tiffAllPagesFromViewer=true&allowtiffing=true&DocumentSharePermission=true&isTiffingFromVod=true&searchGuid=${
          this.searchGUID
        }&module=VodSearch${paramDocShareToken}`

        this.tiffViewerContainer.nativeElement.focus()
        this.cdr.detectChanges()
      })
  }

  public ngAfterViewInit(): void {
    this.documentFacade.viewerComponentReady = Viewer.Tiff
    this.#initTiffPaginationShortcutKeys()
    this.tiffViewerContainer.nativeElement.focus()
    this.tiffViewer.nativeElement.blur()
  }

  private tiffShortcutHandlers: Partial<{
    [key in ShortcutKeyBindings]: {
      description: ShortcutKeyDescriptions
      handler: (event: Event) => void
    }
  }> = {
    [ShortcutKeyBindings.MOVE_PREVIOUS_PAGE]: {
      description: ShortcutKeyDescriptions.MOVE_PREVIOUS_PAGE,
      handler: (event) =>
        this.#navigatePage(event, ShortcutKeyBindings.MOVE_PREVIOUS_PAGE),
    },
    [ShortcutKeyBindings.MOVE_NEXT_PAGE]: {
      description: ShortcutKeyDescriptions.MOVE_PREVIOUS_PAGE,
      handler: (event) =>
        this.#navigatePage(event, ShortcutKeyBindings.MOVE_NEXT_PAGE),
    },
    [ShortcutKeyBindings.FIRST_PAGE]: {
      description: ShortcutKeyDescriptions.FIRST_PAGE,
      handler: (event) =>
        this.#navigatePage(event, ShortcutKeyBindings.FIRST_PAGE),
    },
    [ShortcutKeyBindings.LAST_PAGE]: {
      description: ShortcutKeyDescriptions.LAST_PAGE,
      handler: (event) =>
        this.#navigatePage(event, ShortcutKeyBindings.LAST_PAGE),
    },
  }

  #navigatePage(event: Event, key: string): void {
    event.preventDefault()
    event.stopPropagation()
    const origin = this.controlSettingService.getControlSetting.WEB_BASE_URL
    this.tiffViewer.nativeElement.contentWindow.postMessage(
      {
        type: key,
        data: {
          type: key,
        },
      },
      origin
    )
  }

  #initTiffPaginationShortcutKeys(): void {
    this.shortcutManager = new ShortcutManager()
    Object.entries(this.tiffShortcutHandlers).forEach(
      ([combo, { description, handler }]) => {
        this.shortcutManager.bind(combo, handler, { description })
      }
    )
  }

  #removeTiffPaginationShortcutKeys(): void {
    Object.entries(this.tiffShortcutHandlers).forEach(
      ([combo, { handler }]) => {
        this.shortcutManager.unbind(combo, undefined, handler)
      }
    )
  }

  #hanldeLegacyTiffRedactionNotifcation(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (message) =>
            message.type === 'LEGACY' &&
            (message.payload as MessageContent).type ===
              MessageType.TIFF_REDACTION_UPDATE &&
            Boolean(message.payload?.['content']?.['status'])
        ),
        map((message) => message.payload as MessageContent),
        takeUntil(this.toDestory$)
      )
      .subscribe(() => {
        this.#sendNotificaitonToDocumentHistory()
        this.setFocus()
      })
  }

  #handleLegacyPageTiffNotification(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (message) =>
            message.type === 'LEGACY' &&
            ((message.payload as MessageContent).type ===
              MessageType.TIFF_PAGE_LOADED ||
              (message.payload as MessageContent).type ===
                MessageType.TIFF_PAGE_NAVIGATION) &&
            Boolean(message.payload?.['content']?.['status'])
        ),
        map((message) => message.payload as MessageContent),
        takeUntil(this.toDestory$)
      )
      .subscribe(() => {
        this.setFocus()
      })
  }

  #sendNotificaitonToDocumentHistory(): void {
    if (!this.isViewerPanelPopout) {
      this.reviewPanelFacade.refrehDocumentHistory(
        UpdateDocumentHistoryPage.UPDATE_TIFF_REDACTION
      )
      return
    }
    this.#sendReactionNotificationToParentWindow()
  }

  /**
   * Sends a redaction notification to the parent window.
   * @returns {void} This method does not return anything.
   */
  #sendReactionNotificationToParentWindow(): void {
    if (!this.isViewerPanelPopout) return
    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      payload: {
        type: MessageType.REDACTION_UPDATE,
        content: {
          isRefreshPage: true,
        },
      },
      eventTriggeredFor: 'ALL_WINDOW',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
    })
  }

  @DebounceTimer(500)
  private setFocus(): void {
    this.tiffViewerContainer.nativeElement.focus()
    this.cdr.markForCheck()
  }

  public ngOnDestroy(): void {
    this.#removeTiffPaginationShortcutKeys()
    this.toDestory$.next()
    this.toDestory$.complete()
  }
}
