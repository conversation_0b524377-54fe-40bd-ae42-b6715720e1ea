import {
  ChangeDetectionStrategy,
  Component,
  ComponentRef,
  computed,
  effect,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
  signal,
  ViewContainerRef,
} from '@angular/core'
import {
  PasswordBankFacade,
  PasswordBankModel,
} from '@venio/data-access/review'
import { trashIcon } from '@progress/kendo-svg-icons'
import { toSignal } from '@angular/core/rxjs-interop'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import {
  ConfirmationDialogComponent,
  VenioNotificationService,
} from '@venio/feature/notification'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import { Subject, take, takeUntil } from 'rxjs'
import { ImportPasswordBankComponent } from '../import-password-bank/import-password-bank.component'

@Component({
  selector: 'venio-lotus-notes-password',
  templateUrl: './lotus-notes-password.component.html',
  styleUrl: './lotus-notes-password.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LotusNotesPasswordComponent implements OnInit, OnDestroy {
  public projectId = input.required<number>()

  private readonly passwordBankFacade = inject(PasswordBankFacade)

  private readonly notificationService = inject(VenioNotificationService)

  private readonly dialogService = inject(DialogService)

  private readonly formBuilder = inject(FormBuilder)

  private vcr = inject(ViewContainerRef)

  private readonly toDestroy$ = new Subject<void>()

  public form: FormGroup

  private confirmationDialogRef: DialogRef

  protected readonly trashIcon = trashIcon

  public selectedFileName: string

  public selectedPasswordIds: number[] = []

  public filePath = signal<string | null>(null)

  public enablePasswordInput = signal(false) // disable password textbox if user doesn't select password option

  public isAddingPassword = signal(false) // to show spinner on add password button

  public deleteInProgress = signal(false) // to show spinner on delete button

  public readonly passwordBanks = toSignal(
    this.passwordBankFacade.selectPasswordBanks$
  )

  public readonly passwordAddResponse = toSignal(
    this.passwordBankFacade.selectAddPasswordResponse$
  )

  public readonly passwordDeleteResponse = toSignal(
    this.passwordBankFacade.selectDeletePasswordResponse$
  )

  public readonly lotusNotesPasswords = computed<PasswordBankModel[]>(() =>
    this.passwordBanks().filter((p) => p.isUserIdFile)
  )

  constructor() {
    this.#initForm()
    this.#setPasswordValidators()
    this.#enableDisablePasswordInput()
    this.#resetFormAfterAddingPassword()
    this.#reenableDeleteButtonAfterDeletion()
  }

  #setPasswordValidators(): void {
    // Set/Remove validators for password input based on whether a password option is selected or not
    effect(() => {
      const passwordInputControl = this.form.get('passwordInput')

      if (this.enablePasswordInput()) {
        passwordInputControl?.setValidators([Validators.required])
      } else {
        passwordInputControl?.clearValidators()
      }
      passwordInputControl?.updateValueAndValidity()
    })
  }

  #enableDisablePasswordInput(): void {
    // enable/disable password input based on the selected password option
    effect(() => {
      if (!this.enablePasswordInput() || this.isAddingPassword()) {
        this.form.get('passwordInput')?.disable()
      } else {
        this.form.get('passwordInput')?.enable()
      }
    })

    // disable password input if password is being added (in progress)
    effect(() => {
      if (this.isAddingPassword()) {
        this.form.get('hasNsfPassword')?.disable()
      } else {
        this.form.get('hasNsfPassword')?.enable()
      }
    })
  }

  #resetFormAfterAddingPassword(): void {
    // to reset the form after successful submission
    effect(
      () => {
        const response = this.passwordAddResponse()
        if (response?.status?.toLocaleLowerCase() === 'success') {
          this.form.reset({
            passwordBankId: null,
            passwordInput: null,
            hasNsfPassword: false,
            idFileData: null,
            originalUserIdFilePath: '',
          })
          this.filePath.set('')
        }
        this.isAddingPassword.set(false)
      },
      { allowSignalWrites: true }
    )
  }

  #reenableDeleteButtonAfterDeletion(): void {
    // to stop delete spinner after successful deletion
    effect(
      () => {
        const response = this.passwordDeleteResponse()
        if (response?.message) {
          this.deleteInProgress.set(false)
          this.selectedPasswordIds = []
        }
      },
      { allowSignalWrites: true }
    )
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngOnInit(): void {
    this.#fetchPasswordList()
  }

  #initForm(): void {
    this.form = this.formBuilder.group({
      passwordBankId: null,
      passwordInput: [{ value: null, disabled: true }],
      hasNsfPassword: false,
      idFileData: null,
      originalUserIdFilePath: ['', Validators.required],
    })

    this.form.get('hasNsfPassword')?.valueChanges.subscribe((value) => {
      this.enablePasswordInput.set(value)
    })
  }

  public openImportNsfUserIdPasswordDialog(): void {
    const dialogRef = this.dialogService.open({
      content: ImportPasswordBankComponent,
      cssClass: 'v-dialog-import-password',
      width: '60rem',
      appendTo: this.vcr,
    })
    const instance: ComponentRef<ImportPasswordBankComponent> =
      dialogRef.content
    instance.setInput('isForLotusNotes', true)
    instance.setInput('projectId', this.projectId())
  }

  // handle add password button click
  public addPassword(): void {
    if (!this.form.valid) {
      this.notificationService.showError('Please fill in all required fields.')
      return
    }

    const hasNsfPassword = this.form.get('hasNsfPassword')?.value // check if user selected password option
    const pwd = this.form.get('passwordInput')?.value
    const passwordBankModel: PasswordBankModel = {
      password: hasNsfPassword ? pwd : null,
      isUserIdFile: true,
      originalUserIdFilePath: this.form.get('originalUserIdFilePath')?.value,
    }

    this.isAddingPassword.set(true) // show spinner on add password button
    this.passwordBankFacade.addPassword(
      this.projectId(),
      passwordBankModel,
      this.form.get('idFileData')?.value
    )
  }

  // handle delete button click on each row
  public deletePassword(dataItem: any): void {
    this.#confirmDeletePassword([dataItem?.passwordBankId])
  }

  // handle delete button click on top of grid
  public deleteMultiplePasswords(): void {
    this.#confirmDeletePassword(this.selectedPasswordIds)
  }

  #fetchPasswordList(): void {
    this.passwordBankFacade.fetchPasswordBanks(this.projectId())
  }

  #confirmDeletePassword(deletePasswordIds: number[]): void {
    this.confirmationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-delete',
      width: '35rem',
      appendTo: this.vcr,
    })
    this.confirmationDialogRef.content.instance.title = 'Delete Confirmation'
    this.confirmationDialogRef.content.instance.message =
      deletePasswordIds.length > 1
        ? 'Are you sure you want to delete multiple passwords?'
        : 'Are you sure you want to delete this password?'

    this.confirmationDialogRef.result
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe((result) => {
        if (typeof result === 'boolean' && result === true) {
          this.deleteInProgress.set(true) // show spinner on delete button
          this.passwordBankFacade.deletePassword(
            this.projectId(),
            deletePasswordIds
          )
        }
      })
  }

  public passwordSelectionChange(passowrdIds: number[]): void {
    this.selectedPasswordIds = passowrdIds
  }

  // handle file selection for NSF user id file
  public handleFileSelection(event: Event): void {
    const input = event.target as HTMLInputElement
    if (input.files && input.files.length > 0) {
      const file = input.files[0]
      if (file.name.endsWith('.id')) {
        this.form.get('idFileData')?.setValue(file)
        this.form.get('originalUserIdFilePath')?.setValue(file.name)
        this.filePath.set(file.name)
        input.value = ''
      }
    }
  }
}
