<kendo-dialog-titlebar (close)="closeImportPasswordDialog()">
  <div>Password List</div>
</kendo-dialog-titlebar>
<div class="t-flex t-flex-col t-items-start t-gap-4 t-mb-4">
  <!-- Browse File -->
  <div class="t-flex t-flex-row t-w-full t-items-center t-gap-[15px]">
    <span>
      <kendo-label text="Browse file"></kendo-label>
    </span>
    <span class="t-w-[40%]">
      <input
        kendoTextBox
        placeholder="Choose a text file (*.txt, *.csv)"
        [disabled]="isImporting()"
        [value]="filePath() || ''"
        readonly />
    </span>
    <span>
      <button
        kendoButton
        (click)="fileInput.click()"
        [disabled]="isImporting()"
        class="v-custom-secondary-button"
        themeColor="secondary"
        fillMode="outline">
        Browse
      </button>
    </span>
    <input
      #fileInput
      type="file"
      class="t-hidden"
      [disabled]="isImporting()"
      (change)="handleFileSelection($event)"
      accept=".txt, .csv"
      multiple />
    <span
      class="t-cursor-pointer t-text-center t-rounded-full t-bg-[#2F3080] t-font-black t-text-white t-h-[24px] t-w-[24px] t-flex t-items-center t-justify-center"
      (click)="openPasswordBankHelpDialog()"
      >?</span
    >
  </div>

  <!-- Search Field -->
  <div class="t-flex t-justify-end t-gap-2">
    <kendo-textbox
      class="!t-border-[#ccc] !t-w-[25rem]"
      placeholder="Search"
      (valueChange)="onSearchTextChange($event)"
      [clearButton]="true">
      <ng-template kendoTextBoxSuffixTemplate>
        <button
          kendoButton
          fillMode="clear"
          class="t-text-[#1EBADC]"
          imageUrl="assets/svg/icon-updated-search.svg"></button>
      </ng-template>
    </kendo-textbox>
  </div>
</div>

<!-- Password Import Grid -->
<kendo-grid
  class="t-grid t-border-b-1 t-relative t-overflow-y-auto"
  [kendoGridBinding]="filteredData()"
  [sortable]="false"
  scrollable="virtual"
  [rowHeight]="36"
  [pageSize]="30"
  [groupable]="false"
  [reorderable]="false"
  [sort]=""
  [height]="300"
  [resizable]="true"
  [loading]="isGridLoading()"
  kendoGridSelectBy="seq"
  [ngClass]="{
    'k-disabled t-opacity-60': isImporting()
  }"
  [(selectedKeys)]="selectedPasswords"
  (selectedKeysChange)="passwordSelectionChange($event)"
  kendoGridSelectBy="seq"
  [selectable]="{ checkboxOnly: true, mode: 'multiple' }">
  <kendo-grid-checkbox-column
    [headerClass]="{ 'k-checkbox-wrap': true }"
    [width]="40"
    [resizable]="false">
    <ng-template kendoGridHeaderTemplate>
      <input
        type="checkbox"
        kendoCheckBox
        id="selectAllCheckboxId"
        kendoGridSelectAllCheckbox
        [state]="selectAllState()"
        (selectAllChange)="onSelectAllChange($event)" />
      <label class="k-checkbox-label" for="selectAllCheckboxId"></label>
    </ng-template>
  </kendo-grid-checkbox-column>
  <kendo-grid-column
    headerClass="t-text-primary"
    field="seq"
    title="#"
    [width]="75"></kendo-grid-column>
  <kendo-grid-column
    headerClass="t-text-primary"
    field="password"
    title="Password"
    [width]="200"></kendo-grid-column>
  <kendo-grid-column
    headerClass="t-text-primary"
    field="originalUserIdFilePath"
    title="NSF User Id File"
    *ngIf="isForLotusNotes()"
    [width]="300"></kendo-grid-column>
  <kendo-grid-column
    headerClass="t-text-primary"
    field="isAdded"
    title="Added"
    [width]="80">
    <ng-template kendoGridCellTemplate let-dataItem>
      <input
        type="checkbox"
        class="t-m-1"
        [checked]="dataItem.isAdded"
        [disabled]="true"
        kendoCheckBox />
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column
    headerClass="t-text-primary"
    field="errorMessage"
    title="Error Message">
    <ng-template kendoGridCellTemplate let-dataItem>
      <div [innerHTML]="dataItem.errorMessage"></div>
    </ng-template>
  </kendo-grid-column>
</kendo-grid>

<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      *ngIf="selectedValidCount() > 0"
      (click)="importPasswords()">
      Import ({{ selectedValidCount() }})
      <kendo-loader *ngIf="isImporting()" type="pulsing" themeColor="success" />
    </button>
    <button
      kendoButton
      (click)="closeImportPasswordDialog()"
      themeColor="dark"
      fillMode="outline">
      Cancel
    </button>
  </div>
</kendo-dialog-actions>

<!--Help dialog -->
<kendo-dialog
  [width]="'600'"
  *ngIf="showHelpDialog()"
  (close)="closeHelpDialog()">
  <kendo-dialog-titlebar
    ><kendo-label
      text="Password Bank Help"
      class="t-text-base t-text-[#2F3080]">
    </kendo-label
  ></kendo-dialog-titlebar>

  <!-- Help dialog content -->
  <div class="t-flex t-flex-col t-items-start t-gap-4 t-mb-4">
    @if (isForLotusNotes()) {
    <div>
      <kendo-label
        text="Sample Load File (*.txt,*.csv) Encoding UTF8:"
        class="t-text-base t-text-[#2F3080]">
      </kendo-label>
      <div
        class="t-text-xs/4 t-mt-4 t-font-normal t-mb-4 t-align-left t-text-left t-text-[#000000]">
        <div class="t-pb-[2px]">FilePath, Password</div>
        <div class="t-pb-[2px]">"\\ShareServer\ShareFolder\user1.id",</div>
        <div class="t-pb-[2px]">
          "\\ShareServer\ShareFolder\user2.id","password2"
        </div>
        <div class="t-pb-[2px]">...</div>
        <div class="t-pb-[2px]">...</div>
        <div class="t-pb-[2px]">
          "\\ShareServer\ShareFolder\userN.id","passwordN"
        </div>
      </div>

      <kendo-grid
        [data]="sampleNSFData"
        [sortable]="false"
        [filterable]="false"
        [resizable]="false"
        class="t-w-[550px]"
        [pageable]="false">
        <kendo-grid-column
          headerClass="t-text-primary t-w-[30%]"
          class="t-w-[30%]"
          field="FileName"
          title="Field Name">
        </kendo-grid-column>
        <kendo-grid-column
          headerClass="t-text-primary"
          field="Description"
          title="Description">
        </kendo-grid-column>
      </kendo-grid>
    </div>
    } @else {
    <div>
      <kendo-label
        text="Sample Load File (*.txt,*.csv) Encoding UTF8:"
        class="t-text-base t-text-[#2F3080]">
      </kendo-label>
      <div
        class="t-text-xs/4 t-mt-4 t-font-normal t-mb-4 t-align-left t-text-left t-text-[#000000]">
        <div class="t-pb-[2px]">Password1</div>
        <div class="t-pb-[2px]">Password2</div>
        <div class="t-pb-[2px]">...</div>
        <div class="t-pb-[2px]">...</div>
        <div class="t-pb-[2px]">PasswordN</div>
      </div>
    </div>
    }

    <div>
      <kendo-label text="Note:" class="t-text-base t-text-[#2F3080]">
      </kendo-label>
      <div>
        <p class="t-pb-[2px]">- Import file size must be < <b>15 MB</b>.</p>
        <p class="t-pb-[2px]">- Import file must be <= <b>25000</b> lines.</p>
        <p class="t-pb-[2px]">- File must be in ".csv" or ".txt" extension.</p>
      </div>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="closeHelpDialog()"
        themeColor="dark"
        fillMode="outline">
        Cancel
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>

<!-- Template for showing multiline notification messages -->
<ng-template #notificationTemplate>
  <div *ngFor="let line of notificationMessages()">{{ line }}</div>
</ng-template>
