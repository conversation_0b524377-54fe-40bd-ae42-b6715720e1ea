<div
  class="t-flex t-flex-col t-items-start t-my-2 t-gap-[5px] t-mt-[15px] t-p-[5px]"
  [formGroup]="form">
  <div class="t-flex t-flex-row t-justify-between t-items-center t-mb-3">
    <span class="t-flex t-pr-[50px]">
      <kendo-label
        text="Add Password"
        class="t-text-base t-font-bold t-text-[#2F3080]">
      </kendo-label>
    </span>
    <span class="t-flex">
      <button
        kendoButton
        class="v-custom-secondary-button"
        themeColor="secondary"
        fillMode="outline"
        (click)="openImportPasswordDialog()">
        Import Password List
      </button>
    </span>
  </div>
  <div>
    <p>New Password <span class="t-text-[#DC3545]">*</span></p>
  </div>
  <div class="t-flex t-flex-row t-gap-[5px]">
    <input
      kendoTextBox
      placeholder="Password"
      class="t-w-[250px]"
      formControlName="passwordInput" />
    <button
      kendoButton
      themeColor="secondary"
      fillMode="outline"
      class="v-custom-secondary-button"
      [disabled]="!form.valid || isAddingPassword()"
      (click)="addPassword()">
      Add
      <kendo-loader
        *ngIf="isAddingPassword()"
        type="pulsing"
        themeColor="success" />
    </button>
  </div>
  <div
    *ngIf="
      (form?.controls.passwordInput?.invalid &&
        form?.controls.passwordInput?.dirty &&
        !form?.controls.passwordInput?.untouched) ||
      (form?.controls.passwordInput?.dirty &&
        !form?.controls.passwordInput?.untouched &&
        form?.controls.passwordInput?.value.trim() === '')
    "
    class="t-m-1 t-accent-error t-text-error t-text-[11.23px]">
    {{
      form?.controls.passwordInput.hasError('required')
        ? 'Password is a required field'
        : ''
    }}
  </div>
  <div class="t-flex t-flex-col">
    <div class="t-flex t-flex-row t-justify-between t-items-center t-mb-3">
      <p class="t-my-1 t-text-base t-font-bold t-text-[#2F3080] t-mt-4 t-mb-2">
        Password List
      </p>

      <button
        kendoButton
        (click)="deleteMultiplePasswords()"
        fillMode="outline"
        *ngIf="selectedPasswordIds.length > 1"
        class="t-text-red-600"
        [svgIcon]="trashIcon">
        {{ 'Delete (' + selectedPasswordIds.length + ')' }}
        <kendo-loader
          *ngIf="deleteInProgress()"
          type="pulsing"
          themeColor="success" />
      </button>
    </div>
    <kendo-grid
      [data]="passwordList()"
      [selectable]="true"
      [height]="360"
      kendoGridSelectBy
      [resizable]="true"
      [selectable]="{ mode: 'multiple', cell: false, checkboxOnly: true }"
      [(selectedKeys)]="selectedPasswordIds"
      (selectedKeysChange)="passwordSelectionChange($event)"
      kendoGridSelectBy="passwordBankId">
      <kendo-grid-checkbox-column
        [width]="45"
        [columnMenu]="false"
        [showSelectAll]="true"
        [resizable]="false"></kendo-grid-checkbox-column>
      <kendo-grid-column
        headerClass="t-text-primary"
        field="password"
        title="Password"></kendo-grid-column>
      <kendo-grid-column
        title="Delete"
        [width]="100"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate let-dataItem>
          <button
            kendoButton
            data-qa="delete-password"
            class="t-p-1"
            (click)="deletePassword(dataItem)"
            fillMode="clear"
            [svgIcon]="trashIcon"></button>
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>
  </div>
</div>
