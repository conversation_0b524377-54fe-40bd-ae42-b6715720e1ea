import { TestBed, ComponentFixture } from '@angular/core/testing'
import { PasswordManagementComponent } from './password-management.component'
import {
  ReactiveFormsModule,
  FormGroup,
  FormControl,
  FormsModule,
} from '@angular/forms'
import { ChangeDetectorRef } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideRouter } from '@angular/router'
import { VenioNotificationService } from '@venio/feature/notification'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { ButtonModule, ButtonsModule } from '@progress/kendo-angular-buttons'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule, TextBoxComponent } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { CommonModule } from '@angular/common'
import {
  DialogModule,
  DialogRef,
  DialogService,
} from '@progress/kendo-angular-dialog'
import { SVGIconComponent } from '@progress/kendo-angular-icons'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import {
  PasswordBankFacade,
  PasswordBankImportModel,
  PasswordBankModel,
} from '@venio/data-access/review'
import { of } from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'

describe('PasswordManagementComponent', () => {
  let component: PasswordManagementComponent
  let fixture: ComponentFixture<PasswordManagementComponent>
  let mockChangeDetectorRef: jest.Mocked<ChangeDetectorRef>
  let mockVenioNotificationService: jest.Mocked<VenioNotificationService>
  let mockDialogService: Partial<DialogService>
  let mockPasswordBankFacade: Partial<jest.Mocked<PasswordBankFacade>>

  class MockDialogRef {
    public close = jest.fn()

    public dialog = {
      location: jest.fn(),
    }
    // Add other methods and properties as needed
  }

  beforeEach(async () => {
    mockChangeDetectorRef = {
      markForCheck: jest.fn(),
      detectChanges: jest.fn(),
    } as unknown as jest.Mocked<ChangeDetectorRef>

    mockVenioNotificationService = {
      showSuccess: jest.fn(),
      showError: jest.fn(),
    } as unknown as jest.Mocked<VenioNotificationService>

    mockDialogService = {
      open: jest.fn(),
    }

    mockPasswordBankFacade = {
      fetchPasswordBanks: jest.fn(),
      selectAddPasswordResponse$: of({
        status: 'Success',
        message: 'Password added successfully',
        data: [] as PasswordBankModel[],
      } as ResponseModel),
      selectPasswordBanks$: of([
        {
          passwordBankId: 1,
          originalUserIdFilePath: null,
          password: 'test',
          isUserIdFile: false,
          nsfUserIdFilePath: null,
        },
      ]),
      selectDeletePasswordResponse$: of({
        status: 'Success',
        message: 'Password deleted successfully',
        data: [] as PasswordBankModel[],
      } as ResponseModel),
      selectPasswordImportModels$: of([
        {
          passwordBankId: 1,
          originalUserIdFilePath: null,
          password: 'test',
          isUserIdFile: false,
          nsfUserIdFilePath: null,
          errorMessage: null,
          nsfFileSize: 34,
          isAdded: true,
          seq: 1,
        },
      ] as PasswordBankImportModel[]),
    }

    await TestBed.configureTestingModule({
      imports: [
        ReactiveFormsModule,
        GridModule,
        InputsModule,
        LabelModule,
        ButtonsModule,
        SvgLoaderDirective,
        FormsModule,
        NoopAnimationsModule,
        CommonModule,
        DialogModule,
        LayoutModule,
        IndicatorsModule,
        TextBoxComponent,
        TooltipModule,
        SVGIconComponent,
        ButtonModule,
      ],
      declarations: [PasswordManagementComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideRouter([]),
        provideMockStore({}),
        { provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
        {
          provide: VenioNotificationService,
          useValue: mockVenioNotificationService,
        },
        { provide: DialogService, useValue: mockDialogService },
        { provide: DialogRef, useClass: MockDialogRef },
        { provide: PasswordBankFacade, useValue: mockPasswordBankFacade },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(PasswordManagementComponent)
    component = fixture.componentInstance

    // Initialize the form
    component.form = new FormGroup({
      passwordInput: new FormControl(''),
    })
    fixture.componentRef.setInput('projectId', 1)

    fixture.detectChanges()
  })

  it('should create the component', () => {
    expect(component).toBeTruthy()
  })
})
