import { TestBed, ComponentFixture } from '@angular/core/testing'
import { PasswordBankContainerComponent } from './password-bank-container.component'
import { ChangeDetectorRef } from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideRouter } from '@angular/router'
import { VenioNotificationService } from '@venio/feature/notification'
import { CommonModule } from '@angular/common'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { ButtonsModule, ButtonModule } from '@progress/kendo-angular-buttons'
import {
  DialogModule,
  DialogRef,
  DialogService,
} from '@progress/kendo-angular-dialog'
import { GridModule } from '@progress/kendo-angular-grid'
import { SVGIconComponent } from '@progress/kendo-angular-icons'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { InputsModule, TextBoxComponent } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import {
  PasswordBankFacade,
  PasswordBankModel,
  PasswordBankImportModel,
} from '@venio/data-access/review'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { of } from 'rxjs'

import { NotificationService } from '@progress/kendo-angular-notification'
describe('PasswordBankContainerComponent', () => {
  let component: PasswordBankContainerComponent
  let fixture: ComponentFixture<PasswordBankContainerComponent>
  let mockChangeDetectorRef: jest.Mocked<ChangeDetectorRef>
  let mockVenioNotificationService: jest.Mocked<VenioNotificationService>
  let mockDialogService: Partial<DialogService>
  let mockPasswordBankFacade: Partial<jest.Mocked<PasswordBankFacade>>

  class MockDialogRef {
    public close = jest.fn()

    public dialog = {
      location: jest.fn(),
    }
    // Add other methods and properties as needed
  }

  beforeEach(async () => {
    mockChangeDetectorRef = {
      markForCheck: jest.fn(),
      detectChanges: jest.fn(),
    } as unknown as jest.Mocked<ChangeDetectorRef>

    mockVenioNotificationService = {
      showSuccess: jest.fn(),
      showError: jest.fn(),
    } as unknown as jest.Mocked<VenioNotificationService>

    mockDialogService = {
      open: jest.fn(),
    }

    mockPasswordBankFacade = {
      fetchPasswordBanks: jest.fn(),
      selectAddPasswordResponse$: of({
        status: 'Success',
        message: 'Password added successfully',
        data: [] as PasswordBankModel[],
      } as ResponseModel),
      selectPasswordBanks$: of([
        {
          passwordBankId: 1,
          originalUserIdFilePath: null,
          password: 'test',
          isUserIdFile: false,
          nsfUserIdFilePath: null,
        },
      ]),
      selectDeletePasswordResponse$: of({
        status: 'Success',
        message: 'Password deleted successfully',
        data: [] as PasswordBankModel[],
      } as ResponseModel),
      selectPasswordImportModels$: of([
        {
          passwordBankId: 1,
          originalUserIdFilePath: null,
          password: 'test',
          isUserIdFile: false,
          nsfUserIdFilePath: null,
          errorMessage: null,
          nsfFileSize: 34,
          isAdded: true,
          seq: 1,
        },
      ] as PasswordBankImportModel[]),
    }

    await TestBed.configureTestingModule({
      imports: [
        ReactiveFormsModule,
        GridModule,
        InputsModule,
        LabelModule,
        ButtonsModule,
        SvgLoaderDirective,
        FormsModule,
        NoopAnimationsModule,
        CommonModule,
        DialogModule,
        LayoutModule,
        IndicatorsModule,
        TextBoxComponent,
        TooltipModule,
        SVGIconComponent,
        ButtonModule,
      ],
      declarations: [PasswordBankContainerComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideRouter([]),
        provideMockStore({}),
        { provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
        {
          provide: VenioNotificationService,
          useValue: mockVenioNotificationService,
        },
        { provide: DialogService, useValue: mockDialogService },
        { provide: DialogRef, useClass: MockDialogRef },
        { provide: PasswordBankFacade, useValue: mockPasswordBankFacade },
        { provide: NotificationService, useValue: { show: jest.fn() } },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(PasswordBankContainerComponent)
    component = fixture.componentInstance
    fixture.componentRef.setInput('projectId', 1)

    fixture.detectChanges()
  })

  it('should create the component', () => {
    expect(component).toBeTruthy()
  })
})
