import {
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
  input,
  signal,
} from '@angular/core'
import { toSignal } from '@angular/core/rxjs-interop'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { SelectEvent } from '@progress/kendo-angular-layout'
import { PasswordBankFacade } from '@venio/data-access/review'
import { VenioNotificationService } from '@venio/feature/notification'
import { ResponseModel } from '@venio/shared/models/interfaces'

@Component({
  selector: 'venio-password-bank-container',
  templateUrl: './password-bank-container.component.html',
  styleUrl: './password-bank-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PasswordBankContainerComponent {
  public projectId = input.required<number>()

  private readonly passwordBankFacade = inject(PasswordBankFacade)

  private readonly notificationService = inject(VenioNotificationService)

  private dialogRef = inject(DialogRef)

  public readonly selectedTabIndex = signal(0)

  public readonly passwordAddResponse = toSignal(
    this.passwordBankFacade.selectAddPasswordResponse$
  )

  public readonly passwordDeleteResponse = toSignal(
    this.passwordBankFacade.selectDeletePasswordResponse$
  )

  constructor() {
    // to handle add password response
    effect(
      () => {
        this.#processResponse(this.passwordAddResponse())
      },
      { allowSignalWrites: true }
    )

    // to handle delete password response
    effect(
      () => {
        this.#processResponse(this.passwordDeleteResponse())
      },
      { allowSignalWrites: true }
    )
  }

  #processResponse(response: ResponseModel): void {
    if (!response) return

    if (response.status?.toLocaleLowerCase() === 'success') {
      this.notificationService.showSuccess(response.message)
    } else if (response.status?.toLocaleLowerCase() === 'error') {
      this.notificationService.showError(response.message)
    }
  }

  public close(): void {
    this.passwordBankFacade.clearResponseMessage()
    this.dialogRef.close()
  }

  public onSelect(e: SelectEvent): void {
    this.passwordBankFacade.clearResponseMessage()
    this.selectedTabIndex.set(e.index)
  }
}
