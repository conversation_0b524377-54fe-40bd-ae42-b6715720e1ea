import { ComponentFixture, TestBed } from '@angular/core/testing'
import { OptionsComponent } from './slipsheet-options.component'
import { DialogModule } from '@progress/kendo-angular-dialog'
import {
  ConvertDocumentFacade,
  CreateSlipsheetFacade,
} from '@venio/data-access/review'
import { SlipsheetFormService } from '../../services/slipsheet-form.service'
import { provideMockStore } from '@ngrx/store/testing'
import { CommonModule } from '@angular/common'
import { HttpClientModule } from '@angular/common/http'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { UploadsModule } from '@progress/kendo-angular-upload'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import {
  BrowserAnimationsModule,
  NoopAnimationsModule,
} from '@angular/platform-browser/animations'

describe('OptionsComponent', () => {
  let component: OptionsComponent
  let fixture: ComponentFixture<OptionsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [OptionsComponent],
      imports: [
        CommonModule,
        DialogModule,
        GridModule,
        InputsModule,
        LabelModule,
        FormsModule,
        ReactiveFormsModule,
        ButtonsModule,
        DropDownListModule,
        HttpClientModule,
        UploadsModule,
        SvgLoaderDirective,
        BrowserAnimationsModule,
        NoopAnimationsModule,
      ],
      providers: [
        ConvertDocumentFacade,
        CreateSlipsheetFacade,
        SlipsheetFormService,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(OptionsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
