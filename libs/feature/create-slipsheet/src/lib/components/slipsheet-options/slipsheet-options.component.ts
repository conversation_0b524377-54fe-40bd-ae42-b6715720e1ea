import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetector<PERSON>ef,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  QueryList,
  ViewChild,
  ViewChildren,
} from '@angular/core'
import {
  AbstractControl,
  FormBuilder,
  FormControlName,
  FormGroup,
  ValidatorFn,
  Validators,
} from '@angular/forms'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import { FileRestrictions, SelectEvent } from '@progress/kendo-angular-upload'
import {
  ConvertDocumentFacade,
  CreateSlipsheetFacade,
  FontStyle,
  SlipSheetType,
  SlipsheetTemplateModel,
  StampLocation,
} from '@venio/data-access/review'
import { Subject, debounceTime, filter, takeUntil } from 'rxjs'
import { SlipsheetFormService } from '../../services/slipsheet-form.service'
import {
  GenericValidator,
  MessageModel,
} from '@venio/feature/generic-validator'
import { SlipsheetImagePreviewComponent } from '../slipsheet-image-preview/slipsheet-image-preview.component'
import { FontSetting } from '../../models/font.models'
import {
  boldIcon,
  italicIcon,
  SVGIcon,
  underlineIcon,
} from '@progress/kendo-svg-icons'
import { ButtonGroupSelection } from '@progress/kendo-angular-buttons'
import { cloneDeep } from 'lodash'
import {
  DropDownListComponent,
  ItemArgs,
} from '@progress/kendo-angular-dropdowns'

@Component({
  selector: 'venio-slipsheet-options',
  templateUrl: './slipsheet-options.component.html',
  styleUrl: './slipsheet-options.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OptionsComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('fileInput') private fileInput!: ElementRef

  @ViewChild('slipsheetField')
  public slipsheetFieldDropdown: DropDownListComponent

  public uploadedFile: { name: string; size: number } | null = null

  public errorMessage: string | null = null

  public selectedRadiobutton = 'rdPlaceholderText'

  private readonly toDestroy$ = new Subject<void>()

  private genericValidator: GenericValidator

  /**
   * Object containing validation fail message defined on generic class.
   */
  public displayMessage: MessageModel

  /**
   * Directives to monitor changes so we can perform validation and update message rules accordingly.
   */
  @ViewChildren(FormControlName, { read: ElementRef })
  private readonly formInputElements: QueryList<ElementRef>

  public slipsheetFields: string[] = []

  public fontNames = []

  public fontStyles: (string | FontStyle)[] = []

  public fontSizes = [
    8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72,
  ]

  public fontSetting: FontSetting

  public fontSelectionForm: FormGroup

  public selectionMode: ButtonGroupSelection = 'single'

  private dialogRef: DialogRef

  public get slipsheetForm(): FormGroup {
    return this.slipsheetFormService.slipsheetForm
  }

  public slipsheetTemplates: SlipsheetTemplateModel[] = []

  public SlipSheetTypeEnum = SlipSheetType

  public enableTextControls = false

  public stampLocationList = Object.values(StampLocation).filter(
    (x) => typeof x === 'string'
  )

  public defaultFieldItem = 'Add Field'

  public slipsheetImageAllowedTypes: FileRestrictions = {
    allowedExtensions: [
      '.xbm',
      '.tif',
      '.pjp',
      '.apng',
      '.svgz',
      '.jpg',
      '.jpeg',
      '.ico',
      '.tiff',
      '.gif',
      '.svg',
      '.jfif',
      '.webp',
      '.png',
      '.bmp',
      '.pjpeg',
      '.avif',
    ],
  }

  public boldSVG: SVGIcon = boldIcon

  public italicSVG: SVGIcon = italicIcon

  public underlineSVG: SVGIcon = underlineIcon

  constructor(
    private cdr: ChangeDetectorRef,
    private slipsheetFormService: SlipsheetFormService,
    private dialogService: DialogService,
    private convertDocumentFacade: ConvertDocumentFacade,
    private slipSheetFacade: CreateSlipsheetFacade,
    private fb: FormBuilder
  ) {}

  public ngOnInit(): void {
    this.initSlices()
    this.initilaizeFontSelectionform()
    this.toggleValidation('rdPlaceholderText')
    this.slipSheetFacade.fetchSlipsheetTemplates()
    this.slipSheetFacade.fetchSlipsheetFields()
  }

  public ngAfterViewInit(): void {
    this.initValidationRules()
    this.validationWatcher()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  private initilaizeFontSelectionform(): void {
    this.initDropdownValues()

    // if opening for the second time then restore previous selection
    if (this.slipsheetFormService.fontSetting) {
      this.fontSetting = cloneDeep(this.slipsheetFormService.fontSetting)
    }

    this.initForm()
  }

  private initForm(): void {
    this.fontSelectionForm = this.fb.group({
      fontName: [this.fontSetting?.fontName || ''],
      fontStyle: [this.fontSetting?.fontStyle || ''],
      fontSize: [this.fontSetting?.fontSize || ''],
    })

    // Sync form values with fontSetting and slipsheetFormService
    this.fontSelectionForm.valueChanges
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((fs) => {
        this.fontSetting.fontName = fs.fontName
        this.fontSetting.fontSize = fs.fontSize
        this.fontSetting.fontStyle = fs.fontStyle

        this.slipsheetFormService.fontSetting = this.fontSetting
      })
  }

  public onFontStyleChange(value: string): void {
    const currentValue = this.fontSelectionForm.get('fontStyle')?.value

    if (currentValue === value) {
      // If the same button is clicked again, reset to 'Regular'
      this.fontSelectionForm.get('fontStyle')?.setValue('Regular')
    } else {
      // Set the new value
      this.fontSelectionForm.get('fontStyle')?.setValue(value)
    }
    this.cdr.detectChanges()
  }

  public isSelected(value: string): boolean {
    // Return true only if the value matches the form's value and is not 'Regular'
    return this.fontSelectionForm.get('fontStyle')?.value === value
  }

  public fileSelectWrapper(fileSelect: any): void {
    // Programmatically trigger the file selection dialog
    const fileInput = fileSelect.fileInput.nativeElement
    if (fileInput) {
      fileInput.click()
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public onSlipsheetOptionChange(event: any): void {
    this.toggleValidation(event.target.id)
    this.selectedRadiobutton = event?.target?.id
    this.cdr.markForCheck()
  }

  private initSlices(): void {
    this.slipSheetFacade.getSlipsheetTemplates$
      .pipe(
        filter((t) => !!t),
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe((templates) => {
        this.slipsheetTemplates = templates
        this.cdr.markForCheck()
      })

    this.slipSheetFacade.getSlipsheetFields$
      .pipe(
        filter((t) => !!t),
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe((fields) => {
        this.slipsheetFormService.slipsheetFields = fields
        this.slipsheetFields = this.slipsheetFormService?.slipsheetFields?.map(
          (field) => field?.internalFieldName
        )
        this.cdr.markForCheck()
      })
  }

  // persist selected field to the form service
  public fieldSelectionChange(field: string): void {
    // append selected field to the existing placeholder text
    if (field) {
      const placeholderTextControl =
        this.slipsheetFormService.slipsheetForm.get('placeHolderText')
      placeholderTextControl.setValue(
        placeholderTextControl.value + '%' + field + '%'
      )
      this.slipsheetFieldDropdown.value = null // reset the dropdown value after appending value to the textbox
    }
  }

  // Disable default item logic
  public disableDefaultItem = (context: ItemArgs): boolean => {
    return context.dataItem === this.defaultFieldItem
  }

  private initDropdownValues(): void {
    this.fontStyles = Object.values(FontStyle).filter(
      (x) => typeof x === 'string'
    )

    //todo: get font list from control settings KEY: WATERMARK_FONTS
    this.fontNames =
      'Arial;Book Antiqua;Calibri;Cambria;Century;Century Gothic;Comic Sans MS;Courier;Courier New;Georgia;Lucida Console;Lucida Sans;Lucida Sans Unicode;Microsoft Sans Serif;MS Outlook;MS Sans Serif;Tahoma;Times New Roman;Trebuchet MS;Verdana'.split(
        ';'
      )
  }

  /**
   * A generic validation rules, properties with message.
   * Must be called before the form being initialized.
   * @returns {void}
   */
  private initValidationRules(): void {
    this.genericValidator = new GenericValidator({
      slipsheetTemplateId: {
        required: 'Slipsheet Template is a required field.',
      },
      placeHolderPosition: {
        required: 'Slipsheet text location is required.',
      },
      placeHolderText: {
        required: 'Slipsheet text is required.',
      },
      placeHolderFile: {
        required: 'Slipsheet text file is required.',
      },
    })
  }

  /**
   * This validation watcher goes to `ngAfterViewInit` life cycle hook because of we need
   * to track the inputs, elements and render the validation rule messages accordingly.
   *
   * Compares the rule with user input based on validation sets of form group control and populates
   * messages which is defined in `initValidationRules` fn.
   * @returns {void}
   */
  private validationWatcher(): void {
    this.genericValidator
      .initValidationProcess(this.slipsheetForm, this.formInputElements)
      .pipe(takeUntil(this.toDestroy$))
      .subscribe({
        next: (m) => [this.cdr.markForCheck(), (this.displayMessage = m)],
      })
  }

  public toggleValidation(name: string): void {
    const templateDropdown = this.slipsheetForm.get('slipsheetTemplateId') // template selection dropdownlist
    const textPositionDropdown = this.slipsheetForm.get('placeHolderPosition') // text location dropdownlist
    const fontSizeDropdown = this.fontSelectionForm.get('fontSize') // text location dropdownlist
    const fontNameDropdown = this.fontSelectionForm.get('fontName') // text location dropdownlist
    const placeholderTextDropdown = this.slipsheetForm.get('placeHolderText') // text location dropdownlist
    const textControl = this.slipsheetForm.get('placeHolderText') // slipsheet text input
    const replaceFullTextCheckbox = this.slipsheetForm.get('replaceFulltext') // replace full text checkbox
    const fileSelectControl = this.slipsheetForm.get('placeHolderFile') // slipsheet image file upload control
    this.enableTextControls = name === 'rdPlaceholderText' //flag to indicate if the text option is selected (to enable/disable Font and Add Fields links, and the 'replace fulltext' checkbox)

    // if slipsheet template is selected
    if (name === 'rdPlaceholderTemplate') {
      this.enableControl(templateDropdown, Validators.required)
      // this.disableControl(textPositionDropdown)
      this.disableControl(fileSelectControl)
      // this.disableControl(textControl)
      this.disableControl(replaceFullTextCheckbox)
      // this.disableControl(fontSizeDropdown)
      // this.disableControl(fontNameDropdown)
      // this.disableControl(placeholderTextDropdown)
    }
    // if slipsheet text is selected
    else if (name === 'rdPlaceholderText') {
      this.enableControl(textControl, Validators.required)
      this.enableControl(textPositionDropdown)
      this.enableControl(replaceFullTextCheckbox)
      this.enableControl(fontSizeDropdown)
      this.enableControl(fontNameDropdown)
      this.enableControl(placeholderTextDropdown)
      this.disableControl(templateDropdown)
      this.disableControl(fileSelectControl)
      this.disableControl(fileSelectControl)
    }
    // if slipsheet image is selected
    else {
      this.enableControl(fileSelectControl, Validators.required)
      this.disableControl(templateDropdown)
      // this.disableControl(textControl)
      // this.disableControl(textPositionDropdown)
      this.disableControl(replaceFullTextCheckbox)
      // this.disableControl(fontSizeDropdown)
      // this.disableControl(fontNameDropdown)
      // this.disableControl(placeholderTextDropdown)
    }
  }

  /**
   * Enables the control and sets the validator
   * @param {AbstractControl<any, any>} control form control
   * @param {ValidatorFn | ValidatorFn[] | null} validators boolean value to indicate if the control is required field
   * @returns {void}
   */
  private enableControl(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    control: AbstractControl<any, any>,
    validators: ValidatorFn | ValidatorFn[] | null = null
  ): void {
    if (validators) {
      control.setValidators(validators)
    }
    control.updateValueAndValidity()
    control.enable()
  }

  /**
   * Disables the control and clear validation errors and validators
   * @param {AbstractControl<any, any>} control form control
   * @returns {void}
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private disableControl(control: AbstractControl<any, any>): void {
    control.setErrors(null)
    control.clearValidators()
    control.disable()
  }

  /**
   * Reads the selected file and sets the base64 value of valid image to the form control
   * @param {SelectEvent} e select event object
   * @returns {void}
   */
  public fileSelectEvent(e: SelectEvent): void {
    e.files.forEach((file) => {
      if (!file.validationErrors) {
        const reader = new FileReader()

        reader.onload = (ev): void => {
          this.slipsheetForm
            .get('placeHolderFile')
            .setValue((reader.result as string).split(',')[1])
        }

        reader.readAsDataURL(file.rawFile)
      }
    })
  }

  /**
   * Opens the image preview dialog
   * @returns {void}
   */
  public openPreviewDialog(): void {
    const payload = this.slipsheetFormService.getSlipsheetPayload()
    const message = this.slipsheetFormService.validateSlipsheetData(payload)
    if (message !== '') {
      this.convertDocumentFacade.setResponseMessage({ message, success: false })
      return
    }
    this.dialogRef = this.dialogService.open({
      content: SlipsheetImagePreviewComponent,
      width: '80%',
      maxWidth: '700px',
      height: '50vh',
      maxHeight: '800px',
    })
  }

  /**
   * ---------------------------------------------------
   * Manual File Upload Code Instead of kendo Component
   * ---------------------------------------------------
   */

  // Trigger file input programmatically
  public triggerFileInput(): void {
    this.fileInput.nativeElement.click()
  }

  // Handle file selection
  public onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement
    if (input.files && input.files.length > 0) {
      const file = input.files[0]
      this.validateAndProcessFile(file)
    }
  }

  public onDragOver(event: DragEvent): void {
    event.preventDefault()
    const uploadArea = document.querySelector('.v-upload-area')
    if (uploadArea) {
      uploadArea.classList.add('drag-over')
    }
  }

  public onDragLeave(event: DragEvent): void {
    event.preventDefault()
    const uploadArea = document.querySelector('.v-upload-area')
    if (uploadArea) {
      uploadArea.classList.remove('drag-over')
    }
  }

  public onDrop(event: DragEvent): void {
    event.preventDefault()
    const uploadArea = document.querySelector('.v-upload-area')
    if (uploadArea) {
      uploadArea.classList.remove('drag-over')
    }

    // Process the dropped files
    if (event.dataTransfer && event.dataTransfer.files.length > 0) {
      const file = event.dataTransfer.files[0]
      this.validateAndProcessFile(file)
    } else {
      console.error('No files detected on drop')
    }
  }

  // Validate and process file
  private validateAndProcessFile(file: File): void {
    const fileExtension = this.getFileExtension(file.name)

    // Check if file extension is allowed
    if (
      !this.slipsheetImageAllowedTypes.allowedExtensions.includes(fileExtension)
    ) {
      this.errorMessage = `Invalid file type. Allowed types are: ${this.slipsheetImageAllowedTypes.allowedExtensions.join(
        ', '
      )}`
      return
    }

    // Clear error and process the file
    this.errorMessage = null

    // Update uploadedFile details
    this.uploadedFile = {
      name: file.name,
      size: Math.round(file.size / 1024), // Convert bytes to KB
    }

    // Process the file (convert to Base64)
    this.processFile(file)
  }

  // Process file and convert to Base64
  private processFile(file: File): void {
    const reader = new FileReader()

    reader.onload = (): void => {
      const base64Value = (reader.result as string).split(',')[1]
      this.slipsheetForm.get('placeHolderFile').setValue(base64Value)
    }

    reader.readAsDataURL(file)
  }

  // Clear file
  public clearFile(event: MouseEvent): void {
    event.stopPropagation()
    this.uploadedFile = null
    this.fileInput.nativeElement.value = '' // Reset file input
  }

  // Utility function to get file extension
  private getFileExtension(fileName: string): string {
    const parts = fileName.split('.')
    return parts.length > 1 ? `.${parts.pop()?.toLowerCase()}` : ''
  }

  /**
   * ---------------------------------------------------
   * Manual File Upload Code Instead of kendo Component
   * ---------------------------------------------------
   */
}
