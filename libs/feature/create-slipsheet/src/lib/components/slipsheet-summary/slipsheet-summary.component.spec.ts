import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SummaryComponent } from './slipsheet-summary.component'
import { CommonModule } from '@angular/common'
import { HttpClientModule } from '@angular/common/http'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { provideMockStore } from '@ngrx/store/testing'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogModule } from '@progress/kendo-angular-dialog'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { UploadsModule } from '@progress/kendo-angular-upload'
import {
  ConvertDocumentFacade,
  CreateSlipsheetFacade,
} from '@venio/data-access/review'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { SlipsheetFormService } from '../../services/slipsheet-form.service'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('SummaryComponent', () => {
  let component: SummaryComponent
  let fixture: ComponentFixture<SummaryComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [SummaryComponent],
      imports: [
        CommonModule,
        DialogModule,
        GridModule,
        InputsModule,
        LabelModule,
        FormsModule,
        ReactiveFormsModule,
        ButtonsModule,
        DropDownListModule,
        HttpClientModule,
        UploadsModule,
        SvgLoaderDirective,
        NoopAnimationsModule,
      ],
      providers: [
        ConvertDocumentFacade,
        CreateSlipsheetFacade,
        SlipsheetFormService,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(SummaryComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
