{"name": "feature-golden-layout", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/golden-layout/src", "prefix": "venio", "projectType": "library", "tags": ["golden-layout"], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/golden-layout/ng-package.json", "tailwindConfig": "libs/feature/golden-layout/tailwind.config.js"}, "configurations": {"production": {"tsConfig": "libs/feature/golden-layout/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/golden-layout/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/golden-layout/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}