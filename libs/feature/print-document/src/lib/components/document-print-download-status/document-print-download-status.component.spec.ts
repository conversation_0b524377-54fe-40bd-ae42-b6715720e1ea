import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentPrintDownloadStatusComponent } from './document-print-download-status.component'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import {
  NativeDownloadFacade,
  PrintDocumentFacade,
} from '@venio/data-access/review'
import { FormsModule } from '@angular/forms'
import { provideMockStore } from '@ngrx/store/testing'
import { provideAnimations } from '@angular/platform-browser/animations'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { GridModule } from '@progress/kendo-angular-grid'
import { LabelModule } from '@progress/kendo-angular-label'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideRouter } from '@angular/router'

describe('DocumentPrintDownloadStatusComponent', () => {
  let component: DocumentPrintDownloadStatusComponent
  let fixture: ComponentFixture<DocumentPrintDownloadStatusComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DocumentPrintDownloadStatusComponent],
      imports: [
        ButtonModule,
        DropDownListModule,
        FormsModule,
        GridModule,
        InputsModule,
        LabelModule,
        SvgLoaderDirective,
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideRouter([]),
        PrintDocumentFacade,
        NativeDownloadFacade,
        provideMockStore({}),
        provideAnimations(),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentPrintDownloadStatusComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
