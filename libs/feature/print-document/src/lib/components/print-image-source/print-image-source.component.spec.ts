import { ComponentFixture, TestBed } from '@angular/core/testing'
import { PrintImageSourceComponent } from './print-image-source.component'
import { provideMockStore } from '@ngrx/store/testing'
import { DatePipe } from '@angular/common'
import { ReactiveFormsModule } from '@angular/forms'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  PrintDocumentFacade,
  PrintDocumentService,
} from '@venio/data-access/review'
import { PrintDocumentFormService } from '../../services/print-document-form.service'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideRouter } from '@angular/router'

describe('PrintImageSourceComponent', () => {
  let component: PrintImageSourceComponent
  let fixture: ComponentFixture<PrintImageSourceComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PrintImageSourceComponent],
      imports: [LabelModule, ReactiveFormsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideRouter([]),
        DatePipe,
        PrintDocumentFacade,
        PrintDocumentService,
        PrintDocumentFormService,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(PrintImageSourceComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
