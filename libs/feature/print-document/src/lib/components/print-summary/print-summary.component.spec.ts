import { ComponentFixture, TestBed } from '@angular/core/testing'
import { PrintSummaryComponent } from './print-summary.component'
import { provideMockStore } from '@ngrx/store/testing'
import { DatePipe } from '@angular/common'
import {
  DocumentsFacade,
  FieldFacade,
  PrintDocumentFacade,
  PrintDocumentService,
  SearchFacade,
} from '@venio/data-access/review'
import { PrintDocumentFormService } from '../../services/print-document-form.service'
import { GridModule } from '@progress/kendo-angular-grid'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('PrintSummaryComponent', () => {
  let component: PrintSummaryComponent
  let fixture: ComponentFixture<PrintSummaryComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PrintSummaryComponent],
      imports: [GridModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DatePipe,
        DocumentsFacade,
        PrintDocumentFacade,
        SearchFacade,
        FieldFacade,
        PrintDocumentService,
        PrintDocumentFormService,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(PrintSummaryComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
