import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core'
import { Subject, combineLatest, filter, takeUntil } from 'rxjs'
import { FormGroup } from '@angular/forms'
import {
  DocumentsFacade,
  PrintDocumentFacade,
  SearchFacade,
  ShowSummaryStatus,
  Summary,
} from '@venio/data-access/review'
import { PrintDocumentFormService } from '../../services/print-document-form.service'
import { PrintSummaryGridModel } from '../../models/print-summary.models'

@Component({
  selector: 'venio-print-summary',
  templateUrl: './print-summary.component.html',
  styleUrls: ['./print-summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PrintSummaryComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  public summaryModel: Summary

  public summary: ShowSummaryStatus

  private selectedDocumentsCount: number

  public gridData: PrintSummaryGridModel[] = []

  public get printForm(): FormGroup {
    return this.printDocumentFormService.printForm
  }

  constructor(
    private printDocumentFacade: PrintDocumentFacade,
    private searchFacade: SearchFacade,
    private printDocumentFormService: PrintDocumentFormService,
    private documentFacade: DocumentsFacade,
    private cdr: ChangeDetectorRef
  ) {}

  public ngOnInit(): void {
    this.getSelectedDocumentsCount()
    this.setSummaryStatus()
    this.getSummary()
  }

  private getSelectedDocumentsCount(): void {
    combineLatest([
      this.documentFacade.getIsBatchSelected$,
      this.documentFacade.getSelectedDocuments$,
      this.documentFacade.getUnselectedDocuments$,
      this.searchFacade.getSearchInitialParameters$,
    ])
      .pipe(
        filter(
          ([isBatchSelected, selectedDocs, unselectedDocs, searchParams]) =>
            !!selectedDocs && selectedDocs.length > 0
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe(
        ([isBatchSelected, selectedDocs, unselectedDocs, searchParams]) => {
          if (isBatchSelected) {
            this.selectedDocumentsCount =
              searchParams.totalHitCount - unselectedDocs.length
          } else {
            this.selectedDocumentsCount = selectedDocs.length
          }
        }
      )
  }

  private getSummary(): void {
    this.printDocumentFacade.getDocumentSummary$
      .pipe(
        filter((res) => !!res),
        takeUntil(this.toDestroy$)
      )
      .subscribe((documentSummary) => {
        this.cdr.markForCheck()
        this.summaryModel = documentSummary
        this.updateGridData()
      })
  }

  private clearSummary(): void {
    this.printDocumentFacade.clearDocumentSummary()
  }

  private setSummaryStatus(): void {
    const selecteValue = this.printForm?.value
    this.summary = {
      PDFName: selecteValue?.printName,
      QueueFilesForImagingIfNotAvailable:
        selecteValue?.tiffSourceOption.queueFilesForImagingIfNotAvailable ||
        false,
      pageNumberingSelectedValue:
        selecteValue?.tiffSourceOption.endorsementValue,
      EndorsementLocation: selecteValue?.tiffSourceOption.endorsementLocation,
      SlipSheetText: selecteValue?.slipSheetOption.slipsheetSettings,
      AddWaterMark: selecteValue?.waterMarkOption.addWaterMark,
      DocCount: this.selectedDocumentsCount,
    }
    this.updateGridData()
  }

  public ngOnDestroy(): void {
    this.clearSummary()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  private updateGridData(): void {
    this.gridData = [
      {
        field: 'PDF/Zip name:',
        value: this.summary?.PDFName,
      },
      {
        field: 'Queue files for imaging:',
        value: this.summary?.QueueFilesForImagingIfNotAvailable
          ? 'true'
          : 'false',
      },
      {
        field: 'Page numbering:',
        value: this.summary?.pageNumberingSelectedValue,
      },
      {
        field: 'Endorsement location:',
        value: this.summary?.EndorsementLocation,
      },
      {
        field: 'Slipsheet:',
        value: this.summary?.SlipSheetText,
      },
      {
        field: 'Add watermark:',
        value: this.summary?.AddWaterMark ? 'true' : 'false',
      },
      {
        field: 'Total selected document count:',
        value: this.summary?.DocCount?.toString(),
      },
      {
        field: 'Total tiffed document count:',
        value: this.summaryModel?.TotalTiffDoc?.toString(),
      },
      {
        field: 'Total not tiffed document count:',
        value: (
          this.summary?.DocCount - this.summaryModel?.TotalTiffDoc
        )?.toString(),
      },
      {
        field: 'Total tiff pages:',
        value: this.summaryModel?.TotalTiffPage?.toString(),
      },
    ]

    this.cdr.markForCheck()
  }
}
