import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core'
import { Subject, takeUntil } from 'rxjs'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { FormGroup } from '@angular/forms'
import { FieldModel, PrintDocumentFacade } from '@venio/data-access/review'
import { PrintDocumentFormService } from '../../services/print-document-form.service'

@Component({
  selector: 'venio-print-add-field',
  templateUrl: './print-add-field.component.html',
  styleUrls: ['./print-add-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PrintAddFieldComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  public fieldSelected: string

  public fieldList: FieldModel[] = []

  public get slipSheetOption(): FormGroup {
    return this.printDocumentFormService.printForm.get(
      'slipSheetOption'
    ) as FormGroup
  }

  constructor(
    private printDocumentFacade: PrintDocumentFacade,
    private printDocumentFormService: PrintDocumentFormService,
    public dialog: DialogRef,
    public cdr: ChangeDetectorRef
  ) {}

  public ngOnInit(): void {
    this.getSlipsheetFields()
  }

  private getSlipsheetFields(): void {
    this.printDocumentFacade.getSlipsheetFields$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((fields) => {
        this.fieldList = fields
        this.fieldSelected = this.fieldList.length
          ? this.fieldList[0].internalFieldName
          : ''
        this.cdr.markForCheck()
      })
  }

  public onFieldAdded(): void {
    if (this.fieldSelected) {
      const slipsheetText = this.slipSheetOption.get('slipSheetText')
      slipsheetText.setValue(
        slipsheetText.value + '%' + this.fieldSelected + '%'
      )
      this.dialog.close('true')
    }
    this.dialog.close(undefined)
  }

  public closeDialog(): void {
    this.dialog.close(undefined)
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
