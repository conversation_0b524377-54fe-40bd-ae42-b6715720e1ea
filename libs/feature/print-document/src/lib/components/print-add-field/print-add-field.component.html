<form class="t-bg-white t-rounded-md t-pt-2">
  <div
    class="v-custom-grey-bg t-rounded-md t-pb-4 t-border-gray-300-temp t-border t-border-solid">
    <fieldset>
      <div class="t-flex t-flex-wrap t-gap-4">
        <div class="t-flex-1">
          <kendo-dropdownlist
            id="fields"
            [data]="fieldList"
            textField="internalFieldName"
            valueField="internalFieldName"
            [valuePrimitive]="true"
            [(value)]="fieldSelected">
          </kendo-dropdownlist>
        </div>
      </div>
    </fieldset>
  </div>
</form>

<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="onFieldAdded()"
      class="v-custom-secondary-button"
      themeColor="error"
      fillMode="outline"
      data-qa="slipsheet-field-select-add-button">
      Add
    </button>
  </div>
</kendo-dialog-actions>
