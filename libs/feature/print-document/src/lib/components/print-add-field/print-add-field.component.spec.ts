import { ComponentFixture, TestBed } from '@angular/core/testing'
import { PrintAddFieldComponent } from './print-add-field.component'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { TextBoxModule } from '@progress/kendo-angular-inputs'
import { DatePipe } from '@angular/common'
import { LabelModule } from '@progress/kendo-angular-label'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { AnimationBuilder } from '@angular/animations'
import { provideMockStore } from '@ngrx/store/testing'
import {
  PrintDocumentFacade,
  PrintDocumentService,
} from '@venio/data-access/review'
import { PrintDocumentFormService } from '../../services/print-document-form.service'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('PrintAddFieldComponent', () => {
  let component: PrintAddFieldComponent
  let fixture: ComponentFixture<PrintAddFieldComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PrintAddFieldComponent],
      imports: [
        ButtonsModule,
        TextBoxModule,
        DialogsModule,
        LabelModule,
        DropDownListModule,
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        AnimationBuilder,
        DialogRef,
        DatePipe,
        PrintDocumentFacade,
        PrintDocumentService,
        PrintDocumentFormService,
        TextBoxModule,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(PrintAddFieldComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
