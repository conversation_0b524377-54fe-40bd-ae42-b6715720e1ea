import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core'
import { FormGroup } from '@angular/forms'
import { LocationOption } from '@venio/data-access/review'
import { Subject, debounceTime, takeUntil } from 'rxjs'
import { PrintDocumentFormService } from '../../services/print-document-form.service'

@Component({
  selector: 'venio-print-endorsement-option',
  templateUrl: './print-endorsement-option.component.html',
  styleUrls: ['./print-endorsement-option.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PrintEndorsementOptionComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  public get tiffSourceOption(): FormGroup {
    return this.printDocumentFormService.printForm.get(
      'tiffSourceOption'
    ) as FormGroup
  }

  /**
   * List of location options for the dropdown.
   */
  public locationOptions: LocationOption[] = [
    { name: 'Top Left', value: 'TOP_LEFT' },
    { name: 'Top Center', value: 'TOP_CENTER' },
    { name: 'Top Right', value: 'TOP_RIGHT' },
    { name: 'Bottom Left', value: 'BOTTOM_LEFT' },
    { name: 'Bottom Center', value: 'BOTTOM_CENTER' },
    { name: 'Bottom Right', value: 'BOTTOM_RIGHT' },
  ]

  /**
   * List of page numbering options for the dropdown.
   */
  private _pageNumberingList: { name: string; value: string }[] = [
    { name: 'None', value: 'NONE' },
    { name: 'File Id', value: 'FILE_ID' },
    { name: 'File ID with Page Number', value: 'FILE_ID_WITH_PAGE_NUMBER' },
    { name: 'Document Unique Identifier', value: 'Document_Unique_Identifier' },
    { name: 'Bates Number', value: 'BATES_NUMBER' },
  ]

  public pageNumberingList: { name: string; value: string }[] = []

  constructor(
    private printDocumentFormService: PrintDocumentFormService,
    private cdr: ChangeDetectorRef
  ) {}

  public ngOnInit(): void {
    this.setPageNumberingList()
    this.enableDisableStamp()
    this.enableDisableTiffSource()
  }

  private setPageNumberingList(): void {
    if (
      this.tiffSourceOption.get('printOriginal').value === 'PRINT_PRODUCED_TIFF'
    ) {
      this.pageNumberingList = this._pageNumberingList.slice()
    } else {
      this.pageNumberingList = this._pageNumberingList.filter(
        (x) => x.value.toUpperCase() !== 'BATES_NUMBER'
      )
    }
  }

  public isConfidentialStampingChecked(): string | boolean {
    return this.tiffSourceOption.get('isConfidentialStamping').value ? true : ''
  }

  private enableDisableStamp(): void {
    const _tiffSourceOption = this.tiffSourceOption
    if (!_tiffSourceOption) {
      return
    }

    _tiffSourceOption
      .get('isConfidentialStamping')
      .valueChanges.pipe(debounceTime(400), takeUntil(this.toDestroy$))
      .subscribe({
        next: (isChecked) => {
          const StampTextCtrl = _tiffSourceOption.get(
            'confidentialStampingText'
          )

          // Enable/disable based on checked value
          isChecked ? StampTextCtrl.enable() : StampTextCtrl.disable()

          // Reset to default
          StampTextCtrl.reset('')
        },
      })
  }

  private enableDisableTiffSource(): void {
    const _tiffSourceOption = this.tiffSourceOption
    if (!_tiffSourceOption) {
      return
    }
    _tiffSourceOption
      .get('printOriginal')
      .valueChanges.pipe(debounceTime(400), takeUntil(this.toDestroy$))
      .subscribe((selectedTiff) => {
        if (selectedTiff === 'PRINT_PRODUCED_TIFF') {
          this.pageNumberingList = this._pageNumberingList.slice()
          this.cdr.markForCheck()
        } else {
          this.pageNumberingList = this._pageNumberingList.filter(
            (x) => x.value.toUpperCase() !== 'BATES_NUMBER'
          )
          if (
            this.tiffSourceOption.get('endorsementValue').value ===
            'BATES_NUMBER'
          ) {
            this.tiffSourceOption
              .get('endorsementValue')
              .setValue('FILE_ID_WITH_PAGE_NUMBER')
          }
          this.cdr.markForCheck()
        }
      })
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
