import { ComponentFixture, TestBed } from '@angular/core/testing'
import { PrintOutputOptionComponent } from './print-output-option.component'
import { DatePipe } from '@angular/common'
import { LabelModule } from '@progress/kendo-angular-label'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { ReactiveFormsModule } from '@angular/forms'
import { AnimationBuilder } from '@angular/animations'
import { PrintDocumentFormService } from '../../services/print-document-form.service'

describe('PrintOutputOptionComponent', () => {
  let component: PrintOutputOptionComponent
  let fixture: ComponentFixture<PrintOutputOptionComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PrintOutputOptionComponent],
      imports: [LabelModule, DropDownListModule, ReactiveFormsModule],
      providers: [AnimationBuilder, DatePipe, PrintDocumentFormService],
    }).compileComponents()

    fixture = TestBed.createComponent(PrintOutputOptionComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
