import { Injectable, signal } from '@angular/core'
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup } from '@angular/forms'
import { DocumentShareFormModel } from '../models/document-share.models'
import { SharedDocumentDetailModel } from '@venio/data-access/review'

@Injectable({ providedIn: 'root' })
export class DocumentShareFormService {
  private initialValue: DocumentShareFormModel

  public documentShareForm!: FormGroup

  public readonly MAX_LINK_EXPIRY_DAYS = 150

  // Signal to notify changes
  public isReshareBtnDisabled = signal(false)

  public isUnsharedBtnDisabled = signal(false)

  private selectedExternalUsers: string[]

  private selectedInternalUsers: string[]

  constructor(private fb: FormBuilder) {
    this.initializeForm()
  }

  /**
   * Resets the document share form
   * @returns {void}
   */
  public resetForm(): void {
    this.documentShareForm.reset(this.initialValue)
  }

  /**
   * Initializes the document share form
   * @returns {void}
   */
  private initializeForm(): void {
    this.documentShareForm = this.fb.group({
      shareName: '',
      sharedExpiryDate: new Date(Date.now()),
      sendCopyToMe: false,
      internalUsers: this.fb.array([]),
      externalUsers: this.fb.array([]),
      shareToExternalUsers: false,
      allowToTag: false,
      allowToAddNotes: false,
      allowToViewAnalyzePage: false,
      allowRedaction: false,
      instruction: '',
      newEmail: '',
    })

    this.initialValue =
      this.documentShareForm.getRawValue() as DocumentShareFormModel
  }

  /**
   * Initializes the form for edit flow with pre-filled data
   * @param {DocumentShareFormModel} formData - Data to prefill the form
   * @returns {void}
   */
  public initializeFormForEdit(formData: SharedDocumentDetailModel): void {
    this.documentShareForm = this.fb.group({
      shareName: formData?.shareName || '',
      sharedExpiryDate:
        new Date(formData?.sharedExpiryDate) || new Date(Date.now()),
      sendCopyToMe: formData?.carbonCopySender || false,
      internalUsers: this.fb.array(
        (formData?.internalUsers || []).map((user) => this.fb.control(user))
      ),
      externalUsers: this.fb.array(
        (formData?.externalUsers || []).map((user) => this.fb.control(user))
      ),
      allowToTag: formData?.allowToTagUntag || false,
      allowToAddNotes: formData?.allowToAddDocumentNotes || false,
      allowToViewAnalyzePage: formData?.isWrite || false,
      allowRedaction: formData?.allowToApplyRedaction || false,
      instruction: formData?.shareInstruction || '',
    })
  }

  public getFormValue(isEdit?: boolean): DocumentShareFormModel {
    const form = this.documentShareForm.getRawValue() as DocumentShareFormModel

    if (!isEdit) {
      // Remove null or empty values from arrays
      form.internalUsers = (form.internalUsers || []).filter((u) => u?.trim())
      form.externalUsers = (form.externalUsers || []).filter((u) => u?.trim())
    }
    return form
  }

  public setInternalUser(
    selectedInternalUsers: string[],
    isEdit = false
  ): void {
    const internalUsersArray = this.documentShareForm.get(
      'internalUsers'
    ) as FormArray

    internalUsersArray.clear()
    selectedInternalUsers.forEach((extUser) => {
      internalUsersArray.push(this.fb.control(extUser))
    })
    if (isEdit) {
      this.selectedInternalUsers = selectedInternalUsers
      this.toggleUnsharedBtn(this.selectedInternalUsers?.length > 0)
    } else {
      // Update the value of the FormArray
      this.documentShareForm.patchValue({
        internalUsers: internalUsersArray.value,
      })
    }
  }

  public setExternalUser(
    selectedExternalUsers: string[],
    isEdit = false
  ): void {
    const externalUsersArray = this.documentShareForm.get(
      'externalUsers'
    ) as FormArray

    externalUsersArray.clear()

    selectedExternalUsers.forEach((extUser) => {
      externalUsersArray.push(this.fb.control(extUser))
    })

    if (isEdit) {
      this.selectedExternalUsers = selectedExternalUsers
      this.toggleUnsharedBtn(this.selectedExternalUsers?.length > 0)
    } else {
      // Update the value of the FormArray
      this.documentShareForm.patchValue({
        externalUsers: externalUsersArray.value,
      })
    }
  }

  public getUpdatedExternalUsers(): string[] {
    return this.selectedExternalUsers?.length === 0
      ? []
      : this.selectedExternalUsers
  }

  public getUpdatedInternalUsers(): string[] {
    return this.selectedInternalUsers?.length === 0
      ? []
      : this.selectedInternalUsers
  }

  /**
   * Triggers the toggle signal
   */
  public toggleResharedBtn(value): void {
    // Toggle the value of the signal
    this.isReshareBtnDisabled.set(value)
  }

  public toggleUnsharedBtn(value): void {
    // Toggle the value of the signal
    this.isUnsharedBtnDisabled.set(value)
  }

  // Signals to track selected row counts
  private internalSelectedRowsCountSignal = signal<number>(0)

  private externalSelectedRowsCountSignal = signal<number>(0)

  // Public access to signals
  public getInternalSelectedRowsCount(): number {
    return this.internalSelectedRowsCountSignal()
  }

  public getExternalSelectedRowsCount(): number {
    return this.externalSelectedRowsCountSignal()
  }

  // Methods to update signals
  public updateInternalSelectedRowsCount(count: number): void {
    this.internalSelectedRowsCountSignal.set(count)
  }

  public updateExternalSelectedRowsCount(count: number): void {
    this.externalSelectedRowsCountSignal.set(count)
  }
}
