import { NgModule, NO_ERRORS_SCHEMA } from '@angular/core'
import { CommonModule, NgComponentOutlet } from '@angular/common'
import { DocumentShareComponent } from './components/document-share/document-share.component'
import { DialogModule } from '@progress/kendo-angular-dialog'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DocumentShareOptionsComponent } from './components/document-share-options/document-share-options.component'
import { UserOptionsComponent } from './components/user-options/user-options.component'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { EditorModule } from '@progress/kendo-angular-editor'
import { DocumentShareInstructionComponent } from './components/document-share-instruction/document-share-instruction.component'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { DateInputsModule } from '@progress/kendo-angular-dateinputs'
import { DocumentShareFormService } from './services/document-share-form.service'
import { NotificationModule } from '@progress/kendo-angular-notification'
@NgModule({
  declarations: [
    DocumentShareComponent,
    DocumentShareOptionsComponent,
    UserOptionsComponent,
    DocumentShareInstructionComponent,
  ],
  imports: [
    CommonModule,
    DialogModule,
    GridModule,
    InputsModule,
    LabelModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonsModule,
    SvgLoaderDirective,
    EditorModule,
    TooltipsModule,
    LoaderModule,
    DateInputsModule,
    NotificationModule,
    NgComponentOutlet,
  ],
  schemas: [NO_ERRORS_SCHEMA],
  providers: [DocumentShareFormService],
  exports: [DocumentShareComponent],
})
export class DocumentShareModule {}
