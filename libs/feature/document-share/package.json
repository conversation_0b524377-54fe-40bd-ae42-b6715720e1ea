{"name": "@venio/feature/document-share", "version": "0.0.1", "peerDependencies": {"@angular/common": "18.2.1", "@angular/core": "18.2.1", "@progress/kendo-angular-editor": "^16.8.0", "@angular/platform-browser": "18.2.1", "@ngrx/store": "18.0.2", "@angular/router": "18.2.1", "@venio/data-access/review": "0.0.1", "@angular/forms": "18.2.1", "rxjs": "7.8.1", "@progress/kendo-angular-buttons": "^16.8.0", "@progress/kendo-angular-inputs": "^16.8.0", "@progress/kendo-angular-label": "^16.8.0", "@venio/feature/notification": "0.0.1", "@progress/kendo-angular-notification": "^16.8.0", "@progress/kendo-angular-dialog": "^16.8.0", "@venio/feature/shared/directives": "0.0.1", "@progress/kendo-angular-dropdowns": "^16.8.0", "@progress/kendo-angular-grid": "^16.8.0", "@progress/kendo-angular-indicators": "^16.8.0", "@progress/kendo-svg-icons": "^3.0.0", "@progress/kendo-angular-tooltip": "^16.8.0", "@nx/angular": "19.6.4", "@progress/kendo-angular-dateinputs": "^16.8.0", "dayjs": "1.11.10", "@venio/shared/models/interfaces": "0.0.1", "lodash": "^4.17.21"}, "dependencies": {}, "sideEffects": false}