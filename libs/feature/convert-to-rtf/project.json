{"name": "feature-convert-to-rtf", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/convert-to-rtf/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/feature/convert-to-rtf/ng-package.json", "tailwindConfig": "libs/feature/convert-to-rtf/tailwind.config.js"}, "configurations": {"production": {"tsConfig": "libs/feature/convert-to-rtf/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/convert-to-rtf/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/convert-to-rtf/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/feature/convert-to-rtf/**/*.ts", "libs/feature/convert-to-rtf/**/*.html", "libs/feature/convert-to-rtf/package.json"]}}}}