<form [formGroup]="imageForm" class="t-px-2 t-py-4">
  <div class="t-p-2 t-pb-0 t-flex t-flex-row">
    <div class="t-flex t-flex-col t-w-1/2">
      <div class="t-mb-8">
        <div class="t-flex t-items-center">
          <div>
            <input
              data-qa="convert-to-image-project-page-limit"
              id="default-limit"
              type="radio"
              value="PROJECT_PAGE_LIMIT"
              formControlName="pageLimitOption"
              kendoRadioButton />
          </div>
          <div class="t-w-auto t-flex t-flex-wrap">
            <kendo-label
              class="t-px-[5px] t-py-2 t-relative t-top-[2px]"
              for="default-limit"
              [text]="
                'Use default limit from project settings(' +
                projectPageLimit +
                ')'
              "
              [ngClass]="{
                't-text-[#2F3080] t-font-medium':
                  this.imageForm.get('pageLimitOption').value ===
                  'PROJECT_PAGE_LIMIT'
              }"></kendo-label>
          </div>
        </div>
        <div class="t-flex t-items-center">
          <div>
            <input
              data-qa="convert-to-image-custom-page-limit"
              id="custom-limit"
              type="radio"
              value="CUSTOM_PAGE_LIMIT"
              formControlName="pageLimitOption"
              kendoRadioButton />
          </div>
          <div class="t-w-auto t-flex t-flex-wrap">
            <kendo-label
              class="t-px-[5px] t-py-2 t-relative t-top-[2px]"
              id="custom-limit-lbl"
              text="Custom max page limit"
              [ngClass]="{
                't-text-[#2F3080] t-font-medium':
                  this.imageForm.get('pageLimitOption').value ===
                  'CUSTOM_PAGE_LIMIT'
              }"></kendo-label>
          </div>
          <div class="t-w-40">
            <kendo-numerictextbox
              placeholder="Set Timeout (In Mins)"
              id="custom-page-limit"
              data-qa="convert-to-image-custom-page-limit"
              placeholder=""
              formControlName="customMaxpageLimit"
              class="t-ml-1"
              format="#"
              [min]="1"
              [max]="projectPageLimit"
              [autoCorrect]="true"
              [spinners]="true"></kendo-numerictextbox>
          </div>
        </div>
      </div>

      <!-- Image DPI & Dimension Section -->
      <div class="t-flex t-flex-col" formGroupName="imageDimensionSettings">
        <div class="t-flex t-items-center t-pb-2">
          <kendo-label
            class="t-font-bold t-text-[#263238] t-text-[16px] t-tracking-[0.02px] t-leading-[20px]"
            text="Image DPI & Dimension"></kendo-label>
        </div>

        <!-- Default Image Dimension -->
        <div class="t-flex t-items-center">
          <div>
            <input
              data-qa="convert-to-image-default-dimension"
              id="default-dimension"
              type="radio"
              value="DEFAULT"
              formControlName="imageDimension"
              kendoRadioButton />
          </div>
          <div class="t-w-auto t-flex t-flex-wrap">
            <kendo-label
              class="t-px-[5px] t-py-2 t-relative t-top-[2px]"
              for="default-dimension"
              text="Default Image Dimension"
              [ngClass]="{
                't-text-[#2F3080] t-font-medium':
                  this.imageForm.get('imageDimensionSettings.imageDimension')
                    .value === 'DEFAULT'
              }"></kendo-label>
          </div>
        </div>

        <!-- Retain Original Image Dimension -->
        <div class="t-flex t-items-center">
          <div>
            <input
              data-qa="convert-to-image-original-dimension"
              id="original-dimension"
              type="radio"
              value="ORIGINAL"
              formControlName="imageDimension"
              kendoRadioButton />
          </div>
          <div class="t-w-auto t-flex t-flex-wrap">
            <kendo-label
              class="t-px-[5px] t-py-2 t-relative t-top-[2px]"
              for="original-dimension"
              text="Retain Original Image Dimension"
              [ngClass]="{
                't-text-[#2F3080] t-font-medium':
                  this.imageForm.get('imageDimensionSettings.imageDimension')
                    .value === 'ORIGINAL'
              }"></kendo-label>
          </div>
        </div>

        <!-- Custom Image Dimension -->
        <div class="t-flex t-items-center">
          <div>
            <input
              data-qa="convert-to-image-custom-dimension"
              id="custom-dimension"
              type="radio"
              value="CUSTOM"
              formControlName="imageDimension"
              kendoRadioButton />
          </div>
          <div class="t-w-auto t-flex t-flex-wrap">
            <kendo-label
              class="t-px-[5px] t-py-2 t-relative t-top-[2px]"
              for="custom-dimension"
              text="Custom Image Dimension"
              [ngClass]="{
                't-text-[#2F3080] t-font-medium':
                  this.imageForm.get('imageDimensionSettings.imageDimension')
                    .value === 'CUSTOM'
              }"></kendo-label>
          </div>
        </div>

        <!-- Width & Height Custom Dimensions -->
        <div class="t-flex t-flex-row">
          <!-- Width -->
          <div class="t-flex t-items-center t-mr-4 t-w-28">
            <div class="t-flex t-flex-col">
              <div class="k-pl-2">
                <kendo-label
                  id="width-lbl"
                  text="Width"
                  class="t-text-[#263238] t-text-[10px] t-font-medium t-tracking-[1.2px] t-leading-[20px] t-uppercase">
                </kendo-label>
              </div>
              <div class="">
                <kendo-numerictextbox
                  id="custom-width"
                  data-qa="convert-to-image-custom-width"
                  placeholder=""
                  formControlName="customWidth"
                  format="#"
                  [min]="1"
                  [max]="10000"
                  [autoCorrect]="true"
                  [spinners]="true"></kendo-numerictextbox>
              </div>
            </div>
          </div>

          <!-- Height -->
          <div class="t-flex t-items-center t-mr-2 t-w-28">
            <div class="t-flex t-flex-col">
              <div class="k-pl-2">
                <kendo-label
                  id="height-lbl"
                  text="Height"
                  class="t-text-[#263238] t-text-[10px] t-font-medium t-tracking-[1.2px] t-leading-[20px] t-uppercase"></kendo-label>
              </div>
              <div>
                <kendo-numerictextbox
                  id="custom-height"
                  data-qa="convert-to-image-custom-height"
                  placeholder=""
                  formControlName="customHeight"
                  format="#"
                  [min]="1"
                  [max]="10000"
                  [autoCorrect]="true"
                  [spinners]="true"></kendo-numerictextbox>
              </div>
            </div>
          </div>

          <!-- DPI Selector -->
          <div class="t-flex t-flex-row">
            <div class="t-flex t-items-center">
              <div class="t-flex t-flex-col">
                <div class="k-pl-2">
                  <kendo-label
                    id="select-dpi-lbl"
                    text="Select DPI"
                    class="t-text-[#263238] t-text-[10px] t-font-medium t-tracking-[1.2px] t-leading-[20px] t-uppercase">
                  </kendo-label>
                </div>
                <div class="">
                  <kendo-dropdownlist
                    id="dpi-selector"
                    data-qa="convert-to-image-dpi-selector"
                    formControlName="dpi"
                    [data]="dpiOptions"
                    [valuePrimitive]="true">
                  </kendo-dropdownlist>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="v-dashed-sperator t-mx-[15px] t-opacity-100 t-w-[1px] t-h-auto"></div>

    <div class="t-flex t-flex-col" formGroupName="systemBates">
      <div class="t-flex t-items-center t-pb-2">
        <kendo-label
          class="t-font-bold t-text-[#263238] t-text-[16px] t-tracking-[0.02px] t-leading-[20px]"
          text="Apply System bates Number option"></kendo-label>
      </div>

      <!--Generates bates number for generated images-->
      <div class="t-flex t-items-center">
        <div>
          <input
            id="generate-bates"
            data-qa="convert-to-image-generate-bates"
            type="checkbox"
            formControlName="generateBates"
            kendoCheckBox />
        </div>
        <div class="t-flex t-flex-wrap">
          <kendo-label
            class="t-px-[5px] t-py-2 t-text-[#5E6366] t-align-middle t-relative t-top-[2px]"
            for="generate-bates"
            text="Generates bates number for generated images"></kendo-label>
        </div>
        <div></div>
        <div></div>
      </div>

      <!--Prefix row-->
      <div class="t-flex t-items-center t-pb-2">
        <div class="t-flex t-flex-row">
          <div class="t-flex t-flex-col t-mr-4 t-w-[183px]">
            <div class="k-pl-2">
              <kendo-label
                class="t-text-[#263238] t-text-[10px] t-font-medium t-tracking-[1.2px] t-leading-[20px] t-uppercase"
                id="prefix-lbl"
                text="Prefix"></kendo-label>
            </div>
            <div>
              <kendo-dropdownlist
                id="prefix-download-list"
                data-qa="convert-to-image-prefix-download-list"
                formControlName="prefixType"
                [data]="prifixDownloadList"
                [valuePrimitive]="true">
              </kendo-dropdownlist>
            </div>
          </div>
          <div class="t-w-[183px]">
            <div class="k-pl-2"><kendo-label></kendo-label></div>
            <div *ngIf="isPrefixTypeText">
              <kendo-textbox
                id="prefix-text"
                data-qa="convert-to-image-prefix-text"
                [hidden]="!isPrefixTypeText"
                formControlName="prefixText"
                placeholder="IMG"></kendo-textbox>
            </div>
            <div *ngIf="!isPrefixTypeText">
              <kendo-dropdownlist
                id="prefix-field"
                data-qa="convert-to-image-prefix-field"
                class="t-ml-1"
                [hidden]="isPrefixTypeText"
                formControlName="prefixField"
                placeholder="DOCUMENT_UNIQUE_IDENTIFIER"
                [data]="customFielddb"
                [valuePrimitive]="true"></kendo-dropdownlist>
            </div>
          </div>
        </div>
      </div>

      <div class="t-flex t-flex-row t-pb-2">
        <!--Start Number-->
        <div class="t-flex t-items-center t-mr-4 t-w-[183px]">
          <div class="t-flex t-flex-col">
            <div class="k-pl-2">
              <kendo-label
                id="start-number-lbl"
                text="Start Number"
                class="t-text-[#263238] t-text-[10px] t-font-medium t-tracking-[1.2px] t-leading-[20px] t-uppercase">
              </kendo-label>
            </div>
            <div class="">
              <kendo-numerictextbox
                id="start-number"
                data-qa="convert-to-image-start-number"
                placeholder=""
                formControlName="startingNum"
                format="#"
                [min]="1"
                [max]="2147483647"
                [autoCorrect]="true"
                [spinners]="true"></kendo-numerictextbox>
            </div>
          </div>
        </div>

        <!--Padding-->
        <div class="t-flex t-items-center t-w-[183px]">
          <div class="t-flex t-flex-col">
            <div class="k-pl-2">
              <kendo-label
                id="padding-lbl"
                text="Padding"
                class="t-text-[#263238] t-text-[10px] t-font-medium t-tracking-[1.2px] t-leading-[20px] t-uppercase"></kendo-label>
            </div>
            <div>
              <kendo-numerictextbox
                id="padding"
                data-qa="convert-to-image-padding"
                placeholder=""
                formControlName="padding"
                format="#"
                [min]="1"
                [max]="99"
                [autoCorrect]="true"
                [spinners]="true"></kendo-numerictextbox>
            </div>
          </div>
        </div>
      </div>

      <!--Radio buttons-->
      <div class="t-flex t-flex-row t-pb-2">
        <div class="t-flex t-items-center t-mr-2">
          <div class="t-p-[2px]">
            <input
              id="image-without-branding"
              data-qa="convert-to-image-without-branding"
              formControlName="brandingBates"
              type="radio"
              [value]="false"
              [ngClass]="{
                'k-disabled': !generateImageRadioEnabled,
                '': generateImageRadioEnabled
              }"
              kendoRadioButton />
          </div>
          <div>
            <kendo-label
              class="t-px-[5px] t-py-2 t-relative t-top-[2px] t-flex t-flex-wrap t-text-[13px]"
              for="image-without-branding"
              text="Without branding bates number"
              [ngClass]="{
                'k-disabled': !generateImageRadioEnabled,
                '': generateImageRadioEnabled,
                't-text-[#2F3080] t-font-medium':
                  !this.imageForm.get('systemBates.brandingBates').value &&
                  generateImageRadioEnabled
              }"></kendo-label>
          </div>
          <div></div>
        </div>
        <div class="t-flex t-items-center">
          <div class="t-p-[2px]">
            <input
              id="image-with-branding"
              data-qa="convert-to-image-with-branding"
              formControlName="brandingBates"
              type="radio"
              [value]="true"
              [ngClass]="{
                'k-disabled': !generateImageRadioEnabled,
                '': generateImageRadioEnabled
              }"
              kendoRadioButton />
          </div>
          <div>
            <kendo-label
              class="t-px-[5px] t-py-2 t-relative t-top-[2px] t-flex t-flex-wrap t-text-[13px]"
              for="image-with-branding"
              text="With branding bates number"
              [ngClass]="{
                'k-disabled': !generateImageRadioEnabled,
                '': generateImageRadioEnabled,
                't-text-[#2F3080] t-font-medium':
                  this.imageForm.get('systemBates.brandingBates').value &&
                  generateImageRadioEnabled
              }"></kendo-label>
          </div>
          <div></div>
        </div>
      </div>
      <div class="t-flex t-items-center">
        <div>
          <input
            id="ignore-auto"
            data-qa="convert-to-image-ignore-auto"
            type="checkbox"
            formControlName="ignoreAutoTiffJobsForMediaProcessingStatus"
            kendoCheckBox />
        </div>
        <div>
          <kendo-label
            class="t-px-[5px] t-py-2 t-align-middle t-relative t-top-[2px]"
            for="ignore-auto"
            text="Ignore auto image jobs for media processing status"></kendo-label>
        </div>
        <div></div>
        <div></div>
      </div>
    </div>
  </div>
</form>
