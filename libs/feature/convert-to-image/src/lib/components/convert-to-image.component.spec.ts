import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ConvertToImageComponent } from './convert-to-image.component'
import {
  ConvertDocumentFacade,
  ConvertDocumentTab,
  ConvertToImageFacade,
  ImageFileTypeDetailsResponseModel,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { ConverToImageFormService } from '../services/convert-to-image-form.service'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import {
  TextBoxModule,
  NumericTextBoxModule,
} from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { GridModule } from '@progress/kendo-angular-grid'
import { of } from 'rxjs'
import { JobStatusFacade } from '@venio/data-access/common'

// ... imports for your Kendo modules, etc

describe('ConvertToImageComponent - DPI & Dimension value passing', () => {
  let component: ConvertToImageComponent
  let fixture: ComponentFixture<ConvertToImageComponent>
  let convertToImageFacade: any
  let convertDocumentFacade: any
  let jobStatusFacade: any

  const imageType: ImageFileTypeDetailsResponseModel = {
    fileTypeGroup: 'Email',
    fileTypeGroupId: 14,
    cNT: 20,
    myPageLimit: 'Max Pages',
    imagingEngine: 'DEFAULT',
    colorConversion: 'BLACK_AND_WHITE',
    selected: true,
    pageLimitdb: [
      'Max Pages',
      'All Pages',
      '10',
      '100',
      '200',
      '300',
      '400',
      '500',
      '1000',
      '2000',
    ],
    engineDb: [
      'Default',
      'Non-Native Imaging Engine',
      'Generic Imaging Engine',
      'Do not Convert',
      'MS Outlook Imaging Engine',
    ],
    colorDb: [
      'Default',
      'BLACK_AND_WHITE',
      'GRAYSCALE',
      'COLOR',
      'COLOR_FOR_COLOR',
    ],
  }

  beforeEach(async () => {
    convertToImageFacade = {
      resetConvertToImageState: jest.fn(),
      fetchImageSummaryFileTypes: jest.fn(),
      getImageSummary$: of([]),
      getImageFileTypes$: of([imageType]),
      queueForImageConversion: jest.fn(),
      getImageConversionQueueResponse$: of({
        success: true,
        message: 'success',
      }),
      clearImageConversionQueueResponse: jest.fn(),
    }
    convertDocumentFacade = {
      startConvertDocument$: of(ConvertDocumentTab.ImageTab),
      resetStartConvertDocument: jest.fn(),
      getLaunchOptionState$: of(true),
      getRelaunchOptionState$: of(true),
      setLaunchOption: jest.fn(),
      setLaunchOptionAvailability: jest.fn(),
      setRelaunchOptionAvailability: jest.fn(),
      setResponseMessage: jest.fn(),
    }
    jobStatusFacade = {
      setColumnNames: jest.fn(),
    }

    await TestBed.configureTestingModule({
      declarations: [ConvertToImageComponent],
      imports: [
        NoopAnimationsModule,
        LoaderModule,
        DropDownListModule,
        LayoutModule,
        LabelModule,
        TextBoxModule,
        NumericTextBoxModule,
        FormsModule,
        ReactiveFormsModule,
        GridModule,
      ],
      providers: [
        { provide: ConvertToImageFacade, useValue: convertToImageFacade },
        { provide: ConvertDocumentFacade, useValue: convertDocumentFacade },
        { provide: JobStatusFacade, useValue: jobStatusFacade },
        ConverToImageFormService,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ConvertToImageComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should send custom DPI & Dimension values when Custom Image Dimension is chosen', () => {
    // set selection
    component.updatedSelection.push(imageType)

    const form = component.imageForm

    // setting just to pass validations
    form.get('pageLimitOption').setValue('PROJECT_PAGE_LIMIT')
    form.get('customMaxpageLimit').setValue(null)
    form.get('systemBates.startingNum').setValue(1)
    form.get('systemBates.padding').setValue(1)
    form.get('systemBates.prefixType').setValue('TEXT')
    form.get('systemBates.prefixText').setValue('IMG')

    // values to be verified
    form.get('imageDimensionSettings.imageDimension').setValue('CUSTOM')
    form.get('imageDimensionSettings.customWidth').setValue(400)
    form.get('imageDimensionSettings.customHeight').setValue(600)
    form.get('imageDimensionSettings.dpi').setValue(300)

    component.startConvert()

    expect(convertToImageFacade.queueForImageConversion).toHaveBeenCalledTimes(
      1
    )

    const callArg = (convertToImageFacade.queueForImageConversion as jest.Mock)
      .mock.calls[0][0]
    expect(callArg.outputImageDimension.originalImageDimension).toBe(false)
    expect(callArg.outputImageDimension.defaultDimension).toBe(false)
    expect(callArg.outputImageDimension.customDimension).toBe(true)
  })

  it('should send originalImageDimension flag as true when Retain Original Image Dimension is chosen', () => {
    // set selection
    component.updatedSelection.push(imageType)

    const form = component.imageForm

    // setting just to pass validations
    form.get('pageLimitOption').setValue('PROJECT_PAGE_LIMIT')
    form.get('customMaxpageLimit').setValue(null)
    form.get('systemBates.startingNum').setValue(1)
    form.get('systemBates.padding').setValue(1)
    form.get('systemBates.prefixType').setValue('TEXT')
    form.get('systemBates.prefixText').setValue('IMG')

    // values to be verified
    form.get('imageDimensionSettings.imageDimension').setValue('ORIGINAL')

    component.startConvert()

    expect(convertToImageFacade.queueForImageConversion).toHaveBeenCalledTimes(
      1
    )

    const callArg = (convertToImageFacade.queueForImageConversion as jest.Mock)
      .mock.calls[0][0]
    expect(callArg.outputImageDimension.defaultDimension).toBe(false)
    expect(callArg.outputImageDimension.customDimension).toBe(false)
    expect(callArg.outputImageDimension.originalImageDimension).toBe(true)
  })

  it('should send defaultDimension flag as true when Default Image Dimension is chosen', () => {
    // set selection
    component.updatedSelection.push(imageType)

    const form = component.imageForm

    // setting just to pass validations
    form.get('pageLimitOption').setValue('PROJECT_PAGE_LIMIT')
    form.get('customMaxpageLimit').setValue(null)
    form.get('systemBates.startingNum').setValue(1)
    form.get('systemBates.padding').setValue(1)
    form.get('systemBates.prefixType').setValue('TEXT')
    form.get('systemBates.prefixText').setValue('IMG')

    // values to be verified
    form.get('imageDimensionSettings.imageDimension').setValue('DEFAULT')

    component.startConvert()

    expect(convertToImageFacade.queueForImageConversion).toHaveBeenCalledTimes(
      1
    )

    const callArg = (convertToImageFacade.queueForImageConversion as jest.Mock)
      .mock.calls[0][0]
    expect(callArg.outputImageDimension.customDimension).toBe(false)
    expect(callArg.outputImageDimension.originalImageDimension).toBe(false)
    expect(callArg.outputImageDimension.defaultDimension).toBe(true)
  })
})
