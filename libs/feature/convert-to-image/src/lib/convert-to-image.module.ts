import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ConvertToImageComponent } from './components/convert-to-image.component'
import { VenioTreelistModule } from '@venio/shared/treelist'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import {
  DropDownListModule,
  DropDownsModule,
} from '@progress/kendo-angular-dropdowns'
import {
  ExpansionPanelModule,
  LayoutModule,
} from '@progress/kendo-angular-layout'
import {
  TextBoxModule,
  NumericTextBoxModule,
  CheckBoxModule,
  RadioButtonModule,
} from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { ConverToImageFormService } from './services/convert-to-image-form.service'
import { GridModule } from '@progress/kendo-angular-grid'
import { AdvancedOptionComponent } from './components/advanced-option/advanced-option.component'

@NgModule({
  imports: [
    CommonModule,
    LoaderModule,
    VenioTreelistModule,
    DropDownListModule,
    LayoutModule,
    LabelModule,
    TextBoxModule,
    NumericTextBoxModule,
    FormsModule,
    ReactiveFormsModule,
    CheckBoxModule,
    RadioButtonModule,
    ExpansionPanelModule,
    GridModule,
    DropDownsModule,
  ],
  providers: [ConverToImageFormService],
  declarations: [ConvertToImageComponent, AdvancedOptionComponent],
  exports: [ConvertToImageComponent],
})
export class ConvertToImageModule {}
