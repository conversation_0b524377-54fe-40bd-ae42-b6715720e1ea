{"name": "svg-icon", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/feature/svg-icon/src", "prefix": "venio", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"tsConfig": "libs/feature/svg-icon/tsconfig.lib.json", "project": "libs/feature/svg-icon/ng-package.json", "tailwindConfig": "libs/feature/svg-icon/tailwind.config.js"}, "configurations": {"production": {"tsConfig": "libs/feature/svg-icon/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/feature/svg-icon/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/feature/svg-icon/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}