<div class="t-flex t-p-3 t-w-full t-pt-0 t-pb-0 t-pr-[.4rem]">
  <div class="t-w-full t-flex">
    <ng-container *ngIf="isComponentReady">
      <div class="t-flex t-p-3 t-pr-[.4rem] t-w-full t-pt-0 t-pb-0 t-h-auto">
        <div class="t-w-full t-flex t-flex-col">
          <ng-container *ngIf="isComponentReady">
            <div
              class="t-flex t-gap-2 t-w-full t-items-center t-justify-between">
              <div class="t-flex t-gap-2 t-w-full t-items-center">
                <div
                  class="t-flex t-gap-[0.2rem]"
                  *ngIf="showExpandCollapse()"
                  kendoTooltip>
                  @for (icon of svgIconForTagListToggleControls; track
                  icon.actionType) {
                  <button
                    kendoButton
                    #parentElTag
                    class="t-p-1 t-cursor-pointer t-h-fit"
                    [disabled]="isDocumentTagsLoading$ | async"
                    (click)="toggleExpandCollapseAll(icon.actionType)"
                    fillMode="clear"
                    [title]="icon.actionText">
                    <span
                      venioSvgLoader
                      [color]="icon.iconColor"
                      applyEffectsTo="fill"
                      [parentElement]="parentElTag.element"
                      [svgUrl]="icon.iconPath"
                      height="1rem"
                      width="1rem">
                      <kendo-loader size="small"></kendo-loader>
                    </span>
                  </button>
                  }
                </div>
                <div class="t-gap-[0.2rem]">
                  <kendo-dropdownbutton
                    kendoTooltip
                    *ngIf="isAITagExists()"
                    title="Tag Group"
                    [data]="tagGroupActions"
                    class="v-custom-dropdown-tags-btn t-w-[16px] t-max-h-[16px] t-justify-center t-items-center t-bg-[var(--v-custom-sky-blue)] !t-p-[2px] t-rounded-full t-align-top hover:t-bg-[#FFBB12]"
                    (itemClick)="tagGroupActionItemClick($event)"
                    [popupSettings]="{
                      popupClass:
                        'v-custom-dropdown-case-title ' + tagGroupHeaderClass,
                      animate: true
                    }"
                    [ngClass]="">
                    <kendo-svgicon
                      class="t-w-[12px] t-text-[#FFFFFF] t-h-[12px] t-ml-[1px]"
                      [icon]="icons.dotsIcon">
                    </kendo-svgicon>

                    <ng-template
                      kendoDropDownButtonItemTemplate
                      let-dropdownItem>
                      <span>{{ dropdownItem.title }}</span>
                    </ng-template>
                  </kendo-dropdownbutton>
                </div>
              </div>
              <div class="t-flex">
                <button
                  kendoButton
                  kendoTooltip
                  position="left"
                  #copyTag
                  *ngIf="
                    isUserAndSystemTagGroup() &&
                    canShowCopyTags() &&
                    allowToCopyTag()
                  "
                  class="t-p-0 t-cursor-pointer t-h-fit t-rounded-full"
                  fillMode="clear"
                  title="Copy tags from previous document"
                  (click)="applyCopyTag()">
                  <span
                    venioSvgLoader
                    color="#FFBB12"
                    applyEffectsTo="fill"
                    [parentElement]="copyTag.element"
                    svgUrl="assets/svg/icon-clone-splash.svg"
                    height="1.2rem"
                    width="1.2rem">
                    <kendo-loader size="small"></kendo-loader>
                  </span>
                </button>
              </div>
            </div>

            <ng-container
              *ngComponentOutlet="
                tagTreeWrapperComponentItem.component | async;
                inputs: tagTreeWrapperComponentItem.inputs
              "></ng-container>
          </ng-container>
        </div>
      </div>
    </ng-container>
  </div>
</div>
