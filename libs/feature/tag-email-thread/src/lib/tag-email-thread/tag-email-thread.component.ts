import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule, NgComponentOutlet } from '@angular/common'
import {
  DialogContentBase,
  DialogRef,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import {
  Observable,
  Subject,
  combineLatest,
  debounceTime,
  filter,
  take,
  takeUntil,
} from 'rxjs'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { LabelModule } from '@progress/kendo-angular-label'
import { TextAreaModule } from '@progress/kendo-angular-inputs'
import { FormsModule } from '@angular/forms'
import { ExpansionPanelModule } from '@progress/kendo-angular-layout'
import {
  DocumentsFacade,
  ReviewFacade,
  ReviewSetStateService,
  SearchFacade,
  UserRights,
} from '@venio/data-access/review'
import {
  DataAccessDocumentUtilityModule,
  DocumentTagFacade,
  TagActionType,
} from '@venio/data-access/document-utility'
import { ActivatedRoute } from '@angular/router'
import { DataAccessCommonModule, TagsFacade } from '@venio/data-access/common'
import { SVGIcon, chevronLeftIcon } from '@progress/kendo-svg-icons'
import {
  SvgLoaderDirective,
  UserGroupRightCheckDirective,
} from '@venio/feature/shared/directives'
import { PageControlActionType } from '@venio/shared/models/constants'
import { DocumentTagUtilityService } from '../utility-services/document-tag-utility'
import { VenioNotificationService } from '@venio/feature/notification'
import { DocumentTagComponent } from '../document-tag/document-tag.component'

@Component({
  selector: 'venio-tag-email-thread',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    IndicatorsModule,
    ButtonsModule,
    LabelModule,
    TextAreaModule,
    FormsModule,
    ExpansionPanelModule,
    DataAccessCommonModule,
    DataAccessDocumentUtilityModule,
    UserGroupRightCheckDirective,
    NgComponentOutlet,
    SvgLoaderDirective,
  ],
  templateUrl: './tag-email-thread.component.html',
  styleUrl: './tag-email-thread.component.scss',
})
export class TagEmailThreadComponent
  extends DialogContentBase
  implements OnInit, AfterViewInit, OnDestroy
{
  public tagActionType: TagActionType

  public inclusiveEmailTempTable: string

  private toDestroy$: Subject<void> = new Subject<void>()

  public rights = UserRights

  public dialogTitle: string

  public selectedDocumentCount = 0

  public isSaving: Observable<boolean>

  public isSavingData = signal<boolean>(false)

  public isTagAddUpdate = signal<boolean>(false)

  public isTagFormValid = signal<boolean>(false)

  public isRefreshNeeded = false

  public itemNote = ''

  public leftSvg: SVGIcon = chevronLeftIcon

  public documentTagComponent: Promise<typeof DocumentTagComponent>

  public tagSearchComponent = import('../tag-search/tag-search.component').then(
    (m) => m.TagSearchComponent
  )

  public lazyTagFormComp = import('../tag-form/tag-form.component').then(
    (m) => m.TagFormComponent
  )

  private reviewSetId: number

  constructor(
    public dialog: DialogRef,
    private searchFacade: SearchFacade,
    private documentTagFacade: DocumentTagFacade,
    private documentsFacade: DocumentsFacade,
    private documentTagUtilityService: DocumentTagUtilityService,
    private venioNotifiacationService: VenioNotificationService,
    private tagsFacade: TagsFacade,
    private reviewSetState: ReviewSetStateService,
    private reviewFacade: ReviewFacade,
    private activatedRoute: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef
  ) {
    super(dialog)
  }

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public ngOnInit(): void {
    this.#initReviewSetId()
    this.#fetchTagTree()
    this.#fetchInclusiveEmailData()
    this.#setTagDialogTitle()
    this.#selectTagCodingResponses()
    this.fetchProjectTags()
    this.fetchTagSettings()
    this.getSelectedDocumentCount()
    this.loadDocumentTagComponent()
  }

  public ngAfterViewInit(): void {
    this.#notifyTagFormValid()
    this.#selectTagAddOrUpdateResponses()
    this.#notifySwitchToTagViewAfterSuccess()
  }

  public loadDocumentTagComponent(): void {
    this.documentTagComponent = import(
      '../document-tag/document-tag.component'
    ).then(({ DocumentTagComponent }) => DocumentTagComponent)
  }

  #fetchInclusiveEmailData(): void {
    this.reviewFacade.prepareInclusiveEmailDataAction()
  }

  #initReviewSetId(): void {
    this.reviewSetId = this.reviewSetState.isBatchReview()
      ? this.reviewSetState.reviewSetId()
      : 0
  }

  #selectTagCodingResponses(): void {
    combineLatest([
      this.documentTagFacade.applyDocumentTagSuccessResponse$,
      this.documentTagFacade.applyDocumentTagErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => !!success || !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        if (!success && !error) return

        if (success) {
          this.venioNotifiacationService.showSuccess(success.message)
        } else if (error) {
          this.venioNotifiacationService.showError(error.message)
        }

        this.isSavingData.set(false)
        this.#resetDocumentTagResponseState()
        if (success) {
          this.#closeDialog()
        }
        this.changeDetectorRef.markForCheck()
      })
  }

  private getSelectedDocumentCount(): void {
    combineLatest([
      this.documentsFacade.getIsBatchSelected$,
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.getUnselectedDocuments$,
      this.searchFacade.getTotalHitCount$,
    ])
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(
        ([
          isBatchSelected,
          selectedDocuments,
          unselectedDocuments,
          totalHitCount,
        ]) => {
          if (isBatchSelected) {
            this.selectedDocumentCount =
              totalHitCount - unselectedDocuments.length
          } else {
            this.selectedDocumentCount = selectedDocuments.length
          }
        }
      )
  }

  #notifyTagFormValid(): void {
    this.tagsFacade.selectTagFormGroupValid$
      .pipe(
        debounceTime(100),
        filter((isValid) => Boolean(isValid)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((isValid) => {
        this.isTagFormValid.set(isValid)
      })
  }

  #selectTagAddOrUpdateResponses(): void {
    this.tagsFacade.selectTagAddOrUpdateSuccessResponse$
      .pipe(
        filter((success) => !!success),
        takeUntil(this.toDestroy$)
      )
      .subscribe((success) => {
        if (!success) return
        // this.newTagId = success.data
        // this.reset()
        this.fetchProjectTags()
        this.tagsFacade.notifyToSwitchTagView(false)

        this.isSavingData.set(false)
        //Switch To bulk tag after form Success
        // this.toggleTagFieldForm()
      })
  }

  public toggleTagFieldForm(): void {
    this.changeDetectorRef.markForCheck()
    this.isTagAddUpdate.set(!this.isTagAddUpdate())
    this.#setTagDialogTitle()
  }

  #notifySwitchToTagViewAfterSuccess(): void {
    this.tagsFacade.selectSwitchToTagViewNotified$
      .pipe(
        debounceTime(500),
        filter((isSwitchToTagView) => Boolean(isSwitchToTagView)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((isSwitchToTagView) => {
        this.changeDetectorRef.markForCheck()
        this.isSavingData.set(false)
        // this.isTagAddUpdate.set(!isSwitchToTagView)
        this.tagsFacade.notifyToSwitchTagView(false)

        //Switch To bulk tag after form Success
        this.toggleTagFieldForm()
      })
  }

  public fetchProjectTags(): void {
    this.documentTagFacade.fetchProjectTags(this.projectId, false, false)
  }

  public fetchTagSettings(): void {
    this.documentTagFacade.fetchTagSettings(this.projectId)
  }

  #fetchTagTree(): void {
    this.tagsFacade.fetchTagTree(this.projectId, this.reviewSetId)
  }

  public close(): void {
    this.#closeDialog()
  }

  #closeDialog(): void {
    this.dialog.close()
  }

  public saveTag(): void {
    this.isRefreshNeeded = true

    this.isSavingData.set(true)

    if (this.isTagAddUpdate() && this.isTagFormValid()) {
      this.#saveTagForm()
      return
    }

    this.documentTagFacade.selectIsTagDataModified$
      .pipe(take(1))
      .subscribe((isTagModified) => {
        if (isTagModified) {
          this.documentTagUtilityService.saveTag(
            this.projectId,
            PageControlActionType.SAVE,
            this.tagActionType,
            null,
            null,
            true
          )
        } else {
          this.isSavingData.set(false)
          this.venioNotifiacationService.showWarning('No changes to save.')
        }
      })
  }

  #saveTagForm(): void {
    this.tagsFacade.notifyIsAddOrUpdateTagField(true)
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public navigateTagForm(): void {
    this.changeDetectorRef.markForCheck()
    this.isTagAddUpdate.set(false)
    this.#setTagDialogTitle()
  }

  public setFlagForTagAddUpdate(): void {
    this.changeDetectorRef.markForCheck()
    this.isTagFormValid.set(false)
    this.isTagAddUpdate.set(true)
    this.#setTagDialogTitle()
  }

  #setTagDialogTitle(): void {
    this.dialogTitle = this.isTagAddUpdate()
      ? 'Add Tags'
      : this.tagActionType === TagActionType.TagAllInclusiveEmails
      ? 'Tag All Inclusive Emails'
      : 'Tag Email Thread'
  }

  #resetDocumentTagResponseState(): void {
    this.documentTagFacade.resetDocumentTagState([
      'applyDocumentTagSuccessResponse',
      'applyDocumentTagErrorResponse',
    ])
  }
}
