import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagEmailThreadComponent } from './tag-email-thread.component'
import { provideMockStore } from '@ngrx/store/testing'
import { StoreModule } from '@ngrx/store'
import { VenioNotificationService } from '@venio/feature/notification'
import { NotificationService } from '@progress/kendo-angular-notification'
import { CommonModule } from '@angular/common'
import { EffectsModule } from '@ngrx/effects'
import { TagsFacade } from '@venio/data-access/common'
import { DocumentTagFacade } from '@venio/data-access/document-utility'
import {
  SearchFacade,
  DocumentsFacade,
  ReviewFacade,
  FieldFacade,
  SearchResultFacade,
} from '@venio/data-access/review'
import { DocumentTagUtilityService } from '../utility-services/document-tag-utility'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { ActivatedRoute } from '@angular/router'
import { BehaviorSubject } from 'rxjs'

jest.mock('@angular/router', () => ({
  ActivatedRoute: {
    snapshot: {
      paramMap: {
        get: jest.fn(),
      },
    },
  },
  Router: jest.fn(() => ({
    navigate: jest.fn(),
  })),
}))

describe('TagEmailThreadComponent', () => {
  let component: TagEmailThreadComponent
  let fixture: ComponentFixture<TagEmailThreadComponent>
  let queryParamsSubject: BehaviorSubject<any>

  beforeEach(async () => {
    queryParamsSubject = new BehaviorSubject({ test: 'value' })
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        TagEmailThreadComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      providers: [
        provideMockStore({}),
        VenioNotificationService,
        NotificationService,
        SearchFacade,
        DocumentTagFacade,
        DocumentsFacade,
        DocumentTagUtilityService,
        TagsFacade,
        ReviewFacade,
        FieldFacade,
        SearchResultFacade,
        DialogRef,
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { queryParams: {} },
            queryParams: queryParamsSubject.asObservable(),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(TagEmailThreadComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
