import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  FormControl,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  Subject,
  debounceTime,
  distinctUntilChanged,
  filter,
  takeUntil,
} from 'rxjs'
import {
  DocumentTagFacade,
  ProjectTag,
} from '@venio/data-access/document-utility'

@Component({
  selector: 'venio-tag-comments',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    InputsModule,
    LabelModule,
    FormsModule,
  ],
  templateUrl: './tag-comments.component.html',
  styleUrls: ['./tag-comments.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagCommentsComponent
  implements OnChanges, AfterViewInit, OnDestroy
{
  @Input()
  public dataItem: ProjectTag

  @Input() public tagCommentChanged: (
    comment: string,
    dataItem: ProjectTag
  ) => void

  public commentCtrl: FormControl = new FormControl('')

  private readonly toDestroy$ = new Subject<void>()

  public get label(): string {
    return this.dataItem?.CommentLabel || ''
  }

  public get value(): string {
    return this.dataItem?.Comments || ''
  }

  public get required(): boolean {
    return (
      (this.dataItem?.TagCommentRequirement || '').toLowerCase() === 'required'
    )
  }

  public get placeholder(): string {
    const requirement = (
      this.dataItem?.TagCommentRequirement || ''
    ).toLowerCase()

    return requirement === 'recommended' || requirement === 'optional'
      ? this.dataItem?.TagCommentRequirement
      : ''
  }

  public get isInvalid(): boolean {
    return (
      this.required &&
      this.commentCtrl?.dirty &&
      this.commentCtrl?.invalid &&
      this.commentCtrl?.touched &&
      !this.commentCtrl?.pristine &&
      !this.commentCtrl.value?.trim()
    )
  }

  constructor(
    private docuemtTagFacade: DocumentTagFacade,
    private cdr: ChangeDetectorRef
  ) {}

  public ngOnChanges(): void {
    this.initCommentControl()
    this.cdr.markForCheck()
  }

  public ngAfterViewInit(): void {
    this.tagCommentInputValueChange()
    this.selectRequiredTagMissingCommentsValidation()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  private initCommentControl(): void {
    this.commentCtrl.setValue(this.value, {
      emitEvent: false,
    })

    if (this.required) {
      this.commentCtrl.setValidators(Validators.required)
    } else {
      this.commentCtrl.removeValidators(Validators.required)
    }
    this.cdr.markForCheck()
  }

  private tagCommentInputValueChange(): void {
    this.commentCtrl.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged(),
        takeUntil(this.toDestroy$)
      )
      .subscribe((value) => this.tagCommentChanged(value, this.dataItem))
  }

  private selectRequiredTagMissingCommentsValidation(): void {
    this.docuemtTagFacade.areTagCommentsMissing$
      .pipe(
        debounceTime(300),
        filter(
          (isRequiredFound) => isRequiredFound && this.commentCtrl.pristine
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.cdr.markForCheck()
        this.commentCtrl.markAsDirty()
        this.commentCtrl.markAsTouched()
        this.commentCtrl.updateValueAndValidity()
      })
  }
}
