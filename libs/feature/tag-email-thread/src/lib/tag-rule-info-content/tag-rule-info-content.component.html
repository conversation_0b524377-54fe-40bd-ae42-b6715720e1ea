<div
  class="t-uppercase t-text-[var(--v-custom-sky-blue)] t-font-medium"
  *ngIf="isTagHeaderRequired$ | async">
  Tag Rules
</div>

<div class="t-text-[0.85rem] t-pt-1 t-text-[#6e6e6e]" *ngIf="showTagConflict()">
  Please check and resolve the tag conflict.
</div>

<ul
  class="t-list-none !t-border-0 t-w-full t-mt-1 t-max-h-[122px] t-text-[#525252] t-font-semibold t-flex t-flex-col t-gap-1 t-overflow-y-auto">
  <ng-container *ngIf="tagRuleDescription().length > 0; else noData">
    <li
      *ngFor="
        let item of tagRuleDescription();
        let rowNumber = index;
        trackBy: tagRuleDescriptionTrackByFn
      "
      class="t-flex t-items-start t-p-1 !t-border-0">
      <div class="t-w-7 t-justify-end t-pr-0.5 t-text-sm t-text-gray-500">
        {{ rowNumber + 1 }}.
      </div>
      <div class="t-flex t-flex-1">
        {{ item.rule }}
      </div>
    </li>
  </ng-container>
</ul>

<ng-template #noData>
  <li class="t-flex t-items-start t-p-1 !t-border-0">
    <div class="t-flex t-flex-1">No data to display</div>
  </li>
</ng-template>
