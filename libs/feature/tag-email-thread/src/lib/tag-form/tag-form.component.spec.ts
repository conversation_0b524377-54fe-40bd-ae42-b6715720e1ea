import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagFormComponent } from './tag-form.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideRouter } from '@angular/router'

describe('TagFormComponent', () => {
  let component: TagFormComponent
  let fixture: ComponentFixture<TagFormComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [NoopAnimationsModule, TagFormComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        provideRouter([]),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(TagFormComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
