import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TagSearchComponent } from './tag-search.component'
import { DocumentTagFacade } from '@venio/data-access/document-utility'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('TagSearchComponent', () => {
  let component: TagSearchComponent
  let fixture: ComponentFixture<TagSearchComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TagSearchComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentTagFacade,
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(TagSearchComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
