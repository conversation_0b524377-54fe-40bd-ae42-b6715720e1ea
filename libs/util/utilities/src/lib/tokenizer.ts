export interface TokenizerOptions {
  minTokenLength: number
  maxConsecutiveRepeats: number
}

export class Tokenizer {
  private readonly minTokenLength: number

  private readonly maxConsecutiveRepeats: number

  constructor(options: TokenizerOptions) {
    this.minTokenLength = options.minTokenLength
    this.maxConsecutiveRepeats = options.maxConsecutiveRepeats
  }

  /**
   * Detects the likely language of the text based on character content.
   * @param {string} text - The text to analyze.
   * @returns {string} - A string representing the detected language group.
   */
  private detectLanguage(text: string): string {
    const chineseJapaneseKoreanRegex =
      /[\u4E00-\u9FFF\u3040-\u30FF\uAC00-\uD7AF]/
    if (chineseJapaneseKoreanRegex.test(text)) {
      return 'cjk' // Chinese, Japanese, Korean
    }
    return 'other'
  }

  /**
   * Tokenizes the text based on the detected language.
   * @param {string} text - The text to tokenize.
   * @returns {string[]} - An array of tokens.
   */
  private tokenize(text: string): string[] {
    const language = this.detectLanguage(text)

    if (language === 'cjk') {
      // Split each character for CJK languages
      return text.split('').filter((token) => this.isValidToken(token))
    }
    // For other languages, split by whitespace
    return text.split(/\s+/).filter((token) => this.isValidToken(token))
  }

  /**
   * Checks if a token is valid based on its length.
   * @param {string} token - The token to validate.
   * @returns {boolean} - True if the token meets the minimum length requirement, false otherwise.
   */
  private isValidToken(token: string): boolean {
    return token.length >= this.minTokenLength
  }

  /**
   * Filters out consecutive repeated tokens beyond the maximum allowed limit.
   * @param {string[]} tokens - The array of tokens to filter.
   * @returns {string[]} - The filtered array of tokens.
   */
  private filterConsecutiveRepeats(tokens: string[]): string[] {
    const filteredTokens: string[] = []
    let prevToken = ''
    let consecutiveCount = 0

    for (const token of tokens) {
      if (token === prevToken) {
        consecutiveCount++
        if (consecutiveCount <= this.maxConsecutiveRepeats) {
          filteredTokens.push(token)
        }
      } else {
        prevToken = token
        consecutiveCount = 1
        filteredTokens.push(token)
      }
    }

    return filteredTokens
  }

  /**
   * Processes the input text to produce a sequence of tokens that are of sufficient length and with controlled repeats.
   * @param {string} text - The input text to process.
   * @returns {string[]} - An array of tokens that meet the minimum length and repetition requirements.
   */
  public process(text: string): string[] {
    const tokens = this.tokenize(text)
    return this.filterConsecutiveRepeats(tokens)
  }
}
