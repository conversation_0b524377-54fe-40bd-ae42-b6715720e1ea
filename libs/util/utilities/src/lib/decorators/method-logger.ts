/**
 * Decorator for logging and debugging method paths and invocations.
 * It will log the paths of the methods and properties, the call stack, the arguments, and the results.
 *
 * @param {any} target - The target object or prototype.
 * @param {string} propertyKey - The name of the property or method.
 * @param {PropertyDescriptor} [descriptor] - The property descriptor (optional).
 * @returns {PropertyDescriptor | void} The modified descriptor for methods, or void for properties.
 *
 * @example
 * ```ts
 * class MyClass {
 *   @Logger
 *   myMethod(arg1: string, arg2: number) {
 *     // Method logic here
 *   }
 *
 *   @Logger
 *   public search$ = createEffect(() =>
 *     this.actions$.pipe(
 *       // ...
 *     )
 *   );
 * }
 * ```
 */
export function Logger(
  target: any,
  propertyKey: string,
  descriptor?: PropertyDescriptor
): any | void {
  if (descriptor && typeof descriptor.value === 'function') {
    // Regular method decorator
    const originalMethod = descriptor.value

    descriptor.value = function (...args: any[]): unknown {
      const fullPath = getFullPath(target, propertyKey)
      const callStack = getDetailedCallStack()

      console.group(
        `%c[LOG] Invoking: ${fullPath}`,
        'color: blue; font-weight: bold;'
      )
      console.log(`%c[LOG] Call Stack:`, 'color: green;')
      callStack.forEach((entry, index) => {
        console.log(`%c  ${index + 1}. ${entry}`, 'color: green;')
      })
      console.log(`%c[LOG] Arguments:`, 'color: purple;', args)

      try {
        const result = originalMethod.apply(this, args)
        console.log(`%c[LOG] Result:`, 'color: orange;', result)
        console.log(`%c[LOG] Completed: ${fullPath}`, 'color: blue;')
        console.groupEnd()
        return result
      } catch (error) {
        console.error(`%c[ERROR] Method: ${fullPath}`, 'color: red;')
        console.error(`%c[ERROR] Error:`, 'color: red;', error)
        console.groupEnd()
        throw error
      }
    }

    return descriptor
  } else if (typeof target === 'function') {
    // Class decorator
    console.log(`%c[LOG] Class: ${target.name}`, 'color: cyan;')
  } else {
    // Property decorator (assuming it's an observable stream)
    const fullPath = getFullPath(target, propertyKey)

    const originalValue = target[propertyKey]

    if (typeof originalValue === 'function') {
      target[propertyKey] = function (...args: any[]): unknown {
        console.group(
          `%c[LOG] Invoking: ${fullPath}`,
          'color: blue; font-weight: bold;'
        )
        console.log(`%c[LOG] Arguments:`, 'color: purple;', args)

        try {
          const result = originalValue.apply(this, args)
          console.log(`%c[LOG] Result:`, 'color: orange;', result)
          console.log(`%c[LOG] Completed: ${fullPath}`, 'color: blue;')
          console.groupEnd()
          return result
        } catch (error) {
          console.error(`%c[ERROR] Method: ${fullPath}`, 'color: red;')
          console.error(`%c[ERROR] Error:`, 'color: red;', error)
          console.groupEnd()
          throw error
        }
      }
    }
  }
}

/**
 * Retrieves the full path of a method or property.
 *
 * @param {any} target - The target object or prototype.
 * @param {string} propertyKey - The name of the property or method.
 * @returns {string} The full path of the method or property.
 */
function getFullPath(target: any, propertyKey: string): string {
  const pathParts: string[] = []

  let currentTarget = target
  while (currentTarget && currentTarget.constructor) {
    pathParts.unshift(currentTarget.constructor.name)
    currentTarget = Object.getPrototypeOf(currentTarget)
  }

  return `${pathParts.join('.')}.${propertyKey}`
}

/**
 * Retrieves the detailed call stack.
 *
 * @returns {string[]} The call stack as an array of strings.
 */
function getDetailedCallStack(): string[] {
  const stack = new Error().stack
  const lines = stack?.split('\n').slice(2) || []
  const callStack: string[] = []

  for (const line of lines) {
    const match = line.match(/at\s+(.*?)\s+\((.*?):(\d+):(\d+)\)/)
    if (match) {
      const [, functionName, fileName, lineNumber, columnNumber] = match
      callStack.push(
        `${functionName} (${fileName}:${lineNumber}:${columnNumber})`
      )
    }
  }

  return callStack
}
