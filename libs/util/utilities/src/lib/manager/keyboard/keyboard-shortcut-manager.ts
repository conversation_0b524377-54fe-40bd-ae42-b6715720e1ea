/**
 * Represents a mapping from keyboard key codes to their respective key names.
 * This is useful for translating numeric key codes into human-readable key names,
 * aiding in understanding which key is pressed without needing to refer to keycode charts.
 *
 * @example
 * const myKeyMap: KeyMap = {
 *   13: 'enter',
 *   27: 'esc'
 * }
 */
type KeyMap = { [key: number]: string }

/**
 * Represents a mapping from modifier key names to their respective values.
 * This type can be used to define which modifier keys (like 'Shift', 'Ctrl', etc.) are active
 * in a given context or configuration.
 *
 * @example
 * const myModifierMap: ModifierMap = {
 *   Shift: 'shift',
 *   Ctrl: 'ctrl'
 * }
 */
type ModifierMap = { [key: string]: string }

/**
 * Defines a callback function type for handling keyboard events.
 * The callback may be triggered by an event listener on keyboard actions,
 * and it can perform custom logic based on the event and the key combination pressed.
 *
 * @param {KeyboardEvent} e - The KeyboardEvent object provided by the DOM when a key is pressed.
 * @param {string} [combo] - An optional string representing the combination of keys that were pressed.
 * @returns {boolean | void} Returns either a boolean to indicate whether the event should be propagated further, or void.
 *
 * @example
 * function handleShortcut(e: KeyboardEvent, combo?: string): boolean | void {
 *   if (combo === 'Ctrl+S') {
 *     console.log('Save shortcut triggered');
 *     return false; // Prevent default action
 *   }
 * }
 */
export type Callback = (e: KeyboardEvent, combo?: string) => boolean | void

/**
 * Defines a group of related keyboard shortcuts. Each group can contain multiple shortcut configurations,
 * each specifying the keys involved, the callback to execute, and optional metadata like a description or action name.
 * This is useful for organizing shortcuts by functionality or context within the application.
 *
 * @example
 * const editorShortcuts: ShortcutGroup = {
 *   editing: [
 *     {
 *       keys: 'Ctrl+S',
 *       callback: saveDocument,
 *       description: 'Saves the current document.'
 *     },
 *     {
 *       keys: ['Ctrl', 'Z'],
 *       callback: undoAction,
 *       description: 'Undo the last action.'
 *     }
 *   ]
 * };
 */
export interface ShortcutGroup {
  [groupName: string]: {
    keys: string | string[]
    callback: Callback
    action?: string
    description?: string
  }[]
}

export interface BindOptions {
  /**
   * The allowed types for the shortcut to trigger in.
   * E.g., ['input', 'textarea'] to allow the shortcut in input fields.
   */
  allowIn?: string[]
  description?: string
  action?: string
}

/**
 * Represents an individual item in a callback registry, detailing the specific configuration
 * for a keyboard shortcut including the keys, modifiers involved, and the callback to execute.
 * Additional properties like sequence and level can be used to specify ordering or priority among shortcuts.
 *
 * @property {Callback} callback - The callback function to execute when the key combination is triggered.
 * @property {string[]} modifiers - A list of modifiers (e.g., 'Shift', 'Ctrl') required for this shortcut.
 * @property {string} action - A brief description of the action performed by this shortcut.
 * @property {string} [seq] - Optional sequence identifier if ordering of callbacks is necessary.
 * @property {number} [level] - Optional level to specify priority over other shortcuts.
 * @property {string} combo - The key combination that triggers the callback.
 * @public
 * @example
 * const saveShortcut: CallbackItem = {
 *   callback: saveDocument,
 *   modifiers: ['Ctrl'],
 *   action: 'Save Document',
 *   combo: 'Ctrl+S',
 *   description: 'Saves the current document.'
 * };
 */
export interface CallbackItem {
  callback: Callback
  modifiers: string[]
  action: string
  seq?: string
  level?: number
  combo: string
  description?: string
  allowIn?: string[]
}

/**
 * Maps numeric key codes to string descriptions (e.g., 'backspace', 'tab', 'enter'). This map helps translate
 * key codes received from keyboard events into more human-readable forms.
 */
const MAP: KeyMap = {
  8: 'backspace',
  9: 'tab',
  13: 'enter',
  16: 'shift',
  17: 'ctrl',
  18: 'alt',
  20: 'capslock',
  27: 'esc',
  32: 'space',
  33: 'pageup',
  34: 'pagedown',
  35: 'end',
  36: 'home',
  37: 'left',
  38: 'up',
  39: 'right',
  40: 'down',
  45: 'ins',
  46: 'del',
  91: 'meta',
  93: 'meta',
  224: 'meta',
}

const KEYCODE_MAP: KeyMap = {
  106: '*',
  107: '+',
  109: '-',
  110: '.',
  111: '/',
  186: ';',
  187: '=',
  188: ',',
  189: '-',
  190: '.',
  191: '/',
  192: '`',
  219: '[',
  220: '\\',
  221: ']',
  222: "'",
}

const SHIFT_MAP: ModifierMap = {
  '~': '`',
  '!': '1',
  '@': '2',
  '#': '3',
  $: '4',
  '%': '5',
  '^': '6',
  '&': '7',
  '*': '8',
  '(': '9',
  ')': '0',
  _: '-',
  '+': '=',
  ':': ';',
  '"': "'",
  '<': ',',
  '>': '.',
  '?': '/',
  '|': '\\',
}

/**
 * A mapping of special key aliases to their corresponding key names.
 * This is useful for mapping common key names or aliases to their standardized key names,
 * ensuring consistent interpretation of key events across different platforms or configurations.
 *
 * @example
 * SPECIAL_ALIASES['option'] = 'alt';
 * SPECIAL_ALIASES['return'] = 'enter';
 */
const SPECIAL_ALIASES: ModifierMap = {
  option: 'alt',
  command: 'meta',
  return: 'enter',
  escape: 'esc',
  plus: '+',
  mod: /Mac|iPod|iPhone|iPad/.test(navigator.platform) ? 'meta' : 'ctrl',
}

let REVERSE_MAP: KeyMap | undefined

for (let i = 1; i < 20; ++i) {
  MAP[111 + i] = 'f' + i
}

for (let i = 0; i <= 9; ++i) {
  MAP[i + 96] = i.toString()
}

/**
 * Attaches an event listener to the specified object.
 *
 * @param {HTMLElement | Document} object - The object to attach the event listener to.
 * @param {string} type - The type of event to listen for (e.g., 'keydown', 'click').
 * @param {EventListenerOrEventListenerObject} callback - The callback function to execute when the event is triggered.
 * @param {boolean} useCapture=false] - Optional flag to specify whether to use event capturing instead of bubbling.
 * @returns {void}
 *
 * @example
 * const element = document.getElementById('myElement');
 * addEvent(element, 'click', handleClick);
 */
function addEvent(
  object: HTMLElement | Document,
  type: string,
  callback: EventListenerOrEventListenerObject,
  useCapture = false
): void {
  if (object.addEventListener) {
    object.addEventListener(type, callback, useCapture)
  } else {
    ;(object as any).attachEvent('on' + type, callback)
  }
}

/**
 * Retrieves the character representation of a keyboard event.
 *
 * @param {KeyboardEvent} e - The keyboard event to retrieve the character from.
 * @returns {string} The character representation of the key event.
 *
 * @example
 * document.addEventListener('keydown', function(e) {
 *   const character = characterFromEvent(e);
 *   console.log('Key pressed:', character);
 * });
 */
function characterFromEvent(e: KeyboardEvent): string {
  if (e.type === 'keypress') {
    let character = String.fromCharCode(e.which)
    if (!e.shiftKey) {
      character = character.toLowerCase()
    }
    return character
  }

  if (MAP[e.which]) {
    return MAP[e.which]
  }

  if (KEYCODE_MAP[e.which]) {
    return KEYCODE_MAP[e.which]
  }

  return String.fromCharCode(e.which).toLowerCase()
}

/**
 * Checks if two arrays of modifiers match.
 *
 * @param {string[]} modifiers1 - The first array of modifiers to compare.
 * @param {string[]} modifiers2 - The second array of modifiers to compare.
 * @returns {boolean} True if the modifier arrays match, false otherwise.
 *
 * @example
 * const modifiers1 = ['shift', 'ctrl'];
 * const modifiers2 = ['ctrl', 'shift'];
 * const match = modifiersMatch(modifiers1, modifiers2);
 * console.log(match); // Output: true
 */
function modifiersMatch(modifiers1: string[], modifiers2: string[]): boolean {
  return modifiers1.sort().join(',') === modifiers2.sort().join(',')
}

/**
 * Retrieves the active modifier keys from a keyboard event.
 *
 * @param {KeyboardEvent} e - The keyboard event to retrieve the modifiers from.
 * @returns {string[]} An array of active modifier keys.
 *
 * @example
 * document.addEventListener('keydown', function(e) {
 *   const modifiers = eventModifiers(e);
 *   console.log('Active modifiers:', modifiers);
 * });
 */
function eventModifiers(e: KeyboardEvent): string[] {
  const modifiers: string[] = []

  if (e.shiftKey) {
    modifiers.push('shift')
  }

  if (e.altKey) {
    modifiers.push('alt')
  }

  if (e.ctrlKey) {
    modifiers.push('ctrl')
  }

  if (e.metaKey) {
    modifiers.push('meta')
  }

  return modifiers
}

/**
 * Prevents the default action of an event.
 *
 * @param {Event} e - The event to prevent the default action for.
 * @returns {void}
 *
 * @example
 * document.addEventListener('contextmenu', function(e) {
 *   preventDefault(e);
 * });
 */
function preventDefault(e: Event): void {
  if (e.preventDefault) {
    e.preventDefault()
  } else {
    ;(e as any).returnValue = false
  }
}

/**
 * Stops the propagation of an event.
 *
 * @param {Event} e - The event to stop the propagation for.
 * @returns {void}
 *
 * @example
 * document.addEventListener('click', function(e) {
 *   stopPropagation(e);
 * });
 */
function stopPropagation(e: Event): void {
  if (e.stopPropagation) {
    e.stopPropagation()
  } else {
    ;(e as any).cancelBubble = true
  }
}

/**
 * Checks if a given key is a modifier key.
 *
 * @param {string} key - The key to check.
 * @returns {boolean} True if the key is a modifier, false otherwise.
 *
 * @example
 * console.log(isModifier('shift')); // Output: true
 * console.log(isModifier('a')); // Output: false
 */
function isModifier(key: string): boolean {
  return key === 'shift' || key === 'ctrl' || key === 'alt' || key === 'meta'
}

/**
 * Retrieves the reverse mapping of key names to key codes.
 *
 * @returns {KeyMap} The reverse key map.
 *
 * @example
 * const reverseMap = getReverseMap();
 * console.log(reverseMap['enter']); // Output: "13"
 */
function getReverseMap(): KeyMap {
  if (!REVERSE_MAP) {
    REVERSE_MAP = {}
    for (const key in MAP) {
      if (Number(key) > 95 && Number(key) < 112) {
        continue
      }

      if (Object.prototype.hasOwnProperty.call(MAP, key)) {
        REVERSE_MAP[MAP[key]] = key
      }
    }
  }
  return REVERSE_MAP
}

/**
 * Picks the best action for a given key event based on the key and modifiers.
 *
 * @param {string} key - The key pressed.
 * @param {string[]} modifiers - The active modifier keys.
 * @param {string} [action] - The default action (optional).
 * @returns {string} The best action for the key event.
 *
 * @example
 * const action = pickBestAction('a', ['shift'], 'keydown');
 * console.log(action); // Output: 'keydown'
 */
function pickBestAction(
  key: string,
  modifiers: string[],
  action?: string
): string {
  if (!action) {
    action = getReverseMap()[key] ? 'keydown' : 'keypress'
  }

  if (action === 'keypress' && modifiers.length) {
    action = 'keydown'
  }

  return action
}

/**
 * Converts a key combination string to an array of individual keys.
 *
 * @param {string} combination - The key combination string.
 * @returns {string[]} An array of individual keys.
 *
 * @example
 * const keys = keysFromString('Ctrl+Shift+A');
 * console.log(keys); // Output: ['Ctrl', 'Shift', 'A']
 */
function keysFromString(combination: string): string[] {
  if (combination === '+') {
    return ['+']
  }

  combination = combination.replace(/\+{2}/g, '+plus')
  return combination.split('+')
}

interface KeyInfo {
  key: string
  modifiers: string[]
  action: string
}

/**
 * Retrieves information about a key combination.
 *
 * @param {string} combination - The key combination string.
 * @param {string} [action] - The action associated with the key combination (optional).
 * @returns {KeyInfo} An object containing the key, modifiers, and action.
 *
 * @example
 * const keyInfo = getKeyInfo('Ctrl+Shift+A', 'keydown');
 * console.log(keyInfo);
 * // Output: { key: 'A', modifiers: ['ctrl', 'shift'], action: 'keydown' }
 */
function getKeyInfo(combination: string, action?: string): KeyInfo {
  const keys: string[] = keysFromString(combination)
  const modifiers: string[] = []

  let key: string
  for (let i = 0; i < keys.length; ++i) {
    key = keys[i]

    if (SPECIAL_ALIASES[key]) {
      key = SPECIAL_ALIASES[key]
    }

    if (action && action !== 'keypress' && SHIFT_MAP[key]) {
      key = SHIFT_MAP[key]
      modifiers.push('shift')
    }

    if (isModifier(key)) {
      modifiers.push(key)
    }
  }

  action = pickBestAction(key, modifiers, action)

  return {
    key: key,
    modifiers: modifiers,
    action: action,
  }
}

/**
 * Checks if an element belongs to a specific ancestor element.
 *
 * @param {HTMLElement | null} element - The element to check.
 * @param {HTMLElement | Document} ancestor - The ancestor element to check against.
 * @returns {boolean} True if the element belongs to the ancestor, false otherwise.
 *
 * @example
 * const child = document.getElementById('childElement');
 * const parent = document.getElementById('parentElement');
 * const belongs = belongsTo(child, parent);
 * console.log(belongs); // Output: true
 */
function belongsTo(
  element: HTMLElement | null,
  ancestor: HTMLElement | Document
): boolean {
  if (!element || !ancestor) {
    return false
  }

  if (element === ancestor) {
    return true
  }

  return belongsTo(element.parentNode as HTMLElement, ancestor)
}

/**
 * The ShortcutManager class provides functionality for managing keyboard shortcuts.
 * It allows binding and unbinding shortcuts, handling keyboard events, and executing
 * associated callbacks when shortcuts are triggered.
 *
 * @param {HTMLElement | Document} [targetElement] - The target element or document to attach event listeners to.
 * @param {number} [sequenceDelay=1000] - The delay (in milliseconds) between key presses in a sequence.
 * @param {number} [sequenceTimeout=1000] - The timeout (in milliseconds) for completing a sequence.
 * @param {boolean} [handleKeyEvents=true] - Flag to indicate whether the manager should handle key events.
 * @param {boolean} [captureKeyEvents=false] - Flag to indicate whether to use event capturing for key events.
 *
 * @example
 * // Basic usage
 * const launchAdvancedSearch = (event, combo) => {
 *   // event return keyboard event
 *   // combo return the key combination e.g. 'Ctrl+Shift+A'
 *  }
 * const shortcutManager = new ShortcutManager();
 * shortcutManager.bind('Ctrl+S', handleSave);
 * // Sequence binding with space delimiter
 * shortcutManager.bind('a d v a n c e d', launchAdvanced);
 * // Multiple key binding
 * shortcutManager.bind(['Ctrl+C', 'Meta+C'], handleCopy);
 * shortcutManager.bind('ctrl+alt+a', launchAdvancedSearch, {
 *    allowIn: ['TEXTAREA'],
 *    description: 'Launch Advanced Search',
 *  }
 * )
 *
 * // Unbinding shortcuts
 * shortcutManager.unbind('Ctrl+S');
 * shortcutManager.unbind(['Ctrl+C', 'Meta+C'], handleCopy, {options});
 *
 * // Triggering shortcuts programmatically
 * shortcutManager.trigger('Ctrl+S');
 *
 * // Resetting all bindings
 * shortcutManager.reset();
 *
 * // Binding context-specific shortcuts
 * shortcutManager.bindContext('.editor', 'Ctrl+B', handleBold);
 *
 * // Adding custom key combinations
 * shortcutManager.addCustomKeyCombination('Ctrl+Shift+A', handleCustomAction);
 *
 * // Creating and managing shortcut groups
 * shortcutManager.createGroup('editorShortcuts', [
 *   { keys: 'Ctrl+B', callback: handleBold },
 *   { keys: 'Ctrl+I', callback: handleItalic }
 * ]);
 * shortcutManager.enableGroup('editorShortcuts');
 * shortcutManager.disableGroup('editorShortcuts');
 *
 * // Generating help string
 * const helpString = shortcutManager.generateHelp();
 * console.log(helpString);
 *
 * // Gotchas and considerations
 * // - Be careful when binding shortcuts that conflict with default browser shortcuts.
 * //   For example, binding 'Ctrl+P' may interfere with the browser's print functionality.
 * //   Consider using more specific or less common key combinations to avoid conflicts.
 * //
 * // - When binding shortcuts within specific contexts (e.g., using bindContext()),
 * //   ensure that the selector accurately identifies the desired elements. Incorrect
 * //   selectors may lead to unexpected behavior or shortcuts not working as intended.
 * //
 * // - If you need to remove a specific callback from a shortcut without removing all
 * //   callbacks, use the unbind() method with the callback parameter. This allows you
 * //   to selectively remove individual callbacks while keeping other callbacks intact.
 * //
 * // - The ShortcutManager automatically handles most common key combinations, but if
 * //   you require custom key combinations that are not recognized by default, you can
 * //   use the addCustomKeyCombination() method to add them.
 * //
 * // - When creating shortcut groups, be mindful of the grouping logic and ensure that
 * //   the shortcuts within a group are related and make sense to be grouped together.
 * //   Proper grouping can enhance code organization and make it easier to manage shortcuts.
 * //
 * // - The generateHelp() method provides a convenient way to generate a string representation
 * //   of the registered shortcuts and their descriptions. This can be useful for displaying
 * //   a help menu or documentation to users, informing them about the available shortcuts.
 * //
 * // - If you need to customize the behavior of the ShortcutManager further, you can extend
 * //   the class and override specific methods to suit your needs. This allows for greater
 * //   flexibility and customization options.
 */
export class ShortcutManager {
  private readonly target: HTMLElement | Document

  private namespace = 'venio-shortcut-manager'

  private callbacks: { [key: string]: CallbackItem[] } = {}

  private directMap: { [key: string]: Callback } = {}

  private ignoreNextKeyup: boolean | string = false

  private ignoreNextKeypress = false

  private nextExpectedAction: boolean | string = false

  private resetTimer: any | undefined

  private sequenceLevels: { [key: string]: number } = {}

  private customCombinations: { [combination: string]: Callback } = {}

  private contextMap: { [selector: string]: CallbackItem[] } = {}

  private shortcutGroups: ShortcutGroup = {}

  constructor(
    targetElement?: HTMLElement | Document,
    private sequenceDelay = 1000,
    private sequenceTimeout = 1000,
    private handleKeyEvents = true,
    private captureKeyEvents = false
  ) {
    this.target = targetElement || document
    this.init()
  }

  /**
   * Initializes the ShortcutManager by attaching event listeners to the target element or document.
   *
   * @returns {void}
   */
  private init(): void {
    if (this.handleKeyEvents) {
      const eventOptions = this.captureKeyEvents
      addEvent(
        this.target,
        'keypress',
        this.handleKeyEvent.bind(this),
        eventOptions
      )
      addEvent(
        this.target,
        'keydown',
        this.handleKeyEvent.bind(this),
        eventOptions
      )
      addEvent(
        this.target,
        'keyup',
        this.handleKeyEvent.bind(this),
        eventOptions
      )
    }
  }

  /**
   * Handles the keyboard event by processing the key pressed and executing the associated callbacks.
   *
   * @param {KeyboardEvent} e - The keyboard event object.
   * @returns {void}
   */
  private handleKeyEvent(e: KeyboardEvent): void {
    const character = characterFromEvent(e)
    const modifiers = eventModifiers(e)
    const combo = [...modifiers.reverse(), character].join('+')

    const element = e.target as HTMLElement

    // Check if the shortcut should be stopped based on the target element
    if (this.stopCallback(e, element, combo)) {
      return
    }

    if (!character) {
      return
    }

    if (e.type === 'keyup' && this.ignoreNextKeyup === character) {
      this.ignoreNextKeyup = false
      return
    }

    this.handleKey(character, eventModifiers(e), e)
  }

  /**
   * Resets the sequence levels and timers.
   *
   * @param {Object} [doNotReset={}] - An object specifying which sequences should not be reset.
   * @returns {void}
   */
  private resetSequences(doNotReset: { [key: string]: boolean } = {}): void {
    let activeSequences = false

    for (const key in this.sequenceLevels) {
      if (doNotReset[key]) {
        activeSequences = true
        continue
      }
      this.sequenceLevels[key] = 0
    }

    if (!activeSequences) {
      this.nextExpectedAction = false
    }

    this.resetSequenceTimer()
  }

  /**
   * Retrieves the matching callbacks for a given key event.
   *
   * @param {string} character - The character pressed.
   * @param {string[]} modifiers - The active modifier keys.
   * @param {KeyboardEvent} e - The keyboard event object.
   * @param {string} [sequenceName] - The name of the sequence (if applicable).
   * @param {string} [combination] - The key combination string.
   * @param {number} [level] - The level of the sequence.
   * @param {CallbackItem[]} [context] - The context-specific callback items.
   * @returns {CallbackItem[]} An array of matching callback items.
   */
  private getMatches(
    character: string,
    modifiers: string[],
    e: KeyboardEvent,
    sequenceName?: string,
    combination?: string,
    level?: number,
    context?: CallbackItem[]
  ): CallbackItem[] {
    const matches: CallbackItem[] = []
    const action = e.type

    if (!this.callbacks[character]) {
      return []
    }

    if (action === 'keyup' && isModifier(character)) {
      modifiers = [character]
    }

    const callbacks = context ? context : this.callbacks[character]
    for (let i = 0; i < callbacks.length; ++i) {
      const callback = callbacks[i]

      if (
        !sequenceName &&
        callback.seq &&
        this.sequenceLevels[callback.seq] !== callback.level
      ) {
        continue
      }

      if (action !== callback.action) {
        continue
      }

      if (
        (action === 'keypress' && !e.metaKey && !e.ctrlKey) ||
        modifiersMatch(modifiers, callback.modifiers)
      ) {
        const deleteCombo = !sequenceName && callback.combo === combination
        const deleteSequence =
          sequenceName &&
          callback.seq === sequenceName &&
          callback.level === level
        if (deleteCombo || deleteSequence) {
          callbacks.splice(i, 1)
        }

        matches.push(callback)
      }
    }

    return matches
  }

  /**
   * Fires the callback associated with a key event.
   *
   * @param {Callback} callback - The callback function to execute.
   * @param {KeyboardEvent} e - The keyboard event object.
   * @param {string} [combo] - The key combination string.
   * @param {string} [sequence] - The sequence name (if applicable).
   * @returns {void}
   */
  private fireCallback(
    callback: Callback,
    e: KeyboardEvent,
    combo?: string,
    sequence?: string
  ): void {
    if (this.stopCallback(e, e.target as HTMLElement, combo)) {
      return
    }

    const result = callback(e, combo)
    if (result === false) {
      preventDefault(e)
      stopPropagation(e)
    }
  }

  /**
   * Handles a key event by finding matches and executing the associated callbacks.
   *
   * @param {string} character - The character pressed.
   * @param {string[]} modifiers - The active modifier keys.
   * @param {KeyboardEvent} e - The keyboard event object.
   * @returns {void}
   */
  private handleKey = (
    character: string,
    modifiers: string[],
    e: KeyboardEvent
  ): void => {
    const callbacks = this.getMatches(character, modifiers, e)
    const doNotReset: { [key: string]: boolean } = {}
    let maxLevel = 0
    let processedSequenceCallback = false

    // Check for custom key combinations
    const prefix = `${character}:${modifiers.join('+')}`
    if (this.customCombinations[prefix]) {
      this.fireCallback(this.customCombinations[prefix], e)
    }

    // Check for context-specific shortcuts
    const context = this.getContext(e.target as HTMLElement)

    if (context) {
      const contextCallbacks = this.getMatches(
        character,
        modifiers,
        e,
        undefined,
        undefined,
        undefined,
        context
      )
      for (let i = 0; i < contextCallbacks.length; ++i) {
        this.fireCallback(
          contextCallbacks[i].callback,
          e,
          contextCallbacks[i].combo
        )
      }
    }

    for (let i = 0; i < callbacks.length; ++i) {
      if (callbacks[i].seq) {
        maxLevel = Math.max(maxLevel, callbacks[i].level || 0)
      }
    }

    for (let i = 0; i < callbacks.length; ++i) {
      if (callbacks[i].seq) {
        if (callbacks[i].level !== maxLevel) {
          continue
        }

        processedSequenceCallback = true
        doNotReset[callbacks[i].seq] = true
        this.fireCallback(
          callbacks[i].callback,
          e,
          callbacks[i].combo,
          callbacks[i].seq
        )
        continue
      }

      if (!processedSequenceCallback) {
        this.fireCallback(callbacks[i].callback, e, callbacks[i].combo)
      }
    }

    const ignoreThisKeypress = e.type === 'keypress' && this.ignoreNextKeypress
    if (
      e.type === this.nextExpectedAction &&
      !isModifier(character) &&
      !ignoreThisKeypress
    ) {
      this.resetSequences(doNotReset)
    }

    this.ignoreNextKeypress = processedSequenceCallback && e.type === 'keydown'
  }

  /**
   * Retrieves the context-specific callback items for a given element.
   *
   * @param {HTMLElement} element - The element to retrieve the context for.
   * @returns {CallbackItem[] | undefined} An array of context-specific callback items, or undefined if no context is found.
   */
  private getContext(element: HTMLElement): CallbackItem[] | undefined {
    for (const selector in this.contextMap) {
      if (element.matches(selector)) {
        return this.contextMap[selector]
      }
    }
    return undefined
  }

  /**
   * Resets the sequence timer.
   *
   * @returns {void}
   */
  private resetSequenceTimer(): void {
    clearTimeout(this.resetTimer)
    this.resetTimer = setTimeout(
      this.resetSequences.bind(this),
      this.sequenceTimeout
    )
  }

  /**
   * Binds a sequence of keys to a callback.
   *
   * @param {string} combo - The key combination string.
   * @param {string[]} keys - An array of keys in the sequence.
   * @param {Callback} callback - The callback function to execute.
   * @param {string} [action] - The action associated with the sequence.
   * @param {BindOptions} options - Additional options for binding the sequence.
   * @returns {void}
   */
  private bindSequence(
    combo: string,
    keys: string[],
    callback: Callback,
    action?: string,
    options?: BindOptions
  ): void {
    this.sequenceLevels[combo] = 0

    const increaseSequence = (nextAction: string): Callback => {
      return (): void => {
        this.nextExpectedAction = nextAction
        ++this.sequenceLevels[combo]
        this.resetSequenceTimer()
      }
    }

    const callbackAndReset = (e: KeyboardEvent): void => {
      this.fireCallback(callback, e, combo)

      if (action !== 'keyup') {
        this.ignoreNextKeyup = characterFromEvent(e)
      }

      setTimeout(this.resetSequences.bind(this), this.sequenceDelay)
    }

    for (let i = 0; i < keys.length; ++i) {
      const isFinal = i + 1 === keys.length
      const wrappedCallback = isFinal
        ? callbackAndReset
        : increaseSequence(getKeyInfo(keys[i + 1]).action)
      this.bindSingle(
        keys[i],
        wrappedCallback,
        action,
        combo,
        i,
        '',
        options?.description,
        options
      )
    }
  }

  /**
   * Binds a single key combination to a callback.
   *
   * @param {string} combination - The key combination string.
   * @param {Callback} callback - The callback function to execute.
   * @param {string} [action] - The action associated with the combination.
   * @param {string} [sequenceName] - The name of the sequence (if applicable).
   * @param {number} [level] - The level of the sequence.
   * @param {string} [selector] - The selector for context-specific bindings.
   * @param {string} [description] - The description of the shortcut.
   * @param {BindOptions} options - Additional options for binding the shortcut.
   * @returns {void}
   */
  private bindSingle(
    combination: string,
    callback: Callback,
    action?: string,
    sequenceName?: string,
    level?: number,
    selector?: string,
    description?: string,
    options?: BindOptions
  ): void {
    this.directMap[combination + ':' + action] = callback

    combination = combination.replace(/\s+/g, ' ')

    const sequence = combination.split(' ')
    if (sequence.length > 1) {
      this.bindSequence(combination, sequence, callback, action, options)
      return
    }

    const info = getKeyInfo(combination, action)

    this.callbacks[info.key] = this.callbacks[info.key] || []

    this.getMatches(
      info.key,
      info.modifiers,
      { type: info.action } as KeyboardEvent,
      sequenceName,
      combination,
      level
    )

    const callbackItem: CallbackItem = {
      callback: callback,
      modifiers: info.modifiers,
      action: info.action,
      seq: sequenceName,
      level: level,
      combo: combination,
      description: description,
      allowIn: options?.allowIn,
    }

    if (selector) {
      this.contextMap[selector] = this.contextMap[selector] || []
      this.contextMap[selector].push(callbackItem)
    } else {
      this.callbacks[info.key][sequenceName ? 'unshift' : 'push'](callbackItem)
    }
  }

  /**
   * Binds multiple key combinations to a callback.
   *
   * @param {string[]} combinations - An array of key combination strings.
   * @param {Callback} callback - The callback function to execute.
   * @param {string} [action] - The action associated with the combinations.
   * @param {string} [selector] - The selector for context-specific bindings.
   * @param {string} [description] - The description of the shortcuts.
   * @param { BindOptions } options - Additional options for binding the shortcuts.
   * @returns {void}
   */
  private bindMultiple(
    combinations: string[],
    callback: Callback,
    action?: string,
    selector?: string,
    description?: string,
    options?: BindOptions
  ): void {
    for (let i = 0; i < combinations.length; ++i) {
      this.bindSingle(
        combinations[i],
        callback,
        action,
        undefined,
        undefined,
        selector,
        description,
        options
      )
    }
  }

  /**
   * Binds one or more key combinations to a callback.
   *
   * @param {string | string[]} keys - A key combination string or an array of key combination strings.
   * @param {Callback} callback - The callback function to execute.
   * @param {BindOptions} options - Additional options for binding the shortcuts.
   * @returns {this} The ShortcutManager instance for chaining.
   *
   * @example
   * shortcutManager.bind('Ctrl+S', handleSave);
   * shortcutManager.bind(['Ctrl+C', 'Meta+C'], handleCopy, 'Copy');
   */
  public bind(
    keys: string | string[],
    callback: Callback,
    options?: BindOptions
  ): this {
    keys = keys instanceof Array ? keys : [keys]
    this.bindMultiple(
      keys,
      callback,
      options?.action || '',
      undefined,
      options?.description || '',
      options
    )
    return this
  }

  /**
   * Unbinds one or more key combinations or a specific callback.
   *
   * @param {string | string[]} keys - A key combination string or an array of key combination strings.
   * @param {string} [action] - The action associated with the combinations.
   * @param {Callback} [callback] - The specific callback function to unbind.
   * @returns {this} The ShortcutManager instance for chaining.
   *
   * @example
   * shortcutManager.unbind('Ctrl+S');
   * shortcutManager.unbind(['Ctrl+C', 'Meta+C'], 'Copy', handleCopy);
   */
  public unbind(
    keys: string | string[],
    action?: string,
    callback?: Callback
  ): this {
    if (callback) {
      // Remove the specific callback from the callbacks object
      const keyInfo = getKeyInfo(keys instanceof Array ? keys[0] : keys, action)
      const callbackIndex = this.callbacks[keyInfo.key].findIndex(
        (item) => item.callback === callback
      )
      if (callbackIndex !== -1) {
        this.callbacks[keyInfo.key].splice(callbackIndex, 1)
      }
    } else {
      // Remove all callbacks for the specified keys and action
      return this.bind(keys, () => {}, { action })
    }
    return this
  }

  /**
   * Triggers a key combination programmatically.
   *
   * @param {string} keys - The key combination string to trigger.
   * @param {string} [action] - The action associated with the combination.
   * @returns {this} The ShortcutManager instance for chaining.
   *
   * @example
   * shortcutManager.trigger('Ctrl+S');
   */
  public trigger(keys: string, action?: string): this {
    if (this.directMap[keys + ':' + action]) {
      this.directMap[keys + ':' + action]({} as KeyboardEvent, keys)
    }
    return this
  }

  /**
   * Resets all bindings and clears the ShortcutManager state.
   *
   * @returns {ShortcutManager} The ShortcutManager instance for chaining.
   *
   * @example
   * shortcutManager.reset();
   */
  public reset(): ShortcutManager {
    this.callbacks = {}
    this.directMap = {}
    return this
  }

  /**
   * Determines whether the ShortcutManager should stop the callback execution for a given element.
   *
   * @param {KeyboardEvent} e - The keyboard event object.
   * @param {HTMLElement | null} element - The element to check.
   * @param {string} combo - The key combination string.
   * @returns {boolean} True if the callback execution should be stopped, false otherwise.
   */
  public stopCallback(
    e: KeyboardEvent,
    element: HTMLElement | null,
    combo: string
  ): boolean {
    if (!element) {
      return false
    }

    if ((' ' + element.className + ' ').indexOf(' shortcut-manager ') > -1) {
      return false
    }

    const callback = this.callbacks[e.key]?.find((item) => item.combo === combo)
    const allowIn = callback?.allowIn

    // There are scenarios where the element is not the target element and
    // are input, select, textarea, or contenteditable elements.
    const tagName = element.tagName.toLowerCase()

    if (allowIn?.[0]) {
      const isAllowedElement = allowIn.some(
        (selector) => element.matches(selector) || allowIn.includes(tagName)
      )
      return !isAllowedElement
    }

    if (belongsTo(element, this.target)) {
      return false
    }

    if ('composedPath' in e && typeof e.composedPath === 'function') {
      const initialEventTarget = e.composedPath()[0] as HTMLElement
      if (initialEventTarget !== e.target) {
        element = initialEventTarget
      }
    }

    return (
      element &&
      (element.tagName === 'INPUT' ||
        element.tagName === 'SELECT' ||
        element.tagName === 'TEXTAREA' ||
        element.isContentEditable)
    )
  }

  /**
   * Binds one or more key combinations to a context-specific callback.
   *
   * @param {string} selector - The selector for the context.
   * @param {string | string[]} keys - A key combination string or an array of key combination strings.
   * @param {Callback} callback - The callback function to execute.
   * @param {string} [action] - The action associated with the combinations.
   * @param {string} [description] - The description of the shortcuts.
   * @param {BindOptions} options - Additional options for binding the shortcuts.
   * @returns {this} The ShortcutManager instance for chaining.
   *
   * @example
   * shortcutManager.bindContext('.editor', 'Ctrl+B', handleBold);
   */
  public bindContext(
    selector: string,
    keys: string | string[],
    callback: Callback,
    action?: string,
    description?: string,
    options?: BindOptions
  ): this {
    keys = keys instanceof Array ? keys : [keys]
    this.bindMultiple(keys, callback, action, selector, description, options)
    return this
  }

  /**
   * Adds a custom key combination to the ShortcutManager.
   *
   * @param {string} combination - The custom key combination string.
   * @param {Callback} callback - The callback function to execute.
   * @returns {this} The ShortcutManager instance for chaining.
   *
   * @example
   * shortcutManager.addCustomKeyCombination('Ctrl+Shift+A', handleCustomAction);
   */
  public addCustomKeyCombination(
    combination: string,
    callback: Callback
  ): this {
    this.customCombinations[combination] = callback
    return this
  }

  /**
   * Creates a group of shortcuts.
   *
   * @param {string} groupName - The name of the group.
   * @param {{ keys: string | string[], callback: Callback, action?: string, description?: string }[]} shortcuts - An array of shortcut configurations.
   * @returns {this} The ShortcutManager instance for chaining.
   *
   * @example
   * shortcutManager.createGroup('editorShortcuts', [
   *   { keys: 'Ctrl+B', callback: handleBold },
   *   { keys: 'Ctrl+I', callback: handleItalic }
   * ]);
   */
  public createGroup(
    groupName: string,
    shortcuts: {
      keys: string | string[]
      callback: Callback
      action?: string
      description?: string
    }[]
  ): this {
    this.shortcutGroups[groupName] = shortcuts.map((shortcut) => ({
      keys: shortcut.keys,
      callback: shortcut.callback,
      action: shortcut.action,
      description: shortcut.description,
    }))
    return this
  }

  /**
   * Enables a group of shortcuts.
   *
   * @param {string} groupName - The name of the group to enable.
   * @returns {this} The ShortcutManager instance for chaining.
   *
   * @example
   * shortcutManager.enableGroup('editorShortcuts');
   */
  public enableGroup(groupName: string): this {
    const shortcuts = this.shortcutGroups[groupName]
    if (shortcuts) {
      shortcuts.forEach((shortcut) => {
        this.bind(shortcut.keys, shortcut.callback, {
          action: shortcut.action,
          description: shortcut.description,
        })
      })
    }
    return this
  }

  /**
   * Disables a group of shortcuts.
   *
   * @param {string} groupName - The name of the group to disable.
   * @returns {this} The ShortcutManager instance for chaining.
   *
   * @example
   * shortcutManager.disableGroup('editorShortcuts');
   */
  public disableGroup(groupName: string): this {
    const shortcuts = this.shortcutGroups[groupName]
    if (shortcuts) {
      shortcuts.forEach((shortcut) => {
        this.unbind(shortcut.keys, shortcut.action)
      })
    }
    return this
  }

  /**
   * Generates a string representation of the registered shortcuts and their descriptions.
   *
   * @returns {string} The generated help string.
   *
   * @example
   * const helpString = shortcutManager.generateHelp();
   * console.log(helpString);
   */
  public generateHelp(): string {
    const help: string[] = []

    // Generate help for regular shortcuts
    for (const key in this.callbacks) {
      this.callbacks[key].forEach((item) => {
        if (item.description) {
          help.push(`${item.combo}: ${item.description}`)
        }
      })
    }

    // Generate help for context-specific shortcuts
    for (const selector in this.contextMap) {
      this.contextMap[selector].forEach((item) => {
        if (item.description) {
          help.push(`${selector} - ${item.combo}: ${item.description}`)
        }
      })
    }

    return help.join('\n')
  }

  /**
   * Adds custom key codes to the ShortcutManager.
   *
   * @param {KeyMap} object - An object containing custom key codes.
   * @returns {void}
   *
   * @example
   * ShortcutManager.addKeycodes({
   *   174: 'audiovolumendown',
   *   175: 'audiovolumeup'
   * });
   */
  public static addKeycodes(object: KeyMap): void {
    for (const key in object) {
      if (Object.prototype.hasOwnProperty.call(object, key)) {
        MAP[key] = object[key]
      }
    }
    REVERSE_MAP = null
  }
}
