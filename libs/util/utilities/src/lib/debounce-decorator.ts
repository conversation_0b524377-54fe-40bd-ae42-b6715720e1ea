interface FuncOptionModel {
  timer: any
  args: any[]
}

interface FuncModel {
  (...rewriteArgs: any[]): void
  options: FuncOptionModel
}

export function cancel(func: FuncModel): void {
  if (func?.options) {
    clearTimeout(func.options?.timer)
  }
}

function fnWrapper(
  debounceTime: number,
  leading: boolean,
  originalMethod: (...args: any[]) => any,
  that?: any
): any {
  const options: FuncOptionModel = {
    timer: undefined,
    args: [],
  }

  let funcModel: FuncModel = function (this: any, ...args: any[]) {
    options.args = args

    if (!options.timer) {
      if (leading) originalMethod.apply(this, options.args)

      options.timer = setTimeout(() => {
        if (!leading) originalMethod.apply(this, options.args)
        options.timer = undefined
      }, debounceTime)
    } else {
      clearTimeout(options.timer)

      options.timer = setTimeout(() => {
        if (!leading) originalMethod.apply(this, options.args)
        options.timer = undefined
      }, debounceTime)
    }
  } as FuncModel

  if (that) {
    funcModel = funcModel.bind(that)
  }

  funcModel.options = options

  return funcModel
}

function defineProperty(
  debounceTime: number,
  leading: boolean,
  target: any,
  name: string
): void {
  let wrapperFunc: any

  Object.defineProperty(target, name, {
    configurable: true,
    enumerable: false,
    get() {
      return wrapperFunc
    },
    set(value: any) {
      wrapperFunc = fnWrapper(debounceTime, leading, value, this)
    },
  })
}

function modifyDescriptor(
  debounceTime: number,
  leading: boolean,
  descriptor: PropertyDescriptor
): PropertyDescriptor {
  const originalMethod = descriptor.value
  descriptor.value = fnWrapper(debounceTime, leading, originalMethod)
  return descriptor
}

function debounceCreated(
  debounceTime: number,
  leading: boolean,
  ...args: any[]
): any {
  if (args.length === 0)
    throw new Error('function applied debounce decorator should be a method')
  if (args.length === 1)
    throw new Error('method applied debounce decorator should have valid name')

  const target = args[0],
    name = args[1]
  const descriptor =
    args.length === 3 && args[2]
      ? args[2]
      : Object.getOwnPropertyDescriptor(target, name)

  if (descriptor) {
    return modifyDescriptor(debounceTime, leading, descriptor)
  }
  defineProperty(debounceTime, leading, target, name)
}

export function DebounceTimer(...opts: any[]): any {
  let debounceTime = 500
  let leading = false

  if (
    opts.length &&
    (typeof opts[0] === 'number' ||
      (typeof opts[0] === 'object' && opts[0].leading !== undefined))
  ) {
    if (typeof opts[0] === 'number') debounceTime = opts[0]

    let options
    if (typeof opts[0] === 'object' && opts[0].leading !== undefined)
      options = opts[0]
    if (
      opts.length > 1 &&
      typeof opts[1] === 'object' &&
      opts[1].leading !== undefined
    )
      options = opts[1]
    if (options) leading = options.leading

    return function (...args: any[]) {
      return debounceCreated(debounceTime, leading, ...args)
    }
  }

  return debounceCreated(debounceTime, leading, ...opts)
}
