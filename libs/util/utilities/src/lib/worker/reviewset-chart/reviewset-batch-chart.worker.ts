import {
  ProgressChartDataOutput,
  ReviewerChartDataOutput,
  ReviewStatusModel,
} from '@venio/shared/models/interfaces'
import { expose } from 'comlink'

export function processTagStatus(
  tagStatusData: ReviewStatusModel[]
): ReviewStatusModel[] {
  return tagStatusData.map((item, index) => ({
    ...item,
    color: generateHslColor(index, tagStatusData.length),
  }))
}

export function processReviewerChartData(
  reviewerData: ReviewStatusModel[]
): ReviewerChartDataOutput {
  //Sort and Group Data
  const groupedData = reviewerData
    .sort(
      (a, b) =>
        new Date(a.reviewedDate).getTime() - new Date(b.reviewedDate).getTime()
    )
    .reduce((acc, { reviewedDate, reviewedDocCount, userName }) => {
      const dateKey = new Date(reviewedDate).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      })
      acc[dateKey] = acc[dateKey] || {}
      acc[dateKey][userName] = (acc[dateKey][userName] || 0) + reviewedDocCount
      return acc
    }, {} as Record<string, Record<string, number>>)

  // Extract Unique Dates & Users
  const formattedDates = Object.keys(groupedData)
  const uniqueUsers = [...new Set(reviewerData.map(({ userName }) => userName))]

  //Build Stacked Series Data
  const stackedData = uniqueUsers.reduce(
    (acc, user) => ({
      ...acc,
      [user]: formattedDates.map((date) => groupedData[date][user] || 0),
    }),
    {} as Record<string, number[]>
  )

  const usersColor = uniqueUsers.reduce((acc, user, index) => {
    acc[user] = generateHslColor(index, uniqueUsers.length)
    return acc
  }, {} as Record<string, string>)

  return { formattedDates, uniqueUsers, stackedData, usersColor }
}

export function processProgressChartData(
  progressData: ReviewStatusModel[]
): ProgressChartDataOutput {
  // Sort and Group Data
  const groupedMap = progressData
    .sort(
      (a, b) =>
        new Date(a.reviewedDate).getTime() - new Date(b.reviewedDate).getTime()
    )
    .reduce((acc, { reviewedDate, reviewedDocCount }) => {
      const dateKey = new Date(reviewedDate).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      })
      acc.set(dateKey, (acc.get(dateKey) || 0) + reviewedDocCount)
      return acc
    }, new Map<string, number>())

  const groupedData = Array.from(groupedMap, ([date, value], index) => ({
    date,
    value,
    color: generateHslColor(index, groupedMap.size),
  }))

  const formattedDates = groupedData.map((item) => item.date)

  return { formattedDates, groupedData }
}

function generateHslColor(index: number, totalItems: number): string {
  const hue = (index * (360 / totalItems)) % 360
  const saturation = 55 + (index % 20)
  const lightness = 75 + (index % 15)

  return `hsl(${hue}, ${saturation}%, ${lightness}%)`
}

expose({ processTagStatus, processReviewerChartData, processProgressChartData })
