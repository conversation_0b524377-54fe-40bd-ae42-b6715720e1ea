import { convertCase } from './case-convertor.worker'

describe('convertCase', () => {
  it('should convert all keys in a simple object from camelCase to kebab-case', () => {
    // GIVEN a simple object with keys in camelCase
    const data = { simpleKey: 'value', anotherKey: 'value' }

    // WHEN convertCase is called with this object and target case as kebab-case
    const result = convertCase(data, 'kebab-case')

    // THEN the function should return an object with keys in kebab-case
    expect(result).toEqual({ 'simple-key': 'value', 'another-key': 'value' })
  })
  it('should convert keys in a deeply nested object from PascalCase to snake_case', () => {
    // GIVEN a deeply nested object with keys in PascalCase
    const data = { OuterKey: { InnerKey: { AnotherKey: 'value' } } }

    // WHEN convertCase is called with this object and target case as snake_case
    const result = convertCase(data, 'snake_case')

    // THEN the function should return a deeply nested object with keys in snake_case
    expect(result).toEqual({
      outer_key: { inner_key: { another_key: 'value' } },
    })
  })
  it('should convert keys in an array containing objects, arrays, and primitives', () => {
    // GIVEN an array containing objects, nested arrays, and primitive types
    const data = [{ arrayKey: ['value', { nestedKey: 'value' }] }, 42, 'string']

    // WHEN convertCase is called with this array and target case as kebab-case
    const result = convertCase(data, 'kebab-case')

    // THEN the function should return an array with appropriately converted keys
    expect(result).toEqual([
      { 'array-key': ['value', { 'nested-key': 'value' }] },
      42,
      'string',
    ])
  })
  it('should convert keys in an object that contains array properties', () => {
    // GIVEN an object that contains array properties
    const data = {
      listOfValues: [{ itemKey: 'value1' }, { itemKey: 'value2' }],
    }

    // WHEN convertCase is called with this object and target case as PascalCase
    const result = convertCase(data, 'PascalCase')

    // THEN the function should return an object with array properties having converted keys
    expect(result).toEqual({
      ListOfValues: [{ ItemKey: 'value1' }, { ItemKey: 'value2' }],
    })
  })
  it('should return the input as is for non-object/non-array top-level data types', () => {
    // GIVEN a string, number, and boolean as top-level inputs
    const dataString = 'testString'
    const dataNumber = 123
    const dataBoolean = true

    // WHEN convertCase is called with these inputs and any target case
    const resultString = convertCase(dataString, 'snake_case')
    const resultNumber = convertCase(dataNumber, 'snake_case')
    const resultBoolean = convertCase(dataBoolean, 'snake_case')

    // THEN the function should return the inputs as is
    expect(resultString).toBe(dataString)
    expect(resultNumber).toBe(dataNumber)
    expect(resultBoolean).toBe(dataBoolean)
  })
  it('should handle strings with unusual patterns correctly when converting keys', () => {
    // GIVEN an object with keys having unusual patterns
    const data = { '': 'empty', '123': 'numeric', $pecialKey: 'special' }

    // WHEN convertCase is called with this object and target case as PascalCase
    const result = convertCase(data, 'PascalCase')

    // THEN the function should handle these keys appropriately
    expect(result).toEqual({
      '': 'empty',
      '123': 'numeric',
      $PecialKey: 'special',
    })
  })
  it('should not alter the keys if the target case is the same as the source case', () => {
    // GIVEN an object with keys in snake_case
    const data = { snake_key: 'value' }

    // WHEN convertCase is called with this object and target case as snake_case
    const result = convertCase(data, 'snake_case')

    // THEN the function should return the object as is
    expect(result).toEqual(data)
  })
  it('should return an empty object or array as is when converting cases', () => {
    // GIVEN an empty object and an empty array
    const emptyObject = {}
    const emptyArray = [] as any

    // WHEN convertCase is called with these and target case as kebab-case
    const resultObject = convertCase<any>(emptyObject, 'kebab-case')
    const resultArray = convertCase<any>(emptyArray, 'kebab-case')

    // THEN the function should return them as is
    expect(resultObject).toEqual(emptyObject)
    expect(resultArray).toEqual(emptyArray)
  })
  it('should only convert enumerable keys in an object', () => {
    // GIVEN an object with enumerable and non-enumerable properties
    const data = Object.create(null, {
      enumerableKey: { value: 'value', enumerable: true },
      nonEnumerableKey: { value: 'hidden', enumerable: false },
    })

    // WHEN convertCase is called with this object and target case as PascalCase
    const result = convertCase(data, 'PascalCase')

    // THEN the function should only convert the enumerable key
    expect(result).toHaveProperty('EnumerableKey')
    expect(result).not.toHaveProperty('hidden')
  })
  it('should ignore symbol properties when converting cases', () => {
    // GIVEN an object with a symbol property
    const symbolKey = Symbol('symbolKey')
    const data = { [symbolKey]: 'value', normalKey: 'value' }

    // WHEN convertCase is called with this object and target case as camelCase
    const result = convertCase<any>(data, 'camelCase')

    // THEN the function should ignore the symbol property
    expect(result[symbolKey]).toBe('value')
    expect(result).toHaveProperty('normalKey')
  })
  it('should correctly handle objects with various types of values', () => {
    // GIVEN an object with different types of values (string, number, array, object)
    const data = {
      keyString: 'text',
      keyNumber: 42,
      keyArray: ['one', 'two'],
      keyObject: { nestedKey: 'value' },
    }

    // WHEN convertCase is called with this object and target case as kebab-case
    const result = convertCase(data, 'kebab-case')

    // THEN the function should convert keys correctly and preserve the value types
    expect(result).toEqual({
      'key-string': 'text',
      'key-number': 42,
      'key-array': ['one', 'two'],
      'key-object': { 'nested-key': 'value' },
    })
  })
  it('should handle complex nested structures including arrays and objects', () => {
    // GIVEN a complex nested structure with arrays and objects
    const data = {
      outerKey: [
        { innerKey: { anotherKey: ['value1', { deepKey: 'value2' }] } },
      ],
    }

    // WHEN convertCase is called with this structure and target case as PascalCase
    const result = convertCase(data, 'PascalCase')

    // THEN the function should convert all keys correctly in the nested structure
    expect(result).toEqual({
      OuterKey: [
        { InnerKey: { AnotherKey: ['value1', { DeepKey: 'value2' }] } },
      ],
    })
  })
  it('should handle mixed case strings when converting keys', () => {
    // GIVEN an object with mixed case strings
    const data = { 'my-KEY_Snake': 'value' }

    // WHEN convertCase is called with this object and target case as camelCase
    const result = convertCase(data, 'camelCase')

    // THEN the function should handle mixed case conversion correctly
    expect(result).toEqual({ myKeySnake: 'value' })
  })
  it('should retain internal special characters when converting keys', () => {
    // GIVEN an object with keys that have internal special characters
    const data = { name$Value: 'someValue', 'another-Name': 'anotherValue' }

    // WHEN convertCase is called with this object and target case as camelCase
    const result = convertCase(data, 'camelCase')

    // THEN the function should retain the internal special characters
    expect(result).toEqual({
      name$value: 'someValue',
      anotherName: 'anotherValue',
    })
  })
  it('should handle keys with accented characters correctly when converting cases', () => {
    // GIVEN an object with keys that have accented characters
    const data = { naïveKey: 'value', résumé: 'value' }

    // WHEN convertCase is called with this object and target case as camelCase
    const result = convertCase(data, 'camelCase')

    // THEN the function should handle accented characters correctly
    expect(result).toEqual({ naïveKey: 'value', résumé: 'value' })
  })
  it('should handle string representations of boolean and null values correctly when converting keys', () => {
    // GIVEN an object with keys that are string representations of boolean and null values
    const data = { trueKey: 'value', falseKey: 'value', nullKey: 'value' }

    // WHEN convertCase is called with this object and target case as snake_case
    const result = convertCase(data, 'snake_case')

    // THEN the function should handle these string representations correctly
    expect(result).toEqual({
      true_key: 'value',
      false_key: 'value',
      null_key: 'value',
    })
  })
  it('should handle keys with multiple consecutive special characters correctly when converting cases', () => {
    // GIVEN an object with keys that have multiple consecutive special characters
    const data = { $$$key: 'value', '###Key': 'value' }

    // WHEN convertCase is called with this object and target case as camelCase
    const result = convertCase(data, 'camelCase')

    // THEN the function should handle multiple special characters correctly
    expect(result).toStrictEqual({ $$$key: 'value', '###key': 'value' })
  })
  it('should handle string representations of numeric values with suffixes correctly when converting keys', () => {
    // GIVEN an object with keys that are numeric values followed by letters
    const data = { '123abc': 'value', '456def': 'value' }

    // WHEN convertCase is called with this object and target case as kebab-case
    const result = convertCase(data, 'kebab-case')

    // THEN the function should handle numeric values with suffixes correctly
    expect(result).toEqual({ '123abc': 'value', '456def': 'value' })
  })
  it('should convert all upper or lower case strings correctly', () => {
    // GIVEN an object with all upper or lower case strings
    const data = { ALLUPPER: 'value', alllower: 'value' }

    // WHEN convertCase is called with this object and target case as PascalCase
    const result = convertCase(data, 'PascalCase')

    // THEN the function should convert them correctly
    expect(result).toStrictEqual({ allupper: 'value', Alllower: 'value' })
  })
  it('should handle stringifies object property names correctly when converting cases', () => {
    // GIVEN an object with stringified object property names
    const data = { toString: 'value', valueOf: 'value' }

    // WHEN convertCase is called with this object and target case as camelCase
    const result = convertCase(data, 'camelCase')

    // THEN the function should handle stringified object property names correctly
    expect(result).toEqual({ toString: 'value', valueOf: 'value' })
  })
  it('should ignore non-string keys when converting cases', () => {
    // GIVEN a Map with non-string keys
    const data = new Map([
      [
        [{}, 'value'],
        [function (): void {}, 'value'],
      ],
    ])

    // WHEN convertCase is called with this Map and target case as snake_case
    const result = convertCase(data, 'snake_case')

    // THEN the function should ignore non-string keys
    expect(result).toBe(data) // result should be unchanged as Maps are not processed
  })
  it('should handle very long strings without performance issues when converting cases', () => {
    // GIVEN an object with a very long string as a key
    const longString = 'a'.repeat(10000) // 10,000 characters
    const data = { [longString]: 'value' }

    // WHEN convertCase is called with this object and target case as kebab-case
    const result = convertCase(data, 'kebab-case')

    // THEN the function should handle the very long string correctly
    expect(result).toHaveProperty(longString, 'value')
  })
  it('should not alter getter/setter definitions when converting cases', () => {
    // GIVEN an object with getter/setter properties
    const data = Object.defineProperties(
      {},
      {
        getKey: {
          get: function () {
            return 'value'
          },
          enumerable: true,
        },
      }
    )

    // WHEN convertCase is called with this object and target case as snake_case
    const result = convertCase(data, 'snake_case')

    // THEN the function should not alter getter/setter definitions
    const descriptor = Object.getOwnPropertyDescriptor(result, 'get_key')
    expect(descriptor?.get).toBeDefined()
    expect(descriptor?.set).toBeUndefined()
  })
  it('should not convert inherited prototype properties', () => {
    // GIVEN an object with inherited prototype properties
    const parent = { inheritedKey: 'value' }
    const data = Object.create(parent)

    // WHEN convertCase is called with this object and target case as camelCase
    const result = convertCase(data, 'camelCase')

    // THEN the function should not convert inherited prototype properties
    expect(result).not.toHaveProperty('inheritedKey')
  })
  it('should skip read-only and non-configurable properties when converting cases', () => {
    // GIVEN an object with read-only and non-configurable properties
    const data = Object.defineProperties(
      {},
      {
        readOnlyKey: {
          value: 'readOnly',
          writable: false,
          configurable: true,
          enumerable: true,
        },
        nonConfigurableKey: {
          value: 'nonConfigurable',
          writable: true,
          configurable: false,
          enumerable: true,
        },
      }
    )

    // WHEN convertCase is called with this object and target case as kebab-case
    const result = convertCase(data, 'kebab-case')

    // THEN the function should skip these properties
    expect(result).toHaveProperty('read-only-key', 'readOnly')
    expect(result).toHaveProperty('non-configurable-key', 'nonConfigurable')
  })
  it('should correctly handle sparse arrays when converting cases', () => {
    // GIVEN a sparse array
    const data = ['first', undefined, 'third'] // index 1 is empty

    // WHEN convertCase is called with this array and target case as snake_case
    const result = convertCase<any>(data, 'snake_case')

    // THEN the function should correctly handle the sparse array
    expect(result[1]).toBeUndefined()
    expect(result).toHaveLength(3)
  })
  it('should ignore function properties in objects when converting cases', () => {
    // GIVEN an object with function properties
    const data = {
      doSomething: function (): string {
        return 'action'
      },
    }

    // WHEN convertCase is called with this object and target case as PascalCase
    const result = convertCase<any>(data, 'PascalCase')

    // THEN the function properties should be ignored in the case conversion
    expect(typeof result.DoSomething).toBe('function')
    expect(result.DoSomething()).toBe('action')
  })
})
