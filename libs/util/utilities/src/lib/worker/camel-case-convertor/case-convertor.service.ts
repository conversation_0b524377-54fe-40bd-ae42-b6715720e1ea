import { wrap } from 'comlink'
import { CaseConvertorWorkerType, CaseType } from './case-convertor.model'

/**
 * This service provides functionality to convert the property names of an object or an array of objects
 * from given case type using web workers. Each instance of this class creates its own worker,
 * and the functionality is exposed per worker context using Comlink. This design means that each instance
 * operates independently and is not intended to be used as a singleton.
 *
 *
 * @example
 * // Example of using CaseConvertorService.
 * // Assuming you have an object or an array of objects with PascalCase properties.
 * // For example, let person = { FirstName: 'John', LastName: 'Doe', Address: [{Location: 'h', Contact: 1234}] };
 * const camelCaseService = new CaseConvertorService();
 *
 * // Convert the object's properties to camelCase.
 * camelCaseService.convertToCase<PersonType>(person)
 *   .then(convertedPerson => {
 *     // Output the converted object.
 *     console.log(convertedPerson); // { firstName: '<PERSON>', lastName: '<PERSON><PERSON>', address: [{location: 'h', contact: 1234}] }
 *   });
 *
 * // Remember to terminate the service when it's no longer needed.
 * camelCaseService.terminate();
 */
export class CaseConvertorService {
  private readonly worker: Worker

  constructor() {
    this.worker = new Worker(
      new URL('./case-convertor.worker', import.meta.url),
      { type: 'module' }
    )
  }

  /**
   * Converts the property names of a given object or each object in an array by given case type.
   * This method uses a web worker to perform the conversion asynchronously.
   *
   * @param {unknown} data - The object or array of objects with property names to be converted.
   * @param {CaseType} caseType - The target case type for conversion.
   * @returns {Promise<T>} A promise resolving to the object(s) with target case  property names.
   * @template T - The expected type of the converted data, which should reflect the structure of the input with target case type
   */
  public convertToCase<T>(data: unknown, caseType: CaseType): Promise<T> {
    const proxy = wrap<CaseConvertorWorkerType>(this.worker)
    return proxy.convertCase(data, caseType) as Promise<T>
  }

  /**
   * Terminates the web worker associated with this service.
   * This method should be called to free up resources once the conversion task is complete.
   * @returns {void}
   */
  public terminate(): void {
    this.worker.terminate()
  }
}
