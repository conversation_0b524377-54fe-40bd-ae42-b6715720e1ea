import {
  ReviewStatusModel,
  TagRateUserData,
} from '@venio/shared/models/interfaces'
import { expose } from 'comlink'

function tagRateUserDataTransform(
  data: ReviewStatusModel[]
): TagRateUserData[] {
  // Extract unique user names for columns
  const users = [...new Set(data.map((item) => item.userName))]

  // Group data by tagName
  const groupedData = data.reduce((acc, item) => {
    if (!acc[item.tagName]) {
      acc[item.tagName] = {}
    }
    // Calculate percentage and ensure it's a string with '%'
    acc[item.tagName][item.userName] =
      ((item.taggedDocCount / item.reviewedDocCount) * 100).toFixed(2) + '%'
    return acc
  }, {} as Record<string, Record<string, string>>)

  // Create row values
  const rowValues = Object.keys(groupedData).map((tagName) => {
    const row: TagRateUserData = { tag: tagName }
    users.forEach((user) => {
      row[user] = groupedData[tagName][user] || ''
    })
    return row
  })

  return rowValues
}

expose({ tagRateUserDataTransform })
