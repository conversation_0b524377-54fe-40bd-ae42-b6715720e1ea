import { expose } from 'comlink'
import { orderBy, keyBy, uniq } from 'lodash'

function documentSimilarDataTranform(
  similarDocuments: any,
  selectedFields: string[],
  panelFields: any[]
): any {
  const headers: string[] = []
  const metadata = similarDocuments[0].metadata
    .slice()
    .filter((f) => selectedFields.includes(f.key))
  const fieldOrderMap = keyBy(panelFields, 'fieldName')
  const filteredHeaders = orderBy(
    metadata,
    [
      (f): number =>
        fieldOrderMap[f.key]?.fieldOrder ?? Number.MAX_SAFE_INTEGER,
    ],
    ['asc']
  )
    .filter((f) => selectedFields.includes(f.key))
    .map((f) => f.key)
  const desiredHeaders = uniq(['Score', ...filteredHeaders])
  filteredHeaders.forEach((header) => headers.push(header))
  similarDocuments?.forEach((doc) => {
    for (const field of desiredHeaders) {
      const fieldValue = doc?.metadata?.find(
        (data) => data.key === field
      )?.value
      if (field === 'Score') {
        doc['Similarity'] = (+fieldValue).toFixed(2)
      } else doc[field] = fieldValue
    }
  })
  if (headers[0] === 'Internal File Id') {
    headers.splice(1, 0, 'Similarity') // Insert behind Internal File Id
  } else {
    headers.splice(0, 0, 'Similarity') // Insert at the very beginning
  }
  const documentSimiarlViewModel = {
    similarDocuments,
    headers,
  }
  return documentSimiarlViewModel
}

expose({ documentSimilarDataTranform })
