import { wrap } from 'comlink'
import { SyntaxManagerWorkerType } from './syntax-manager-worker.type'
import { ConditionGroup } from '@venio/shared/models/interfaces'

/**
 * This class handles the management of search query syntax. It utilizes a web worker
 * to process and generate query syntaxes asynchronously. The service is designed to
 * take a collection of items (ConditionGroup[]) and generate appropriate search syntaxes.
 */
export class SyntaxManagerWorkerService {
  private readonly worker: Worker

  /**
   * Initializes the SyntaxManagerWorkerService and creates a new web worker
   * for handling syntax generation tasks.
   */
  constructor() {
    this.worker = new Worker(
      new URL('./syntax-manager.worker', import.meta.url),
      { type: 'module' }
    )
  }

  /**
   * Generates a complete search syntax or syntax's based on the provided collection
   * of condition groups. This method leverages the web worker for asynchronous processing,
   * ensuring non-blocking operation for syntax generation.
   *
   * @param {ConditionGroup[]} collection - The collection of condition groups used to generate the syntax.
   * @returns {string} A promise that resolves to the generated search syntax.
   */
  public getGeneratedSyntax(collection: ConditionGroup[]): Promise<string> {
    const proxy = wrap<SyntaxManagerWorkerType>(this.worker)
    return proxy.generateCompleteSyntax(collection)
  }

  /**
   * Terminates the web worker associated with this service. It is recommended to call
   * this method to free up resources once the syntax generation task is complete or
   * the service is no longer needed. This ensures efficient resource management.
   *
   * @returns {void}
   */
  public terminate(): void {
    this.worker.terminate()
  }
}
