import { expose } from 'comlink'
import { SyntaxManagerWorkerType } from './syntax-manager-worker.type'
import { ConditionGroup, ConditionType } from '@venio/shared/models/interfaces'

/**
 * Processes a group of conditions to generate a string representation of their syntax.
 *
 * @param {ConditionGroup} group - The condition group to be processed.
 * @returns {string} A string representation of the condition group's syntax.
 */
function processGroup(group: ConditionGroup): string {
  if (group.conditionType === 'group' && group.conditions) {
    const groupConditions = group.conditions
      .filter((c) => c?.conditionSyntax?.trim())
      .map((c) => c.conditionSyntax)
      .join(` ${group.operator} `)
    if (!groupConditions) return ''
    return `(${groupConditions})`
  }
  return ''
}

/**
 * The role of this function is to obtain the filtered conditions from the viewExpressionJson.
 * It is important to verify if the syntax of each condition is valid and discard any that are not valid.
 * Thus, the parsed JSON for the UI will only include valid conditions.
 * @param {ConditionGroup[]} conditions - The conditions to be filtered.
 * @returns {ConditionGroup[]} - The filtered conditions.
 */
function getFilteredConditions(conditions: ConditionGroup[]): ConditionGroup[] {
  const validConditions: ConditionGroup[] = []
  let lastGroupHadConditions = false

  for (const conditionGroup of conditions) {
    if (conditionGroup.conditionType === ConditionType.Group) {
      const filteredConditions =
        conditionGroup.conditions?.filter(
          (condition) => condition?.conditionSyntax
        ) || []
      if (filteredConditions.length > 0) {
        validConditions.push({
          ...conditionGroup,
          conditions: filteredConditions,
        })
        lastGroupHadConditions = true
      } else {
        lastGroupHadConditions = false
      }
    } else if (conditionGroup.conditionType === ConditionType.Operator) {
      // Add the operator only if the last group had conditions and it's not the first element
      if (lastGroupHadConditions && validConditions.length > 0) {
        validConditions.push(conditionGroup)
      }
    }
  }

  // Remove trailing operator if any
  if (
    validConditions.length > 0 &&
    validConditions[validConditions.length - 1].conditionType ===
      ConditionType.Operator
  ) {
    validConditions.pop()
  }

  return validConditions
}

/**
 * Generates a complete syntax string from an array of condition groups.
 * It concatenates individual condition syntaxes, applying logical operators where necessary.
 *
 * @param {ConditionGroup[]} conditions - An array of condition groups.
 * @returns {string} A complete syntax string generated from the condition groups.
 */
export function generateCompleteSyntax(conditions: ConditionGroup[]): string {
  let result = ''
  let pendingOperator = ''
  const filteredConditions = getFilteredConditions(conditions)

  filteredConditions.forEach((condition, index) => {
    if (!condition) return

    const isFirstElementIsOperatorType =
      index === 0 && condition.conditionType === ConditionType.Operator

    if (isFirstElementIsOperatorType) return

    const isGroupType = condition.conditionType === ConditionType.Group

    const hasNextSyntax = (
      isGroupType
        ? filteredConditions[index + 2]
        : filteredConditions[index + 1]
    )?.conditions?.some((c) => c.conditionSyntax)

    if (condition.conditionType === ConditionType.Group) {
      const syntax = processGroup(condition)

      result += `${pendingOperator}${syntax}`
      if (!syntax || !hasNextSyntax) return
      pendingOperator = ' AND '
    } else if (condition.conditionType === ConditionType.Operator) {
      if (!hasNextSyntax) {
        return
      }

      if (condition.operator?.toUpperCase() === 'NOT') {
        pendingOperator = ' AND NOT '
      } else {
        pendingOperator = ` ${condition.operator?.toUpperCase()} `
      }
    }
  })

  return result
}

expose({ generateCompleteSyntax } as SyntaxManagerWorkerType)
