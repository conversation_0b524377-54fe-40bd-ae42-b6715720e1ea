import { UsersListModel } from '@venio/shared/models/interfaces'
import { expose } from 'comlink'

function addInternalUserDataTransform(
  selectedItems: UsersListModel[],
  deselectedItems: UsersListModel[],
  internalUsersList: Map<number, string>
): Map<number, string> {
  selectedItems
    ?.filter((user) => !internalUsersList.has(user.userId))
    .map((user) =>
      internalUsersList.set(
        user.userId,
        `${user.userId}:${user.email}:${user.userName}:${user.groupName}`
      )
    )
  deselectedItems
    ?.filter((user) => internalUsersList.has(user.userId))
    .map((user) => internalUsersList.delete(user.userId))
  return internalUsersList
}

expose({ addInternalUserDataTransform })
