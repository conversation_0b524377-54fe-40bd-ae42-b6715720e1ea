type SimpleColumn = string
export type ComplexColumn = {
  title: string
  field: string
  mergeFields?: string[]
}

export type ColumnConfig = SimpleColumn | ComplexColumn

export interface CsvGeneratorWorkerType {
  generateCSVChunks: (
    columns: ColumnConfig[],
    data: any[],
    chunkSize: number
  ) => Blob[]
}

export type CSVGeneratorWorkerEvent = {
  kind: 'success' | 'error' | 'progress'
  result: unknown
}
