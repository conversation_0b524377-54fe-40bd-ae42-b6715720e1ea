{"name": "utilities", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/util/utilities/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/util/utilities/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/util/utilities/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/util/utilities/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/util/utilities/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}