// These are the base changes and common in all the dialogs ui

@layer {
  kendo-window {
    @apply t-border-[0] #{!important};
    &.v-custom-window {
      @apply t-top-[0px] #{!important};
    }
  }

  kendo-window{
    .k-window-titlebar{
      @apply t-pb-1 #{!important};
    }
  }
  kendo-dialog {
    &.k-dialog-wrapper {
      .k-dialog {
        box-shadow: 0px 20px 20px #00000029 !important;
        @apply t-rounded-md t-transition-all t-delay-300 #{!important};
        @apply t-overflow-hidden #{!important};
        .k-dialog-content {
          @apply t-pt-0 #{!important};
        }
      }

      .k-dialog-title {
        color: $dialog-title-text-color !important;
        @apply t-flex t-items-center t-capitalize t-font-medium t-text-[#263238] t-text-[16px] #{!important};
      }

      .k-window-titlebar-actions {
        // display: none; // This is to hide the close button
        .k-window-titlebar-action {
          background: var(--tb-kendo-error-100) !important;
          @apply t-rounded-full #{!important};
          color: var(--tb-kendo-body-bg) !important;
          @apply t-p-0.5 #{!important};
        }
      }

      .k-actions-horizontal {
        @apply t-border t-border-l-0 t-border-b-0 t-border-r-0 t-border-[#dbdbdb] #{!important};
      }

      .k-dialog-titlebar {
        @apply t-pb-4 #{!important};

        .k-dialog-title {
          @apply t-pb-0 #{!important};
        }
      }

      .k-window-actions {
        @apply t-pt-3 #{!important};
      }

      .v-custom-grey-bg {
        padding: 0.8rem;
        background: $form-bg-color;
      }
    }

    &.v-custom-dialog-pos {
      .k-dialog {
        @apply t-translate-y-[-26vh] #{!important};
      }
    }
  }
}

// Variables
$icon-size: 3.5rem;
$dialog-width: 27.5rem;
$border-radius-circle: 100px;
$border-radius-box: 4px;

// Placeholder for background image configuration
%background-image-config {
  background-repeat: no-repeat;
  background-position: center;
  background-size: 1rem;
}

// Mixin for dialog icon types
@mixin dialog-icon($icon-url) {
  background-image: url($icon-url);
  @extend %background-image-config;
}

.v-highlight-selected {
  background: #ff9632 !important;
}
.v-search-text-highlight {
  background-color: #7ac4ce;
  &.v-search-text-highlight-active {
    background-color: #04aec5;
    color: #ffffff;
  }
}
.v-similar-term-highlight {
  background-color: #deecab;
  &.search-text-highlight-active {
    background-color: #ddf3a0;
    color: #ffffff;
  }
}

.v-confirmation-dialog {
  @layer {
    .v-dialog-content {
      @apply t-flex t-gap-4 t-mb-5;

      &-icon {
        background-color: #f5faf6;

        @apply t-grid t-w-14 t-h-14 t-rounded-full t-place-content-center;
      }

      &-message {
        @apply t-flex-1;
      }    
    }
    .k-dialog-actions {
      @apply t-border-none #{!important};
    }
  }

  @each $type in save, warning, info, delete, rebatch, reassign {
    &.v-dialog-#{$type} {
      .v-dialog-content-icon {
        background-size: 13px;
        @include dialog-icon(
          '~libs/shared/assets/src/icons/svg/icon-material-#{$type}.svg'
        );
        @if $type == rebatch {
          background-color: #FFF7E5;
        }
        @if $type == delete {
          background-color: #FDF1E9;
        }        
      }
    }
  }
  &.v-dialog-save {
    .k-actions {
      .v-dialog-action-button-cancel {
        @apply t-hidden;
      }
    }
    .k-dialog-actions {
      @apply t-border-none #{!important};
    }
  }

  .k-dialog {
    overflow: hidden;
    padding: 1rem 0;
    min-width: $dialog-width;
    border-radius: $border-radius-box;
    box-shadow: 0 20px 20px #00000029;
    color: var(--kendo-neutral-220);

    @layer {
      .k-dialog-titlebar {
        @apply t-my-2 t-py-0;

        .k-dialog-titlebar-actions {
          @apply t-hidden;
        }

        .k-dialog-title {
          color: var(--kendo-neutral-220);
        }
      }

      .k-overlay {
        @apply t-opacity-80;
      }
    }

    .k-actions {
      @layer {
        &.k-dialog-actions {
          @apply t-gap-4 t-p-0 t-px-6 t-justify-end #{!important};
        }
      }

      &.k-actions-stretched > * {
        flex: 0 0 20%;
        flex-basis: auto;
      }

      @layer {
        button span {
          @apply t-text-xl;
        }
      }

      .v-dialog-action-button {
        width: 2.6rem;
        height: 2rem;
        border-radius: $border-radius-box;
        line-height: 1.3;

        &-cancel {
          background-color: #ffefed;
          color: #ec3737;

          &:hover {
            background-color: #fbe6e4;
          }
        }

        &-confirm {
          background-color: #ecf7da;
          color: #88b13f;

          &:hover {
            background-color: #e7f6cf;
          }
        }
      }
    }
  }
}
