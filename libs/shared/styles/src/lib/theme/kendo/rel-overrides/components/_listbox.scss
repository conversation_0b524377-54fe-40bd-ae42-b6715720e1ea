@layer {
  .v-custom-listbox {
    .k-listbox-actions {
      @apply t-w-[13.7%] t-items-end t-justify-center #{!important};

      .k-button {
        @apply t-w-7 t-h-7 #{!important};

        &:nth-child(-n + 2) {
          // @apply t-hidden #{!important};
        }
      }
    }
  }

  // document history timeline UI
  .v-custom-timeline-block {
    &:nth-child(1) {
      .v-custom-timeline-block__wrap__line {
        @apply t-top-[39px] #{!important};
      }
    }

    &__wrap {
      &:nth-child(1) {
        @apply t-pt-0  #{!important};
      }

      &__listview {
        padding-bottom: 0.5rem;

        &__item {
          &::before {
            content: '';
            @apply t-left-[89px] t-mt-[-16px] t-top-[50%] t-w-[13px] t-h-[13px] t-rounded-full t-bg-[#718792] t-border t-border-[#718792] t-border-2 t-z-[2] t-block t-absolute;
          }
        }
      }
    }

    // if only one item in the document history (per group) we will hide the line
    .k-listview-item {
      &:only-child {
        .v-custom-timeline-block__wrap__listview__item {
          @apply t-overflow-hidden;
          &::after {
            content: '';
            @apply t-left-[95px] t-mt-[-3px] t-top-[50%] t-w-[2px] t-h-[100%] t-rounded-full t-bg-[#ffffff]  t-z-[2] t-block t-absolute;
          }
        }
      }
    }
  }
}
