@layer {

  // kendo expansion panel
  kendo-expansionpanel {
    &.k-expander {
      @apply t-border-0 t-bg-[#ffffff] t-outline-none #{!important};

      &.k-focus {
        @apply t-shadow-none t-border-0 #{!important};

        &:focus {
          @apply t-shadow-none t-border-0;
        }

        &::after {
          @apply t-hidden;
        }
      }

      &:focus,
      &:active,
      &:visited {
        background-color: none !important;
        @apply t-shadow-none t-border-0 t-outline-none;
      }

      &:hover {
        background-color: transparent;
        @apply t-border-0 t-outline-none;
      }

      .k-expander-header {
        color: var(--kendo-primary-100) !important;
        @apply t-flex t-place-self-start t-flex-row t-p-0 t-pb-2 t-gap-2 #{!important};

        .header-content {
          @apply t-outline-none t-border-0 t-font-semibold #{!important};
        }

        &:hover {
          background-color: transparent !important;
          @apply t-border-0;
        }

        .k-expander-indicator {
          @apply t-m-0 t-bg-[transparent] #{!important};

          .k-icon-wrapper-host {
            .k-svg-i-caret-alt-up {
              svg {
                @apply t-hidden #{!important};
              }

              &::before {
                content: ' ';
                width: 20px;
                height: 20px;
                background: url('~apps/venio-next/src/assets/svg/icon-down-chevron-sleek.svg') no-repeat center/16px 16px;
              }
            }

            .k-svg-i-caret-alt-down {
              svg {
                @apply t-hidden #{!important};
              }

              &::before {
                content: ' ';
                width: 20px;
                height: 20px;
                background: url('~apps/venio-next/src/assets/svg/icon-down-chevron-sleek.svg') no-repeat center/16px 16px;
                transform: rotate(180deg);
              }
            }
          }
        }
      }



      &::after {
        @apply t-hidden;
      }
    }

    &.v-review-tags-panel {
      .k-expander-content-wrapper {
        .k-expander-content {
          @apply t-p-0 #{!important};
        }
      }

      @apply t-w-full;

      .v-review-tags-panel-title {
        @apply t-text-[#000000] #{!important};
      }

      .k-expander-header {
        .k-button {
          &.k-disabled {
            background: #fff !important;
          }
        }

        @apply t-mt-1;
        @apply t-flex t-items-center t-flex-row t-justify-between t-p-0 t-pb-2 t-w-full t-border t-border-l-0 t-border-r-0 t-border-t-0 t-border-b t-border-[#dbdbdb] #{!important};

        &:hover,
        &:focus,
        &:active,
        &:visited {
          @apply t-border t-border-l-0 t-border-r-0 t-border-t-0 t-border-b t-border-[#dbdbdb] #{!important};
        }
      }

      .k-expander-indicator {
        background-color: #1ebadc;
        width: 15px;
        height: 15px;
        color: #ffffff !important;
        @apply t-absolute t-cursor-pointer t-right-[44px] t-rounded-full t-text-[#ffffff];

        .k-svg-icon {
          @apply t-text-[#ffffff];
        }
      }

      &.k-expanded {
        .v-review-tags-panel-title {
          @apply t-text-[#1EBADC] #{!important};
        }
      }
    }

    &.v-custom-expansion-panel {
      .k-expander-content-wrapper {
        .k-expander-content {
          @apply t-p-0 #{!important};
        }
      }

      width: calc(100% - 2px);
      @apply t-my-2 #{!important};

      // &.k-expander{
      //   box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), 0 0 4px rgba(0, 0, 0, 0.06) !important;
      // }
      &.k-expander.k-expanded,
      .k-expander.k-focus {
        box-shadow: none !important;
      }

      // .k-expander-header[aria-expanded="true"]{
      //   box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), 0 0 4px rgba(0, 0, 0, 0.06) !important;
      //   margin-top: 3px;
      // }

      .k-expander-header {
        @apply t-flex t-items-center t-justify-between t-rounded-lg t-opacity-100 t-w-full t-mt-[2px] t-px-4 t-py-4 t-w-[calc(100%-2px)] t-justify-items-center t-self-center #{!important};
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), 0 0 4px rgba(0, 0, 0, 0.06) !important;

        .k-spacer {
          display: none !important;
        }

        .k-expander-sub-title {
          font: Roboto;
          @apply t-text-left t-font-normal t-text-[14px] t-leading-[1px] t-tracking-normal t-opacity-100 #{!important};
        }

        .k-expander-indicator {
          @apply t-m-0 t-bg-[transparent] #{!important};

          .k-icon-wrapper-host {
            .k-svg-i-caret-alt-up {
              svg {
                @apply t-hidden #{!important};
              }

              &::before {
                content: ' ';
                width: 20px;
                height: 20px;
                background: url('~apps/venio-next/src/assets/svg/accordin-arrow-up.svg') no-repeat center/16px 16px;
              }
            }

            .k-svg-i-caret-alt-down {
              svg {
                @apply t-hidden #{!important};
              }

              &::before {
                content: ' ';
                width: 20px;
                height: 20px;
                background: url('~apps/venio-next/src/assets/svg/accordin-arrow-down.svg') no-repeat center/16px 16px;
                transform: none;
              }
            }
          }
        }

        &[aria-expanded="true"] {
          .k-expander-sub-title {
            @apply t-text-[#1DBADC] #{!important};
          }
        }

        &[aria-expanded="false"] {
          .k-expander-sub-title {
            @apply t-text-[#707070] #{!important};
          }
        }
      }

      .k-expander-content-wrapper {
        width: calc(100% - 1px);
        border: 1px solid #DADADA !important;
        border-radius: 0px 0px 4px 4px !important;
      }
    }

    // Transcript side panel
    &.v-custom-expansionpanel-trans {
      .k-expander-content-wrapper {
        .k-expander-content {
          @apply t-p-0 #{!important};
        }
      }

      .k-expander-header {
        @apply t-w-full t-flex t-p-0 #{!important};

        .k-expander-indicator {
          @apply t-hidden #{!important};
        }
      }

      &.k-expander {
        @apply t-bg-[#ffffff] #{!important};

        &.k-expanded {
          @apply t-mt-0 #{!important};
        }
      }

      .k-expander-content-wrapper {
        @apply t-w-full t-flex t-flex-col;

        .k-expander-content {
          @apply t-w-full t-p-0 #{!important};
        }
      }
    }

    &.v-custom-expansion-case {

      .k-expander-header {
        @apply t-shadow-lg t-rounded-[4px] t-w-full t-relative t-p-5 t-h-[50px] #{!important};

        .k-expander-indicator {
          @apply t-text-info #{!important};

          .k-icon {
            @apply t-w-[24px] #{!important};
          }
        }

        &[aria-expanded="true"] {
          @apply t-shadow-lg #{!important};
        }
      }

      .k-expander-content-wrapper {
        @apply t-border-[2px] t-border-[#DADADA] t-w-full t-rounded-b-md t-self-center t-border-t-0 t-overflow-hidden t-p-2 t-shadow-lg t-bg-[#ffffff] #{!important};
      }

    }


  }

  .v-custom-upload-stepper {
    .k-step-list {
      .k-step {
        @apply t-mb-[40px] #{!important};

        .k-step-link {
          @apply t-my-[20px] #{!important};

          .k-step-indicator {
            @apply t-border-[transparent] #{!important};

            &::before {
              @apply t-bg-[#70707063] t-border-[transparent] #{!important};
            }

            kendo-icon-wrapper {
              .k-step-indicator-icon {
                @apply t-hidden #{!important};
              }
            }
          }
        }

        .k-step-label {
          .k-step-text {
            @apply t-text-[#C7C7C7] #{!important};
          }
        }

        &.k-step-current {
          .k-step-link {
            .k-step-indicator {
              @apply t-bg-[#1DBADC] t-border-[#1DBADC] #{!important};

              &::before {
                @apply t-bg-[transparent] t-border-[transparent] #{!important};
              }

              &::after {
                @apply t-hidden #{!important};
              }
            }

            .k-step-label {
              .k-step-text {
                @apply t-text-[#4A4B90] #{!important};
              }
            }
          }
        }

        &.k-step-done {
          .k-step-link {
            .k-step-indicator {
              @apply t-bg-[#9BD2A7] t-border-[#9BD2A7] #{!important};

              &::before {
                @apply t-bg-[transparent] t-border-[transparent] #{!important};
              }

              &::after {
                @apply t-hidden #{!important};
              }
            }

            .k-step-label {
              .k-step-text {
                @apply t-text-[#9BD2A7] #{!important};
              }
            }
          }
        }

      }
    }

    kendo-progressbar {
      @apply t-border-[rgba(112,112,112,0.39)] t-bg-[rgba(112,112,112,0.39)] t-w-[1px] #{!important};

      .k-selected {
        @apply t-border-[transparent] t-bg-[transparent] t-text-[#4A4B90] #{!important};
      }
    }
  }

  .v-custom-import-stepper {
    .k-step-list {
      .k-step {
        @apply t-mb-[40px] #{!important};

        .k-step-link {
          @apply t-my-[20px] #{!important};

          .k-step-indicator {
            @apply t-border-[transparent] #{!important};

            &::before {
              @apply t-bg-[#70707063] t-border-[transparent] #{!important};
            }

            kendo-icon-wrapper {
              .k-step-indicator-icon {
                @apply t-hidden #{!important};
              }
            }
          }
        }

        .k-step-label {
          .k-step-text {
            @apply t-text-[#C7C7C7] #{!important};
          }
        }

        &.k-step-current {
          .k-step-link {
            .k-step-indicator {
              @apply t-bg-[#9BD2A7] t-border-[#9BD2A7] #{!important};

              &::before {
                @apply t-bg-[transparent] t-border-[transparent] #{!important};
              }

              &::after {
                @apply t-hidden #{!important};
              }
            }

            .k-step-label {
              .k-step-text {
                @apply  t-text-[#9BD2A7] #{!important};
              }
            }
          }
        }

        &.k-step-done {
          .k-step-link {
            .k-step-indicator {
              @apply t-bg-[#1DBADC] t-border-[#1DBADC] #{!important};

              &::before {
                @apply t-bg-[transparent] t-border-[transparent] #{!important};
              }

              &::after {
                @apply t-hidden #{!important};
              }
            }

            .k-step-label {
              .k-step-text {
                @apply t-text-[#1DBADC] #{!important};
              }
            }
          }
        }

      }
    }

    kendo-progressbar {
      @apply t-border-[rgba(112,112,112,0.39)] t-bg-[rgba(112,112,112,0.39)] t-w-[1px] #{!important};

      .k-selected {
        @apply t-border-[transparent] t-bg-[transparent] t-text-[#4A4B90] #{!important};
      }
    }
  }
}