@import 'variables';

// These are the base changes and common in all the input ui
@layer {
  kendo-formerror {
    @apply t-text-[#ED7425] t-text-sm #{!important};
  }
  /* Apply default styles - Firefox compatibility */
  textarea {
    @apply t-leading-none t-min-h-[30px] #{!important};
  }
  .k-input,
  .k-picker {
    border-color: var(--tb-kendo-neutral-40);
  }

  .k-radio {
    &.v-custom-radio-sm {
      display: flex !important;
      align-items: center !important;
      justify-items: center !important;
      width: 12px !important; // Outer circle size
      height: 12px !important;
      border-radius: 50%;
      display: inline-block;
      position: relative;
      border: 1px solid #000; // Adjust border color as needed
      margin: 0;
      box-sizing: border-box;

      &:checked::before {
        content: '' !important;
        width: 50% !important; // Inner circle as a percentage of outer circle
        height: 50% !important;
        background-color: #000; // Inner circle color
        border-radius: 50% !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important; // Center the inner circle
      }
    }
    &:focus {
      @apply t-outline-0 #{!important};
    }
    &:checked {
      color: var(--kendo-primary-100) !important;
      border-color: var(--kendo-primary-100) !important;
    }
  }

  .k-radio-sm {
    .v-custom-small-radio-btn {
      transform: scale(0.7); /* Adjust the scale to make it smaller */
      transform-origin: center;
      &:hover {
        color: var(--kendo-primary-100) !important;
        border-color: var(--kendo-primary-100) !important;
      }
      &:focus {
        @apply t-outline-0 #{!important};
      }
      &:checked {
        color: var(--kendo-primary-100) !important;
        border-color: var(--kendo-primary-100) !important;
      }
    }
  }

  // custom checkbox
  .k-checkbox {
    border-color: $border-grey-color !important;
    @apply t-border-2 t-w-4 t-h-4 #{!important};
    &.custom-sm-checkbox{
      @apply t-h-[11px] t-w-[12px] #{!important};
    }
    &.custom-checkbox {
      @apply t-h-[14px] t-w-[14px] #{!important};
    }
    &:checked {
      border-color: var(
        --kendo-checkbox-checked-bg,
        var(--kendo-primary-100, inherit)
      ) !important;
      background-color: var(--kendo-primary-100) !important;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='square' stroke-linejoin='square' stroke-width='2' d='M3,8 l3,3 l7-7'/%3e%3c/svg%3e") !important;
    }
    &:hover:not(:checked):not(:disabled):not(.k-disabled) {
      border-color: var(--kendo-primary-100) !important;
      background-color: rgba(47, 48, 128, 0.1) !important;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%232F3080' stroke-linecap='square' stroke-linejoin='square' stroke-width='2' d='M3,8 l3,3 l7-7'/%3e%3c/svg%3e") !important;
    }
    &:focus {
      @apply t-outline-0 #{!important};
    }
    &:indeterminate {
      background-image: var(
        --kendo-checkbox-indeterminate-image,
        url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3crect x='3' y='3' width='10' height='10' rx='2' fill='%232F3080'/%3e%3c/svg%3e")
      ) !important;
    }
    &.k-indeterminate {
      background-image: var(
        --kendo-checkbox-indeterminate-image,
        url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3crect x='3' y='3' width='10' height='10' rx='2' fill='%232F3080'/%3e%3c/svg%3e")
      ) !important;
    }

    &:disabled {
      @apply t-grayscale t-opacity-40 t-pointer-events-none t-shadow-none #{!important};
    }
    &.k-disabled {
      @apply t-grayscale t-opacity-40 t-pointer-events-none t-shadow-none #{!important};
    }
  }

  // custom colorpicker
  .k-colorpicker {
    .k-input-inner {
      @apply t-p-1.5 #{!important};
    }
  }

  // custom input
  .k-input {
    @apply t-border-[#BEBEBE] #{!important};
    &.v-purple-input {
      box-shadow: inset 0px 0px 0px 1px #cbb9ff !important;
      @apply t-border-[#CBB9FF] #{!important};
      input[type='text'] {
        @apply t-text-center;
      }
    }
  }

  // disabled labels
  // to make a disabled labels use the class label-disabled class
  // for e.g. [class.label-disabled]="newCustomFieldDisabled"
  .label-disabled {
    color: var(--tb-kendo-neutral-80);
  }

  // reset kendo switch
  kendo-switch {
    &::after {
      &:focus {
        @apply t-outline-0 t-border-0 #{!important};
      }
    }
    .k-switch-track {
      @apply t-border-[#D6D6D6] t-bg-[#D6D6D6] #{!important};
      .k-switch-label-on,
      .k-switch-label-off {
        @apply t-inline #{!important};
      }
    }
    .k-switch-thumb-wrap {
      .k-switch-thumb {
        box-shadow: 0px 0px 0px 2px #ffffff !important;
        @apply t-bg-[#9A9A9A] #{!important};
        @apply t-border-[2px] t-border-[#ffffff];
      }
    }


    &.v-upload-custom-switch {
      &.k-switch-on {
        .k-switch-thumb-wrap {
          .k-switch-thumb {
            @apply t-bg-[#000000] #{!important};
          }
        }
      }
    }

    &.k-switch-on {
      .k-switch-track {
        border-color: var(--kendo-custom-secondary-100) !important;
        background-color: var(--kendo-custom-secondary-100) !important;
      }
      .k-switch-thumb-wrap {
        .k-switch-thumb {
          @apply t-bg-[#1EBADC] #{!important};
        }
      }
    }
  }

  kendo-slider {
    &.v-custom-slider {
      .k-slider-track {
        @apply t-bg-[#ffffff] t-h-[7px] t-shadow-[inset_0_0px_0px_1px_rgba(0,0,0,0.2)] #{!important};
        .k-slider-selection {
          @apply t-bg-[#1EBADC] #{!important};
        }
        .k-draghandle {
          @apply t-bg-[#1EBADC] t-border-0 t-w-[7px] t-rounded-[1px] #{!important};
        }
      }
    }
  }

  kendo-textarea {
    &.v-list-search-textarea {
      textarea {
        @apply t-leading-normal #{!important};
      }
    }
  }

  .v-custom-magic-ai-box-default {
    kendo-textarea {
      @apply t-min-h-fit #{!important};
      textarea {
        @apply t-max-h-[100px];
        @apply t-leading-normal #{!important};
      }
      &.v-empty-textarea {
        textarea {
          @apply t-h-[35px] t-transition-all #{!important};
        }
      }
      &.k-input {
        @apply t-border-[3px] t-border-[#cccccc] #{!important};
      }

      &.k-focus {
        @apply t-shadow-none #{!important};
      }
    }
  }
  .v-custom-magic-ai-box {
    @apply t-inline-block t-overflow-hidden t-relative;

    kendo-textarea {
      @apply t-min-h-fit #{!important};
      textarea {
        @apply t-max-h-[100px];
        @apply t-leading-normal #{!important};
      }
      &.k-focus {
        @apply t-shadow-none #{!important};
      }

      &.v-empty-textarea {
        textarea {
          @apply t-h-[35px] t-transition-all #{!important};
        }
      }

      &.k-input {
        @apply t-border-[3px] t-border-[transparent] t-rounded-[4px] t-bg-clip-padding t-box-border #{!important};

        &::after {
          @apply t-hidden #{!important};

          &:focus,
          &:hover,
          &:active,
          &:focus-within {
            @apply t-border-0 t-shadow-none t-outline-none #{!important};
          }
        }
      }
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 4px;
      padding: 2px;
      background: linear-gradient(270deg, #1ebadc, #73c8bf, #1ebadc, #73c8bf);
      background-size: 400% 400%;
      animation: gradientAnimation 5s ease infinite;
      z-index: -1;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      border-radius: 4px;
      background: rgba(255, 255, 255, 0.3);
      transform: skewX(-45deg);
      animation: shineAnimation 1.5s infinite;
      z-index: -1;
    }

    @keyframes shineAnimation {
      0% {
        left: -110%;
      }

      50% {
        left: 100%;
      }

      100% {
        left: 110%;
      }
    }

    @keyframes gradientAnimation {
      0% {
        background-position: 0% 50%;
      }

      50% {
        background-position: 100% 50%;
      }

      100% {
        background-position: 0% 50%;
      }
    }
  }

  // custom date range calendar
  .v-custom-daterange {
    .k-calendar-td {
      &.k-today {
        .k-link {
          @apply t-bg-[#E1F3C1] t-text-[#000000] #{!important};
        }
      }
    }
    .k-calendar-td.k-range-start,
    .k-calendar-td.k-range-end,
    .k-calendar-td.k-range-mid {
      @apply t-bg-[#EEF8DC] #{!important};
      &::before {
        @apply t-border-[#EEF8DC] #{!important};
      }
    }

    .k-today {
      .k-link {
        @apply t-bg-[#E1F3C1] t-text-[#000000] #{!important};
      }
    }
    .k-calendar-td {
      &:hover {
        &::before {
          @apply t-hidden;
        }
        .k-link {
          @apply t-shadow-none t-bg-[#EEF8DC] #{!important};
        }
      }
    }

    .k-calendar-th {
      @apply t-text-[#979797] #{!important};
    }
  }

  kendo-dropdownlist,
  kendo-combobox {
    @apply t-rounded-[4px] t-shadow-none #{!important};
    .k-button {
      @apply t-border-[#BEBEBE] #{!important};
    }
    .k-button-icon {
      svg {
        @apply t-hidden #{!important};
      }
      &::before {
        content: ' ';
        width: 20px;
        height: 20px;
        background: url('~apps/venio-next/src/assets/svg/icon-down-chevron-sleek.svg')
          no-repeat center/16px 16px;
      }
    }
    &:focus {
      @apply t-outline-0 t-shadow-none #{!important};
    }
    &.v-custom-project-selector {
      @apply t-border-0 t-outline-none t-shadow-none t-bg-[#f6f6f6] t-rounded-[4px] #{!important};
      .k-input-value-text {
        @apply t-text-[#477150] t-font-medium;
      }
      &.v-hide-dropdown {
        @apply t-select-none;
        .k-button {
          @apply t-hidden #{!important};
        }
      }
    }

    &.v-custom-dropdown-template{
      .k-list-item{
        @apply t-px-2 t-py-1 #{!important};
      }
    }
  }

  kendo-dropdowntree {
    @apply t-rounded-[4px] t-shadow-none t-border-[#BEBEBE] #{!important};
    .k-button {
      @apply t-border-[#BEBEBE] #{!important};
    }
    .k-button-icon {
      svg {
        @apply t-hidden #{!important};
      }
      &::before {
        content: ' ';
        width: 20px;
        height: 20px;
        background: url('~apps/venio-next/src/assets/svg/icon-down-chevron-sleek.svg')
          no-repeat center/16px 16px;
      }
    }
    .k-svg-i-x {
      @apply t-text-[var(--tb-kendo-error-100)];
    }
    &:focus,
    &:hover {
      @apply t-outline-0 t-shadow-none #{!important};
    }
  }

  input[type='checkbox']:checked.k-checkbox + kendo-label {
    @apply t-font-semibold;
  }

  kendo-textbox {
    @apply t-rounded #{!important};
    &:focus,
    &:hover {
      @apply t-outline-0 t-shadow-none #{!important};
    }
    .k-clear-value {
      .k-svg-i-x {
        @apply t-text-[var(--tb-kendo-error-100)];
      }
    }
    &.v-custom-filed-operator-textbox {
      @apply t-rounded-tr-none t-rounded-br-none t-border-r-0 #{!important};
    }

    &.v-input-l-none {
      @apply t-rounded-l-none  #{!important};
    }
  }

  kendo-numerictextbox {
    &.v-custom-numerictextbox-adornment {
      @apply t-relative #{!important};

      .k-input {
        @apply t-flex t-items-center t-justify-between #{!important};
      }

      .k-input-prefix,
      .k-input-suffix,
      .k-input-spinner {
        @apply t-flex #{!important};
      }

      .k-input-inner {
        @apply t-order-1 t-pr-2 t-border-none #{!important};
      }

      .k-input-spinner {
        @apply t-order-2 #{!important};
      }

      .k-input-suffix {
        @apply t-order-3 t-flex t-items-center t-px-2 t-text-[#777] t-bg-white t-border-l t-border-[#ccc] #{!important};
      }

      .suffix-after-spinner {
        @apply t-text-[14px] #{!important};
      }
    }

    span.k-input-spinner {
      @apply t-p-[3px];
      .k-spinner-increase {
        .k-icon-wrapper-host {
          .k-svg-i-caret-alt-up {
            svg {
              @apply t-hidden #{!important};
            }

            &::before {
              @apply t-w-[20px] t-h-[20px];
              content: ' ';
              background: url('~apps/venio-next/src/assets/svg/icon-down-chevron-sleek.svg')
                no-repeat center/16px 16px;
              transform: rotate(180deg);
            }
          }
        }
      }

      .k-spinner-decrease {
        .k-icon-wrapper-host {
          .k-svg-i-caret-alt-down {
            svg {
              @apply t-hidden #{!important};
            }

            &::before {
              content: ' ';
              @apply t-w-[20px] t-h-[20px];
              background: url('~apps/venio-next/src/assets/svg/icon-down-chevron-sleek.svg')
                no-repeat center/16px 16px;
            }
          }
        }
      }
    }

    &.k-disabled {
      .k-spinner-increase,
      .k-spinner-decrease {
        @apply t-bg-[transparent] t-opacity-30 #{!important};
      }
    }
  }

  kendo-multiselecttree {
    @apply t-rounded-[4px] t-shadow-none #{!important};
  }

  kendo-datepicker {
    // default values
    input {
      @apply t-uppercase #{!important};
    }
  }

  .v-custom-upload-container {
    .v-upload-area.drag-over {
      @apply t-bg-[#e6f7ff] t-border-[#1ebadc];
    }
  }
}
