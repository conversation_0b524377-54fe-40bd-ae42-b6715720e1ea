export class LocalStorage {
  public static set(key: string, value: unknown): void {
    localStorage.setItem(key, JSON.stringify(value))
  }

  public static get<T>(key: string): T {
    const content = localStorage.getItem(key)

    try {
      return JSON.parse(content || '') as T
    } catch {
      return content as unknown as T
    }
  }

  public static remove(key: string | string[]): void {
    if (Array.isArray(key)) {
      key.forEach((k) => localStorage.removeItem(k))
    } else {
      localStorage.removeItem(key)
    }
  }

  public static clear(): void {
    localStorage.clear()
  }
}
