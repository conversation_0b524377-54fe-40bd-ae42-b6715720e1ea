export interface SamplingModel {
  //SamplingModel
  projectId?: number
  sampleId?: number
  sampleName?: string
  samplePurpose?: string
  sampleDate?: string

  confidenceLevel?: number
  confidenceInterval?: number
  samplePercent?: number
  sampleNumber?: number
  applyTagId?: number
  searchId?: number
  excludeNoText?: boolean
  excludeTags?: boolean
  tagIds?: number[]
  excludeTagIds?: number[]
  sampleAction?: string

  sampleActionDetail?: string
  excludeExistingProfileReviewset?: boolean
  excludeManualCatDocs?: boolean
  sampleTrackingTagId?: number
  sampleTrackingFor?: string
  sampleTrackingTagCount?: number
  samplePreviousTagCount?: number

  populationSize?: number
  sampleSize?: number
  sampleTagCondition?: string
  samplePopulation?: string
  stratifiedBy?: string
  samplingMethod?: string

  searchResultTempTable?: string
  selectedFileIds?: number[]
  unselectedFileID?: number[]
  isBatchSelection?: boolean
  isAllDocuments?: boolean

  isParent?: boolean
  module?: string
}
