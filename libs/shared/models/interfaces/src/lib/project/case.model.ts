import { CommonActionTypes } from '@venio/shared/models/constants'
import { ReviewSetSummary } from './reviewset.model'

export interface CaseDetailModel {
  sn?: number
  caseCreatedDate: string
  caseCreatedTime: string
  clientName: string
  custodianCount: number
  documentCount: number
  isFavoriteProject: boolean
  projectCreator: string
  projectId: number
  projectName: string
  projectUpdatedVersion: string
  reviewSetCount: ReviewSetSummary
  isExportServiceCase?: boolean
  caseType: CaseType
}

export interface CaseDetailResponseModel {
  caseDetailEntries: CaseDetailModel[]
  totalCaseCount: number
  totalCustodianCount: number
  totalReviewSetCount: ReviewSetSummary
}

export interface CaseDetailRequestInfo {
  // Only for the client side as it won't be sent to the server
  totalCaseCount?: number

  // For server side
  /**
   * Page number to fetch the data
   */
  pageNumber: number
  /**
   * Number of items to fetch in a page
   */
  pageSize: number
  /**
   * Sort field to sort the data
   */
  sortField?: string
  /**
   * Sort order to sort the data
   */
  sortOrder?: string
  /**
   * Search text to filter the data
   */
  searchText?: string
  /**
   * Comma separated client ids to filter the data
   */
  clientIdFilter?: string

  /**
   * Whether to filter the favorite projects
   */
  filterFavoriteProjects?: boolean

  /**
   * Case type filter enum
   */
  caseTypeFilter?: CaseType
}

export interface LaunchpadAction {
  actionType: CommonActionTypes
  // Content could be if it is a case, document share, or review set or any other content object
  content: any
}

export enum LaunchpadTabTypes {
  CASE = 'All Cases',
  REVIEW_SET = 'Review Set',
  SHARED_DOCUMENT = 'Shared document',
}

export enum CaseType {
  ALL_CASES = -1,
  VOD_SERVICE,
  DIRECT_EXPORT_SERVICE,
  PRINT_SERVICE,
  PDF_SERVICE,
  VoDR_STANDARD_CONCORDANCE_SERVICE,
  VoDR_STANDARD_SUMMANTION_SERVICE,
  VODR_IMPORT_TO_RELATIVITY_SERVICE,
}
