/**
 * Enum for Reports Types.
 * There could be more types in the future that we extend here.
 */
export enum ReportTypes {
  LOG_IN_OUT_REPORTS = 'Login & Logout Report',
  LOCKED_USERS_REPORTS = 'Locked Users Report',
  UNLOCKED_USERS_REPORTS = 'Unlocked Users Report',
  CREATION_AND_DEACTIVATION_REPORTS = 'Creation & Deactivation Report',
  DATA_EXPORT_AND_DOWNLOAD_REPORTS = 'Data Export & Download Report',
  ROLE_CHANGE_REPORTS = 'Role Change Report',
  DELETED_EXPORTS = 'Deleted Exports',
  PROJECT_ACCESS_REPORT = 'Project Access Report',
  ACTIVITY_REPORT = 'Activity Report',
  MATTER_DETAIL_REPORT = 'Matter Detail Report',
  ACTIVATED_DEACTIVATED_CUSTODIAN_REPORT = 'Activated & Deactivated Custodian Report',
  // Add more types here
}

export enum ReportDownloadFileNames {
  BULK_REDACTION_REPORT = 'BulkRedactionLog',
  // Add more types here
}
