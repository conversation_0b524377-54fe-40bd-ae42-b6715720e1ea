import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import * as LegalHoldReportActions from './legal-hold-report.actions'
import * as LegalHoldReportSelectors from './legal-hold-report.selectors'
import { LegalHoldReportState } from './legal-hold-report.reducer'

type LegalHoldReportStateKeys =
  | keyof LegalHoldReportState
  | Array<keyof LegalHoldReportState>

@Injectable({ providedIn: 'root' })
export class LegalHoldReportFacade {
  // Legal Hold list selectors
  public readonly selectLegalHoldList$ = this.store.pipe(
    select(LegalHoldReportSelectors.getLegalHoldList)
  )

  public readonly selectLegalHoldListLoading$ = this.store.pipe(
    select(
      LegalHoldReportSelectors.getStateFromLegalHoldReportStore(
        'isLegalHoldListLoading'
      )
    )
  )

  public readonly selectLegalHoldListSuccessResponse$ = this.store.pipe(
    select(
      LegalHoldReportSelectors.getStateFromLegalHoldReportStore(
        'legalHoldListSuccessResponse'
      )
    )
  )

  public readonly selectLegalHoldListErrorResponse$ = this.store.pipe(
    select(
      LegalHoldReportSelectors.getStateFromLegalHoldReportStore(
        'legalHoldListErrorResponse'
      )
    )
  )

  // Custodian list selectors
  public readonly selectCustodianList$ = this.store.pipe(
    select(LegalHoldReportSelectors.getCustodianList)
  )

  public readonly selectCustodianListLoading$ = this.store.pipe(
    select(
      LegalHoldReportSelectors.getStateFromLegalHoldReportStore(
        'isCustodianListLoading'
      )
    )
  )

  public readonly selectCustodianListSuccessResponse$ = this.store.pipe(
    select(
      LegalHoldReportSelectors.getStateFromLegalHoldReportStore(
        'custodianListSuccessResponse'
      )
    )
  )

  public readonly selectCustodianListErrorResponse$ = this.store.pipe(
    select(
      LegalHoldReportSelectors.getStateFromLegalHoldReportStore(
        'custodianListErrorResponse'
      )
    )
  )

  // Status selectors
  public readonly selectStatusList$ = this.store.pipe(
    select(LegalHoldReportSelectors.getStatusList)
  )

  public readonly selectStatusLoading$ = this.store.pipe(
    select(LegalHoldReportSelectors.isStatusLoading)
  )

  public readonly selectStatusSuccessResponse$ = this.store.pipe(
    select(
      LegalHoldReportSelectors.getStateFromLegalHoldReportStore(
        'statusSuccessResponse'
      )
    )
  )

  public readonly selectStatusErrorResponse$ = this.store.pipe(
    select(
      LegalHoldReportSelectors.getStateFromLegalHoldReportStore(
        'statusErrorResponse'
      )
    )
  )

  // Active Terminated Legal Hold List selectors
  public readonly selectActiveTerminatedLegalHoldListLoading$ = this.store.pipe(
    select(
      LegalHoldReportSelectors.getStateFromLegalHoldReportStore(
        'isActiveTerminatedLegalHoldListLoading'
      )
    )
  )

  public readonly selectActiveTerminatedLegalHoldListSuccessResponse$ =
    this.store.pipe(
      select(
        LegalHoldReportSelectors.getStateFromLegalHoldReportStore(
          'ActiveTerminatedLegalHoldListSuccessResponse'
        )
      )
    )

  public readonly selectActiveTerminatedLegalHoldListErrorResponse$ =
    this.store.pipe(
      select(
        LegalHoldReportSelectors.getStateFromLegalHoldReportStore(
          'ActiveTerminatedLegalHoldListErrorResponse'
        )
      )
    )

  // Active Terminated Status selectors
  public readonly selectActiveTerminatedStatusLoading$ = this.store.pipe(
    select(
      LegalHoldReportSelectors.getStateFromLegalHoldReportStore(
        'isActiveTerminatedStatusLoading'
      )
    )
  )

  public readonly selectActiveTerminatedStatusSuccessResponse$ =
    this.store.pipe(
      select(
        LegalHoldReportSelectors.getStateFromLegalHoldReportStore(
          'ActiveTerminatedStatusSuccessResponse'
        )
      )
    )

  public readonly selectActiveTerminatedStatusErrorResponse$ = this.store.pipe(
    select(
      LegalHoldReportSelectors.getStateFromLegalHoldReportStore(
        'ActiveTerminatedStatusErrorResponse'
      )
    )
  )

  constructor(private readonly store: Store) {}

  public fetchLegalHoldList(): void {
    this.store.dispatch(LegalHoldReportActions.fetchLegalHoldList())
  }

  public fetchCustodianList(holdIds: number[]): void {
    this.store.dispatch(LegalHoldReportActions.fetchCustodianList({ holdIds }))
  }

  public fetchStatus(): void {
    this.store.dispatch(LegalHoldReportActions.fetchStatus())
  }

  public fetchActiveTerminatedLegalHoldList(status = null): void {
    this.store.dispatch(
      LegalHoldReportActions.fetchActiveTerminatedLegalHoldList({ status })
    )
  }

  public fetchActiveTerminatedStatus(): void {
    this.store.dispatch(LegalHoldReportActions.fetchActiveTerminatedStatus())
  }

  /**
   * Resetting a specific property of the Legal Hold Report State
   * @param {LegalHoldReportStateKeys} stateKey - state keys
   * @returns {void}
   */
  public resetLegalHoldReportState(stateKey: LegalHoldReportStateKeys): void {
    this.store.dispatch(
      LegalHoldReportActions.resetLegalHoldReportState({ stateKey })
    )
  }
}
