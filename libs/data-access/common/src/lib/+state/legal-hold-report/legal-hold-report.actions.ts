import { createAction, props } from '@ngrx/store'
import { ResponseModel } from '@venio/shared/models/interfaces'

export enum LegalHoldReportActionTypes {
  // Actions for fetching legal hold list
  FetchLegalHoldList = '[LegalHoldReport] Fetch Legal Hold List',
  FetchLegalHoldListSuccess = '[LegalHoldReport] Fetch Legal Hold List: Success',
  FetchLegalHoldListFailure = '[LegalHoldReport] Fetch Legal Hold List: Failure',

  // Actions for fetching custodian list
  FetchCustodianList = '[LegalHoldReport] Fetch Custodian List',
  FetchCustodianListSuccess = '[LegalHoldReport] Fetch Custodian List: Success',
  FetchCustodianListFailure = '[LegalHoldReport] Fetch Custodian List: Failure',

  // Actions for fetching status
  FetchStatus = '[LegalHoldReport] Fetch Status',
  FetchStatusSuccess = '[LegalHoldReport] Fetch Status: Success',
  FetchStatusFailure = '[LegalHoldReport] Fetch Status: Failure',

  FetchActiveTerminatedLegalHoldList = '[ActiveTerminatedLegalHoldReport] Fetch Legal Hold List',
  FetchActiveTerminatedLegalHoldListSuccess = '[ActiveTerminatedLegalHoldReport] Fetch Legal Hold List: Success',
  FetchActiveTerminatedLegalHoldListFailure = '[ActiveTerminatedLegalHoldReport] Fetch Legal Hold List: Failure',

  FetchActiveTerminatedStatus = '[ActiveTerminatedLegalHoldReport] Fetch Active Terminated Status',
  FetchActiveTerminatedStatusSuccess = '[ActiveTerminatedLegalHoldReport] Fetch Active Terminated Status: Success',
  FetchActiveTerminatedStatusFailure = '[ActiveTerminatedLegalHoldReport] Fetch Active TerminatedStatus: Failure',

  // Resetting Legal Hold Report State
  ResetLegalHoldReportState = '[LegalHoldReport] Reset State',
}

export const resetLegalHoldReportState = createAction(
  LegalHoldReportActionTypes.ResetLegalHoldReportState,
  props<{
    stateKey: keyof any | Array<keyof any>
  }>()
)

export const fetchLegalHoldList = createAction(
  LegalHoldReportActionTypes.FetchLegalHoldList
)

export const fetchLegalHoldListSuccess = createAction(
  LegalHoldReportActionTypes.FetchLegalHoldListSuccess,
  props<{ legalHoldListSuccessResponse: ResponseModel }>()
)

export const fetchLegalHoldListFailure = createAction(
  LegalHoldReportActionTypes.FetchLegalHoldListFailure,
  props<{ legalHoldListErrorResponse: ResponseModel }>()
)

export const fetchCustodianList = createAction(
  LegalHoldReportActionTypes.FetchCustodianList,
  props<{ holdIds: number[] }>()
)

export const fetchCustodianListSuccess = createAction(
  LegalHoldReportActionTypes.FetchCustodianListSuccess,
  props<{ custodianListSuccessResponse: ResponseModel }>()
)

export const fetchCustodianListFailure = createAction(
  LegalHoldReportActionTypes.FetchCustodianListFailure,
  props<{ custodianListErrorResponse: ResponseModel }>()
)

// Actions for fetching status
export const fetchStatus = createAction(LegalHoldReportActionTypes.FetchStatus)

export const fetchStatusSuccess = createAction(
  LegalHoldReportActionTypes.FetchStatusSuccess,
  props<{ statusSuccessResponse: ResponseModel }>()
)

export const fetchStatusFailure = createAction(
  LegalHoldReportActionTypes.FetchStatusFailure,
  props<{ statusErrorResponse: ResponseModel }>()
)

export const fetchActiveTerminatedLegalHoldList = createAction(
  LegalHoldReportActionTypes.FetchActiveTerminatedLegalHoldList,
  props<{ status: boolean | null }>()
)

export const fetchActiveTerminatedLegalHoldListSuccess = createAction(
  LegalHoldReportActionTypes.FetchActiveTerminatedLegalHoldListSuccess,
  props<{ ActiveTerminatedLegalHoldListSuccessResponse: ResponseModel }>()
)

export const fetchActiveTerminatedLegalHoldListFailure = createAction(
  LegalHoldReportActionTypes.FetchActiveTerminatedLegalHoldListFailure,
  props<{ ActiveTerminatedLegalHoldListErrorResponse: ResponseModel }>()
)

export const fetchActiveTerminatedStatus = createAction(
  LegalHoldReportActionTypes.FetchActiveTerminatedStatus
)

export const fetchActiveTerminatedStatusSuccess = createAction(
  LegalHoldReportActionTypes.FetchActiveTerminatedStatusSuccess,
  props<{ ActiveTerminatedStatusSuccessResponse: ResponseModel }>()
)

export const fetchActiveTerminatedStatusFailure = createAction(
  LegalHoldReportActionTypes.FetchActiveTerminatedStatusFailure,
  props<{ ActiveTerminatedStatusErrorResponse: ResponseModel }>()
)
