import { Action, createReducer, on } from '@ngrx/store'
import {
  ResponseModel,
  TagGroupModel,
  TagsModel,
} from '@venio/shared/models/interfaces'
import * as TagsActions from './tags.actions'
import { resetStateProperty } from '@venio/util/utilities'
import { CommonActionTypes } from '@venio/shared/models/constants'

export const DOCUMENT_TAGS_FEATURE_KEY = 'documentTags'

export interface TagsState {
  // Loading State Indicators: Flags to indicate if a certain operation is currently in progress
  isTagTreeLoading: boolean | undefined
  isTagDeleting: boolean | undefined
  isAddOrUpdateGatLoading: boolean | undefined

  // Tag Tree Operation Responses: Success and error responses for tag tree related operations
  tagTreeSuccessResponse: ResponseModel | undefined
  tagTreeErrorResponse: ResponseModel | undefined

  // Tag Group Operation Responses: Success and error responses for tag group related operations
  tagGroupSuccessResponse: ResponseModel | undefined
  tagGroupErrorResponse: ResponseModel | undefined
  tagGroupActionSuccessResponse: ResponseModel | undefined
  tagGroupActionErrorResponse: ResponseModel | undefined

  // Tag Deletion Operation Responses: Success and error responses for tag deletion operations
  tagDeleteSuccessResponse: ResponseModel | undefined
  tagDeleteErrorResponse: ResponseModel | undefined

  // Tag tree with count operation responses: Success and error responses for tag tree with count operations
  tagWithCountSuccessResponse: ResponseModel | undefined
  tagWithCountErrorResponse: ResponseModel | undefined

  // Tag Addition/Update Operation Responses: Success and error responses for adding or updating tags
  tagAddOrUpdateSuccessResponse: ResponseModel | undefined
  tagAddOrUpdateErrorResponse: ResponseModel | undefined

  // Selected Tag and Tag Group: Details about the currently selected tag or tag group and the action being performed
  selectedTagGroup:
    | { selected: TagGroupModel; actionType: CommonActionTypes }
    | undefined
  selectedTag:
    | { projectId: number; selected: TagsModel; actionType: CommonActionTypes }
    | undefined

  // Tag Propagation Settings: Details about the tag propagation settings
  tagPropagationSettingErrorResponse: ResponseModel | undefined
  tagPropagationSettingSuccessResponse: ResponseModel | undefined

  // Tag Fields: Notifies the store that Tag Fields are being add or update
  isAddOrUpdateTagFieldNotified: boolean | undefined
  tagFormGroupValid: boolean | undefined
  switchToTagView: boolean | undefined

  // Tag Reorder Operation Responses: Success and error responses for reorder tags
  tagReorderSuccessResponse: ResponseModel | undefined
  tagReorderErrorResponse: ResponseModel | undefined
}

export const tagInitialState: TagsState = {
  tagTreeErrorResponse: undefined,
  tagTreeSuccessResponse: undefined,
  tagGroupErrorResponse: undefined,
  tagGroupSuccessResponse: undefined,
  isTagTreeLoading: undefined,
  isTagDeleting: undefined,
  tagDeleteErrorResponse: undefined,
  tagDeleteSuccessResponse: undefined,
  tagGroupActionErrorResponse: undefined,
  tagGroupActionSuccessResponse: undefined,
  selectedTagGroup: undefined,
  selectedTag: undefined,
  isAddOrUpdateGatLoading: undefined,
  tagAddOrUpdateErrorResponse: undefined,
  tagAddOrUpdateSuccessResponse: undefined,
  tagPropagationSettingErrorResponse: undefined,
  tagPropagationSettingSuccessResponse: undefined,
  isAddOrUpdateTagFieldNotified: undefined,
  tagFormGroupValid: undefined,
  switchToTagView: undefined,
  tagWithCountErrorResponse: undefined,
  tagWithCountSuccessResponse: undefined,
  tagReorderSuccessResponse: undefined,
  tagReorderErrorResponse: undefined,
}

export interface TagsPartialState {
  readonly [DOCUMENT_TAGS_FEATURE_KEY]: TagsState
}

const reducer = createReducer<TagsState>(
  tagInitialState,
  on(TagsActions.resetTagsState, (state, { stateKey }) =>
    resetStateProperty<TagsState>(state, tagInitialState, stateKey)
  ),
  on(TagsActions.fetchTagTree, (state) => ({
    ...state,
    isTagTreeLoading: true,
  })),
  on(TagsActions.fetchTagTreeSuccess, (state, { tagTreeSuccessResponse }) => ({
    ...state,
    tagTreeErrorResponse: undefined,
    tagTreeSuccessResponse,
    isTagTreeLoading: false,
  })),
  on(TagsActions.fetchTagTreeFailure, (state, { tagTreeErrorResponse }) => ({
    ...state,
    tagTreeErrorResponse,
    tagTreeSuccessResponse: undefined,
  })),
  on(
    TagsActions.fetchTagGroupSuccess,
    (state, { tagGroupSuccessResponse }) => ({
      ...state,
      tagGroupSuccessResponse,
    })
  ),
  on(TagsActions.fetchTagGroupFailure, (state, { tagGroupErrorResponse }) => ({
    ...state,
    tagGroupErrorResponse,
  })),
  on(TagsActions.deleteTag, (state) => ({
    ...state,
    isTagDeleting: true,
  })),
  on(TagsActions.deleteTagSuccess, (state, { tagDeleteSuccessResponse }) => ({
    ...state,
    isTagDeleting: false,
    tagDeleteSuccessResponse,
  })),
  on(TagsActions.deleteTagFailure, (state, { tagDeleteErrorResponse }) => ({
    ...state,
    isTagDeleting: false,
    tagDeleteErrorResponse,
  })),
  on(
    TagsActions.setSelectedTagGroup,
    (state, { payload: { data, actionType } }) => ({
      ...state,
      selectedTagGroup: {
        actionType,
        selected: data,
      },
    })
  ),
  on(
    TagsActions.tagGroupActionSuccess,
    (state, { tagGroupActionSuccessResponse }) => ({
      ...state,
      tagGroupActionSuccessResponse,
    })
  ),
  on(
    TagsActions.tagGroupActionFailure,
    (state, { tagGroupActionErrorResponse }) => ({
      ...state,
      tagGroupActionErrorResponse,
    })
  ),
  on(TagsActions.setSelectedTag, (state, { selectedTag }) => ({
    ...state,
    selectedTag,
  })),
  on(TagsActions.tagAddOrUpdate, (state) => ({
    ...state,
    isAddOrUpdateGatLoading: true,
  })),
  on(
    TagsActions.tagAddOrUpdateSuccess,
    (state, { tagAddOrUpdateSuccessResponse }) => ({
      ...state,
      isAddOrUpdateGatLoading: false,
      tagAddOrUpdateSuccessResponse,
    })
  ),
  on(
    TagsActions.tagAddOrUpdateFailure,
    (state, { tagAddOrUpdateErrorResponse }) => ({
      ...state,
      isAddOrUpdateGatLoading: false,
      tagAddOrUpdateErrorResponse,
    })
  ),
  on(
    TagsActions.fetchTagPropagationSettingSuccess,
    (state, { tagPropagationSettingSuccessResponse }) => ({
      ...state,
      tagPropagationSettingSuccessResponse,
    })
  ),
  on(
    TagsActions.fetchTagPropagationSettingFailure,
    (state, { tagPropagationSettingErrorResponse }) => ({
      ...state,
      tagPropagationSettingErrorResponse,
    })
  ),
  on(
    TagsActions.fetchTagWithCountSuccess,
    (state, { tagWithCountSuccessResponse }) => ({
      ...state,
      tagWithCountSuccessResponse,
    })
  ),
  on(
    TagsActions.fetchTagWithCountFailure,
    (state, { tagWithCountErrorResponse }) => ({
      ...state,
      tagWithCountErrorResponse,
    })
  ),
  on(
    TagsActions.notifyAddOrUpdateTagField,
    (state, { isAddOrUpdateTagFieldNotified }) => ({
      ...state,
      isAddOrUpdateTagFieldNotified,
    })
  ),
  on(TagsActions.notifyTagFormGroupValid, (state, { tagFormGroupValid }) => ({
    ...state,
    tagFormGroupValid,
  })),
  on(TagsActions.notifyToSwitchTagView, (state, { switchToTagView }) => ({
    ...state,
    switchToTagView,
  })),
  on(TagsActions.saveTagReorder, (state) => ({
    ...state,
    isTagReordering: true,
  })),
  on(
    TagsActions.fetchTagReorderSuccess,
    (state, { tagReorderSuccessResponse }) => ({
      ...state,
      isTagReordering: false,
      tagReorderSuccessResponse,
    })
  ),
  on(
    TagsActions.fetchTagReorderFailure,
    (state, { tagReorderErrorResponse }) => ({
      ...state,
      isTagReordering: false,
      tagReorderErrorResponse,
    })
  )
)

export function tagsReducer(
  state: TagsState | undefined,
  action: Action
): TagsState {
  return reducer(state, action)
}
