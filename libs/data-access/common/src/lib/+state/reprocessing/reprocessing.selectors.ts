import {
  createFeatureSelector,
  createSelector,
  MemoizedSelector,
} from '@ngrx/store'
import {
  REPROCESSING_FEATURE_KEY,
  ReprocessingState,
} from './reprocessing.reducer'

export const getReprocessingState = createFeatureSelector<ReprocessingState>(
  REPROCESSING_FEATURE_KEY
)

export const getStateFromReprocessingStore = <
  T extends keyof ReprocessingState
>(
  stateKey: T
): MemoizedSelector<object, ReprocessingState[T], unknown> =>
  createSelector(
    getReprocessingState,
    (state: ReprocessingState) => state[stateKey]
  )
