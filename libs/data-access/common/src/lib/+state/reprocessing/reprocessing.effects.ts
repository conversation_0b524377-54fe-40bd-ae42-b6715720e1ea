import { inject, Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { ReprocessingService } from '../../services/reprocessing/reprocessing.service'
import * as ReprocessingAction from './reprocessing.actions'
import { fetch } from '@ngrx/router-store/data-persistence'
import { catchError, from, map, switchMap } from 'rxjs'
import { HttpErrorResponse } from '@angular/common/http'
import {
  Custodian,
  ReprocessFileInfoDetail,
  ReprocessStatusModel,
  ReprocessTag,
  ResponseModel,
} from '@venio/shared/models/interfaces'
import { CaseConvertorService } from '@venio/util/utilities'

@Injectable()
export class ReprocessingEffects {
  private readonly actions$ = inject(Actions)

  private readonly reprocessingService = inject(ReprocessingService)

  public fetchProjectTags$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReprocessingAction.getReprocessingTagsAction),
      fetch({
        run: ({ projectId, mediaList }) => {
          return this.reprocessingService.getTags(projectId, mediaList).pipe(
            map((res: ResponseModel) =>
              ReprocessingAction.getReprocessingTagsSuccessAction({
                tags: res.data as ReprocessTag[],
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const tagsErrorResponse: ResponseModel = error.error
          return ReprocessingAction.getReprocessingTagsFailureAction({
            error: tagsErrorResponse,
          })
        },
      })
    )
  )

  public fetchEdocFields$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReprocessingAction.getReprocessingEdocFieldsAction),
      fetch({
        run: ({ projectId }) => {
          return this.reprocessingService.getEdocFields(projectId).pipe(
            map((edocFields: ResponseModel) =>
              ReprocessingAction.getReprocessingEdocFieldsSuccessAction({
                edocFields: edocFields.data as string[],
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const edocFieldsErrorResponse: ResponseModel = error.error
          return ReprocessingAction.getReprocessingEdocFieldsFailureAction({
            error: edocFieldsErrorResponse,
          })
        },
      })
    )
  )

  public fetchEmailFields$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReprocessingAction.getReprocessingEmailFieldsAction),
      fetch({
        run: ({ projectId }) => {
          return this.reprocessingService.getEmailFields(projectId).pipe(
            map((emailFields: ResponseModel) =>
              ReprocessingAction.getReprocessingEmailFieldsSuccessAction({
                emailFields: emailFields.data as string[],
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const emailFieldsErrorResponse: ResponseModel = error.error
          return ReprocessingAction.getReprocessingEmailFieldsFailureAction({
            error: emailFieldsErrorResponse,
          })
        },
      })
    )
  )

  public fetchDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReprocessingAction.getReprocessingDocumentAction),
      fetch({
        run: ({ projectId, reprocessRequestModel }) => {
          return this.reprocessingService
            .getDocumentList(projectId, reprocessRequestModel)
            .pipe(
              map((reprocessFileInfoDetail: ResponseModel) =>
                ReprocessingAction.getReprocessingDocumentSuccessAction({
                  reprocessFileInfoDetail:
                    reprocessFileInfoDetail.data as ReprocessFileInfoDetail,
                })
              ),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const documentErrorResponse: ResponseModel = error.error
          return ReprocessingAction.getReprocessingDocumentFailureAction({
            error: documentErrorResponse,
          })
        },
      })
    )
  )

  public queueFileForReprocess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReprocessingAction.queueFileForReprocessAction),
      fetch({
        run: ({ projectId, fileReprocessModel }) => {
          return this.reprocessingService
            .queueFileForReprocess(projectId, fileReprocessModel)
            .pipe(
              map((reprocessResponse: ResponseModel) =>
                ReprocessingAction.queueFileForReprocessSuccessAction({
                  reprocessResponse,
                })
              ),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const queueFileErrorResponse: ResponseModel = error.error
          return ReprocessingAction.queueFileForReprocessFailureAction({
            error: queueFileErrorResponse,
          })
        },
      })
    )
  )

  public dropTempTable$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReprocessingAction.dropTempTableAction),
      fetch({
        run: ({ projectId, sessionId }) => {
          return this.reprocessingService
            .dropTempTable(projectId, sessionId)
            .pipe(
              map(() => ReprocessingAction.dropTableSuccessAction()),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
      })
    )
  )

  public fetchCustodianInfo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReprocessingAction.getCustodianAction),
      fetch({
        run: ({ projectId }) => {
          return this.reprocessingService.getCustodianInfo(projectId).pipe(
            switchMap((res: ResponseModel) => {
              const camelCaseConvertorService = new CaseConvertorService()
              const convertedData = camelCaseConvertorService.convertToCase<
                Custodian[]
              >(res, 'camelCase')
              return from(convertedData)
            }),
            map((custodians) =>
              ReprocessingAction.getCustodianSuccessAction({
                custodians: custodians,
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
      })
    )
  )

  public fetchIsFallbackIngestionEngine$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReprocessingAction.GetIsFallbackIngestionEngineFlagAction),
      fetch({
        run: ({ projectId }) => {
          return this.reprocessingService
            .fetchIsFallbackIngestionEngine(projectId)
            .pipe(
              map((res: ResponseModel) =>
                ReprocessingAction.GetIsFallbackIngestionEngineFlagSuccessAction(
                  {
                    isFallbackIngestionEngine: res?.data,
                  }
                )
              ),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
      })
    )
  )

  public fetchReprocessingStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReprocessingAction.getReprocessingStatusAction),
      fetch({
        run: ({ projectId }) => {
          return this.reprocessingService.getReprocessStatus(projectId).pipe(
            map((res: ResponseModel) =>
              ReprocessingAction.getReprocessingStatusSuccessAction({
                reprocessStatus: {
                  status: this.getReprocessStatusModel(res?.data),
                  hasAlreadyCompletedJobs: res?.data?.hasAlreadyCompletedJobs,
                },
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
      })
    )
  )

  public getReprocessStatusModel(data: any): ReprocessStatusModel[] {
    const reprocessStatusList: ReprocessStatusModel[] = []

    const custodianList: any[] = data?.processingStatusModel?.custodianList

    custodianList.forEach((custodian) => {
      custodian.mediaList.forEach((media: any) => {
        reprocessStatusList.push({
          custodianName: custodian.name,
          createdDate: custodian.created_date,
          queuedOn: media.queuedOn,
          mediaName: media.mediaName,
          currentlyInProgressJob: media.currentlyInProgressJob,
          JobDetails: media.jobList,
        })
      })
    })

    return reprocessStatusList
  }
}
