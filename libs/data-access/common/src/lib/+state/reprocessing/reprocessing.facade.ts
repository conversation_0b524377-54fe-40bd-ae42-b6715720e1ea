import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import * as ReprocessingActions from './reprocessing.actions'
import {
  FileReprocessModel,
  ReprocessRequestModel,
} from '@venio/shared/models/interfaces'
import * as ReprocessingSelectors from './reprocessing.selectors'
import { ReprocessingService } from '../../services/reprocessing/reprocessing.service'
import { Observable } from 'rxjs'

@Injectable({ providedIn: 'root' })
export class ReprocessingFacade {
  public readonly getTags$ = this.store.pipe(
    select(ReprocessingSelectors.getStateFromReprocessingStore('tags'))
  )

  public readonly getEdocFields$ = this.store.pipe(
    select(ReprocessingSelectors.getStateFromReprocessingStore('edocFields'))
  )

  public readonly getEmailFields$ = this.store.pipe(
    select(ReprocessingSelectors.getStateFromReprocessingStore('emailFields'))
  )

  public readonly getReprocessFileInfoDetail$ = this.store.pipe(
    select(
      ReprocessingSelectors.getStateFromReprocessingStore(
        'reprocessFileInfoDetail'
      )
    )
  )

  public readonly getReprocessResponse$ = this.store.pipe(
    select(
      ReprocessingSelectors.getStateFromReprocessingStore('reprocessResponse')
    )
  )

  public readonly getCustodians$ = this.store.pipe(
    select(ReprocessingSelectors.getStateFromReprocessingStore('custodians'))
  )

  public readonly getIsFallbackIngestionEngineFlag$ = this.store.pipe(
    select(
      ReprocessingSelectors.getStateFromReprocessingStore(
        'isFallbackIngestionEngine'
      )
    )
  )

  public readonly getReprocessStatus$ = this.store.pipe(
    select(
      ReprocessingSelectors.getStateFromReprocessingStore('reprocessStatus')
    )
  )

  constructor(
    private readonly store: Store,
    private readonly service: ReprocessingService
  ) {}

  public fetchIsFallbackIngestionEngine(projectId: number): Observable<any> {
    return this.service.fetchIsFallbackIngestionEngine(projectId)
  }

  public fetchTagsForReprocessing(projectId: number, mediaList: string): void {
    this.store.dispatch(
      ReprocessingActions.getReprocessingTagsAction({ projectId, mediaList })
    )
  }

  public fetchEdocFieldsForReprocessing(projectId: number): void {
    this.store.dispatch(
      ReprocessingActions.getReprocessingEdocFieldsAction({ projectId })
    )
  }

  public fetchEmailFieldsForReprocessing(projectId: number): void {
    this.store.dispatch(
      ReprocessingActions.getReprocessingEmailFieldsAction({ projectId })
    )
  }

  public fetchDocumentForReprocessing(
    projectId: number,
    reprocessRequestModel: ReprocessRequestModel
  ): void {
    this.store.dispatch(
      ReprocessingActions.getReprocessingDocumentAction({
        projectId,
        reprocessRequestModel,
      })
    )
  }

  public clearDocumentForReprocessing(): void {
    this.store.dispatch(ReprocessingActions.clearFileForReprocessAction())
  }

  public queueFileForReprocess(
    projectId: number,
    fileReprocessModel: FileReprocessModel
  ): void {
    this.store.dispatch(
      ReprocessingActions.queueFileForReprocessAction({
        projectId,
        fileReprocessModel,
      })
    )
  }

  public clearReprocessResponse(): void {
    this.store.dispatch(ReprocessingActions.clearReprocessResponseAction())
  }

  public clearCustodianInfo(): void {
    this.store.dispatch(ReprocessingActions.ResetCustodianAction())
  }

  public clearReprocessingStatus(): void {
    this.store.dispatch(ReprocessingActions.clearReprocessingStatusAction())
  }

  public dropTempTable(projectId: number, sessionId: number): void {
    this.store.dispatch(
      ReprocessingActions.dropTempTableAction({ projectId, sessionId })
    )
  }

  public fetchCustodianInfo(projectId: number): void {
    this.store.dispatch(ReprocessingActions.getCustodianAction({ projectId }))
  }

  public resetAll(): void {
    this.store.dispatch(ReprocessingActions.resetAllAction())
  }

  public getSupportedExtractors(
    projectId: number,
    fileType: string
  ): Observable<unknown> {
    return this.service.getSupportedExtractors(projectId, fileType)
  }

  public getMediaInfo(projectId: number, mediaId: number): Observable<unknown> {
    return this.service.getMedia(projectId, mediaId)
  }

  public saveLoadFile(
    projectId: number,
    fileReprocessModel: FileReprocessModel
  ): Observable<unknown> {
    return this.service.saveLoadFile(projectId, fileReprocessModel)
  }

  public fetchReprocessingStatus(projectId: number): void {
    this.store.dispatch(
      ReprocessingActions.getReprocessingStatusAction({ projectId })
    )
  }
}
