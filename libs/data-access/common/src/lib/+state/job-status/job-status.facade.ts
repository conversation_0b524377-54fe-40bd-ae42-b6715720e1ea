import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import { Observable } from 'rxjs'
import * as JobStatusActions from './job-status.actions'
import * as JobStatusSelectors from './job-status.selectors'
import { Column } from './job-status.reducer'
import { ResponseModel } from '@venio/shared/models/interfaces'

@Injectable({ providedIn: 'root' })
export class JobStatusFacade {
  public readonly selectColumnNames$: Observable<Column[]> = this.store.pipe(
    select(JobStatusSelectors.selectColumnNames)
  )

  public readonly jobStatusResponseAndError$: Observable<{
    jobStatusResponse: ResponseModel
    jobStatusError: ResponseModel
  }> = this.store.pipe(select(JobStatusSelectors.selectJobStatusResponse))

  constructor(private readonly store: Store) {}

  public setColumnNames(columnNames: Column[]): void {
    this.store.dispatch(
      JobStatusActions.setColumnNames({ payload: { columnNames } })
    )
  }

  public fetchJobStatusData(): void {
    this.store.dispatch(JobStatusActions.fetchJobStatusData())
  }
}
