import { createAction, props } from '@ngrx/store'
import { ResponseModel } from '@venio/shared/models/interfaces'

export interface DetailGridColumn {
  field: string
  title: string
  width?: number
}
export enum JobStatusActionTypes {
  SetColumnNames = '[Job Status] Set Column Names',
  FetchJobStatusData = '[Job Status] Fetch Data',
  FetchJobStatusDataSuccess = '[Job Status] Fetch Data Success',
  FetchJobStatusDataFailure = '[Job Status] Fetch Data Failure',
}

export const setColumnNames = createAction(
  JobStatusActionTypes.SetColumnNames,
  props<{
    payload: {
      columnNames: DetailGridColumn[]
    }
  }>()
)

export const fetchJobStatusData = createAction(
  JobStatusActionTypes.FetchJobStatusData
)

export const fetchDataSuccess = createAction(
  JobStatusActionTypes.FetchJobStatusDataSuccess,
  props<{ payload: { jobStatusResponse: ResponseModel } }>()
)

export const fetchDataFailure = createAction(
  JobStatusActionTypes.FetchJobStatusDataFailure,
  props<{ payload: { jobStatusError: ResponseModel } }>()
)
