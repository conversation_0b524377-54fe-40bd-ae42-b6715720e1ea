import { inject, Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { fetch } from '@ngrx/router-store/data-persistence'
import { map, catchError, switchMap, from, of } from 'rxjs'
import { HttpErrorResponse } from '@angular/common/http'
import * as DirectExportActions from './direct-export.actions'
import { DirectExportService } from '../../services/direct-export.service'
import { CaseConvertorService } from '@venio/util/utilities'
import { ResponseModel } from '@venio/shared/models/interfaces'

@Injectable()
export class DirectExportEffects {
  private readonly actions$ = inject(Actions)

  private readonly directExportService = inject(DirectExportService)

  public fetchTimeZones$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DirectExportActions.fetchTimeZones),
      fetch({
        run: () => {
          return this.directExportService.fetchTimeZones().pipe(
            switchMap((response) => {
              const caseConvertWorker = new CaseConvertorService()
              return from(
                caseConvertWorker.convertToCase<any[]>(response, 'camelCase')
              )
            }),
            map((timeZones) =>
              DirectExportActions.fetchTimeZonesSuccess({ timeZones })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const timeZonesError = error.error
          return DirectExportActions.fetchTimeZonesFailure({
            error: timeZonesError,
          })
        },
      })
    )
  )

  public fetchServiceTypeList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DirectExportActions.fetchServiceTypeList),
      fetch({
        run: () => {
          return this.directExportService.fetchServiceTypeList().pipe(
            map((serviceTypeList) =>
              DirectExportActions.fetchServiceTypeListSuccess({
                serviceTypeList,
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          return DirectExportActions.fetchServiceTypeListFailure({ error })
        },
      })
    )
  )

  public fetchServiceTypeDefaultData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DirectExportActions.fetchServiceTypeDefaultData),
      fetch({
        run: ({ serviceType }) => {
          return this.directExportService
            .fetchServiceTypeDefaultData(serviceType)
            .pipe(
              map((defaultData) =>
                DirectExportActions.fetchServiceTypeDefaultDataSuccess({
                  defaultData,
                })
              ),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          return DirectExportActions.fetchServiceTypeDefaultDataFailure({
            error,
          })
        },
      })
    )
  )

  public fetchExportTemplates$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DirectExportActions.fetchExportTemplates),
      fetch({
        run: ({ templateType }) => {
          return this.directExportService
            .fetchExportTemplates(templateType)
            .pipe(
              map((response: any[]) => {
                return DirectExportActions.fetchExportTemplatesSuccess({
                  exportFieldTemplates: response,
                })
              }),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const exportTemplatesError = error.error
          return DirectExportActions.fetchExportTemplatesFailure({
            error: exportTemplatesError,
          })
        },
      })
    )
  )

  public fetchExistingCaseData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DirectExportActions.fetchExistingCaseData),
      fetch({
        run: ({ caseId }) => {
          return this.directExportService.fetchExistingCaseData(caseId).pipe(
            map((caseData) => {
              return DirectExportActions.fetchExistingCaseDataSuccess({
                caseData,
              })
            }),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
      })
    )
  )

  public createServiceTypeCase$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DirectExportActions.createServiceTypeCase),
      fetch({
        run: ({ settingsInfo }) => {
          return this.directExportService
            .createServiceTypeCase(settingsInfo)
            .pipe(
              map((caseData) => {
                return DirectExportActions.createServiceTypeCaseSuccess({
                  caseData,
                })
              }),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, caseDataError: HttpErrorResponse) => {
          return DirectExportActions.createServiceTypeCaseError({
            caseDataError,
          })
        },
      })
    )
  )

  public fetchConnectorEnvironments$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DirectExportActions.fetchConnectorEnvironments),
      fetch({
        run: ({ clientId }) => {
          return this.directExportService
            .fetchRelativityConnectorEnvironments(clientId)
            .pipe(
              map((response: ResponseModel) => {
                const relativityEnvironments = response?.data
                return DirectExportActions.fetchConnectorEnvironmentsSuccess({
                  relativityEnvironments,
                })
              }),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, error: ResponseModel) => {
          return DirectExportActions.fetchConnectorEnvironmentsFailure({
            relativityEnvironmentsError: error,
          })
        },
      })
    )
  )

  public fetchRelativityTemplates$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DirectExportActions.fetchRelativityTemplates),
      fetch({
        run: ({ clientId }) => {
          return this.directExportService.templateList(clientId).pipe(
            map((response: ResponseModel) =>
              DirectExportActions.fetchRelativityTemplatesSuccess({
                relativityFieldTemplates: response?.data,
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, relativityFieldTemplatesFailure: ResponseModel) => {
          return DirectExportActions.fetchRelativityTemplatesFailure({
            relativityFieldTemplatesFailure,
          })
        },
      })
    )
  )

  public fetchRelativityWorkspaces$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DirectExportActions.fetchRelativityWorkspaces),
      fetch({
        run: ({ environmentId, userEnvironmentId }) => {
          return this.directExportService
            .fetchRelativityWorkspaces(environmentId, userEnvironmentId)
            .pipe(
              map((response: ResponseModel) =>
                DirectExportActions.fetchRelativityWorkspacesSuccess({
                  relativityWorkspaces: response?.data,
                })
              ),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, relativityWorkspaceError: ResponseModel) => {
          return DirectExportActions.fetchRelativityWorkspacesFailure({
            relativityWorkspaceError,
          })
        },
      })
    )
  )

  public fetchRelativityWorkspaceFileshares$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DirectExportActions.fetchRelativityWorkspaceFileshares),
      fetch({
        run: ({ environmentId, workspaceId, userEnvironmentId }) => {
          return this.directExportService
            .fetchRelativityWorkspaceFileshares(
              environmentId,
              workspaceId,
              userEnvironmentId
            )
            .pipe(
              map((response: ResponseModel) =>
                DirectExportActions.fetchRelativityWorkspaceFilesharesSuccess({
                  relativityFileshares: response?.data,
                })
              ),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, relativityFilesharesFailure: ResponseModel) => {
          return DirectExportActions.fetchRelativityWorkspaceFilesharesFailure({
            relativityFilesharesFailure,
          })
        },
      })
    )
  )

  public createUserEnvironment$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DirectExportActions.createUserEnvironment),
      fetch({
        run: ({ userEnvironmentModel }) => {
          return this.directExportService
            .createUserEnvironment(userEnvironmentModel)
            .pipe(
              map((response: ResponseModel) =>
                DirectExportActions.createUserEnvironmentSuccess({
                  createUserEnvironmentSuccess: response,
                })
              ),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, createUserEnvironmentError: ResponseModel) => {
          return DirectExportActions.createUserEnvironmentFailure({
            createUserEnvironmentError,
          })
        },
      })
    )
  )

  public updateUserEnvironment$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DirectExportActions.updateUserEnvironment),
      fetch({
        run: ({ userEnvironmentId, userEnvironmentModel }) => {
          return this.directExportService
            .updateUserEnvironment(userEnvironmentId, userEnvironmentModel)
            .pipe(
              map((response: ResponseModel) =>
                DirectExportActions.updateUserEnvironmentSuccess({
                  updateUserEnvironmentSuccess: response,
                })
              ),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, updateUserEnvironmentError: ResponseModel) => {
          return DirectExportActions.updateUserEnvironmentFailure({
            updateUserEnvironmentError,
          })
        },
      })
    )
  )

  public checkIfControlNumberHasConflict$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DirectExportActions.checkIfControlNumberHasConflict),
      fetch({
        run: ({ projectId, settings }) => {
          return this.directExportService
            .checkIfControlNumberHasConflict(projectId, settings)
            .pipe(
              map((response: ResponseModel) =>
                DirectExportActions.setControlNumberConflictResponse({
                  response,
                })
              )
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const response = error.error as ResponseModel
          return of(
            DirectExportActions.setControlNumberConflictResponse({
              response,
            })
          )
        },
      })
    )
  )

  public clearUserEnvironmentResponse$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DirectExportActions.clearUserEnvironmentResponse),
      map(() => DirectExportActions.clearUserEnvironmentResponse())
    )
  )
}
