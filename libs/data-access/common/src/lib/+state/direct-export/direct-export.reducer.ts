import { Action, createReducer, on } from '@ngrx/store'
import { ResponseModel } from '@venio/shared/models/interfaces'
import * as DirectExportActions from './direct-export.actions'
import { resetStateProperty } from '@venio/util/utilities'

export const DIRECT_EXPORT_FEATURE_KEY = 'directExportStore'

export interface DirectExportState {
  timeZones: any[] | undefined
  isLoadingTimeZones: boolean | undefined
  timeZonesError: any | undefined
  exportFieldTemplates: any[] | undefined
  isExportTemplatesLoading: boolean | undefined
  exportTemplatesError: any | undefined
  serviceTypeList: ResponseModel | undefined
  isServiceTypeListLoading: boolean | undefined
  serviceTypeListError: any | undefined
  defaultData: any | undefined
  isDefaultDataLoading: boolean | undefined
  defaultDataError: any | undefined
  caseData: ResponseModel | undefined
  isCaseDataLoading: boolean | undefined
  caseDataError: any | undefined
  serviceTypeCaseSuccess: any[] | null
  serviceTypeCaseError: any | null
  isServiceTypeCreationLoading: boolean | undefined
  relativityEnvironments: any[] | []
  isRelativityEnvironmentsLoading: boolean
  relativityEnvironmentsError: ResponseModel | undefined
  relativityWorkspaces: any[] | []
  relativityWorkspacesError: ResponseModel | undefined
  isRelativityWorkspacesLoading: boolean | undefined
  relativityFieldTemplates: any[] | undefined
  relativityFieldTemplatesFailure: ResponseModel
  isRelativityFieldTemplatesLoading: boolean | undefined
  relativityFileshares: any[] | []
  relativityFilesharesFailure: ResponseModel
  isRelativityFilesharesLoading: boolean | undefined

  // New properties for user environment management
  createUserEnvironmentSuccess: ResponseModel | undefined
  createUserEnvironmentError: ResponseModel | undefined
  updateUserEnvironmentError: ResponseModel | undefined
  updateUserEnvironmentSuccess: ResponseModel | undefined

  controlNumberConflictResponse: ResponseModel | undefined
}

export const directExportInitialState: DirectExportState = {
  timeZones: undefined,
  isLoadingTimeZones: undefined,
  timeZonesError: undefined,
  exportFieldTemplates: undefined,
  isExportTemplatesLoading: undefined,
  exportTemplatesError: undefined,
  serviceTypeList: undefined,
  isServiceTypeListLoading: undefined,
  serviceTypeListError: undefined,
  defaultData: undefined,
  isDefaultDataLoading: undefined,
  defaultDataError: undefined,
  caseData: undefined,
  isCaseDataLoading: undefined,
  caseDataError: undefined,
  serviceTypeCaseSuccess: null,
  serviceTypeCaseError: null,
  isServiceTypeCreationLoading: undefined,
  relativityEnvironments: [],
  isRelativityEnvironmentsLoading: false,
  relativityEnvironmentsError: undefined,
  relativityWorkspaces: [],
  relativityWorkspacesError: undefined,
  isRelativityWorkspacesLoading: false,
  relativityFieldTemplates: [],
  relativityFieldTemplatesFailure: undefined,
  isRelativityFieldTemplatesLoading: false,
  relativityFileshares: [],
  relativityFilesharesFailure: undefined,
  isRelativityFilesharesLoading: false,

  // New initial state for user environment management
  createUserEnvironmentSuccess: undefined,
  createUserEnvironmentError: undefined,
  updateUserEnvironmentError: undefined,
  updateUserEnvironmentSuccess: undefined,

  controlNumberConflictResponse: undefined,
}

export interface DirectExportPartialState {
  readonly [DIRECT_EXPORT_FEATURE_KEY]: DirectExportState
}

const reducer = createReducer<DirectExportState>(
  directExportInitialState,

  on(DirectExportActions.resetDirectExportState, (state, { stateKey }) =>
    resetStateProperty<DirectExportState>(
      state,
      directExportInitialState,
      stateKey
    )
  ),
  on(DirectExportActions.fetchTimeZones, (state) => ({
    ...state,
    isLoadingTimeZones: true,
    timeZonesError: undefined,
  })),
  on(DirectExportActions.fetchTimeZonesSuccess, (state, { timeZones }) => ({
    ...state,
    timeZones,
    isLoadingTimeZones: false,
  })),
  on(DirectExportActions.fetchTimeZonesFailure, (state, { error }) => ({
    ...state,
    timeZonesError: error,
    isLoadingTimeZones: false,
  })),
  on(DirectExportActions.fetchExportTemplates, (state) => ({
    ...state,
    isExportTemplatesLoading: true,
    exportTemplatesError: undefined,
  })),
  on(
    DirectExportActions.fetchExportTemplatesSuccess,
    (state, { exportFieldTemplates }) => ({
      ...state,
      exportFieldTemplates,
      isExportTemplatesLoading: false,
    })
  ),
  on(DirectExportActions.fetchExportTemplatesFailure, (state, { error }) => ({
    ...state,
    exportTemplatesError: error,
    isExportTemplatesLoading: false,
  })),
  on(DirectExportActions.fetchServiceTypeList, (state) => ({
    ...state,
    isServiceTypeListLoading: true,
    serviceTypeListError: undefined,
  })),
  on(
    DirectExportActions.fetchServiceTypeListSuccess,
    (state, { serviceTypeList }) => ({
      ...state,
      serviceTypeList,
      isServiceTypeListLoading: false,
    })
  ),
  on(DirectExportActions.fetchServiceTypeListFailure, (state, { error }) => ({
    ...state,
    serviceTypeListError: error,
    isServiceTypeListLoading: false,
  })),

  on(
    DirectExportActions.updateDefaultDataFromForm,
    (state, { serviceData }) => ({
      ...state,
      defaultData: {
        ...state.defaultData,
        ...serviceData,
      },
    })
  ),

  on(DirectExportActions.fetchServiceTypeDefaultData, (state) => ({
    ...state,
    isDefaultDataLoading: true,
    defaultDataError: undefined,
  })),
  on(
    DirectExportActions.fetchServiceTypeDefaultDataSuccess,
    (state, { defaultData }) => ({
      ...state,
      defaultData: defaultData?.data,
      isDefaultDataLoading: false,
    })
  ),
  on(
    DirectExportActions.fetchServiceTypeDefaultDataFailure,
    (state, { error }) => ({
      ...state,
      defaultDataError: error,
      isDefaultDataLoading: false,
    })
  ),

  on(DirectExportActions.fetchExistingCaseData, (state) => ({
    ...state,
    isCaseDataLoading: true,
    caseDataError: undefined,
  })),
  on(
    DirectExportActions.fetchExistingCaseDataSuccess,
    (state, { caseData }) => ({
      ...state,
      caseData,
      isCaseDataLoading: false,
    })
  ),
  on(DirectExportActions.fetchExistingCaseDataFailure, (state, { error }) => ({
    ...state,
    caseDataError: error,
    isCaseDataLoading: false,
  })),

  on(DirectExportActions.createServiceTypeCase, (state) => ({
    ...state,
    isServiceTypeCreationLoading: true,
    serviceTypeCaseError: null,
  })),

  on(
    DirectExportActions.createServiceTypeCaseSuccess,
    (state, { caseData }) => ({
      ...state,
      serviceTypeCaseSuccess: caseData,
      serviceTypeCaseError: null,
      isServiceTypeCreationLoading: false,
    })
  ),

  on(DirectExportActions.fetchConnectorEnvironments, (state) => ({
    ...state,
    isRelativityEnvironmentsLoading: true,
    relativityEnvironments: [],
  })),

  on(
    DirectExportActions.fetchConnectorEnvironmentsSuccess,
    (state, { relativityEnvironments }) => ({
      ...state,
      isRelativityEnvironmentsLoading: false,
      relativityEnvironments: relativityEnvironments,
    })
  ),

  on(
    DirectExportActions.fetchConnectorEnvironmentsFailure,
    (state, { relativityEnvironmentsError }) => ({
      ...state,
      isRelativityEnvironmentsLoading: false,
      relativityEnvironmentsError,
    })
  ),

  on(DirectExportActions.fetchRelativityWorkspaces, (state) => ({
    ...state,
    isRelativityWorkspacesLoading: true,
  })),

  on(
    DirectExportActions.fetchRelativityWorkspacesSuccess,
    (state, { relativityWorkspaces }) => ({
      ...state,
      isRelativityWorkspacesLoading: false,
      relativityWorkspaces,
    })
  ),

  on(
    DirectExportActions.fetchRelativityWorkspacesFailure,
    (state, { relativityWorkspaceError }) => ({
      ...state,
      isRelativityWorkspacesLoading: false,
      relativityWorkspaceError,
    })
  ),

  on(DirectExportActions.fetchRelativityTemplates, (state) => ({
    ...state,
    isRelativityFieldTemplatesLoading: true,
  })),

  on(
    DirectExportActions.fetchRelativityTemplatesSuccess,
    (state, { relativityFieldTemplates }) => ({
      ...state,
      isRelativityFieldTemplatesLoading: false,
      relativityFieldTemplates,
    })
  ),

  on(
    DirectExportActions.fetchRelativityTemplatesFailure,
    (state, { relativityFieldTemplatesFailure }) => ({
      ...state,
      isRelativityFieldTemplatesLoading: false,
      relativityFieldTemplatesFailure,
    })
  ),

  on(DirectExportActions.fetchRelativityWorkspaceFileshares, (state) => ({
    ...state,
    isRelativityFilesharesLoading: true,
  })),

  on(
    DirectExportActions.fetchRelativityWorkspaceFilesharesSuccess,
    (state, { relativityFileshares }) => ({
      ...state,
      isRelativityFilesharesLoading: false,
      relativityFileshares,
    })
  ),

  on(
    DirectExportActions.fetchRelativityWorkspaceFilesharesFailure,
    (state, { relativityFilesharesFailure }) => ({
      ...state,
      isRelativityFilesharesLoading: false,
      relativityFilesharesFailure,
    })
  ),

  on(DirectExportActions.createUserEnvironment, (state) => ({
    ...state,
    isUserEnvironmentLoading: true,
    userEnvironmentError: undefined,
  })),
  on(
    DirectExportActions.createUserEnvironmentSuccess,
    (state, { createUserEnvironmentSuccess }) => ({
      ...state,
      createUserEnvironmentSuccess: createUserEnvironmentSuccess,
      userEnvironmentError: undefined,
    })
  ),
  on(
    DirectExportActions.createUserEnvironmentFailure,
    (state, { createUserEnvironmentError }) => ({
      ...state,
      createUserEnvironmentError: createUserEnvironmentError,
    })
  ),

  on(DirectExportActions.updateUserEnvironment, (state) => ({
    ...state,
    updateUserEnvironmentError: undefined,
  })),
  on(
    DirectExportActions.updateUserEnvironmentSuccess,
    (state, { updateUserEnvironmentSuccess }) => ({
      ...state,
      updateUserEnvironmentSuccess: updateUserEnvironmentSuccess,
      updateUserEnvironmentError: undefined,
    })
  ),
  on(
    DirectExportActions.updateUserEnvironmentFailure,
    (state, { updateUserEnvironmentError }) => ({
      ...state,
      updateUserEnvironmentError: updateUserEnvironmentError,
    })
  ),

  on(
    DirectExportActions.setControlNumberConflictResponse,
    (state, { response }) => ({
      ...state,
      controlNumberConflictResponse: response,
    })
  ),
  on(DirectExportActions.clearControlNumberConflictResponse, (state) => ({
    ...state,
    controlNumberConflictResponse: undefined,
  })),

  on(DirectExportActions.clearUserEnvironmentResponse, (state) => ({
    ...state,
    userEnvironmentResponse: undefined,
  })),

  on(DirectExportActions.clearRelativityWorkspaceList, (state) => ({
    ...state,
    relativityWorkspaces: [],
  })),

  on(DirectExportActions.clearRelativityWorkspaceFileshareList, (state) => ({
    ...state,
    relativityFileshares: [],
  })),

  on(DirectExportActions.clearCreateEnvironmentSuccessResponse, (state) => ({
    ...state,
    createUserEnvironmentSuccess: undefined,
  })),

  on(DirectExportActions.clearCreateEnvironmentErrorResponse, (state) => ({
    ...state,
    createUserEnvironmentError: undefined,
  }))
)

export function directExportReducer(
  state: DirectExportState | undefined,
  action: Action
): DirectExportState {
  return reducer(state, action)
}
