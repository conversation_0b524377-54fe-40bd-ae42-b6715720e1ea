import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import * as DeletedExportActions from './deleted-exports.actions'
import * as DeletedExportSelectors from './deleted-exports.selectors'
import { DeletedExportState } from './deleted-exports.reducer'

type DeletedExportStateKeys =
  | keyof DeletedExportState
  | Array<keyof DeletedExportState>

@Injectable({ providedIn: 'root' })
export class DeletedExportFacade {
  // Deleted export mode list selectors
  public readonly selectDeletedExportModeList$ = this.store.pipe(
    select(DeletedExportSelectors.getDeletedExportModeList)
  )

  public readonly selectDeletedExportModeListLoading$ = this.store.pipe(
    select(
      DeletedExportSelectors.getStateFromDeletedExportStore(
        'isDeletedExportModeListLoading'
      )
    )
  )

  public readonly selectDeletedExportModeListSuccessResponse$ = this.store.pipe(
    select(
      DeletedExportSelectors.getStateFromDeletedExportStore(
        'deletedExportModeListSuccessResponse'
      )
    )
  )

  public readonly selectDeletedExportModeListErrorResponse$ = this.store.pipe(
    select(
      DeletedExportSelectors.getStateFromDeletedExportStore(
        'deletedExportModeListErrorResponse'
      )
    )
  )

  constructor(private readonly store: Store) {}

  public fetchDeletedExportModeList(): void {
    this.store.dispatch(DeletedExportActions.fetchDeletedExportModeList())
  }

  /**
   * Resetting a specific property of the Deleted Export State
   * @param {DeletedExportStateKeys} stateKey - state keys
   * @returns {void}
   */
  public resetDeletedExportState(stateKey: DeletedExportStateKeys): void {
    this.store.dispatch(
      DeletedExportActions.resetDeletedExportState({ stateKey })
    )
  }
}
