import {
  createFeatureSelector,
  createSelector,
  MemoizedSelector,
} from '@ngrx/store'
import {
  DeletedExportState,
  DELETED_EXPORT_FEATURE_KEY,
} from './deleted-exports.reducer'
import { ResponseModel } from '@venio/shared/models/interfaces'

export const memoizedSelector = createFeatureSelector<DeletedExportState>(
  DELETED_EXPORT_FEATURE_KEY
)

export const getStateFromDeletedExportStore = <
  T extends keyof DeletedExportState
>(
  stateKey: T
): MemoizedSelector<object, DeletedExportState[T], unknown> =>
  createSelector(
    memoizedSelector,
    (state: DeletedExportState) => state[stateKey]
  )

export const getDeletedExportModeList = createSelector(
  getStateFromDeletedExportStore('deletedExportModeListSuccessResponse'),
  (stateResponseModel: ResponseModel) => stateResponseModel?.data
)
