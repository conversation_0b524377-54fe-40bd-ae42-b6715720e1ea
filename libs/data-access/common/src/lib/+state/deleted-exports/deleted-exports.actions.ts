import { createAction, props } from '@ngrx/store'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { DeletedExportState } from './deleted-exports.reducer'

export enum DeletedExportActionTypes {
  // Actions for fetch deleted export mode list
  FetchDeletedExportModeList = '[DeletedExport] Fetch Deleted Export Mode List',
  FetchDeletedExportModeListSuccess = '[DeletedExport] Fetch Deleted Export Mode List: Success',
  FetchDeletedExportModeListFailure = '[DeletedExport] Fetch Deleted Export Mode List: Failure',

  // Resetting Deleted Export State
  ResetDeletedExportState = '[DeletedExport] Reset State',
}

export const resetDeletedExportState = createAction(
  DeletedExportActionTypes.ResetDeletedExportState,
  props<{
    stateKey: keyof DeletedExportState | Array<keyof DeletedExportState>
  }>()
)

export const fetchDeletedExportModeList = createAction(
  DeletedExportActionTypes.FetchDeletedExportModeList
)

export const fetchDeletedExportModeListSuccess = createAction(
  DeletedExportActionTypes.FetchDeletedExportModeListSuccess,
  props<{ deletedExportModeListSuccessResponse: ResponseModel }>()
)

export const fetchDeletedExportModeListFailure = createAction(
  DeletedExportActionTypes.FetchDeletedExportModeListFailure,
  props<{ deletedExportModeListErrorResponse: ResponseModel }>()
)
