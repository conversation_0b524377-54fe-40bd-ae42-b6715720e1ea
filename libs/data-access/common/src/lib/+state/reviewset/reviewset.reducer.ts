import {
  ReviewStatusRequestModel,
  ReviewUserModel,
  ResponseModel,
  ReviewSetBatchSummaryModel,
  ReviewSetBatchSummaryRateModel,
  ReviewStatusModel,
  ReviewBatchSummary,
} from '@venio/shared/models/interfaces'
import * as ReviewSetActions from './reviewset.actions'
import { resetStateProperty } from '@venio/util/utilities'
import { Action, createReducer, on } from '@ngrx/store'

export const REVIEWSET_FEATURE_KEY = 'reviewsetStore'

export interface ReviewSetState {
  // Review set Dashboard
  reviewSetReviewersSuccess: ReviewUserModel[] | undefined
  reviewSetReviewersError: ResponseModel | undefined
  isReviewSetBatchDashboardLoading: boolean | undefined
  isReviewSetBatchSummaryLoading: boolean | undefined
  reviewSetBatchSummarySuccess: ReviewSetBatchSummaryModel | undefined
  reviewSetBatchSummaryError: ResponseModel | undefined
  reviewSetBatchSummaryRateSuccess: ReviewSetBatchSummaryRateModel | undefined
  reviewSetBatchSummaryRateError: ResponseModel | undefined

  reviewSetDashboardRequestInfo: ReviewStatusRequestModel

  reviewSetTagRateSuccess: ReviewStatusModel[] | undefined
  reviewSetTagRateError: ResponseModel | undefined
  isReviewSetTagRateLoading: boolean | undefined

  reviewSetTagStatusSuccess: ReviewStatusModel[] | undefined
  reviewSetTagStatusError: ResponseModel | undefined
  isReviewSetTagStatusLoading: boolean | undefined

  reviewSetProgressSuccess: ReviewStatusModel[] | undefined
  reviewSetProgressError: ResponseModel | undefined
  isReviewSetProgressLoading: boolean | undefined

  reviewSetReviewerSuccess: ReviewStatusModel[] | undefined
  reviewSetReviewerError: ResponseModel | undefined
  isReviewSetReviewerLoading: boolean | undefined

  // reviewset batch summary
  reviewSetBatchSummaryDetailSuccess: ReviewBatchSummary[] | undefined
  reviewSetBatchSummaryDetailError: ResponseModel | undefined
  isReviewSetBatchSummaryDetailLoading: boolean | undefined

  selectedFilterFields: string[] | undefined
}

export const reviewSetInitialState: ReviewSetState = {
  isReviewSetBatchDashboardLoading: undefined,
  reviewSetReviewersSuccess: undefined,
  reviewSetReviewersError: undefined,
  isReviewSetBatchSummaryLoading: undefined,
  reviewSetBatchSummarySuccess: undefined,
  reviewSetBatchSummaryError: undefined,
  reviewSetBatchSummaryRateSuccess: undefined,
  reviewSetBatchSummaryRateError: undefined,

  reviewSetDashboardRequestInfo: {
    reviewers: null,
    startDate: null,
    endDate: null,
  },

  reviewSetTagRateSuccess: undefined,
  reviewSetTagRateError: undefined,
  isReviewSetTagRateLoading: undefined,

  reviewSetTagStatusSuccess: undefined,
  reviewSetTagStatusError: undefined,
  isReviewSetTagStatusLoading: undefined,

  reviewSetProgressSuccess: undefined,
  reviewSetProgressError: undefined,
  isReviewSetProgressLoading: undefined,

  reviewSetReviewerSuccess: undefined,
  reviewSetReviewerError: undefined,
  isReviewSetReviewerLoading: undefined,

  reviewSetBatchSummaryDetailSuccess: undefined,
  reviewSetBatchSummaryDetailError: undefined,
  isReviewSetBatchSummaryDetailLoading: undefined,

  selectedFilterFields: undefined,
}

const reducer = createReducer<ReviewSetState>(
  reviewSetInitialState,
  on(ReviewSetActions.resetReviewSetState, (state, { stateKey }) =>
    resetStateProperty<ReviewSetState>(state, reviewSetInitialState, stateKey)
  ),
  on(ReviewSetActions.fetchReviewSetReviewers, (state) => ({
    ...state,
    isReviewSetBatchDashboardLoading: true,
  })),
  on(
    ReviewSetActions.fetchReviewSetReviewersSuccess,
    (state, { reviewSetReviewersSuccess }) => ({
      ...state,
      reviewSetReviewersSuccess,
      reviewSetReviewersError: undefined,
      isReviewSetBatchDashboardLoading: false,
    })
  ),
  on(
    ReviewSetActions.fetchReviewSetReviewersFailure,
    (state, { reviewSetReviewersError }) => ({
      ...state,
      reviewSetReviewersError,
      reviewSetReviewersSuccess: undefined,
      isReviewSetBatchDashboardLoading: false,
    })
  ),
  on(ReviewSetActions.updateReviewSetDashboardRequestInfo, (state, action) => {
    // Remove null or undefined values from the request info object
    const filteredRequestInfo = Object.fromEntries(
      Object.entries(action.reviewSetDashboardRequestInfo).filter(
        ([key, value]) => value !== null && value !== undefined
      )
    )
    return {
      ...state,
      reviewSetDashboardRequestInfo: {
        ...state.reviewSetDashboardRequestInfo,
        ...filteredRequestInfo,
      } as ReviewStatusRequestModel,
    }
  }),
  on(ReviewSetActions.fetchReviewSetBatchSummary, (state) => ({
    ...state,
    isReviewSetBatchSummaryLoading: true,
  })),
  on(
    ReviewSetActions.fetchReviewSetBatchSummarySuccess,
    (state, { reviewSetBatchSummarySuccess }) => ({
      ...state,
      reviewSetBatchSummarySuccess,
      reviewSetBatchSummaryError: undefined,
      isReviewSetBatchSummaryLoading: false,
    })
  ),
  on(
    ReviewSetActions.fetchReviewSetBatchSummaryFailure,
    (state, { reviewSetBatchSummaryError }) => ({
      ...state,
      reviewSetBatchSummaryError,
      reviewSetBatchSummarySuccess: undefined,
      isReviewSetBatchSummaryLoading: false,
    })
  ),
  on(ReviewSetActions.fetchReviewSetBatchSummaryRate, (state) => ({
    ...state,
    isReviewSetBatchSummaryLoading: true,
  })),
  on(
    ReviewSetActions.fetchReviewSetBatchSummaryRateSuccess,
    (state, { reviewSetBatchSummaryRateSuccess }) => ({
      ...state,
      reviewSetBatchSummaryRateSuccess,
      reviewSetBatchSummaryRateError: undefined,
      isReviewSetBatchSummaryLoading: false,
    })
  ),
  on(
    ReviewSetActions.fetchReviewSetBatchSummaryRateFailure,
    (state, { reviewSetBatchSummaryRateError }) => ({
      ...state,
      reviewSetBatchSummaryRateError,
      reviewSetBatchSummaryRateSuccess: undefined,
      isReviewSetBatchSummaryLoading: false,
    })
  ),
  on(ReviewSetActions.fetchReviewSetTagRate, (state) => ({
    ...state,
    isReviewSetTagRateLoading: true,
  })),
  on(
    ReviewSetActions.fetchReviewSetTagRateSuccess,
    (state, { reviewSetTagRateSuccess }) => ({
      ...state,
      reviewSetTagRateSuccess,
      reviewSetTagRateError: undefined,
      isReviewSetTagRateLoading: false,
    })
  ),
  on(
    ReviewSetActions.fetchReviewSetTagRateFailure,
    (state, { reviewSetTagRateError }) => ({
      ...state,
      reviewSetTagRateSuccess: undefined,
      reviewSetTagRateError,
      isReviewSetTagRateLoading: false,
    })
  ),

  on(ReviewSetActions.fetchReviewSetTagStatus, (state) => ({
    ...state,
    isReviewSetTagStatusLoading: true,
  })),
  on(
    ReviewSetActions.fetchReviewSetTagStatusSuccess,
    (state, { reviewSetTagStatusSuccess }) => ({
      ...state,
      reviewSetTagStatusSuccess,
      reviewSetTagStatusError: undefined,
      isReviewSetTagStatusLoading: false,
    })
  ),
  on(
    ReviewSetActions.fetchReviewSetTagStatusFailure,
    (state, { reviewSetTagStatusError }) => ({
      ...state,
      reviewSetTagStatusError,
      reviewSetTagStatusSuccess: undefined,
      isReviewSetTagStatusLoading: false,
    })
  ),

  on(ReviewSetActions.fetchReviewSetProgress, (state) => ({
    ...state,
    isReviewSetProgressLoading: true,
  })),
  on(
    ReviewSetActions.fetchReviewSetProgressSuccess,
    (state, { reviewSetProgressSuccess }) => ({
      ...state,
      reviewSetProgressSuccess,
      reviewSetProgressError: undefined,
      isReviewSetProgressLoading: false,
    })
  ),
  on(
    ReviewSetActions.fetchReviewSetProgressFailure,
    (state, { reviewSetProgressError }) => ({
      ...state,
      reviewSetProgressError,
      reviewSetProgressSuccess: undefined,
      isReviewSetProgressLoading: false,
    })
  ),
  on(ReviewSetActions.fetchReviewSetReviewer, (state) => ({
    ...state,
    isReviewSetReviewerLoading: true,
  })),
  on(
    ReviewSetActions.fetchReviewSetReviewerSuccess,
    (state, { reviewSetReviewerSuccess }) => ({
      ...state,
      reviewSetReviewerSuccess,
      reviewSetReviewerError: undefined,
      isReviewSetReviewerLoading: false,
    })
  ),
  on(
    ReviewSetActions.fetchReviewSetReviewerFailure,
    (state, { reviewSetReviewerError }) => ({
      ...state,
      reviewSetReviewerError,
      reviewSetReviewerSuccess: undefined,
      isReviewSetReviewerLoading: false,
    })
  ),
  on(ReviewSetActions.fetchReviewSetBatchSummaryDetail, (state) => ({
    ...state,
    isReviewSetBatchSummaryDetailLoading: true,
  })),
  on(
    ReviewSetActions.fetchReviewSetBatchSummaryDetailSuccess,
    (state, { reviewSetBatchSummaryDetailSuccess }) => ({
      ...state,
      reviewSetBatchSummaryDetailSuccess,
      reviewSetBatchSummaryDetailError: undefined,
      isReviewSetBatchSummaryDetailLoading: false,
    })
  ),
  on(
    ReviewSetActions.fetchReviewSetBatchSummaryDetailFailure,
    (state, { reviewSetBatchSummaryDetailError }) => ({
      ...state,
      reviewSetBatchSummaryDetailError,
      reviewSetBatchSummaryDetailSuccess: undefined,
      isReviewSetBatchSummaryDetailLoading: false,
    })
  ),
  on(
    ReviewSetActions.setSelectedFilterFields,
    (state, { selectedFilterFields }) => ({
      ...state,
      selectedFilterFields,
    })
  ),
  on(ReviewSetActions.clearReviewSetState, () => reviewSetInitialState)
)

export function reviewSetReducer(
  state: ReviewSetState | undefined,
  action: Action
): ReviewSetState {
  return reducer(state, action)
}
