import { HttpErrorResponse } from '@angular/common/http'
import { Injectable, inject } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { fetch } from '@ngrx/router-store/data-persistence'
import { select, Store } from '@ngrx/store'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { map, switchMap, take } from 'rxjs'
import * as ReviewSetActions from './reviewset.actions'
import * as ReviewSetSelector from './reviewset.selectors'
import { ReviewSetState } from './reviewset.reducer'
import { ReviewSetService } from '../../services/reviewset.service'

@Injectable()
export class ReviewSetEffects {
  private readonly actions$ = inject(Actions)

  private readonly store = inject(Store<ReviewSetState>)

  private readonly reviewsetService = inject(ReviewSetService)

  public fetchReviewSetReviewers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReviewSetActions.fetchReviewSetReviewers),
      fetch({
        run: ({ projectId, reviewSetId }) => {
          return this.reviewsetService
            .getReviewSetReviewers(projectId, reviewSetId)
            .pipe(
              map((response) =>
                ReviewSetActions.fetchReviewSetReviewersSuccess({
                  reviewSetReviewersSuccess: response.data,
                })
              )
            )
        },
        onError: (_, error: HttpErrorResponse) => {
          const reviewSetReviewersError = error.error as ResponseModel
          return ReviewSetActions.fetchReviewSetReviewersFailure({
            reviewSetReviewersError,
          })
        },
      })
    )
  )

  public fetchReviewSetBatchSummary$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReviewSetActions.fetchReviewSetBatchSummary),
      fetch({
        run: ({ projectId, reviewSetId }) => {
          return this.reviewsetService
            .fetchReviewSetBatchSummary(projectId, reviewSetId)
            .pipe(
              map((response) =>
                ReviewSetActions.fetchReviewSetBatchSummarySuccess({
                  reviewSetBatchSummarySuccess: response.data,
                })
              )
            )
        },

        onError: (_, error: HttpErrorResponse) => {
          const reviewSetBatchSummaryError = error.error as ResponseModel
          return ReviewSetActions.fetchReviewSetBatchSummaryFailure({
            reviewSetBatchSummaryError,
          })
        },
      })
    )
  )

  public fetchReviewSetBatchSummaryRate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReviewSetActions.fetchReviewSetBatchSummaryRate),
      fetch({
        run: ({ projectId, reviewSetId }) => {
          return this.reviewsetService
            .fetchReviewSetBatchSummaryRate(projectId, reviewSetId)
            .pipe(
              map((response) =>
                ReviewSetActions.fetchReviewSetBatchSummaryRateSuccess({
                  reviewSetBatchSummaryRateSuccess: response.data,
                })
              )
            )
        },
        onError: (_, error: HttpErrorResponse) => {
          const reviewSetBatchSummaryRateError = error.error as ResponseModel
          return ReviewSetActions.fetchReviewSetBatchSummaryRateFailure({
            reviewSetBatchSummaryRateError,
          })
        },
      })
    )
  )

  public fetchReviewSetTagRate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReviewSetActions.fetchReviewSetTagRate),
      fetch({
        run: ({ projectId, reviewSetId }) =>
          this.store.pipe(
            select(
              ReviewSetSelector.getStateFromReviewSetStore(
                'reviewSetDashboardRequestInfo'
              )
            ),
            take(1),
            switchMap((reviewerDashboardRequestInfo) =>
              this.reviewsetService
                .fetchReviewSetTagRate(
                  projectId,
                  reviewSetId,
                  reviewerDashboardRequestInfo
                )
                .pipe(
                  map((response) =>
                    ReviewSetActions.fetchReviewSetTagRateSuccess({
                      reviewSetTagRateSuccess: response.data,
                    })
                  )
                )
            )
          ),
        onError: (_, error: HttpErrorResponse) => {
          const reviewSetTagRateError = error.error as ResponseModel
          return ReviewSetActions.fetchReviewSetTagRateFailure({
            reviewSetTagRateError,
          })
        },
      })
    )
  )

  public fetchReviewSetTagStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReviewSetActions.fetchReviewSetTagStatus),
      fetch({
        run: ({ projectId, reviewSetId }) =>
          this.store.pipe(
            select(
              ReviewSetSelector.getStateFromReviewSetStore(
                'reviewSetDashboardRequestInfo'
              )
            ),
            take(1),
            switchMap((reviewerDashboardRequestInfo) =>
              this.reviewsetService
                .fetchReviewSetTagStatus(
                  projectId,
                  reviewSetId,
                  reviewerDashboardRequestInfo
                )
                .pipe(
                  map((response) =>
                    ReviewSetActions.fetchReviewSetTagStatusSuccess({
                      reviewSetTagStatusSuccess: response.data,
                    })
                  )
                )
            )
          ),
        onError: (_, error: HttpErrorResponse) => {
          const reviewSetTagStatusError = error.error as ResponseModel
          return ReviewSetActions.fetchReviewSetTagStatusFailure({
            reviewSetTagStatusError,
          })
        },
      })
    )
  )

  public fetchReviewSetProgress$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReviewSetActions.fetchReviewSetProgress),
      fetch({
        run: ({ projectId, reviewSetId }) =>
          this.store.pipe(
            select(
              ReviewSetSelector.getStateFromReviewSetStore(
                'reviewSetDashboardRequestInfo'
              )
            ),
            take(1),
            switchMap((reviewerDashboardRequestInfo) =>
              this.reviewsetService
                .fetchReviewSetProgress(
                  projectId,
                  reviewSetId,
                  reviewerDashboardRequestInfo
                )
                .pipe(
                  map((response) =>
                    ReviewSetActions.fetchReviewSetProgressSuccess({
                      reviewSetProgressSuccess: response.data,
                    })
                  )
                )
            )
          ),
        onError: (_, error: HttpErrorResponse) => {
          const reviewSetProgressError = error.error as ResponseModel
          return ReviewSetActions.fetchReviewSetProgressFailure({
            reviewSetProgressError,
          })
        },
      })
    )
  )

  public fetchReviewSetReviewer$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReviewSetActions.fetchReviewSetReviewer),
      fetch({
        run: ({ projectId, reviewSetId }) =>
          this.store.pipe(
            select(
              ReviewSetSelector.getStateFromReviewSetStore(
                'reviewSetDashboardRequestInfo'
              )
            ),
            take(1),
            switchMap((reviewerDashboardRequestInfo) =>
              this.reviewsetService
                .fetchReviewSetReviewer(
                  projectId,
                  reviewSetId,
                  reviewerDashboardRequestInfo || null
                )
                .pipe(
                  map((response) =>
                    ReviewSetActions.fetchReviewSetReviewerSuccess({
                      reviewSetReviewerSuccess: response.data,
                    })
                  )
                )
            )
          ),
        onError: (_, error: HttpErrorResponse) => {
          const reviewSetReviewerError = error.error as ResponseModel
          return ReviewSetActions.fetchReviewSetReviewerFailure({
            reviewSetReviewerError,
          })
        },
      })
    )
  )

  public fetchReviewSetBatchSummaryDetail$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ReviewSetActions.fetchReviewSetBatchSummaryDetail),
      fetch({
        run: ({ projectId, reviewSetId }) =>
          this.store.pipe(
            select(
              ReviewSetSelector.getStateFromReviewSetStore(
                'reviewSetDashboardRequestInfo'
              )
            ),
            take(1),
            switchMap((reviewerDashboardRequestInfo) =>
              this.reviewsetService
                .fetchReviewSetBatchSummaryDetail(
                  projectId,
                  reviewSetId,
                  reviewerDashboardRequestInfo
                )
                .pipe(
                  map((response) =>
                    ReviewSetActions.fetchReviewSetBatchSummaryDetailSuccess({
                      reviewSetBatchSummaryDetailSuccess: response.data,
                    })
                  )
                )
            )
          ),
        onError: (_, error: HttpErrorResponse) => {
          const reviewSetBatchSummaryDetailError = error.error as ResponseModel
          return ReviewSetActions.fetchReviewSetBatchSummaryDetailFailure({
            reviewSetBatchSummaryDetailError,
          })
        },
      })
    )
  )
}
