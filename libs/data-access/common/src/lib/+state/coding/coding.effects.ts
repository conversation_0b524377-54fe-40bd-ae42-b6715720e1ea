import { Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { fetch } from '@ngrx/router-store/data-persistence'
import { catchError, map } from 'rxjs'
import * as CodingActions from './coding.actions'
import { HttpErrorResponse } from '@angular/common/http'
import { CodingService } from '../../services/coding.service'
import { CustomFieldsModel } from '@venio/shared/models/interfaces'
import { CommonActionTypes } from '@venio/shared/models/constants'

@Injectable()
export class CodingEffects {
  public fetchCustomFieldType$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CodingActions.fetchCustomFieldTypes),
      fetch({
        run: () => {
          return this.codingService.fetchCustomFieldType().pipe(
            map((customFieldTypesSuccessResponse) =>
              CodingActions.fetchCustomFieldTypesSuccess({
                customFieldTypesSuccessResponse,
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const customFieldTypesErrorResponse = error.error
          return CodingActions.fetchCustomFieldTypesFailure({
            customFieldTypesErrorResponse,
          })
        },
      })
    )
  )

  public fetchCodingField$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CodingActions.fetchCodingFields),
      fetch({
        run: ({ projectId }) => {
          return this.codingService.fetchCodingFields(projectId).pipe(
            map((codingFieldSuccessResponse) =>
              CodingActions.fetchCodingFieldsSuccess({
                codingFieldSuccessResponse,
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const codingFieldErrorResponse = error.error
          return CodingActions.fetchCodingFieldsFailure({
            codingFieldErrorResponse,
          })
        },
      })
    )
  )

  public deleteCodingField$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CodingActions.deleteCodingField),
      fetch({
        run: ({ projectId, customFieldId }) => {
          return this.codingService
            .deleteCodingField(projectId, customFieldId)
            .pipe(
              map((deleteCodingFieldSuccessResponse) =>
                CodingActions.deleteCodingFieldSuccess({
                  deleteCodingFieldSuccessResponse,
                })
              ),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const deleteCodingFieldErrorResponse = error.error
          return CodingActions.deleteCodingFieldFailure({
            deleteCodingFieldErrorResponse,
          })
        },
      })
    )
  )

  public fetchDelimiter$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CodingActions.fetchDelimiters),
      fetch({
        run: () => {
          return this.codingService.fetchDelimiters().pipe(
            map((delimiterSuccessResponse) =>
              CodingActions.fetchDelimiterSuccess({
                delimiterSuccessResponse,
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const delimiterErrorResponse = error.error
          return CodingActions.fetchDelimiterFailure({
            delimiterErrorResponse,
          })
        },
      })
    )
  )

  public addOrUpdateCodingField$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CodingActions.addOrUpdateCodingField),
      fetch({
        run: ({ payload }) => {
          return this.codingService.addOrUpdateCustomField(payload).pipe(
            map((addOrUpdateCodingFieldSuccessResponse) =>
              CodingActions.addOrUpdateCodingFieldSuccess({
                addOrUpdateCodingFieldSuccessResponse,
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const addOrUpdateCodingFieldErrorResponse = error.error
          return CodingActions.addOrUpdateCodingFieldFailure({
            addOrUpdateCodingFieldErrorResponse,
          })
        },
      })
    )
  )

  /**
   * Map custom field data for clone action.
   * suffix '-clone' to the field name and set custom field id to 0
   * @param {CustomFieldsModel} data - custom field data
   * @param {CommonActionTypes} actionType - action type
   * @returns {CustomFieldsModel} - mapped custom field data
   */
  #mapCustomFieldData(
    data: CustomFieldsModel,
    actionType: CommonActionTypes
  ): CustomFieldsModel {
    const isClone = actionType === CommonActionTypes.CLONE

    return isClone
      ? {
          ...data,
          customFieldId: 0,
          fieldName: `${data.fieldName}_clone`,
        }
      : data
  }

  public fetchCodingFieldById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CodingActions.fetchCodingFieldById),
      fetch({
        run: ({ payload }) => {
          return this.codingService
            .fetchCustomFieldById(payload.projectId, payload.customFieldId)
            .pipe(
              map((codingFieldByIdSuccessResponse) => {
                codingFieldByIdSuccessResponse.data = this.#mapCustomFieldData(
                  codingFieldByIdSuccessResponse.data,
                  payload.actionType
                )
                return CodingActions.fetchCodingFieldByIdSuccess({
                  codingFieldByIdSuccessResponse,
                })
              }),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const codingFieldByIdErrorResponse = error.error
          return CodingActions.fetchCodingFieldByIdFailure({
            codingFieldByIdErrorResponse,
          })
        },
      })
    )
  )

  public saveCodingReorder$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CodingActions.saveCodingReorder),
      fetch({
        run: ({ projectId, codingReorder }) => {
          return this.codingService
            .saveCodingReorder$(projectId, codingReorder)
            .pipe(
              map((codingReorderSuccessResponse) =>
                CodingActions.fetchCodingReorderSuccess({
                  codingReorderSuccessResponse,
                })
              ),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const codingReorderErrorResponse = error.error
          return CodingActions.fetchCodingReorderFailure({
            codingReorderErrorResponse,
          })
        },
      })
    )
  )

  constructor(
    private readonly actions$: Actions,
    private codingService: CodingService
  ) {}
}
