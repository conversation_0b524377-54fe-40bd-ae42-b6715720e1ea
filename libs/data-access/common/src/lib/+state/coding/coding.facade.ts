import { inject, Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import * as ProjectActions from './coding.actions'
import * as ProjectSelectors from './coding.selectors'
import { CodingState } from './coding.reducer'
import {
  CodingReorderModel,
  CustomFieldsModel,
} from '@venio/shared/models/interfaces'
import { ActivatedRoute } from '@angular/router'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { filter } from 'rxjs'

type CodingStateKeys = keyof CodingState | Array<keyof CodingState>
@Injectable({ providedIn: 'root' })
export class CodingFacade {
  // injectors
  private store = inject(Store)

  private activatedRoute = inject(ActivatedRoute)

  // getters
  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  // selectors
  public readonly selectIsCodingFieldsLoading$ = this.store.pipe(
    select(ProjectSelectors.getStateFromCodingStore('isCodingFieldsLoading'))
  )

  public selectIsCustomFieldTypeLoading$ = this.store.pipe(
    select(ProjectSelectors.getStateFromCodingStore('isCustomFieldTypeLoading'))
  )

  public selectIsDelimiterLoading$ = this.store.pipe(
    select(ProjectSelectors.getStateFromCodingStore('isDelimiterLoading'))
  )

  public selectIsAddOrUpdateCodingFieldLoading$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromCodingStore(
        'isAddOrUpdateCodingFieldLoading'
      )
    )
  )

  public selectIsFetchCodingFieldByIdLoading$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromCodingStore('isFetchCodingFieldByIdLoading')
    )
  )

  public readonly selectCodingFieldSuccessResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromCodingStore('codingFieldSuccessResponse')
    )
  )

  public readonly selectCodingFieldErrorResponse$ = this.store.pipe(
    select(ProjectSelectors.getStateFromCodingStore('codingFieldErrorResponse'))
  )

  public selectDeleteCodingFieldSuccessResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromCodingStore(
        'deleteCodingFieldSuccessResponse'
      )
    )
  )

  public selectDeleteCodingFieldErrorResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromCodingStore('deleteCodingFieldErrorResponse')
    )
  )

  public selectCustomFieldTypesSuccessResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromCodingStore(
        'customFieldTypesSuccessResponse'
      )
    )
  )

  public selectCustomFieldTypesErrorResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromCodingStore('customFieldTypesErrorResponse')
    )
  )

  public selectIsAddOrUpdateCodingFieldNotified$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromCodingStore(
        'isAddOrUpdateCodingFieldNotified'
      )
    )
  )

  public selectDelimiterSuccessResponse$ = this.store.pipe(
    select(ProjectSelectors.getStateFromCodingStore('delimiterSuccessResponse'))
  )

  public selectDelimiterErrorResponse$ = this.store.pipe(
    select(ProjectSelectors.getStateFromCodingStore('delimiterErrorResponse'))
  )

  public selectEditCloneActionType$ = this.store
    .pipe(
      select(ProjectSelectors.getStateFromCodingStore('editCloneActionType'))
    )
    .pipe(
      filter(
        (action) =>
          action === CommonActionTypes.EDIT ||
          action === CommonActionTypes.CLONE
      )
    )

  public selectAddOrUpdateCodingFieldSuccessResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromCodingStore(
        'addOrUpdateCodingFieldSuccessResponse'
      )
    )
  )

  public selectAddOrUpdateCodingFieldErrorResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromCodingStore(
        'addOrUpdateCodingFieldErrorResponse'
      )
    )
  )

  public selectCodingFieldByIdSuccessResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromCodingStore('codingFieldByIdSuccessResponse')
    )
  )

  public selectCodingFieldByIdErrorResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromCodingStore('codingFieldByIdErrorResponse')
    )
  )

  public readonly selectCodingReorderSuccessResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromCodingStore('codingReorderSuccessResponse')
    )
  )

  public readonly selectCodingReorderErrorResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromCodingStore('codingReorderErrorResponse')
    )
  )

  // actions

  public fetchCodingFields(): void {
    this.store.dispatch(
      ProjectActions.fetchCodingFields({ projectId: this.projectId })
    )
  }

  public fetchCodingFieldById(payload: {
    customFieldId: number
    actionType: CommonActionTypes
  }): void {
    this.store.dispatch(
      ProjectActions.fetchCodingFieldById({
        payload: {
          projectId: this.projectId,
          customFieldId: payload.customFieldId,
          actionType: payload.actionType,
        },
      })
    )
  }

  public fetchCustomFieldType(): void {
    this.store.dispatch(ProjectActions.fetchCustomFieldTypes())
  }

  public fetchDelimiters(): void {
    this.store.dispatch(ProjectActions.fetchDelimiters())
  }

  public addOrUpdateCodingField(payload: {
    actionType: CommonActionTypes
    data: CustomFieldsModel
  }): void {
    this.store.dispatch(
      ProjectActions.addOrUpdateCodingField({
        payload: { ...payload.data, projectId: this.projectId },
        actionType: payload.actionType,
      })
    )
  }

  public notifyIsAddOrUpdateCodingField(
    isAddOrUpdateCodingFieldNotified: boolean
  ): void {
    this.store.dispatch(
      ProjectActions.notifyAddOrUpdateCodingField({
        isAddOrUpdateCodingFieldNotified,
      })
    )
  }

  public deleteCodingField(customFieldId: number): void {
    this.store.dispatch(
      ProjectActions.deleteCodingField({
        projectId: this.projectId,
        customFieldId,
      })
    )
  }

  public saveCodingReorder(
    projectId: number,
    codingReorder: CodingReorderModel
  ): void {
    this.store.dispatch(
      ProjectActions.saveCodingReorder({ projectId, codingReorder })
    )
  }

  /**
   * Resetting a specific property of the Coding State
   * @param {CodingStateKeys}stateKey - state keys
   * @returns {void}
   */
  public resetCodingState(stateKey: CodingStateKeys): void {
    this.store.dispatch(ProjectActions.resetCodingState({ stateKey }))
  }
}
