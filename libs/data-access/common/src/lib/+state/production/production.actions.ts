import { HttpEvent } from '@angular/common/http'
import { createAction, props } from '@ngrx/store'
import {
  ProductionShareInvitationModel,
  ProductionShareUserModel,
  ProductionStatusDetailModel,
  UserMessageModel,
} from '@venio/data-access/review'
import { ResponseModel } from '@venio/shared/models/interfaces'

export enum ProductionActionTypes {
  // Actions for fetching production status
  FetchProductionStatus = '[Production] Fetch Production Status',
  FetchProductionStatusSuccess = '[Production] Fetch Production Status: Success',
  FetchProductionStatusFailure = '[Production] Fetch Production Status: Failure',

  // Actions for deleting production
  DeleteProduction = '[Production] Delete Production',
  DeleteProductionSuccess = '[Production] Delete Production: Success',
  DeleteProductionFailure = '[Production] Delete Production: Failure',

  // Actions for sending production download invitation
  SendProductionDownloadInvitation = '[Production] Send Production Download Invitation',
  SendProductionDownloadInvitationSuccess = '[Production] Send Production Download Invitation: Success',
  SendProductionDownloadInvitationFailure = '[Production] Send Production Download Invitation: Failure',

  // Actions for downloading export
  DownloadExport = '[Production] Download Export',
  DownloadExportSuccess = '[Production] Download Export: Success',
  DownloadExportFailure = '[Production] Download Export: Failure',

  // Actions for fetching internal user
  FetchInternalUser = '[Production] Fetch Internal User',
  FetchInternalUserSuccess = '[Production] Fetch Internal User: Success',
  FetchInternalUserFailure = '[Production] Fetch Internal User: Failure',

  // Actions for fetching external user
  FetchExternalUser = '[Production] Fetch External User',
  FetchExternalUserSuccess = '[Production] Fetch External User: Success',
  FetchExternalUserFailure = '[Production] Fetch External User: Failure',
  // Resetting Production State
  ResetProductionState = '[Production] Reset State',
}

export const resetProductionState = createAction(
  ProductionActionTypes.ResetProductionState,
  props<{
    stateKey: keyof any | Array<keyof any>
  }>()
)

export const fetchProductionStatus = createAction(
  ProductionActionTypes.FetchProductionStatus,
  props<{ projectId: number; productionStatus: number; exportId: number }>()
)

export const fetchProductionStatusSuccess = createAction(
  ProductionActionTypes.FetchProductionStatusSuccess,
  props<{ productionStatusSuccessResponse: ProductionStatusDetailModel[] }>()
)

export const fetchProductionStatusFailure = createAction(
  ProductionActionTypes.FetchProductionStatusFailure,
  props<{ productionStatusErrorResponse: ResponseModel }>()
)

export const deleteProduction = createAction(
  ProductionActionTypes.DeleteProduction,
  props<{ projectId: number; exportId: number; deletionType: string }>()
)

export const deleteProductionSuccess = createAction(
  ProductionActionTypes.DeleteProductionSuccess,
  props<{ deleteProductionSuccessResponse: ResponseModel }>()
)

export const deleteProductionFailure = createAction(
  ProductionActionTypes.DeleteProductionFailure,
  props<{ deleteProductionErrorResponse: ResponseModel }>()
)

export const sendProductionDownloadInvitation = createAction(
  ProductionActionTypes.SendProductionDownloadInvitation,
  props<{
    projectId: number
    productionDownloadInvitation: ProductionShareInvitationModel
  }>()
)

export const sendProductionDownloadInvitationSuccess = createAction(
  ProductionActionTypes.SendProductionDownloadInvitationSuccess,
  props<{ sendInvitationSuccessResponse: ResponseModel }>()
)

export const sendProductionDownloadInvitationFailure = createAction(
  ProductionActionTypes.SendProductionDownloadInvitationFailure,
  props<{ sendInvitationErrorResponse: ResponseModel }>()
)

export const downloadExport = createAction(
  ProductionActionTypes.DownloadExport,
  props<{ sourceUrl: string }>()
)

export const downloadExportSuccess = createAction(
  ProductionActionTypes.DownloadExportSuccess,
  props<{ downloadExportSuccessResponse: HttpEvent<Blob> }>()
)

export const downloadExportFailure = createAction(
  ProductionActionTypes.DownloadExportFailure,
  props<{ downloadExportErrorResponse: ResponseModel }>()
)

export const fetchInternalUser = createAction(
  ProductionActionTypes.FetchInternalUser,
  props<{ projectId: number }>()
)

export const fetchInternalUserSuccess = createAction(
  ProductionActionTypes.FetchInternalUserSuccess,
  props<{ internalUserSuccessResponse: ProductionShareUserModel[] }>()
)

export const fetchInternalUserFailure = createAction(
  ProductionActionTypes.FetchInternalUserFailure,
  props<{ internalUserErrorResponse: ResponseModel }>()
)

// Actions for Fetch External User
export const fetchExternalUser = createAction(
  ProductionActionTypes.FetchExternalUser
)

export const fetchExternalUserSuccess = createAction(
  ProductionActionTypes.FetchExternalUserSuccess,
  props<{ externalUserSuccessResponse: ProductionShareUserModel[] }>()
)

export const fetchExternalUserFailure = createAction(
  ProductionActionTypes.FetchExternalUserFailure,
  props<{ externalUserErrorResponse: ResponseModel }>()
)

export const setUserMessage = createAction(
  '[Production] Set Response Message',
  props<{ messageModel: UserMessageModel }>()
)

export const setInvitationInProgressFlag = createAction(
  '[Production] Set Invitation In Progress Flag',
  props<{ isInvitationInProgress: boolean }>()
)

export const downloadRelativityErrorLog = createAction(
  '[Production] Download Relativity Error Log',
  props<{ projectId: number; exportId: number; errorLog: boolean }>()
)

export const relativityErrorLogDownloadSuccess = createAction(
  '[Production] Relativity Error Log Download Success',
  props<{ relativityErrorLogResponse: ResponseModel }>()
)

export const relativityErrorLogDownloadFailure = createAction(
  '[Production] Relativity Error Log Download Failure',
  props<{ errorLogFailure: ResponseModel }>()
)
