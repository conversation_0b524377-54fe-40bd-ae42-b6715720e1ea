import { HttpClient, HttpEvent } from '@angular/common/http'
import { Injectable } from '@angular/core'
import {
  ProductionShareInvitationModel,
  ProductionShareUserModel,
  ProductionStatusDetailModel,
} from '@venio/data-access/review'
import { environment } from '@venio/shared/environments'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { Observable } from 'rxjs'

@Injectable({
  providedIn: 'root',
})
export class ProductionService {
  constructor(private http: HttpClient) {}

  private get _apiUrl(): string {
    return environment.apiUrl.replace(/\/+$/, '')
  }

  public readonly fetchProductionStatus = (payload: {
    projectId: number
    productionStatus: number
    exportId: number
  }): Observable<ProductionStatusDetailModel[]> => {
    const params = {
      ProductionStatus: payload.productionStatus.toString(),
      exportId: payload.exportId.toString(),
    }
    return this.http.get<ProductionStatusDetailModel[]>(
      `${this._apiUrl}/production/project/${payload.projectId}/status`,
      { params }
    )
  }

  public readonly deleteProduction = (payload: {
    projectId: number
    exportId: number
    deletionType: string
  }): Observable<ResponseModel> => {
    const params = {
      exportId: payload.exportId.toString(),
      deleteFiles: payload.deletionType,
    }
    return this.http.delete<ResponseModel>(
      `${this._apiUrl}/production/project/${payload.projectId}/delete`,
      { params }
    )
  }

  public readonly sendProductionDownloadInvitation = (payload: {
    projectId: number
    productionDownloadInvitation: ProductionShareInvitationModel
  }): Observable<ResponseModel> =>
    this.http.post<ResponseModel>(
      `${this._apiUrl}/production-download-invitation/project/${payload.projectId}`,
      payload.productionDownloadInvitation
    )

  public readonly downloadExport = (payload: {
    sourceUrl: string
  }): Observable<HttpEvent<Blob>> => {
    return this.http.get(payload?.sourceUrl, {
      responseType: 'blob',
      observe: 'response',
      reportProgress: true,
    })
  }

  public readonly downloadErrorLog = (payload: {
    projectId: number
    exportId: number
    errorLog: boolean
  }): Observable<ResponseModel> => {
    const params = {
      errorLog: payload.errorLog,
    }
    return this.http.get<ResponseModel>(
      `${this._apiUrl}/full-production/cases/${payload.projectId}/productions/${payload.exportId}/download`,
      { params: params }
    )
  }

  public readonly fetchInternalUser = (payload: {
    projectId: number
  }): Observable<ProductionShareUserModel[]> => {
    const params = {
      projectId: payload.projectId,
    }
    return this.http.get<ProductionShareUserModel[]>(
      `${this._apiUrl}/UploadInvitation`,
      { params }
    )
  }

  public readonly fetchExternalUser = (): Observable<
    ProductionShareUserModel[]
  > => {
    return this.http.get<ProductionShareUserModel[]>(
      `${this._apiUrl}/UploadInvitation`
    )
  }
}
