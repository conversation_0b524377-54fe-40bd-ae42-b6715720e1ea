import { HttpClient, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from '@venio/shared/environments'
import {
  FileReprocessModel,
  ReprocessRequestModel,
} from '@venio/shared/models/interfaces'
import { Observable } from 'rxjs'

@Injectable({ providedIn: 'root' })
export class ReprocessingService {
  constructor(private http: HttpClient) {}

  private get _url(): string {
    return environment.apiUrl
  }

  /**
   * get fallback ingestion engine type
   */
  public readonly fetchIsFallbackIngestionEngine = <T>(
    projectId: number
  ): Observable<T> => {
    return this.http.get<T>(
      `${this._url}/upload/project/${projectId}/fallback-engine-flag`
    )
  }

  /**
   * Gets Reprocessing Tag from API ResponseModel
   * @param projectId project Id
   * @param mediaList media list
   */
  public readonly getTags = <T>(
    projectId: number,
    mediaList: string
  ): Observable<T> => {
    return this.http.get<T>(
      `${this._url}/file-reprocess/project/${projectId}/tags`,
      {
        params: new HttpParams().set('mediaList', mediaList),
      }
    )
  }

  /**
   * Gets Edoc Fields from API ResponseModel
   * @param projectId project Id
   */
  public readonly getEdocFields = <T>(projectId: number): Observable<T> => {
    return this.http.get<T>(
      `${this._url}/file-reprocess/project/${projectId}/edoc-fields`
    )
  }

  /**
   * Gets Email Fields from API ResponseModel
   * @param projectId project Id
   */
  public readonly getEmailFields = <T>(projectId: number): Observable<T> => {
    return this.http.get<T>(
      `${this._url}/file-reprocess/project/${projectId}/email-fields`
    )
  }

  /**
   * Gets document informations
   * @param projectId project Id
   * @param reprocessRequestModel reprocess request model
   */
  public readonly getDocumentList = <T>(
    projectId: number,
    reprocessRequestModel: ReprocessRequestModel
  ): Observable<T> =>
    this.http.post<T>(
      `${this._url}/file-reprocess/project/${projectId}/documents`,
      reprocessRequestModel
    )

  /**
   * Queue files for reprocess
   * @param projectId project Id
   * @param reprocessRequestModel reprocess request model
   */
  public readonly queueFileForReprocess = <T>(
    projectId: number,
    fileReprocessModel: FileReprocessModel
  ): Observable<T> =>
    this.http.post<T>(
      `${this._url}/file-reprocess/project/${projectId}`,
      fileReprocessModel
    )

  /**
   * Get supported extractors for file type
   */
  public readonly getSupportedExtractors = <T>(
    projectId: number,
    fileType: string
  ): Observable<T> => {
    return this.http.get<T>(
      `${this._url}/filereplacement/projects/${projectId}/filetypes/${fileType}/supported-extractors`
    )
  }

  /**
   * Drop temp table
   */
  public readonly dropTempTable = <T>(
    projectId: number,
    sessionId: number
  ): Observable<T> => {
    return this.http.delete<T>(
      `${this._url}/file-reprocess/project/${projectId}/temp-table?sessionId=${sessionId}`
    )
  }

  /**
   * Get media information
   */
  public readonly getMedia = <T>(
    projectId: number,
    mediaId: number
  ): Observable<T> => {
    return this.http.get<T>(
      `${this._url}/media/projects/${projectId}?mediaId=${mediaId}`
    )
  }

  /** Save load file */
  public readonly saveLoadFile = <T>(
    projectId: number,
    fileReprocessModel: FileReprocessModel
  ): Observable<T> =>
    this.http.post<T>(
      `${this._url}/file-reprocess/project/${projectId}/export-replacement-file`,
      fileReprocessModel
    )

  public readonly getCustodianInfo = <T>(projectId: number): Observable<T> => {
    return this.http.get<T>(
      `${this._url}/production/project/${projectId}/source-medias`
    )
  }

  public readonly getReprocessStatus = <T>(
    projectId: number
  ): Observable<T> => {
    return this.http.get<T>(
      `${this._url}/file-reprocess/project/${projectId}/status?sessionId=''`
    )
  }
}
