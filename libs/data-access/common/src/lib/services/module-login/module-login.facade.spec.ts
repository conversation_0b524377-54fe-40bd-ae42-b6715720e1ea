import { TestBed, fakeAsync, tick } from '@angular/core/testing'
import { ModuleLoginFacade } from './module-login.facade'
import { ModuleLoginService } from './module-login.service'
import { ModuleLoginStateService } from './module-login-state.service'
import { BehaviorSubject, of } from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { ModuleLoginModel } from '../../models/interfaces/module-login.model'
import { DestroyRef } from '@angular/core'
// These imports are used in the test

describe('ModuleLoginFacade', () => {
  let facade: ModuleLoginFacade
  let moduleLoginService: jest.Mocked<ModuleLoginService>
  let moduleLoginState: ModuleLoginStateService
  let destroyRef: DestroyRef

  // Mock data
  const mockProjectId = 123
  const mockReviewSetId = 456
  const mockLoginDetailId = '789'
  const mockModuleLoginId = 101
  const mockProjectLoginId = 202

  const mockResponseData: ResponseModel = {
    status: 'success',
    data: 303,
    message: 'Operation successful',
  }

  // Mock BehaviorSubject for projectId$
  const projectIdSubject = new BehaviorSubject<number>(mockProjectId)

  // Create mock signals
  let mockLoginDetailIdValue: number | undefined = undefined
  let mockProjectLoginIdValue: number | undefined = mockProjectLoginId
  let mockModuleLoginIdValue: number | undefined = mockModuleLoginId

  beforeEach(() => {
    // Create mocks for dependencies
    moduleLoginService = {
      fetchLoginDetail$: jest.fn().mockReturnValue(of(mockResponseData)),
      insertProjectLoginDetails: jest
        .fn()
        .mockReturnValue(of(mockResponseData)),
      updateProjectLoginDetails: jest
        .fn()
        .mockReturnValue(of(mockResponseData)),
      addModuleLogin$: jest.fn().mockReturnValue(of(mockResponseData)),
      updateModuleLogin$: jest.fn().mockReturnValue(of(mockResponseData)),
    } as unknown as jest.Mocked<ModuleLoginService>

    // Reset mock signal values
    mockLoginDetailIdValue = undefined
    mockProjectLoginIdValue = mockProjectLoginId
    mockModuleLoginIdValue = mockModuleLoginId

    // Create mock for ModuleLoginStateService
    moduleLoginState = {
      projectId$: projectIdSubject,
      loginDetailId: () => mockLoginDetailIdValue,
      projectLoginId: () => mockProjectLoginIdValue,
      moduleLoginId: () => mockModuleLoginIdValue,
      updateLoginDetailId: jest.fn().mockImplementation((id: string) => {
        mockLoginDetailIdValue = +id
      }),
      updateProjectLoginId: jest.fn().mockImplementation((id: number) => {
        mockProjectLoginIdValue = id
      }),
      updateModuleLoginId: jest.fn().mockImplementation((id: number) => {
        mockModuleLoginIdValue = id
      }),
      reset: jest.fn().mockImplementation(() => {
        mockLoginDetailIdValue = undefined
        mockProjectLoginIdValue = undefined
        mockModuleLoginIdValue = undefined
      }),
      updatePreviousReviewSetId: jest.fn(),
      previousReviewSetId: jest.fn(),
    } as unknown as ModuleLoginStateService

    // Create mock for DestroyRef
    destroyRef = {
      onDestroy: jest.fn(),
    } as unknown as DestroyRef

    TestBed.configureTestingModule({
      providers: [
        ModuleLoginFacade,
        { provide: ModuleLoginService, useValue: moduleLoginService },
        { provide: ModuleLoginStateService, useValue: moduleLoginState },
        { provide: DestroyRef, useValue: destroyRef },
      ],
    })

    facade = TestBed.inject(ModuleLoginFacade)
  })

  describe('fetchLoginDetail$', () => {
    it('should call moduleLoginService.fetchLoginDetail$ with the correct projectId', () => {
      // GIVEN a project ID

      // WHEN fetching login details
      const result = facade.fetchLoginDetail$(mockProjectId)

      // THEN the service method should be called with the correct ID
      expect(moduleLoginService.fetchLoginDetail$).toHaveBeenCalledWith(
        mockProjectId
      )

      // AND the result should be the expected response
      result.subscribe((response) => {
        expect(response).toEqual(mockResponseData)
      })
    })
  })

  describe('insertProjectLoginDetails', () => {
    it('should call moduleLoginService.insertProjectLoginDetails with the correct parameters', () => {
      // GIVEN a project ID and login detail ID

      // WHEN inserting project login details
      const result = facade.insertProjectLoginDetails(
        mockProjectId,
        mockLoginDetailId
      )

      // THEN the service method should be called with the correct parameters
      expect(moduleLoginService.insertProjectLoginDetails).toHaveBeenCalledWith(
        mockProjectId,
        mockLoginDetailId
      )

      // AND the result should be the expected response
      result.subscribe((response) => {
        expect(response).toEqual(mockResponseData)
      })
    })
  })

  describe('updateProjectLoginDetails', () => {
    it('should call moduleLoginService.updateProjectLoginDetails with the correct projectId', () => {
      // GIVEN a project ID

      // WHEN updating project login details
      const result = facade.updateProjectLoginDetails(mockProjectId)

      // THEN the service method should be called with the correct ID
      expect(moduleLoginService.updateProjectLoginDetails).toHaveBeenCalledWith(
        mockProjectId
      )

      // AND the result should be the expected response
      result.subscribe((response) => {
        expect(response).toEqual(mockResponseData)
      })
    })
  })

  describe('createNewModuleLogin', () => {
    it('should create a module login model and call addModuleLogin$ with correct parameters', () => {
      // GIVEN a project ID and review set ID
      const expectedModuleLoginModel: ModuleLoginModel = {
        module: 'ReviewSet',
        reviewSetId: mockReviewSetId,
        projectLoginId: mockProjectLoginId,
        updateEstimatedLogout: true,
      }

      // WHEN creating a new module login
      const result = facade.createNewModuleLogin(mockProjectId, mockReviewSetId)

      // THEN the service method should be called with the correct parameters
      expect(moduleLoginService.addModuleLogin$).toHaveBeenCalledWith(
        mockProjectId,
        expectedModuleLoginModel
      )

      // AND the result should be the expected response
      result.subscribe((response) => {
        expect(response).toEqual(mockResponseData)
      })
    })

    it('should update moduleLoginId when response data is available', () => {
      // GIVEN a project ID and review set ID

      // WHEN creating a new module login
      const result = facade.createNewModuleLogin(mockProjectId, mockReviewSetId)

      // THEN the moduleLoginId should be updated with the response data
      result.subscribe(() => {
        expect(moduleLoginState.updateModuleLoginId).toHaveBeenCalledWith(
          mockResponseData.data
        )
      })
    })

    it('should not update moduleLoginId when response data is null', () => {
      // GIVEN a response with null data
      const emptyResponse: ResponseModel = {
        status: 'success',
        data: null,
        message: 'No data',
      }
      moduleLoginService.addModuleLogin$.mockReturnValue(of(emptyResponse))

      // WHEN creating a new module login
      const result = facade.createNewModuleLogin(mockProjectId, mockReviewSetId)

      // THEN the moduleLoginId should not be updated
      result.subscribe(() => {
        expect(moduleLoginState.updateModuleLoginId).not.toHaveBeenCalled()
      })
    })
  })

  describe('updateExistingModuleLogin', () => {
    it('should create a module login model and call updateModuleLogin$ with correct parameters', () => {
      // GIVEN a project ID and review set ID
      const expectedModuleLoginModel: ModuleLoginModel = {
        module: 'ReviewSet',
        reviewSetId: mockReviewSetId,
        projectLoginId: mockProjectLoginId,
        updateEstimatedLogout: true,
      }

      // WHEN updating an existing module login
      const result = facade.updateExistingModuleLogin(
        mockProjectId,
        mockReviewSetId
      )

      // THEN the service method should be called with the correct parameters
      expect(moduleLoginService.updateModuleLogin$).toHaveBeenCalledWith(
        mockProjectId,
        mockModuleLoginId,
        expectedModuleLoginModel
      )

      // AND the result should be the expected response
      result.subscribe((response) => {
        expect(response).toEqual(mockResponseData)
      })
    })
  })

  describe('constructor', () => {
    it('should call #handleProjectIdSelectionChange when instantiated', fakeAsync(() => {
      // GIVEN a new instance of ModuleLoginFacade
      // The constructor is called during TestBed.inject(ModuleLoginFacade)

      // WHEN the projectId changes
      const newProjectId = 999
      projectIdSubject.next(newProjectId)
      tick()

      // THEN the state should be reset
      expect(moduleLoginState.reset).toHaveBeenCalled()
    }))
  })

  describe('#handleProjectIdSelectionChange', () => {
    it('should reset state when projectId changes', fakeAsync(() => {
      // GIVEN a change in projectId
      const newProjectId = 998

      // WHEN the projectId changes
      projectIdSubject.next(newProjectId)
      tick()

      // THEN the state should be reset
      expect(moduleLoginState.reset).toHaveBeenCalled()
    }))

    it('should fetch login details when projectId changes and loginDetailId is not set', fakeAsync(() => {
      // GIVEN loginDetailId is not set
      mockLoginDetailIdValue = undefined

      // WHEN the projectId changes
      const newProjectId = 999
      projectIdSubject.next(newProjectId)
      tick()

      // THEN login details should be fetched
      expect(moduleLoginService.fetchLoginDetail$).toHaveBeenCalledWith(
        newProjectId
      )
    }))

    it('should not fetch login details when projectId changes but loginDetailId is already set', fakeAsync(() => {
      // GIVEN loginDetailId is already set
      mockLoginDetailIdValue = 123

      // Reset the mock to track new calls
      jest.clearAllMocks()

      // WHEN the projectId changes
      const newProjectId = 999
      projectIdSubject.next(newProjectId)
      tick()

      // THEN login details should not be fetched
      expect(moduleLoginService.fetchLoginDetail$).not.toHaveBeenCalled()
    }))

    it('should update loginDetailId when login details are fetched', fakeAsync(() => {
      // GIVEN a response with detailID
      const mockLoginResponse: ResponseModel = {
        status: 'success',
        data: { detailID: '123' },
        message: 'Success',
      }
      // Reset and set up the mock for this test
      jest.clearAllMocks()
      moduleLoginService.fetchLoginDetail$.mockReturnValue(
        of(mockLoginResponse)
      )
      mockLoginDetailIdValue = undefined

      // WHEN the projectId changes
      const newProjectId = 999
      projectIdSubject.next(newProjectId)
      tick()

      // THEN loginDetailId should be updated
      expect(moduleLoginState.updateLoginDetailId).toHaveBeenCalledWith('123')
    }))

    it('should insert project login details when projectLoginId is not set', fakeAsync(() => {
      // GIVEN loginDetailId is not set and projectLoginId is not set
      const mockLoginResponse: ResponseModel = {
        status: 'success',
        data: { detailID: '123' },
        message: 'Success',
      }
      jest.clearAllMocks()
      moduleLoginService.fetchLoginDetail$.mockReturnValue(
        of(mockLoginResponse)
      )
      mockLoginDetailIdValue = undefined
      mockProjectLoginIdValue = undefined

      // WHEN the projectId changes
      const newProjectId = 999
      projectIdSubject.next(newProjectId)
      tick()

      // THEN project login details should be inserted
      expect(moduleLoginService.insertProjectLoginDetails).toHaveBeenCalledWith(
        newProjectId,
        '123'
      )
    }))

    it('should not insert project login details when projectLoginId is already set', fakeAsync(() => {
      // GIVEN loginDetailId is not set but projectLoginId is set
      const mockLoginResponse: ResponseModel = {
        status: 'success',
        data: { detailID: '123' },
        message: 'Success',
      }
      jest.clearAllMocks()
      moduleLoginService.fetchLoginDetail$.mockReturnValue(
        of(mockLoginResponse)
      )
      mockLoginDetailIdValue = undefined
      mockProjectLoginIdValue = 456

      // WHEN the projectId changes
      const newProjectId = 999
      projectIdSubject.next(newProjectId)
      tick()

      // THEN project login details should not be inserted
      expect(
        moduleLoginService.insertProjectLoginDetails
      ).not.toHaveBeenCalled()
    }))

    it('should update projectLoginId when project login details are inserted', fakeAsync(() => {
      // GIVEN loginDetailId is not set, projectLoginId is not set, and insertProjectLoginDetails returns data
      const mockLoginResponse: ResponseModel = {
        status: 'success',
        data: { detailID: '123' },
        message: 'Success',
      }
      const mockInsertResponse: ResponseModel = {
        status: 'success',
        data: 456,
        message: 'Success',
      }
      jest.clearAllMocks()
      moduleLoginService.fetchLoginDetail$.mockReturnValue(
        of(mockLoginResponse)
      )
      moduleLoginService.insertProjectLoginDetails.mockReturnValue(
        of(mockInsertResponse)
      )
      mockLoginDetailIdValue = undefined
      mockProjectLoginIdValue = undefined

      // WHEN the projectId changes
      const newProjectId = 999
      projectIdSubject.next(newProjectId)
      tick()

      // THEN projectLoginId should be updated
      expect(moduleLoginState.updateProjectLoginId).toHaveBeenCalledWith(456)
    }))
  })
})
