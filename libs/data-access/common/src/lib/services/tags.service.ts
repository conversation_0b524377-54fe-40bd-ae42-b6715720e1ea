import { HttpClient } from '@angular/common/http'
import { environment } from '@venio/shared/environments'
import { Injectable } from '@angular/core'
import { Observable } from 'rxjs'
import {
  ResponseModel,
  TagGroupModel,
  TagPropagationSettingModel,
  TagReOrderModel,
  TagsModel,
} from '@venio/shared/models/interfaces'

@Injectable({ providedIn: 'root' })
export class TagsService {
  constructor(private http: HttpClient) {}

  private get _apiUrl(): string {
    return environment.apiUrl
  }

  public fetchTagTree(
    projectId: number,
    reviewSetId: number
  ): Observable<ResponseModel> {
    return this.http.get<ResponseModel>(
      `${this._apiUrl}project/${projectId}/TagTree?groupId=-1&reviewSetId=${reviewSetId}`
    )
  }

  public fetchTag(projectId: number, tagId: number): Observable<ResponseModel> {
    return this.http.get<ResponseModel>(
      `${this._apiUrl}project/${projectId}/Tag/${tagId}`
    )
  }

  public addOrUpdateTag(
    projectId: number,
    payload: TagsModel
  ): Observable<ResponseModel> {
    const isUpdate = payload.tagId > 0
    const url = `${this._apiUrl}project/${projectId}/Tag/${
      ('/' && Number(payload.tagId)) || ''
    }`

    return isUpdate
      ? this.http.put<ResponseModel>(url, payload)
      : this.http.post<ResponseModel>(url, payload)
  }

  public fetchTagGroup(
    projectId: number,
    reviewSetId: number
  ): Observable<ResponseModel> {
    if (reviewSetId > 0)
      return this.http.get<ResponseModel>(
        `${this._apiUrl}/project/${projectId}/reviewset/${reviewSetId}/TagGroup`
      )
    else
      return this.http.get<ResponseModel>(
        `${this._apiUrl}project/${projectId}/TagGroup`
      )
  }

  public addOrUpdateTagGroup(
    projectId: number,
    payload: TagGroupModel
  ): Observable<ResponseModel> {
    const isUpdate = payload.tagGroupId > 0
    const url = `${this._apiUrl}project/${projectId}/TagGroup/${
      ('/' && Number(payload.tagGroupId)) || ''
    }`

    return isUpdate
      ? this.http.put<ResponseModel>(url, payload)
      : this.http.post<ResponseModel>(url, payload)
  }

  public fetchProjectGroup(projectId: number): Observable<ResponseModel> {
    return this.http.get<ResponseModel>(
      `${this._apiUrl}project/${projectId}/Group`
    )
  }

  public deleteTag(
    projectId: number,
    tagId: number,
    isTagGroup = false
  ): Observable<ResponseModel> {
    return this.http.delete<ResponseModel>(
      `${this._apiUrl}project/${projectId}${
        isTagGroup ? '/TagGroup' : '/Tag'
      }/${tagId}`
    )
  }

  public fetchTagPropagationSetting(
    projectId: number
  ): Observable<TagPropagationSettingModel> {
    return this.http.get<TagPropagationSettingModel>(
      `${this._apiUrl}/TagSettings/project/${projectId}`
    )
  }

  public fetchTagTreeWithCount(projectId: number): Observable<ResponseModel> {
    return this.http.post<ResponseModel>(
      `${this._apiUrl}project/${projectId}/tag-tree-with-count`,
      {}
    )
  }

  public saveTagOrder(
    projectId: number,
    tagOrderModel: TagReOrderModel
  ): Observable<ResponseModel> {
    return this.http.post<ResponseModel>(
      `${this._apiUrl}/Project/${projectId}/tagReorder`,
      tagOrderModel
    )
  }
}
