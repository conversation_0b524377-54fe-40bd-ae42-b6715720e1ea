import { EventEmitter, Inject, Injectable, InjectionToken } from '@angular/core'
import { IframeManagerService } from './iframe-manager.service'
import { AppIdentitiesTypes } from '../models/app-identities-types'
import { WINDOW } from './window-token'
import { environment } from '@venio/shared/environments'

/**
 * Message service configuration token.
 */
export const MESSAGE_SERVICE_CONFIG = new InjectionToken<MessageServiceConfig>(
  'MessageServiceConfig'
)

/**
 * High-level message types
 */
export enum MessageType {
  DOCUMENT_READY = 'DOCUMENT_READY',
  ROUTE_CHANGE = 'ROUTE_CHANGE',
  STORE_UPDATE = 'STORE_UPDATE',
  LAYOUT_CHANGE = 'LAYOUT_CHANGE',
  SEARCH_CHANGE = 'SEARCH_CHANGE',
  TOKEN_UPDATE = 'TOKEN_UPDATE',
  AUTH_UPDATE = 'AUTH_UPDATE',
  WINDOW_CHANGE = 'WINDOW_CHANGE',
  PRODUCTION_LAUNCH = 'PRODUCTION_LAUNCH',
  REPORTS = 'REPORTS',
  TIFF_REDACTION_UPDATE = 'TIFF_REDACTION_UPDATE',
  TIFF_PAGE_LOADED = 'TIFF_PAGE_LOADED',
  TIFF_PAGE_NAVIGATION = 'TIFF_PAGE_NAVIGATION',
  REDACTION_UPDATE = 'REDACTION_UPDATE',
  NOTIFY_CHANGE = 'NOTIFY_CHANGE',
  UI_STATE_CHANGE = 'UI_STATE_CHANGE',
  IDP_AUTHENTICATE = 'IDP_AUTHENTICATE',
  // add more generic types; please don't add detail types here as it can be directly send with content
}

export interface MessageContent {
  type: MessageType
  content: any
}

/**
 * Message payload for sharing data between the main app and micro apps.
 */
export interface Message {
  /**
   * Defines the type of the message. Use 'MICRO_APP_DATA_CHANGE' to filter events.
   */
  type?: 'MICRO_APP_DATA_CHANGE' | 'LEGACY'

  /**
   * The payload of the message, which includes the message type and content.
   */
  payload: MessageContent | Array<MessageContent>

  /**
   * Identifies the iframe sending the message (e.g., micro app identity).
   */
  iframeIdentity: AppIdentitiesTypes

  /**
   * Identifies the app that triggered the event.
   */
  eventTriggeredBy: AppIdentitiesTypes

  /**
   * Specifies whether the event was triggered for the parent window or a frame window.
   * It can be either 'PARENT_WINDOW' or 'FRAME_WINDOW'.
   */
  eventTriggeredFor: 'ALL_WINDOW' | 'PARENT_WINDOW' | 'FRAME_WINDOW' | Window
}

/**
 * Configuration options for the Message Service.
 */
export interface MessageServiceConfig {
  /**
   * The origin from which messages are expected. If provided, messages from other origins will be filtered out.
   */
  origin?: string

  /**
   * Identifies the iframe or micro app associated with this configuration.
   */
  iframeIdentity?: AppIdentitiesTypes
}

/**
 * @fileoverview This module provides a messaging service for facilitating communication
 * between a main application and one or more micro-applications loaded in iframes.
 *
 * Usage Example:
 * ```typescript
 * // In AppModule:
 * import { MESSAGE_SERVICE_CONFIG, MessageService,
 * IframeMessengerModule } from '@venio/data-access/iframe-messenger';
 *
 * @NgModule({
 *   imports: [
 *    IframeMessengerModule.forRoot({
 *         origin: '*', // Specify the origin for postMessage security checks
 *         iframeIdentity: 'AppIdentitiesTypes', // Specify the identity of the host app
 *       })
 *   ],
 *   // ... other module configurations
 * })
 * export class AppModule { }
 *
 * // In Components:
 * import { MessageService, MessageType } from './micro-app-config-message.service';  // Adjust the import path
 *
 * @Component({
 *   // ...
 * })
 * export class SomeComponent {
 *   constructor(private messageService: MessageService) { }
 *
 *   sendMessage(): void {
 *     this.messageService.sendMessage('someIframeIdentity', MessageType.ROUTE_CHANGE, { route: '/new-route' });
 *   }
 * }
 * ```
 *
 * The MessageService class encapsulates the message handling logic for communication between
 * the main app and micro apps. It uses the IframeManagerService to manage and find the correct
 * iframe to send messages to based on the iframe's identity.
 * @see IframeMessengerConfigModule
 */
@Injectable({
  providedIn: 'root',
})
export class IframeMessengerService {
  private readonly origin: string // The origin for communication.

  private readonly iframeIdentity: AppIdentitiesTypes // The identity of the iframe.

  /**
   * Event emitter for handling received messages.
   */
  public readonly messageReceived: EventEmitter<Message> = new EventEmitter()

  /**
   * Creates an instance of the IframeMessengerService.
   * @param {WINDOW} windowRef - Reference to the window object.
   * @param {IframeManagerService} iframeManagerService - Service for managing iframes.
   * @param {MessageServiceConfig} config - Configuration options for the message service.
   */
  constructor(
    @Inject(WINDOW) private windowRef: Window,
    private iframeManagerService: IframeManagerService,
    @Inject(MESSAGE_SERVICE_CONFIG) config: MessageServiceConfig
  ) {
    this.origin = config.origin || environment.allowedOrigin
    this.iframeIdentity =
      config.iframeIdentity || ('unknown' as AppIdentitiesTypes) // Default identity to 'unknown' if not provided.

    // Attach a listener to the window's onmessage event to handle incoming messages.
    this.windowRef?.addEventListener('message', (event: MessageEvent): void =>
      this.messageReceived.emit(event.data)
    )
  }

  /**
   * Sends a message to the parent window or a specific iframe.
   * @param {Message} payload - The message payload to be sent.
   * @param {Transferable[]} transfer - An optional array of transferable objects for efficient data transfer.
   * @returns {void}
   */
  public sendMessage(payload: Message, transfer?: Transferable[]): void {
    // if not provided, default is 'MICRO_APP_DATA_CHANGE'
    payload.type = payload.type || 'MICRO_APP_DATA_CHANGE'

    if (!this.windowRef) return

    if (payload.type !== 'MICRO_APP_DATA_CHANGE') return

    if (payload.eventTriggeredFor === 'ALL_WINDOW') {
      // Send the message to all windows.
      this.windowRef.postMessage(payload, this.origin, transfer)
      this.windowRef.opener?.postMessage(payload, this.origin, transfer)
      return
    }

    if (payload.eventTriggeredFor === 'PARENT_WINDOW') {
      // Send the message to the parent window.
      this.windowRef.parent.postMessage(payload, this.origin, transfer)
      return
    }

    if (typeof payload.eventTriggeredFor === 'object') {
      // Send the message to the provided window.
      payload.eventTriggeredFor.postMessage(
        {
          ...payload,
          // now, we cannot send the window object as it is not serializable
          eventTriggeredFor: '',
        },
        this.origin,
        transfer
      )
      return
    }

    // Get the target window associated with the provided iframe identity.
    const targetWindow = this.iframeManagerService.getIframeWindow(
      payload.iframeIdentity
    )

    if (targetWindow) {
      // Send the message to the target iframe's window.
      targetWindow.postMessage(payload, this.origin)
    } else {
      console.error(
        `Iframe with identity ${payload.iframeIdentity} not found. Ensure the iframe is available in the DOM.`
      )
    }
  }
}
