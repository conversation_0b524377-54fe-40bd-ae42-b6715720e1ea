import { createAction, props } from '@ngrx/store'
import {
  MessageContent,
  MessageType,
} from '../services/iframe-messenger.service'

export const resetMessengerStateAction = createAction(
  '[Iframe Messenger] Reset Messenger State',
  props<{
    payload: MessageType | Array<MessageType>
  }>()
)

/**
 * Action to update an iframe or parent window post message to the store
 */
export const updateMessageContentAction = createAction(
  '[Iframe Messenger] Update The Messages',
  props<{
    payload: MessageContent | MessageContent[]
  }>()
)
export const loadedAsMicroAppAction = createAction(
  '[Iframe Messenger] Loaded As Micro App',
  props<{
    payload: { loadedAsMicroApp: boolean }
  }>()
)
