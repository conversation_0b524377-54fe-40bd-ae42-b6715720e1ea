import {
  MemoizedSelector,
  createFeatureSelector,
  createSelector,
} from '@ngrx/store'
import {
  RESPONSIVE_PST_FEATURE_KEY,
  ResponsivePstState,
} from './responsive-pst.reducer'

export const getResponsivePstState = createFeatureSelector<ResponsivePstState>(
  RESPONSIVE_PST_FEATURE_KEY
)

export const getStateOfResponsivePstState = <
  T extends keyof ResponsivePstState
>(
  stateKey: T
): MemoizedSelector<object, ResponsivePstState[T], unknown> =>
  createSelector(
    getResponsivePstState,
    (state: ResponsivePstState) => state[stateKey]
  )
