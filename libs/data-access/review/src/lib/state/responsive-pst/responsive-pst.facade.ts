import { Injectable } from '@angular/core'
import { Store, select } from '@ngrx/store'
import {
  CreatePSTRequest,
  ListFilesForPstRequest,
} from '../../models/interfaces/responsive-pst.model'
import * as responsivePstActions from './responsive-pst.actions'
import * as responsivePstSelectors from './responsive-pst.selectors'
import { ResponsivePstState } from './responsive-pst.reducer'

type ResponsivePstStateKeys =
  | keyof ResponsivePstState
  | Array<keyof ResponsivePstState>

@Injectable()
export class ResponsivePstFacade {
  constructor(private readonly store: Store) {}

  public getFilesForResponsivePst(
    responsivePstRequest: ListFilesForPstRequest,
    projectId: number
  ): void {
    this.store.dispatch(
      responsivePstActions.getFilesForResponsivePst({
        responsivePstRequest,
        projectId,
      })
    )
  }

  public getResponsivePstFiles$ = this.store.pipe(
    select(
      responsivePstSelectors.getStateOfResponsivePstState('responsivePstFiles')
    )
  )

  public getFilesForPstFailureResponse$ = this.store.pipe(
    select(
      responsivePstSelectors.getStateOfResponsivePstState(
        'getFilesForPstFailureResponse'
      )
    )
  )

  public getIsGettingFilesForResponsivePst$ = this.store.pipe(
    select(
      responsivePstSelectors.getStateOfResponsivePstState(
        'isGettingFilesForResponsivePst'
      )
    )
  )

  public getErrorFilesCSV(
    responsivePstRequest: ListFilesForPstRequest,
    projectId: number
  ): void {
    this.store.dispatch(
      responsivePstActions.getErrorFilesCSV({ responsivePstRequest, projectId })
    )
  }

  public getDownloadExcludedFileInfoSuccessResponse$ = this.store.pipe(
    select(
      responsivePstSelectors.getStateOfResponsivePstState(
        'downloadExcludedFileInfoSuccessResponse'
      )
    )
  )

  public getDownloadExcludedFileInfoFailureResponse$ = this.store.pipe(
    select(
      responsivePstSelectors.getStateOfResponsivePstState(
        'downloadExcludedFileInfoFailureResponse'
      )
    )
  )

  public createPST(
    createPSTRequest: CreatePSTRequest,
    projectId: number
  ): void {
    this.store.dispatch(
      responsivePstActions.createPST({
        createPSTRequest,
        projectId,
      })
    )
  }

  public getCreatePstSuceessResponse$ = this.store.pipe(
    select(
      responsivePstSelectors.getStateOfResponsivePstState(
        'createPstSuccessResponse'
      )
    )
  )

  public getCreatePstFailureResponse$ = this.store.pipe(
    select(
      responsivePstSelectors.getStateOfResponsivePstState(
        'createPstFailureResponse'
      )
    )
  )

  public getIsResponsivePstCreating$ = this.store.pipe(
    select(
      responsivePstSelectors.getStateOfResponsivePstState(
        'isResponsivePstCreating'
      )
    )
  )

  public resetResponsivePstState(stateKey: ResponsivePstStateKeys): void {
    this.store.dispatch(
      responsivePstActions.resetResponsivePstState({ stateKey })
    )
  }

  public getPSTFiles(projectId: number): void {
    this.store.dispatch(
      responsivePstActions.getPSTFiles({
        projectId,
      })
    )
  }

  public getPSTStatusSuccessResponse$ = this.store.pipe(
    select(
      responsivePstSelectors.getStateOfResponsivePstState(
        'getPSTStatusSuccessResponse'
      )
    )
  )

  public getPSTStatusFailureResponse$ = this.store.pipe(
    select(
      responsivePstSelectors.getStateOfResponsivePstState(
        'getPSTStatusFailureResponse'
      )
    )
  )

  public deletePST(pstJobId: number, projectId: number): void {
    this.store.dispatch(responsivePstActions.deletePST({ pstJobId, projectId }))
  }

  public deletePstSuccessResponse$ = this.store.pipe(
    select(
      responsivePstSelectors.getStateOfResponsivePstState(
        'deletePstSuccessResponse'
      )
    )
  )

  public deletePstFailureResponse$ = this.store.pipe(
    select(
      responsivePstSelectors.getStateOfResponsivePstState(
        'deletePstFailureResponse'
      )
    )
  )

  public getIsDeletingPst$ = this.store.pipe(
    select(responsivePstSelectors.getStateOfResponsivePstState('isDeletingPst'))
  )

  public downloadPSTInfo(
    pstJobId: number,
    projectId: number,
    fileType: string
  ): void {
    this.store.dispatch(
      responsivePstActions.downloadPSTInfo({ pstJobId, projectId, fileType })
    )
  }

  public downloadPstInfoSuccessResponse$ = this.store.pipe(
    select(
      responsivePstSelectors.getStateOfResponsivePstState(
        'downloadPstInfoSuccessResponse'
      )
    )
  )

  public downloadPstInfoFailureResponse$ = this.store.pipe(
    select(
      responsivePstSelectors.getStateOfResponsivePstState(
        'downloadPstInfoFailureResponse'
      )
    )
  )

  public getIsDownloadingPstInfo$ = this.store.pipe(
    select(
      responsivePstSelectors.getStateOfResponsivePstState(
        'isDownloadingPstInfo'
      )
    )
  )
}
