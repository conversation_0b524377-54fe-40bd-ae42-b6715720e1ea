import { Action, createReducer, on } from '@ngrx/store'
import * as batchReviewActions from './batch-review.actions'
import { BatchModel } from '../../models/interfaces/batch-review.model'

export const BATCH_REVIEW_FEATURE_KEY = 'venioBatchReview'

export interface BatchReviewState {
  selectedReviewSetBatchInfo: BatchModel
}

const batchReviewInitialState: BatchReviewState = {
  selectedReviewSetBatchInfo: null,
}

const _reducer = createReducer(
  batchReviewInitialState,
  on(
    batchReviewActions.fetchSelectedReviewBatchInfoSuccess,
    (state, { batchModel }) => ({
      ...state,
      selectedReviewSetBatchInfo: batchModel,
    })
  )
)

export function batchReviewReducer(
  state: BatchReviewState,
  action: Action
): BatchReviewState {
  return _reducer(state, action)
}
