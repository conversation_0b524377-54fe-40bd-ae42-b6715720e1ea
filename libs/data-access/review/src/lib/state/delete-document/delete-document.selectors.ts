import {
  createFeatureSelector,
  createSelector,
  MemoizedSelector,
} from '@ngrx/store'
import {
  DeleteDocumentState,
  DELETE_DOCUMENT_FEATURE_KEY,
} from './delete-document.reducer'

export const getDeleteDocumentState =
  createFeatureSelector<DeleteDocumentState>(DELETE_DOCUMENT_FEATURE_KEY)

export const getStateOfDeleteDocument = <T extends keyof DeleteDocumentState>(
  stateKey: T
): MemoizedSelector<object, DeleteDocumentState[T], unknown> =>
  createSelector(
    getDeleteDocumentState,
    (state: DeleteDocumentState) => state[stateKey]
  )

export const getCurrentDeleteOption = createSelector(
  getStateOfDeleteDocument('currentDeleteOption'),
  (deleteDocumentSummary) => {
    return deleteDocumentSummary
  }
)
