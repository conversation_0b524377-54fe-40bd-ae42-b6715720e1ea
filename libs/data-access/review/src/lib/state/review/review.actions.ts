import { createAction, props } from '@ngrx/store'
import { EmailThreadVisibleType, ReviewViewType } from '../../models/constants'
import {
  InclusiveEmailResponseModel,
  InclusiveEmailStatus,
} from '../../models/interfaces/inclusive-email.model'

/**
 * Set the selected review view type
 */
export const setReviewViewType = createAction(
  '[Review] Set Review View Type',
  props<{
    viewType: ReviewViewType
  }>()
)

/**
 * Get the status of email thread view like 'Email thread not populated', 'No email files' and 'Some media do not have email thread populated'
 */
export const fetchEmailThreadViewStatus = createAction(
  '[Review] Get Email Thread View Status'
)

export const fetchEmailThreadViewStatusSuccess = createAction(
  '[Review] Get Email Thread View Status Success',
  props<{
    emailThreadViewStatus: string
  }>()
)

export const fetchInclusiveEmailStatus = createAction(
  '[Review] Fetch Inclusive Email Status'
)

export const fetchInclusiveEmailStatusSuccess = createAction(
  '[Review] Fetch Inclusive Email Status Success',
  props<{
    inclusiveEmailStatus: InclusiveEmailStatus
  }>()
)

export const resetInclusiveEmailStatus = createAction(
  '[Review] Reset Inclusive Email Status'
)

export const resetEmailThreadViewStatus = createAction(
  '[Review] Reset Email Thread View Status'
)

export const isSwitchingView = createAction(
  '[Review] Is Switching View',
  props<{
    isSwitchingView: boolean
  }>()
)

export const setExpandedEmailThreads = createAction(
  '[Review] Set Expanded Email Threads',
  props<{
    expandedFileIds: number[]
  }>()
)

export const prepareInclusiveEmailDataAction = createAction(
  '[Review] Prepare Inclusive Email Data'
)

export const setInclusiveEmailResponseModel = createAction(
  '[Review] Set Inclusive Email Response Model',
  props<{
    inclusiveEmailResponseModel: InclusiveEmailResponseModel
  }>()
)

export const setVisibleEmailType = createAction(
  '[Review] Set Visible Email Type',
  props<{
    visibleEmailType: EmailThreadVisibleType
  }>()
)
