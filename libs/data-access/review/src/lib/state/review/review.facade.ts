import { Injectable, inject } from '@angular/core'
import { select, Store } from '@ngrx/store'

import * as ReviewActions from './review.actions'
import * as ReviewSelectors from './review.selectors'
import { EmailThreadVisibleType, ReviewViewType } from '../../models/constants'

@Injectable()
export class ReviewFacade {
  private readonly store = inject(Store)

  public getReviewViewType$ = this.store.pipe(
    select(ReviewSelectors.getStateOfReview('reviewViewType'))
  )

  public getExpandedEmailThreads$ = this.store.pipe(
    select(ReviewSelectors.getStateOfReview('expandedEmailThreads'))
  )

  public getEmailThreadViewStatus$ = this.store.pipe(
    select(ReviewSelectors.getStateOfReview('emailThreadViewStatus'))
  )

  public getIsSwitchingView$ = this.store.pipe(
    select(ReviewSelectors.getStateOfReview('isSwitchingView'))
  )

  public getInclusiveEmailResponseModel$ = this.store.pipe(
    select(ReviewSelectors.getStateOfReview('inclusiveEmailResponseModel'))
  )

  public getVisibleEmailType$ = this.store.pipe(
    select(ReviewSelectors.getStateOfReview('visibleEmailType'))
  )

  public getInclusiveEmailStatus$ = this.store.pipe(
    select(ReviewSelectors.getStateOfReview('inclusiveEmailStatus'))
  )

  public setVisibleEmailType(visibleEmailType: EmailThreadVisibleType): void {
    this.store.dispatch(ReviewActions.setVisibleEmailType({ visibleEmailType }))
  }

  public setReviewViewType(viewType: ReviewViewType): void {
    // set to show all email threads before switching view (if it is set to show only inclusive email threads)
    this.store.dispatch(
      ReviewActions.setVisibleEmailType({
        visibleEmailType: EmailThreadVisibleType.All,
      })
    )
    // set the view type
    this.store.dispatch(ReviewActions.setReviewViewType({ viewType }))
  }

  public setExpandedEmailThreads(expandedFileIds: number[]): void {
    this.store.dispatch(
      ReviewActions.setExpandedEmailThreads({ expandedFileIds })
    )
  }

  public resetEmailThreadViewStatus(): void {
    this.store.dispatch(ReviewActions.resetEmailThreadViewStatus())
  }

  public fetchEmailThreadViewStatus(): void {
    this.resetEmailThreadViewStatus()
    this.store.dispatch(ReviewActions.fetchEmailThreadViewStatus())
  }

  public resetInclusiveEmailStatus(): void {
    this.store.dispatch(ReviewActions.resetInclusiveEmailStatus())
  }

  public fetchInclusiveEmailStatus(): void {
    this.resetInclusiveEmailStatus()
    this.store.dispatch(ReviewActions.fetchInclusiveEmailStatus())
  }

  public isSwitchingView(isSwitchingView: boolean): void {
    this.store.dispatch(ReviewActions.isSwitchingView({ isSwitchingView }))
  }

  public prepareInclusiveEmailDataAction(): void {
    this.store.dispatch(ReviewActions.prepareInclusiveEmailDataAction())
  }
}
