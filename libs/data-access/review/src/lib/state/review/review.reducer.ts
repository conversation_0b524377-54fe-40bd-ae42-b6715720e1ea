import { createReducer, on } from '@ngrx/store'
import { EmailThreadVisibleType, ReviewViewType } from '../../models/constants'
import * as reviewAction from './review.actions'
import {
  InclusiveEmailResponseModel,
  InclusiveEmailStatus,
} from '../../models/interfaces/inclusive-email.model'

export const REVIEW_FEATURE_KEY = 'review'

export interface ReviewState {
  isSwitchingView: boolean
  reviewViewType: ReviewViewType
  emailThreadViewStatus: string
  expandedEmailThreads: number[]
  inclusiveEmailResponseModel: InclusiveEmailResponseModel
  visibleEmailType: EmailThreadVisibleType
  inclusiveEmailStatus: InclusiveEmailStatus | null
}

export const initialReviewState: ReviewState = {
  isSwitchingView: false,
  reviewViewType: ReviewViewType.Search,
  emailThreadViewStatus: '',
  expandedEmailThreads: [],
  inclusiveEmailResponseModel: null,
  visibleEmailType: EmailThreadVisibleType.All,
  inclusiveEmailStatus: null,
}

export const ReviewReducer = createReducer(
  initialReviewState,
  on(reviewAction.setReviewViewType, (state, { viewType }) => ({
    ...state,
    reviewViewType: viewType,
  })),
  on(
    reviewAction.fetchEmailThreadViewStatusSuccess,
    (state, { emailThreadViewStatus }) => ({
      ...state,
      emailThreadViewStatus,
    })
  ),
  on(reviewAction.isSwitchingView, (state, { isSwitchingView }) => ({
    ...state,
    isSwitchingView,
  })),
  on(reviewAction.resetEmailThreadViewStatus, (state) => ({
    ...state,
    emailThreadViewStatus: '',
  })),
  on(
    reviewAction.setExpandedEmailThreads,
    (state, { expandedFileIds: expandedEmailThreads }) => ({
      ...state,
      expandedEmailThreads,
    })
  ),
  on(
    reviewAction.setInclusiveEmailResponseModel,
    (state, { inclusiveEmailResponseModel }) => ({
      ...state,
      inclusiveEmailResponseModel: {
        inclusiveEmailTempTable:
          inclusiveEmailResponseModel.inclusiveEmailTempTable,
        inclusiveEmailCount: inclusiveEmailResponseModel.inclusiveEmailCount,
      },
    })
  ),
  on(reviewAction.setVisibleEmailType, (state, { visibleEmailType }) => ({
    ...state,
    visibleEmailType: visibleEmailType,
  })),
  on(
    reviewAction.fetchInclusiveEmailStatusSuccess,
    (state, { inclusiveEmailStatus }) => ({
      ...state,
      inclusiveEmailStatus,
    })
  )
)
