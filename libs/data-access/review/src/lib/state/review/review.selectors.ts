import {
  createFeatureSelector,
  createSelector,
  MemoizedSelector,
} from '@ngrx/store'
import { ReviewState, REVIEW_FEATURE_KEY } from './review.reducer'

export const getReviewState =
  createFeatureSelector<ReviewState>(REVIEW_FEATURE_KEY)

export const getStateOfReview = <T extends keyof ReviewState>(
  stateKey: T
): MemoizedSelector<object, ReviewState[T], unknown> =>
  createSelector(getReviewState, (state: ReviewState) => state[stateKey])
