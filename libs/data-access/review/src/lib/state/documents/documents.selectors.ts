import {
  createFeatureSelector,
  createSelector,
  MemoizedSelector,
} from '@ngrx/store'
import { DocumentsState, DOCUMENTS_FEATURE_KEY } from './documents.reducer'

export const getDocumentsState = createFeatureSelector<DocumentsState>(
  DOCUMENTS_FEATURE_KEY
)

export const getStateOfDocumentsState = <T extends keyof DocumentsState>(
  stateKey: T
): MemoizedSelector<object, DocumentsState[T], unknown> =>
  createSelector(getDocumentsState, (state: DocumentsState) => state[stateKey])

export const selectDocumentPayloadData = createSelector(
  getStateOfDocumentsState('selectedDocuments'),
  getStateOfDocumentsState('unselectedDocuments'),
  getStateOfDocumentsState('isBatchSelected'),
  getStateOfDocumentsState('isBulkDocument'),
  (selectedDocuments, unselectedDocuments, isBatchSelected, isBulkDocument) => {
    const documentPayloadData = {
      selectedDocuments,
      unselectedDocuments,
      isBatchSelected,
      isBulkDocument,
    }

    return documentPayloadData
  }
)
