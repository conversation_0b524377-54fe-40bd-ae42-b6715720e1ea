import { createAction, props } from '@ngrx/store'
import { DocumentMenuType } from '@venio/shared/models/constants'
import { DocumentsState } from './documents.reducer'
import {
  DocumentTableUpdateModel,
  DocumentViewerLogModel,
  TagFolderHistoryResponseModel,
  TagSummaryModel,
} from '../../models/interfaces'
import { ResponseModel } from '@venio/shared/models/interfaces'

export const resetDocumentState = createAction(
  '[Documents] Reset Document State By Key(s)',
  props<{
    stateKey: keyof DocumentsState | Array<keyof DocumentsState>
  }>()
)

export const setCurrentDocument = createAction(
  '[Documents] Set Current Document',
  props<{
    payload: {
      documentId: number
    }
  }>()
)

export const setCurrentDocumentDetails = createAction(
  '[Documents] Set Current Document Details ',
  props<{
    payload: {
      currentDocument: number
      currentFileName: string
    }
  }>()
)

export const setSelectedDocuments = createAction(
  '[Documents] Set Selected Documents',
  props<{ payload: { selectedDocuments: number[] } }>()
)

export const addToSelectedDocuments = createAction(
  '[Documents] Add To Selected Documents',
  props<{ payload: { documentIds: number[] } }>()
)

export const removeFromSelectedDocuments = createAction(
  '[Documents] Remove From Selected Documents',
  props<{ payload: { documentIds: number[] } }>()
)

export const setUnSelectedDocuments = createAction(
  '[Document] Set Unselected Documents',
  props<{ payload: { unselectedDocuments: number[] } }>()
)

export const setIsBatchSelection = createAction(
  '[Document] Set Is Batch Selection',
  props<{ payload: { isBatchSelection: boolean } }>()
)

export const navigateDocumentByFileId = createAction(
  '[Document] Navigate Document By FileId',
  props<{
    payload: {
      currentDocumentId: number
      documentIds: number[]
      selectedDocumentIds: number[]
      resetSelection: boolean
    }
  }>()
)

export const moveToFirstDocument = createAction(
  '[Document] Move To First Document'
)

export const moveToNextDocument = createAction(
  '[Document] Move To Next Document',
  props<{
    payload: {
      documentIds: number[]
      currentDocumentId: number
    }
  }>()
)

export const moveToPreviousDocument = createAction(
  '[Document] Move To Previous Document',
  props<{
    payload: {
      documentIds: number[]
      currentDocumentId: number
    }
  }>()
)

export const moveToLastDocument = createAction(
  '[Document] Move To Last Document'
)

export const navigateDocumentByNumber = createAction(
  '[Document] Navigate Document By Number',
  props<{
    payload: {
      seqNo: number
    }
  }>()
)

export const navigateEmailThreadByNumber = createAction(
  '[Document] Navigate Email Thread By Number',
  props<{
    payload: {
      seqNo: number
    }
  }>()
)

export const moveToNextDocumentTablePage = createAction(
  '[Document] Move To Next Document Table Page',
  props<{ payload: { resetSelectionItem: string } }>()
)

export const moveBackDocumentTablePage = createAction(
  '[Document] Move Back To Previous Document Table Page',
  props<{ payload: { resetSelectionItem: string } }>()
)

export const setCurrentDocumentTablePageNumber = createAction(
  '[Document] Set Current Document Table Page',
  props<{
    payload: {
      pageNumber: number
      resetSelectionItem?: string
      paginatedDocumentIndex?: number
    }
  }>()
)

export const updateCurrentDocumentTablePageNumber = createAction(
  '[Document] Update Current Document Table Page',
  props<{
    payload: {
      pageNumber: number
      resetSelectionItem?: string
      paginatedDocumentIndex?: number
    }
  }>()
)

export const documentMenuEvent = createAction(
  '[Document] Menu Event',
  props<{
    payload: { menuEventPayload: DocumentMenuType }
  }>()
)

export const downloadCSVReviewDocument = createAction(
  '[Document] Download CSV ReviewDocument'
)

export const isBulkDocument = createAction(
  '[Document] Is Bulk Document',
  props<{
    isBulkDocument: boolean
  }>()
)

export const fetchTagSummary = createAction('[Document] Fetch Tag Summary')

export const setTagSummary = createAction(
  '[Document] Set Tag Summary',
  props<{ tagSummary: TagSummaryModel }>()
)

export const fetchTagSummaryFailure = createAction(
  '[Document] Fetch Tag Summary Failure',
  props<{ tagSummaryErrorResponse: ResponseModel }>()
)

export const fetchTagHistory = createAction('[Document] Fetch Tag History')

export const setTagHistory = createAction(
  '[Document] Set Tag History',
  props<{ tagHistory: TagFolderHistoryResponseModel[] }>()
)

export const fetchTagHistoryFailure = createAction(
  '[Document] Fetch Tag History Failure',
  props<{ tagHistoryErrorResponse: ResponseModel }>()
)

export const fetchSelectedFileDocumentTableFolders = createAction(
  '[Document] Fetch Selected File Document Table Folders',
  props<{ selectedFileIds: number[] }>()
)

export const setDocumentTableUpdateUIData = createAction(
  '[Document] Set Document Table Update UI Data',
  props<{ documentTableUpdatingData: DocumentTableUpdateModel[] }>()
)

export const fetchSelectedFolderSummaryFailure = createAction(
  '[Document] Fetch Folder Summary Failure',
  props<{ folderSummaryErrorResponse: ResponseModel }>()
)

export const saveDocumentViewerLog = createAction(
  '[Document] Save Document Viewer Log',
  props<{ projectId: number; documentViewerLogModel: DocumentViewerLogModel }>()
)

export const saveDocumentViewerLogSuccess = createAction(
  '[Document] Save Document Viewer Log Success',
  props<{ saveDocumentViewerLogSuccessResponse: ResponseModel }>()
)

export const saveDocumentViewerLogFailure = createAction(
  '[Document] Save Document Viewer Log Failure',
  props<{ saveDocumentViewerLogFailureResponse: ResponseModel }>()
)
