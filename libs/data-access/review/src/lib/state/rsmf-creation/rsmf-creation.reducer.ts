import { Action, createReducer, on } from '@ngrx/store'
import * as fromRSMFAction from './rsmf-creation.actions'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { resetStateProperty } from '@venio/util/utilities'

export const RSMF_FEATURE_KEY = 'venioRSMF'

export interface RSMFState {
  isCreatingRSMF: boolean | null
  createRSMFSuccessResponse: ResponseModel | null
  createRSMFFailureResponse: ResponseModel | null

  isDownloadingRSMFInfo: boolean | null
  downloadRSMFInfoSuccessResponse: ResponseModel | null
  downloadRSMFInfoFailureResponse: ResponseModel | null

  isFetchingRSMFStatus: boolean | null
  fetchRSMFStatusSuccessResponse: ResponseModel | null
  fetchRSMFStatusFailureResponse: ResponseModel | null

  isFetchingRSMFStatusDetail: boolean | null
  fetchRSMFStatusDetailSuccessResponse: ResponseModel | null
  fetchRSMFStatusDetailFailureResponse: ResponseModel | null

  isFetchingRSMFieldMapping: boolean | null
  fetchRSMFieldMappingSuccessResponse: ResponseModel | null
  fetchRSMFieldMappingFailureResponse: ResponseModel | null

  isDeletingRSMF: boolean | null
  deleteRSMFSuccessResponse: ResponseModel | null
  deleteRSMFFailureResponse: ResponseModel | null
}

export const rsmfInitialState: RSMFState = {
  isCreatingRSMF: null,
  createRSMFSuccessResponse: null,
  createRSMFFailureResponse: null,

  isDownloadingRSMFInfo: null,
  downloadRSMFInfoSuccessResponse: null,
  downloadRSMFInfoFailureResponse: null,

  isFetchingRSMFStatus: null,
  fetchRSMFStatusSuccessResponse: null,
  fetchRSMFStatusFailureResponse: null,

  isFetchingRSMFStatusDetail: null,
  fetchRSMFStatusDetailSuccessResponse: null,
  fetchRSMFStatusDetailFailureResponse: null,

  isFetchingRSMFieldMapping: null,
  fetchRSMFieldMappingSuccessResponse: null,
  fetchRSMFieldMappingFailureResponse: null,

  isDeletingRSMF: null,
  deleteRSMFSuccessResponse: null,
  deleteRSMFFailureResponse: null,
}

const _reducer = createReducer(
  rsmfInitialState,
  on(fromRSMFAction.resetRSMFCreationState, (state, { stateKey }) =>
    resetStateProperty<RSMFState>(state, rsmfInitialState, stateKey)
  ),
  on(fromRSMFAction.createRSMF, (state) => ({
    ...state,
    isCreatingRSMF: true,
  })),
  on(fromRSMFAction.createRSMFSuccess, (state, { responseModel }) => ({
    ...state,
    isCreatingRSMF: false,
    createRSMFSuccessResponse: responseModel,
  })),
  on(fromRSMFAction.createRSMFFailure, (state, { responseModel }) => ({
    ...state,
    isCreatingRSMF: false,
    createRSMFFailureResponse: responseModel,
  })),
  on(fromRSMFAction.downloadRSMFfInfo, (state) => ({
    ...state,
    isDownloadingRSMFInfo: true,
  })),
  on(fromRSMFAction.downloadRSMFInfoSuccess, (state, { responseModel }) => ({
    ...state,
    isDownloadingRSMFInfo: false,
    downloadRSMFInfoSuccessResponse: responseModel,
  })),
  on(fromRSMFAction.downloadRSMFInfoFailure, (state, { responseModel }) => ({
    ...state,
    isDownloadingRSMFInfo: false,
    downloadRSMFInfoFailureResponse: responseModel,
  })),
  on(fromRSMFAction.fetchRSMFStatus, (state) => ({
    ...state,
    isFetchingRSMFStatus: true,
  })),
  on(fromRSMFAction.fetchRSMFStatusSuccess, (state, { responseModel }) => ({
    ...state,
    isFetchingRSMFStatus: false,
    fetchRSMFStatusSuccessResponse: responseModel,
  })),
  on(fromRSMFAction.fetchRSMFStatusFailure, (state, { responseModel }) => ({
    ...state,
    isFetchingRSMFStatus: false,
    fetchRSMFStatusFailureResponse: responseModel,
  })),
  on(fromRSMFAction.fetchRSMFStatusDetail, (state) => ({
    ...state,
    isFetchingRSMFStatusDetail: true,
  })),
  on(
    fromRSMFAction.fetchRSMFStatusDetailSuccess,
    (state, { responseModel }) => ({
      ...state,
      isFetchingRSMFStatusDetail: false,
      fetchRSMFStatusDetailSuccessResponse: responseModel,
    })
  ),
  on(
    fromRSMFAction.fetchRSMFStatusDetailFailure,
    (state, { responseModel }) => ({
      ...state,
      isFetchingRSMFStatusDetail: false,
      fetchRSMFStatusDetailFailureResponse: responseModel,
    })
  ),
  on(fromRSMFAction.fetchRSMFieldMapping, (state) => ({
    ...state,
    isFetchingRSMFieldMapping: true,
  })),
  on(
    fromRSMFAction.fetchRSMFieldMappingSuccess,
    (state, { responseModel }) => ({
      ...state,
      isFetchingRSMFieldMapping: false,
      fetchRSMFieldMappingSuccessResponse: responseModel,
    })
  ),
  on(
    fromRSMFAction.fetchRSMFieldMappingFailure,
    (state, { responseModel }) => ({
      ...state,
      isFetchingRSMFieldMapping: false,
      fetchRSMFieldMappingFailureResponse: responseModel,
    })
  ),
  on(fromRSMFAction.deleteRSMF, (state) => ({
    ...state,
    isDeletingRSMF: true,
  })),
  on(fromRSMFAction.deleteRSMFSuccess, (state, { responseModel }) => ({
    ...state,
    isDeletingRSMF: false,
    deleteRSMFSuccessResponse: responseModel,
  })),
  on(fromRSMFAction.deleteRSMFFailure, (state, { responseModel }) => ({
    ...state,
    isDeletingRSMF: false,
    deleteRSMFFailureResponse: responseModel,
  }))
)

export function rsmfCreationReducer(
  state: RSMFState,
  action: Action
): RSMFState {
  return _reducer(state, action)
}
