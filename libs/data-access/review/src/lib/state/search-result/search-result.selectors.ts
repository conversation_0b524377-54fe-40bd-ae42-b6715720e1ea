import { Dictionary } from '@ngrx/entity'
import {
  createFeatureSelector,
  createSelector,
  MemoizedSelector,
} from '@ngrx/store'
import {
  SearchDocumentMetadata,
  SearchResponseData,
  SeqNoInfo,
} from '../../models/interfaces/search-result.model'
import {
  searchResultEntityAdapter,
  SearchResultEntityState,
  SearchResultMetadataState,
  SearchResultState,
  SEARCH_RESULT_FEATURE_KEY,
} from './search-result.reducer'
import { NativePrefetchDocumentModel } from '@venio/shared/models/interfaces'

export const getSearchResultState = createFeatureSelector<SearchResultState>(
  SEARCH_RESULT_FEATURE_KEY
)

export const getStateOfSearchResult = <T extends keyof SearchResultState>(
  stateKey: T
): MemoizedSelector<object, SearchResultState[T], unknown> =>
  createSelector(
    getSearchResultState,
    (state: SearchResultState) => state[stateKey]
  )

export const {
  selectAll: selectAllSearchResults,
  selectEntities: selectSearchResultEntities,
  selectIds: selectSearchResultFileIds,
  selectTotal: selectSearchResultCount,
} = searchResultEntityAdapter.getSelectors(
  getStateOfSearchResult('searchResults')
)

export const getSearchResultByFileId = (
  id: number
): MemoizedSelector<object, SearchResponseData, unknown> =>
  createSelector(
    selectSearchResultEntities,
    (entities: Dictionary<SearchResponseData>) => entities[id]
  )

export const getSearchResultByFileIds = (
  ids: number[]
): MemoizedSelector<object, SearchResponseData[], unknown> =>
  createSelector(
    selectSearchResultEntities,
    (entities: Dictionary<SearchResponseData>) => {
      const result: SearchResponseData[] = []
      for (const id of ids) {
        result.push(entities[id])
      }
      return result
    }
  )

export const getSeqNoToFileIdMap = createSelector(
  selectAllSearchResults,
  (searchResults: SearchResponseData[]): Dictionary<SeqNoInfo> => {
    const seqNoToFileIdMap: Dictionary<SeqNoInfo> = {}
    searchResults.forEach((result, index) => {
      const seqNoMetadata = result.metadata.find(
        (meta) => meta.key === 'seq_no'
      )
      if (seqNoMetadata) {
        seqNoToFileIdMap[seqNoMetadata.value] = {
          fileId: result.fileId,
          index: index,
        }
      }
    })
    return seqNoToFileIdMap
  }
)

export const getSearchResultFieldValuesByFileId = (
  id: number
): MemoizedSelector<object, SearchDocumentMetadata[], unknown> =>
  createSelector(
    getSearchResultByFileId(id),
    (searchResult: SearchResponseData) => {
      return searchResult.metadata
    }
  )
export const getIsDocumentReviewedByFileId = (
  id: number
): MemoizedSelector<object, boolean, unknown> =>
  createSelector(
    getSearchResultByFileId(id),
    (searchResult: SearchResponseData) => {
      return searchResult?.metadata?.find((d) => d.key === '__isReviewed')
        ?.value === 'Yes'
        ? true
        : false
    }
  )

export const getSearchResultFieldValues = createSelector(
  selectAllSearchResults,
  (searchResults: SearchResponseData[]) => {
    const fieldValuesArray = []
    const metadataArray = searchResults.map((result) => result.metadata)
    for (const metadata of metadataArray) {
      const fieldValues = {}
      for (const keyValueObject of metadata) {
        fieldValues[keyValueObject.key] = keyValueObject.value
      }
      fieldValuesArray.push(fieldValues)
    }
    return fieldValuesArray
  }
)

export const getSearchResultNormalizedMetadata = createSelector(
  getStateOfSearchResult('searchResults'),
  (searchResultState: SearchResultEntityState) => {
    return searchResultState.normalizedMetadata
  }
)

export const getSearchResultFieldNamesByFileId = (
  id: number
): MemoizedSelector<object, any, unknown> =>
  createSelector(getSearchResultNormalizedMetadata, (normalizedMetadata) => {
    if (!normalizedMetadata) {
      return []
    }
    const metadataState: SearchResultMetadataState = normalizedMetadata[id]
    return metadataState.ids
  })

export const getSearchResultFieldNames = createSelector(
  selectSearchResultFileIds,
  getSearchResultNormalizedMetadata,
  (fileIds, normalizedMetadata) => {
    if (!fileIds || !(fileIds.length > 0) || !normalizedMetadata) {
      return []
    }
    const fileId = fileIds[0]
    const metadataState: SearchResultMetadataState = normalizedMetadata[fileId]
    return metadataState.ids
  }
)

export const getSeqNoByDocumentId = (
  id: number
): MemoizedSelector<object, any, unknown> =>
  createSelector(
    getSearchResultByFileId(id),
    (searchResult: SearchResponseData) => {
      if (searchResult) {
        return searchResult.metadata.find((field) => field.key === '__seq_no')
          .value
      }
      return -1
    }
  )

export const getHtmlConversionEligibleDocumentIds = (
  data: NativePrefetchDocumentModel
): MemoizedSelector<object, any, unknown> =>
  createSelector(
    getSeqNoByDocumentId(data.currentDocumentId),
    getSearchResultFieldValues,
    (seqNo: number, fieldValues: SearchDocumentMetadata[]) => {
      return fieldValues
        .filter((kvPair) => kvPair['__seq_no'] > seqNo)
        .slice(0, data.documentPrefetchCount)
        .filter(
          (fieldValue) =>
            !data.invalidFileExtensions.includes(fieldValue['__extension']) &&
            fieldValue['__filesize'] < data.thresholdFileSize
        )
        .map((fieldValue) => fieldValue['__FileID'])
    }
  )
