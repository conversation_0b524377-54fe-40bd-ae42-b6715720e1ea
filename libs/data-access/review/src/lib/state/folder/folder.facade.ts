import { Injectable } from '@angular/core'
import { Store, select } from '@ngrx/store'
import * as folderActions from './folder.actions'
import * as folderSelectors from './folder.selectors'
import {
  FolderActionPayloadModel,
  AutoFolder,
  FolderModel,
  DynamicFolderModel,
  FolderTabTreeState,
  CustomFolderTabTreeState,
} from '../../models/interfaces'
import { FolderState } from './folder.reducer'

type FolderStateKeys = keyof FolderState | Array<keyof FolderState>

@Injectable()
export class FolderFacade {
  constructor(private readonly store: Store) {}

  public fetchAllFolders(projectId: number): void {
    this.store.dispatch(folderActions.fetchAllFolders({ projectId }))
  }

  public getAllFolders$ = this.store.pipe(
    select(folderSelectors.getAllFolders$)
  )

  public fetchAutoFolders(projectId: number): void {
    this.store.dispatch(folderActions.fetchAutoFolders({ projectId }))
  }

  public getAutoFolders$ = this.store.pipe(
    select(folderSelectors.getAutoFolders$)
  )

  public getFolderGroupPermission$ = this.store.pipe(
    select(folderSelectors.getFolderGroupPermission$)
  )

  public fetchStaticFolders(projectId: number): void {
    this.store.dispatch(folderActions.fetchStaticFolders({ projectId }))
  }

  public getStaticFolders$ = this.store.pipe(
    select(folderSelectors.getStaticFolders$)
  )

  public createStaticFolder(
    projectId: number,
    staticFolder: FolderModel
  ): void {
    this.store.dispatch(
      folderActions.createStaticFolder({ projectId, staticFolder })
    )
  }

  public fetchFolderSecurityGroupAction(
    groupIds?: number[],
    permissions?: string[]
  ): void {
    this.store.dispatch(
      folderActions.fetchFolderSecurityGroupAction({ groupIds, permissions })
    )
  }

  public getSuccessMessage$ = this.store.pipe(
    select(folderSelectors.getSuccessMessage$)
  )

  public getErrorMessage$ = this.store.pipe(
    select(folderSelectors.getErrorMessage$)
  )

  public readonly getSecurityGroupData$ = this.store.pipe(
    select(folderSelectors.getStateOfFolderState('securityGroupData'))
  )

  public clearMessage(): void {
    this.store.dispatch(folderActions.clearMessages())
  }

  public getSendToFolderSuccessResponse$ = this.store.pipe(
    select(folderSelectors.getSendToFolderSuccessResponse$)
  )

  public getSendToFolderErrorResponse$ = this.store.pipe(
    select(folderSelectors.getSendToFolderErrorResponse$)
  )

  public getRemoveFolderSuccessResponse$ = this.store.pipe(
    select(folderSelectors.getRemoveFolderSuccessResponse$)
  )

  public getRemoveFolderErrorResponse$ = this.store.pipe(
    select(folderSelectors.getRemoveFolderErrorResponse$)
  )

  public getIsFolderDeleting$ = this.store.pipe(
    select(folderSelectors.getStateOfFolderState('isFolderDeleting'))
  )

  public selectDeleteFolderSuccessResponse$ = this.store.pipe(
    select(folderSelectors.getStateOfFolderState('deleteFolderSuccessResponse'))
  )

  public selectDeleteFolderErrorResponse$ = this.store.pipe(
    select(folderSelectors.getStateOfFolderState('deleteFolderErrorResponse'))
  )

  public selectUpdateFolderSuccessResponse$ = this.store.pipe(
    select(folderSelectors.getStateOfFolderState('updateFolderSuccessResponse'))
  )

  public selectUpdateFolderErrorResponse$ = this.store.pipe(
    select(folderSelectors.getStateOfFolderState('updateFolderErrorResponse'))
  )

  public deleteFolder(
    projectId: number,
    folderId: number,
    params?: {
      isDynamic: boolean
      isGlobal: boolean
    }
  ): void {
    this.store.dispatch(
      folderActions.deleteFolder({ projectId, folderId, params })
    )
  }

  public updateFolder(
    projectId: number,
    payload: {
      folder: FolderModel & DynamicFolderModel
      isDynamic: boolean
      isGlobal: boolean
    }
  ): void {
    this.store.dispatch(folderActions.updateFolder({ projectId, payload }))
  }

  public fetchFolderGroupPermission(projectId: number): void {
    this.store.dispatch(folderActions.fetchFolderGroupPermission({ projectId }))
  }

  public saveSendToFolder(
    projectId: number,
    payload: FolderActionPayloadModel
  ): void {
    this.store.dispatch(folderActions.saveSendToFolder({ projectId, payload }))
  }

  public saveRemoveFolder(
    projectId: number,
    payload: FolderActionPayloadModel
  ): void {
    this.store.dispatch(folderActions.saveRemoveFolder({ projectId, payload }))
  }

  public fetchDelimeters(): void {
    this.store.dispatch(folderActions.fetchDelimiters())
  }

  public getDelimitersSuccessResponse$ = this.store.pipe(
    select(
      folderSelectors.getStateOfFolderState('fetchDelimitersSuccessResponse')
    )
  )

  public getDelimitersFailureResponse$ = this.store.pipe(
    select(
      folderSelectors.getStateOfFolderState('fetchDelimitersFailureResponse')
    )
  )

  public createAutoFolder(
    projectId: number,
    autoFolderRequestModel: AutoFolder
  ): void {
    this.store.dispatch(
      folderActions.createAutoFolder({ projectId, autoFolderRequestModel })
    )
  }

  public getCreateAutoFolderSuccessResponse$ = this.store.pipe(
    select(
      folderSelectors.getStateOfFolderState('createAutoFolderSuccessResponse')
    )
  )

  public getCreateAutoFolderFailureResponse$ = this.store.pipe(
    select(
      folderSelectors.getStateOfFolderState('createAutoFolderFailureResponse')
    )
  )

  public resetFolderState(stateKey: FolderStateKeys): void {
    this.store.dispatch(folderActions.resetFolderState({ stateKey }))
  }

  public readonly getIsCreateFolderLoading$ = this.store.pipe(
    select(folderSelectors.getStateOfFolderState('isCreateFolderLoading'))
  )

  public setSelectedFolderTreeState(
    selectedFolderTreeState: FolderTabTreeState | CustomFolderTabTreeState
  ): void {
    this.store.dispatch(
      folderActions.setSelectedFolderTreeState({
        selectedFolderTreeState,
      })
    )
  }

  public readonly getSelectedFolderTreeState$ = this.store.pipe(
    select(folderSelectors.getStateOfFolderState('selectedFolderTreeState'))
  )
}
