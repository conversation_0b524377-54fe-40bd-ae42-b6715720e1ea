import { Injectable } from '@angular/core'
import { Store, select } from '@ngrx/store'
import * as tallyActions from './tally.actions'
import { TallyRequestModel } from '../../models/interfaces/tally.model'
import * as tallySelectors from './tally.selectors'

@Injectable()
export class TallyFacade {
  constructor(private readonly store: Store) {}

  public fetchTallyData(
    projectId: number,
    tallyRequestModel: TallyRequestModel
  ): void {
    this.store.dispatch(
      tallyActions.fetchTallyData({ projectId, tallyRequestModel })
    )
  }

  public getTallyDataSource$ = this.store.pipe(
    select(tallySelectors.getStateOfTallyState('tallyResponseModel'))
  )
}
