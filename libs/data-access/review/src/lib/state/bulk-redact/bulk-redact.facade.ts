import { Injectable } from '@angular/core'
import { Store, select } from '@ngrx/store'
import * as bulkRedactActions from './bulk-redact.actions'
import { BulkRedactState } from './bulk-redact.reducer'
import * as bulkRedactSelectors from './bulk-redact.selectors'
import {
  BulkPdfRedactionDataModel,
  BulkRedactionJobDetailRequestModel,
  SearchRequestForBulkRedactModel,
} from '../../models/interfaces'

type BulkRedactStateKeys = keyof BulkRedactState | Array<keyof BulkRedactState>

@Injectable()
export class BulkRedactFacade {
  constructor(private readonly store: Store) {}

  public resetBulkRedactState(stateKey: BulkRedactStateKeys): void {
    this.store.dispatch(bulkRedactActions.resetBulkRedactState({ stateKey }))
  }

  public searchForBulkRedaction(
    searchRequest: SearchRequestForBulkRedactModel,
    projectId: number
  ): void {
    this.store.dispatch(
      bulkRedactActions.searchForBulkRedaction({
        searchRequest,
        projectId,
      })
    )
  }

  public getSearchForBulkRedactionSuccessResponse$ = this.store.pipe(
    select(
      bulkRedactSelectors.getStateOfBulkRedactState(
        'searchForBulkRedactionSuccessResponse'
      )
    )
  )

  public getSearchForBulkRedactionFailureResponse$ = this.store.pipe(
    select(
      bulkRedactSelectors.getStateOfBulkRedactState(
        'searchForBulkRedactionFailureResponse'
      )
    )
  )

  public getIsSearchingForBulkRedaction$ = this.store.pipe(
    select(
      bulkRedactSelectors.getStateOfBulkRedactState(
        'isSearchingForBulkRedaction'
      )
    )
  )

  public queueFilesForBulkRedaction(
    bulkPdfRedactionDataModels: BulkPdfRedactionDataModel[],
    projectId: number
  ): void {
    this.store.dispatch(
      bulkRedactActions.queueFilesForBulkRedaction({
        bulkPdfRedactionDataModels,
        projectId,
      })
    )
  }

  public getQueueFilesForBulkRedactionSuccessResponse$ = this.store.pipe(
    select(
      bulkRedactSelectors.getStateOfBulkRedactState(
        'queueFilesForBulkRedactionSuccessResponse'
      )
    )
  )

  public getQueueFilesForBulkRedactionFailureResponse$ = this.store.pipe(
    select(
      bulkRedactSelectors.getStateOfBulkRedactState(
        'queueFilesForBulkRedactionFailureResponse'
      )
    )
  )

  public getIsQueuingFilesForBulkRedaction$ = this.store.pipe(
    select(
      bulkRedactSelectors.getStateOfBulkRedactState(
        'isQueuingFilesForBulkRedaction'
      )
    )
  )

  public getJobDetailsOfBulkRedaction(
    bulkRedactionJobDetailRequest: BulkRedactionJobDetailRequestModel,
    projectId: number
  ): void {
    this.store.dispatch(
      bulkRedactActions.getJobDetailsOfBulkRedaction({
        bulkRedactionJobDetailRequest,
        projectId,
      })
    )
  }

  public getJobDetailsOfBulkRedactionSuccessResponse$ = this.store.pipe(
    select(
      bulkRedactSelectors.getStateOfBulkRedactState(
        'getJobDetailsOfBulkRedactionSuccessResponse'
      )
    )
  )

  public getJobDetailsOfBulkRedactionFailureResponse$ = this.store.pipe(
    select(
      bulkRedactSelectors.getStateOfBulkRedactState(
        'getJobDetailsOfBulkRedactionFailureResponse'
      )
    )
  )

  public getIsGettingJobDetailsOfBulkRedaction$ = this.store.pipe(
    select(
      bulkRedactSelectors.getStateOfBulkRedactState(
        'isGettingJobDetailsOfBulkRedaction'
      )
    )
  )

  public fetchBulkRedactedFileDetails(projectId: number, jobId: number): void {
    this.store.dispatch(
      bulkRedactActions.fetchBulkRedactedFileDetails({ projectId, jobId })
    )
  }

  public readonly selectBulkRedactionFileDataSuccessResponse$ = this.store.pipe(
    select(
      bulkRedactSelectors.getStateOfBulkRedactState(
        'bulkRedactionFileDataSuccessResponse'
      )
    )
  )

  public readonly selectBulkRedactionFileDataErrorResponse$ = this.store.pipe(
    select(
      bulkRedactSelectors.getStateOfBulkRedactState(
        'bulkRedactionFileDataErrorResponse'
      )
    )
  )
}
