import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import * as fieldActions from './field.actions'
import * as FieldSelectors from './field.selectors'
import * as ViewSelectors from '../view/view.selectors'
import { Observable, Subject } from 'rxjs'
import {
  Field,
  ReviewLayout,
  ReviewPanelDefaultFieldModel,
  ReviewPanelFieldSelectionModel,
} from '../../models/interfaces'
import { ReviewPanelType } from '../../models/constants'
import { FieldState } from './field.reducer'

@Injectable()
export class FieldFacade {
  public notifyFieldChanges: Subject<void> = new Subject<void>()

  public getAllFields$ = this.store.pipe(select(FieldSelectors.getAllFields$))

  public getAllFieldAndDefaultFieldsData$ = this.store.pipe(
    select(FieldSelectors.getAllFieldAndDefaultFieldsData$)
  )

  public getReviewPanleDefaultFields$ = this.store.pipe(
    select(FieldSelectors.getStateOfFieldState('reviewPanelDefaultFields'))
  )

  public getDocumentTableVisibleFields$ = this.store.pipe(
    select(ViewSelectors.getViewFields)
  )

  public getDisplayFieldMap$ = this.store.pipe(
    select(FieldSelectors.getStateOfFieldState('displayFieldMap'))
  )

  public selectIsPermittedFieldLoading$ = this.store.pipe(
    select(FieldSelectors.getStateOfFieldState('isPermittedFieldLoading'))
  )

  public getPermittedFields$ = this.store.pipe(
    select(FieldSelectors.getStateOfFieldState('permittedFields'))
  )

  public getAllReviewPanelFields$ = this.store.pipe(
    select(FieldSelectors.getAllReviewPanelFields$)
  )

  public selectReviewLayoutId$ = this.store.pipe(
    select(FieldSelectors.getStateOfFieldState('selectedReviewLayoutId'))
  )

  public selectReviewLayout$ = this.store.pipe(
    select(FieldSelectors.getStateOfFieldState('selectedReviewLayout'))
  )

  public selectAllVenioFields$ = this.store.pipe(
    select(FieldSelectors.getStateOfFieldState('allVenioFields'))
  )

  public selectIsFieldLoading$ = this.store.pipe(
    select(FieldSelectors.getStateOfFieldState('isFieldLoading'))
  )

  public selectAllCustomFields$ = this.store.pipe(
    select(FieldSelectors.getStateOfFieldState('allCustomFields'))
  )

  public selectAllPermittedFieldsOfCurrentUser$ = this.store.pipe(
    select(FieldSelectors.getStateOfFieldState('permittedFields'))
  )

  public selectFieldSelection$ = this.store.pipe(
    select(FieldSelectors.getStateOfFieldState('fieldPanelMap'))
  )

  public selectVenioFieldsPanelMap$ = this.store.pipe(
    select(FieldSelectors.getStateOfFieldState('venioFieldsPanelMap'))
  )

  public selectCustomFieldsPanelMap$ = this.store.pipe(
    select(FieldSelectors.getStateOfFieldState('customFieldsPanelMap'))
  )

  public getInternalFieldName$ = (
    displayFieldName: string
  ): Observable<string> =>
    this.store.pipe(
      select(FieldSelectors.getInternalFieldName(displayFieldName))
    )

  public getFieldInfoByDisplayFieldName$ = (
    displayFieldName: string
  ): Observable<Field> =>
    this.store.pipe(
      select(FieldSelectors.getFieldInfoByDisplayFieldName(displayFieldName))
    )

  constructor(private readonly store: Store) {}

  public resetField(
    stateKeys: keyof FieldState | Array<keyof FieldState>
  ): void {
    this.store.dispatch(fieldActions.resetField({ stateKeys }))
  }

  public fetchAllCustomFields(
    projectId: number,
    withoutAnyFilter?: boolean
  ): void {
    this.store.dispatch(
      fieldActions.fetchAllCustomFields({ projectId, withoutAnyFilter })
    )
  }

  public fetchAllVenioFields(): void {
    this.store.dispatch(fieldActions.fetchAllVenioFields())
  }

  public fetchAllPermittedFieldOfCurrentUser(projectId: number): void {
    this.store.dispatch(fieldActions.fetchAllPermittedFields({ projectId }))
  }

  public fetchAllPermittedFields(projectId: number): void {
    this.store.dispatch(
      fieldActions.fetchAllPermittedFieldsByComponent({ projectId })
    )
  }

  public fetchReviewPanelDefaltFields(): void {
    this.store.dispatch(fieldActions.fetchReviewPanelDefaultFields())
  }

  public setFieldsMap(fieldsMap: { [key: string]: Field }): void {
    this.store.dispatch(
      fieldActions.setFieldsMap({
        fieldsMap,
      })
    )
  }

  public setReviewPanelDefaultFields(
    reviewPanelDefaultFieldModel: ReviewPanelDefaultFieldModel
  ): void {
    this.store.dispatch(
      fieldActions.fetchReviewPanelDefaultFieldsSuccess({
        reviewPanelDefaultFieldModel,
      })
    )
  }

  // Map the UI selected values to database stored value.
  public mapSelectedMetadataValue(field: Field, fieldValues: string[]): any[] {
    if (field.fieldDataType.toUpperCase() === 'BIT') {
      return fieldValues.map((val) => {
        const value = val.toUpperCase()
        if (value === '') return ''
        // For possible boolean values in UI, return true/false
        return (
          value === 'YES' ||
          value === 'Y' ||
          value === 'TRUE' ||
          value === 'T' ||
          value === '1'
        )
      })
    } else if (field.internalFieldName === 'VOLUME_NAME') {
      // Volume_Name field value generated as '<ExportName>\<VolumeName>'.
      // So, split the string by '\' and return only the volume name for filter query.
      return fieldValues.map((val) => {
        return val.split('\\')[1]
      })
    } else if (
      field?.fieldDataType?.toUpperCase() === 'DATETIME' &&
      field?.isCustomField
    ) {
      const uniqueDates = new Set<string>()
      fieldValues.forEach((dateTimeStr) => {
        const date = new Date(dateTimeStr)
        const year = date.getFullYear()
        const month = (date.getMonth() + 1).toString().padStart(2, '0') // Months are zero-indexed
        const day = date.getDate().toString().padStart(2, '0')

        const formattedDate = `${month}/${day}/${year}`
        uniqueDates.add(formattedDate)
      })
      return Array.from(uniqueDates)
    }
    return fieldValues
  }

  public fetchAllCustomWithPredefinedFields(projectId: number): void {
    this.store.dispatch(
      fieldActions.fetchAllCustomWithPredefinedFields({ projectId })
    )
  }

  public fetchUserLayoutId(userId: number): void {
    this.store.dispatch(fieldActions.fetchUserLayoutId({ userId }))
  }

  public setFieldPanelMap(fieldPanelMap: {
    [name in ReviewPanelType]?: ReviewPanelFieldSelectionModel
  }): void {
    this.store.dispatch(fieldActions.setFieldPanelMap({ fieldPanelMap }))
  }

  public setSelectedReviewLayout(selectedReviewLayout: ReviewLayout): void {
    this.store.dispatch(
      fieldActions.setSelectedReviewLayout({
        selectedReviewLayout,
      })
    )
  }

  public setVenioFieldsPanelMap(venioFieldsPanelMap: {
    [name in ReviewPanelType]?: number[]
  }): void {
    this.store.dispatch(
      fieldActions.setVenioFieldsPanelMap({ venioFieldsPanelMap })
    )
  }

  public setCustomFieldsPanelMap(customFieldsPanelMap: {
    [name in ReviewPanelType]?: number[]
  }): void {
    this.store.dispatch(
      fieldActions.setCustomFieldsPanelMap({ customFieldsPanelMap })
    )
  }

  public setAllVenioFields(allVenioFields: Field[]): void {
    this.store.dispatch(fieldActions.setAllVenioFields({ allVenioFields }))
  }

  public setAllCustomFields(allCustomFields: Field[]): void {
    this.store.dispatch(
      fieldActions.fetchAllCustomFieldWithPredefinedSuccess({ allCustomFields })
    )
  }

  public setPermittedFields(permittedFields: Field[]): void {
    this.store.dispatch(
      fieldActions.fetchAllPermittedFieldsByComponentSuccess({
        fields: permittedFields,
      })
    )
  }
}
