import {
  MemoizedSelector,
  createFeatureSelector,
  createSelector,
} from '@ngrx/store'
import {
  NATIVE_DOWNLOAD_FEATURE_KEY,
  NativeDownloadState,
} from './native-download.reducer'

export const getNativeDownloadState =
  createFeatureSelector<NativeDownloadState>(NATIVE_DOWNLOAD_FEATURE_KEY)

export const getStateOfNativeDownload = <T extends keyof NativeDownloadState>(
  stateKey: T
): MemoizedSelector<object, NativeDownloadState[T], unknown> =>
  createSelector(
    getNativeDownloadState,
    (state: NativeDownloadState) => state[stateKey]
  )

export const getNativeDownloadResponse = createSelector(
  getStateOfNativeDownload('nativeDownloadResponse'),
  (response) => {
    return response
  }
)

export const getNativeDownloadStatus = createSelector(
  getStateOfNativeDownload('nativeDownloadStatus'),
  (response) => {
    return response
  }
)
