import { Injectable, inject } from '@angular/core'
import { Store, select } from '@ngrx/store'

import * as ConvertToHtmlActions from './convert-to-html.actions'
import * as ConvertToHtmlSelectors from './convert-to-html.selectors'

@Injectable()
export class ConvertToHtmlFacade {
  private readonly store = inject(Store)

  public getHtmlSummary$ = this.store.pipe(
    select(ConvertToHtmlSelectors.getStateOfHtmlConvert('summary'))
  )

  public getHtmlFileTypes$ = this.store.pipe(
    select(ConvertToHtmlSelectors.getStateOfHtmlConvert('fileTypes'))
  )

  public fetchHtmlFileTypes(
    launchChecked: boolean,
    relaunchChecked: boolean
  ): void {
    this.store.dispatch(
      ConvertToHtmlActions.fetchHtmlFiletypesAction({
        payload: { launchChecked, relaunchChecked },
      })
    )
  }

  public fetchHtmlSummary(): void {
    this.store.dispatch(ConvertToHtmlActions.fetchHtmlSummaryAction())
  }

  public queueForHtmlConversion(selectedFileTypeIds: number[]): void {
    this.store.dispatch(
      ConvertToHtmlActions.queueForHtmlConversionAction({
        payload: {
          selectedFileTypeIds,
        },
      })
    )
  }
}
