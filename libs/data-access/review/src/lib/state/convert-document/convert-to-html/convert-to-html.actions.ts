import { createAction, props } from '@ngrx/store'
import {
  FileTypesResponseModel,
  HtmlRtfSummaryResponseModel,
} from '../../../models/interfaces'

export const fetchHtmlSummaryAction = createAction(
  '[Html Conversion] Get html summary'
)

export const fetchHtmlFiletypesAction = createAction(
  '[Html Conversion] Get html filetypes',
  props<{
    payload: {
      launchChecked: boolean
      relaunchChecked: boolean
    }
  }>()
)

export const setHtmlSummaryAction = createAction(
  '[Html Conversion] Set html summary',
  props<{ payload: { summaryResponse: HtmlRtfSummaryResponseModel } }>()
)

export const getHtmlSummaryAction = createAction(
  '[Html Conversion] Get html summary'
)

export const clearSummaryResponseAction = createAction(
  '[Html Conversion] Clear html summary'
)

export const setHtmlFiletypesAction = createAction(
  '[Html Conversion] Set html filetypes',
  props<{ payload: { fileTypes: FileTypesResponseModel[] } }>()
)

export const queueForHtmlConversionAction = createAction(
  '[Html Conversion] Start Html Conversion',
  props<{
    payload: {
      selectedFileTypeIds: number[]
    }
  }>()
)

export const setHtmlConversionQueueResponseAction = createAction(
  '[Html Conversion] Set Html Conversion Queue Response',
  props<{
    payload: {
      queueResponse: string
    }
  }>()
)

export const getHtmlConversionQueueResponseAction = createAction(
  '[Html Conversion] Get Html Conversion Queue Response'
)

export const clearHtmlConversionQueueResponseAction = createAction(
  '[Html Conversion] Clear Html Conversion Queue Response'
)
