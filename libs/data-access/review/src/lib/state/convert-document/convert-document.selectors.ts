import {
  MemoizedSelector,
  createFeatureSelector,
  createSelector,
} from '@ngrx/store'
import {
  CONVERT_DOCUMENT_FEATURE_KEY,
  ConvertDocumentState,
} from './convert-document.reducer'

/**
 * Function to get the feature state for ConvertDocument.
 *
 * @returns {MemoizedSelector<object, ConvertDocumentState>} - A memoized selector function that returns the ConvertDocumentState.
 */
export const selectConvertDocumentState =
  createFeatureSelector<ConvertDocumentState>(CONVERT_DOCUMENT_FEATURE_KEY)

/**
 * Function to get the state of a specific key within the ConvertDocumentState.
 *
 * @template T - A type that extends the keys of ConvertDocumentState.
 * @param {T} stateKey - The key for which the state is to be retrieved.
 *
 * @returns {MemoizedSelector} - A memoized selector function that returns the state for the given key.
 */
export const getStateOf = <T extends keyof ConvertDocumentState>(
  stateKey: T
): MemoizedSelector<object, ConvertDocumentState[T], unknown> => {
  /**
   * createSelector is a function that takes one or more selectors and a result function as its parameters.
   *
   * @param {Function} getAuthState - A selector function that returns the ConvertDocumentState.
   * @param {Function} resultFunc - A result function that takes the ConvertDocumentState and returns the state for the given key.
   *
   * @returns {MemoizedSelector<object, ConvertDocumentState[T], unknown>} - A memoized version of the result function.
   */
  return createSelector(
    selectConvertDocumentState,
    (state: ConvertDocumentState) => state[stateKey]
  )
}
