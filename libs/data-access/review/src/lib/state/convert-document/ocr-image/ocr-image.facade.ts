import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'

import * as OcrImageActions from './ocr-image.actions'
import * as OcrImageSelectors from './ocr-image.selectors'

@Injectable()
export class OcrImageFacade {
  constructor(private readonly store: Store) {}

  public getOcrImageSummary$ = this.store.pipe(
    select(OcrImageSelectors.getStateOfOcrImage('summary'))
  )

  public getOcrImageFileTypes$ = this.store.pipe(
    select(OcrImageSelectors.getStateOfOcrImage('fileTypes'))
  )

  public getOcrImageSummary(): void {
    this.store.dispatch(OcrImageActions.fetchOcrSummary())
  }

  public fetchOcrImageFileTypes(
    launchChecked: boolean,
    relaunchChecked: boolean
  ): void {
    this.store.dispatch(
      OcrImageActions.fetchOcrFileTypes({
        payload: { launchChecked, relaunchChecked },
      })
    )
  }

  public queueForOcr(
    selectedFileTypes: string[],
    selectedExtensions: string[]
  ): void {
    this.store.dispatch(
      OcrImageActions.startOCRConversionAction({
        payload: {
          selectedFileTypes,
          selectedExtensions,
        },
      })
    )
  }

  public resetOcrState(): void {
    this.store.dispatch(OcrImageActions.resetOcrState())
  }
}
