import { Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { ConvertToImageService } from '../../../services/convert-document/convert-to-Image.service'
import { ConvertToImageFacade } from './convert-to-image.facade'
import { DocumentsFacade } from '../../documents'
import { SearchFacade } from '../../search'
import { ReviewParamService } from '../../../services'
import {
  catchError,
  concatMap,
  finalize,
  from,
  map,
  of,
  switchMap,
  withLatestFrom,
} from 'rxjs'
import {
  BulkImageRequestModel,
  BulkImageResponseModel,
  ResponseMessage,
} from '../../../models/interfaces'
import { ResponseModel } from '@venio/shared/models/interfaces'
import {
  fetchImageSummaryFileTypesAction,
  queueForImageConversionAction,
  setImageConversionQueueResponseAction,
  setImageCustomFieldsAction,
  setImageFiletypesAction,
  setImageSummaryAction,
} from './convert-to-image.actions'
import { ConvertDocumentFacade } from '../convert-document.facade'
import { HttpErrorResponse } from '@angular/common/http'
import { CaseConvertorService } from '@venio/util/utilities'
import { Action } from '@ngrx/store'

@Injectable()
export class ConvertToImageEffects {
  constructor(
    private readonly actions$: Actions,
    private readonly convertDocumentFacade: ConvertDocumentFacade,
    private readonly convertToImageService: ConvertToImageService,
    private readonly convertToImageFacade: ConvertToImageFacade,
    private readonly documentsFacade: DocumentsFacade,
    private readonly searchFacade: SearchFacade,
    private readonly reviewParamService: ReviewParamService
  ) {}

  public fetchImageSummaryFiletypesSummary$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fetchImageSummaryFileTypesAction),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.reviewParamService.projectId,
            this.searchFacade.getSearchTempTables$,
            this.documentsFacade.getIsBatchSelected$,
            this.documentsFacade.getSelectedDocuments$,
            this.documentsFacade.getUnselectedDocuments$
          )
        )
      ),
      switchMap(
        ([
          action,
          projectId,
          tempTables,
          isBatchSelected,
          selectedDocs,
          unSelectedDocs,
        ]) => {
          const payload: BulkImageRequestModel = {
            projectId: projectId,
            searchTempTableName: tempTables.searchResultTempTable,
            launch: action.payload.isLaunch,
            relaunch: action.payload.isRelaunch,
            selectedImageFileTypeList: [],
            isBatchSelected: isBatchSelected,
            selectedFileIds: selectedDocs,
            unSelectedFileIds: unSelectedDocs,
            computeSummary: !action.updateFileTypesOnly,
            systemBates: null,
            customMaxpageLimit: -1,
            outputImageDimension: null,
          }

          return this.convertToImageService
            .fetchImageSummaryFileTypes(payload)
            .pipe(
              switchMap((response: ResponseModel) => {
                let data: BulkImageResponseModel = response.data
                const camelCaseService = new CaseConvertorService()
                return from(
                  camelCaseService.convertToCase<BulkImageResponseModel>(
                    response.data,
                    'camelCase'
                  )
                ).pipe(
                  switchMap((responseConverted: BulkImageResponseModel) => {
                    if (responseConverted) {
                      data = responseConverted
                    }
                    return this.getSumaryFiletypesActions(action, data)
                  }),
                  catchError((error: unknown) => {
                    return this.getSumaryFiletypesActions(action, data)
                  }),
                  finalize(() => {
                    camelCaseService.terminate()
                  })
                )
              })
            )
        }
      )
    )
  )

  private getSumaryFiletypesActions(
    action: {
      payload: {
        isLaunch: boolean
        isRelaunch: boolean
      }
      updateFileTypesOnly: boolean
    },
    data: BulkImageResponseModel
  ): Action[] {
    if (action.updateFileTypesOnly) {
      return [
        setImageFiletypesAction({
          payload: {
            fileTypesResponse: data.imageFileTypeDetailsResponseModelList,
          },
        }),
      ]
    }
    return [
      setImageSummaryAction({
        payload: {
          summaryResponse: data.imageSummaryResponseModelList,
          projectPageLimit: data.projectMaxPageLimit,
          startNumber: data.systemBates?.startingNum,
        },
      }),
      setImageFiletypesAction({
        payload: {
          fileTypesResponse: data.imageFileTypeDetailsResponseModelList,
        },
      }),
    ]
  }

  public fetchImageCustomFields$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fetchImageSummaryFileTypesAction),
      concatMap((action) =>
        of(action).pipe(withLatestFrom(this.reviewParamService.projectId))
      ),
      switchMap(([, projectId]) => {
        return this.convertToImageService
          .fetchImageCustomFields(projectId)
          .pipe(
            switchMap((response: ResponseModel) => {
              return [
                setImageCustomFieldsAction({
                  payload: { imageCustomFields: response.data },
                }),
              ]
            })
          )
      })
    )
  )

  public queueForImageConversion$ = createEffect(() =>
    this.actions$.pipe(
      ofType(queueForImageConversionAction),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.reviewParamService.projectId,
            this.searchFacade.getSearchTempTables$,
            this.documentsFacade.getIsBatchSelected$,
            this.documentsFacade.getSelectedDocuments$,
            this.documentsFacade.getUnselectedDocuments$,
            this.convertDocumentFacade.getLaunchOptionState$,
            this.convertDocumentFacade.getRelaunchOptionState$
          )
        )
      ),
      switchMap(
        ([
          action,
          projectId,
          tempTables,
          isBatchSelected,
          selectedDocs,
          unSelectedDocs,
          isLaunch,
          isRelaunch,
        ]) => {
          const payload: BulkImageRequestModel = {
            projectId: projectId,
            searchTempTableName: tempTables.searchResultTempTable,
            launch: isLaunch,
            relaunch: isRelaunch,
            selectedImageFileTypeList:
              action.payload.bulkImageRequestModel.selectedImageFileTypeList,
            isBatchSelected: isBatchSelected,
            selectedFileIds: selectedDocs,
            unSelectedFileIds: unSelectedDocs,
            computeSummary: false,
            systemBates: action.payload.bulkImageRequestModel.systemBates,
            customMaxpageLimit:
              action.payload.bulkImageRequestModel.customMaxpageLimit,
            outputImageDimension:
              action.payload.bulkImageRequestModel.outputImageDimension,
          }
          return this.convertToImageService.startImageConversion(payload).pipe(
            map((response: ResponseModel) => {
              const responseMessage: ResponseMessage = {
                success: true,
                message: response.message,
              }
              //set response message
              this.convertDocumentFacade.setResponseMessage(responseMessage)

              //refresh file types and summary
              this.convertToImageFacade.fetchImageSummaryFileTypes(
                isLaunch,
                isRelaunch,
                false
              )

              return setImageConversionQueueResponseAction({
                payload: { queueResponse: response.message },
              })
            }),
            catchError((error: unknown) => {
              if (error instanceof HttpErrorResponse) {
                const errorResponse: HttpErrorResponse = error
                const responseMessage: ResponseMessage = {
                  success: false,
                  message: errorResponse.error.message,
                }
                this.convertDocumentFacade.setResponseMessage(responseMessage)
                return [
                  setImageConversionQueueResponseAction({
                    payload: { queueResponse: errorResponse.error.message },
                  }),
                ]
              }
            })
          )
        }
      )
    )
  )
}
