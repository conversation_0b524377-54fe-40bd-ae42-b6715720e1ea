import { EntityState, EntityAdapter, createEntityAdapter } from '@ngrx/entity'
import { createReducer, Action, on } from '@ngrx/store'

import {
  ConvertToImageEntity,
  ImageFileTypeDetailsResponseModel,
  ImageSummaryResponseModel,
} from '../../../models/interfaces/convert-document/convert-to-image.models'
import {
  clearImageConversionQueueResponseAction,
  resetConvertToImageStateAction,
  setImageConversionQueueResponseAction,
  setImageCustomFieldsAction,
  setImageFiletypesAction,
  setImageSummaryAction,
} from './convert-to-image.actions'

export const CONVERT_TO_IMAGE_FEATURE_KEY = 'convertToImage'

export interface ConvertToImageState extends EntityState<ConvertToImageEntity> {
  selectedId?: string | number // which ConvertToImage record has been selected
  loaded: boolean // has the ConvertToImage list been loaded
  error?: string | null // last known error (if any)
  summary?: ImageSummaryResponseModel[]
  fileTypes?: ImageFileTypeDetailsResponseModel[]
  projectMaxPageLimit?: number
  startNumber?: number
  imageCustomFields?: string[]
  queueResponse?: any
}

export interface ConvertToImagePartialState {
  readonly [CONVERT_TO_IMAGE_FEATURE_KEY]: ConvertToImageState
}

export const convertToImageAdapter: EntityAdapter<ConvertToImageEntity> =
  createEntityAdapter<ConvertToImageEntity>()

export const initialConvertToImageState: ConvertToImageState =
  convertToImageAdapter.getInitialState({
    // set initial required properties
    loaded: false,
  })

const reducer = createReducer(
  initialConvertToImageState,
  on(setImageSummaryAction, (state, action) => ({
    ...state,
    summary: action.payload.summaryResponse,
    projectMaxPageLimit: action.payload.projectPageLimit,
    startNumber: action.payload.startNumber,
  })),
  on(setImageFiletypesAction, (state, action) => ({
    ...state,
    fileTypes: action.payload.fileTypesResponse,
  })),
  on(setImageCustomFieldsAction, (state, action) => ({
    ...state,
    imageCustomFields: action.payload.imageCustomFields,
  })),
  on(setImageConversionQueueResponseAction, (state, action) => ({
    ...state,
    queueResponse: action.payload.queueResponse,
  })),
  on(clearImageConversionQueueResponseAction, (state) => ({
    ...state,
    queueResponse: null,
  })),
  on(resetConvertToImageStateAction, () => ({
    ...initialConvertToImageState,
  }))
)

export function convertToImageReducer(
  state: ConvertToImageState | undefined,
  action: Action
): ConvertToImageState {
  return reducer(state, action)
}
