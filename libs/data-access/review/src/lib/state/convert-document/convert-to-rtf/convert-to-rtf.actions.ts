import { createAction, props } from '@ngrx/store'
import {
  FileTypesResponseModel,
  HtmlRtfSummaryResponseModel,
} from '../../../models/interfaces'

export const fetchRtfSummaryAction = createAction(
  '[Rtf Conversion] Get rtf summary'
)

export const fetchRtfFiletypesAction = createAction(
  '[Rtf Conversion] Get rtf filetypes',
  props<{
    payload: {
      launchChecked: boolean
      relaunchChecked: boolean
    }
  }>()
)

export const setRtfSummaryAction = createAction(
  '[Rtf Conversion] Set rtf summary',
  props<{ payload: { summaryResponse: HtmlRtfSummaryResponseModel } }>()
)

export const getRtfSummaryAction = createAction(
  '[Rtf Conversion] Get rtf summary'
)

export const clearRtfSummaryResponseAction = createAction(
  '[Rtf Conversion] Clear rtf summary'
)

export const setRtfFiletypesAction = createAction(
  '[Rtf Conversion] Set rtf filetypes',
  props<{ payload: { fileTypes: FileTypesResponseModel[] } }>()
)

export const queueForRtfConversionAction = createAction(
  '[Rtf Conversion] Start Rtf Conversion',
  props<{
    payload: {
      selectedFileTypeIds: number[]
    }
  }>()
)

export const setRtfConversionQueueResponseAction = createAction(
  '[Rtf Conversion] Set Rtf Conversion Queue Response',
  props<{
    payload: {
      queueResponse: string
    }
  }>()
)

export const getRtfConversionQueueResponseAction = createAction(
  '[Rtf Conversion] Get Rtf Conversion Queue Response'
)

export const clearRtfConversionQueueResponseAction = createAction(
  '[Rtf Conversion] Clear Rtf Conversion Queue Response'
)
