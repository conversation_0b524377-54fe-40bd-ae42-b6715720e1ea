import { Injectable, inject } from '@angular/core'
import { Store, select } from '@ngrx/store'

import * as OcrGeneratedImageActions from './ocr-generated-image.actions'
import * as OcrGeneratedImageSelectors from './ocr-generated-image.selectors'

@Injectable()
export class OcrGeneratedImageFacade {
  private readonly store = inject(Store)

  public setLaunchOption(launch: boolean, relaunch: boolean): void {
    this.store.dispatch(
      OcrGeneratedImageActions.setLaunchOcrGenImageOption({
        payload: { isLaunch: launch, isRelaunch: relaunch },
      })
    )
  }

  public getOcrImageSummary$ = this.store.pipe(
    select(OcrGeneratedImageSelectors.getStateOfOcrGenImage('summary'))
  )

  public getOcrImageFileTypes$ = this.store.pipe(
    select(OcrGeneratedImageSelectors.getStateOfOcrGenImage('fileTypes'))
  )

  public getOcrImageSummary(): void {
    this.store.dispatch(OcrGeneratedImageActions.fetchGenImageOcrSummary())
  }

  public fetchOcrImageFileTypes(
    launchChecked: boolean,
    relaunchChecked: boolean
  ): void {
    this.store.dispatch(
      OcrGeneratedImageActions.fetchGenImageOcrFileTypes({
        payload: { launchChecked, relaunchChecked },
      })
    )
  }

  public queueForOcr(
    selectedFileTypes: string[],
    selectedExtensions: string[]
  ): void {
    this.store.dispatch(
      OcrGeneratedImageActions.startGenImageOCRConversionAction({
        payload: {
          selectedFileTypes,
          selectedExtensions,
        },
      })
    )
  }

  public resetOcrState(): void {
    this.store.dispatch(OcrGeneratedImageActions.resetGenImageOcrState())
  }
}
