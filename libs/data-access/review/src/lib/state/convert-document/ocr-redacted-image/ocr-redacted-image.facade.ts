import { Injectable, inject } from '@angular/core'
import { Store, select } from '@ngrx/store'

import * as OcrRedactedImageActions from './ocr-redacted-image.actions'
import * as OcrRedactedImageSelectors from './ocr-redacted-image.selectors'

@Injectable()
export class OcrRedactedImageFacade {
  private readonly store = inject(Store)

  public setLaunchOption(launch: boolean, relaunch: boolean): void {
    this.store.dispatch(
      OcrRedactedImageActions.setLaunchOcrRedactImageOption({
        payload: { isLaunch: launch, isRelaunch: relaunch },
      })
    )
  }

  public getOcrImageSummary$ = this.store.pipe(
    select(OcrRedactedImageSelectors.getStateOfOcrRedactImage('summary'))
  )

  public getOcrImageFileTypes$ = this.store.pipe(
    select(OcrRedactedImageSelectors.getStateOfOcrRedactImage('fileTypes'))
  )

  public getRedactions$ = this.store.pipe(
    select(OcrRedactedImageSelectors.getStateOfOcrRedactImage('redactions'))
  )

  public fetchOcrRedactions(): void {
    this.store.dispatch(OcrRedactedImageActions.fetchRedactions())
  }

  public fetchOcrImageSummary(selectedRedaction: string[]): void {
    this.store.dispatch(
      OcrRedactedImageActions.fetchRedactedImageOcrSummary({
        payload: { selectedRedaction },
      })
    )
  }

  public fetchOcrImageFileTypes(
    launchChecked: boolean,
    relaunchChecked: boolean,
    selectedRedaction: string[]
  ): void {
    this.store.dispatch(
      OcrRedactedImageActions.fetchRedactedImageOcrFileTypes({
        payload: { launchChecked, relaunchChecked, selectedRedaction },
      })
    )
  }

  public queueForOcr(
    selectedFileTypes: string[],
    selectedExtensions: string[],
    selectedRedaction: string[]
  ): void {
    this.store.dispatch(
      OcrRedactedImageActions.startRedactedImageOCRConversionAction({
        payload: {
          selectedFileTypes,
          selectedExtensions,
          selectedRedaction,
        },
      })
    )
  }

  public resetOcrState(): void {
    this.store.dispatch(OcrRedactedImageActions.resetRedactedImageOcrState())
  }
}
