import { createAction, props } from '@ngrx/store'
import {
  FileTypesResponseModel,
  OcrRedactedImageResponseModel,
  OcrRedactions,
} from '../../../models/interfaces'

export const setLaunchOcrRedactImageOption = createAction(
  '[OcrRedactedImage] Set Launch Option',
  props<{
    payload: {
      isLaunch: boolean
      isRelaunch: boolean
    }
  }>()
)

export const fetchRedactions = createAction(
  '[OcrRedactedImage] Fetch Redactions'
)

export const fetchRedactionsSucceeded = createAction(
  '[OcrRedactedImage] Fetch Redactions Succeeded',
  props<{
    payload: {
      redactions: OcrRedactions[]
    }
  }>()
)

export const fetchRedactionsFailed = createAction(
  '[OcrRedactedImage] Fetch Redactions Failed',
  props<{
    payload: {
      error: any
    }
  }>()
)

export const fetchRedactedImageOcrFileTypes = createAction(
  '[OcrRedactedImage] Fetch Redacted Image OCR File Types',
  props<{
    payload: {
      launchChecked: boolean
      relaunchChecked: boolean
      selectedRedaction: string[]
    }
  }>()
)

export const fetchRedactedImageOcrFileTypesSucceeded = createAction(
  '[OcrRedactedImage] Fetch Redacted Image OCR File Types Completed',
  props<{
    payload: {
      fileTypes: FileTypesResponseModel[]
    }
  }>()
)

export const fetchRedactedImageOcrFileTypesFailed = createAction(
  '[OcrRedactedImage] Fetch Redacted Image OCR File Types Failed',
  props<{
    payload: {
      error: any
    }
  }>()
)

export const fetchRedactedImageOcrSummary = createAction(
  '[OcrRedactedImage] Fetch Redacted Image OCR Summary',
  props<{
    payload: {
      selectedRedaction: string[]
    }
  }>()
)

export const fetchRedactedImageOcrSummarySucceeded = createAction(
  '[OcrRedactedImage] Fetch Redacted Image OCR Summary Completed',
  props<{
    payload: {
      summary: OcrRedactedImageResponseModel
    }
  }>()
)

export const fetchRedactedImageOcrSummaryFailed = createAction(
  '[OcrRedactedImage] Fetch Redacted Image OCR Summary Failed',
  props<{
    payload: {
      error: any
    }
  }>()
)

export const startRedactedImageOCRConversionAction = createAction(
  '[OcrRedactedImage] Start Redacted Image OCR Conversion',
  props<{
    payload: {
      selectedFileTypes: string[]
      selectedExtensions: string[]
      selectedRedaction: string[]
    }
  }>()
)

export const startRedactedImageOCRConversionActionSucceeded = createAction(
  '[OcrRedactedImage] Start Redacted Image OCR Conversion Succeeded',
  props<{
    payload: {
      message: string
    }
  }>()
)

export const startRedactedImageOCRConversionActionFailed = createAction(
  '[OcrRedactedImage] Start Redacted Image OCR Conversion Failed',
  props<{
    payload: {
      message: string
    }
  }>()
)

export const resetRedactedImageOcrState = createAction(
  '[OcrRedactedImage] Reset Redacted Image OCR State'
)
