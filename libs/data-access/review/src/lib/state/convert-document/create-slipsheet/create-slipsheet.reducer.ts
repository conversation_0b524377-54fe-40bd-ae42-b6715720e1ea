import { Entity<PERSON>tate, Enti<PERSON><PERSON>dapter, createEntityAdapter } from '@ngrx/entity'
import { createReducer, Action, on } from '@ngrx/store'
import * as CreateSlipSheetAction from './create-slipsheet.actions'
import {
  CreateSlipsheetEntity,
  SlipsheetModel,
  SlipsheetSummaryModel,
  SlipsheetTemplateModel,
  StampLocation,
} from '../../../models/interfaces/convert-document/create-slipsheet.models'
import { FieldModel } from '../../../models/interfaces'

export const CREATE_SLIPSHEET_FEATURE_KEY = 'createSlipsheet'

export interface CreateSlipsheetState
  extends EntityState<CreateSlipsheetEntity> {
  selectedId?: string | number // which CreateSlipsheet record has been selected
  loaded: boolean // has the CreateSlipsheet list been loaded
  error?: string | null // last known error (if any)
  message?: string | null
  summary: SlipsheetSummaryModel
  templates: SlipsheetTemplateModel[]
  slipsheetFields: FieldModel[]
  slipsheetPayload: SlipsheetModel
  imagePreview: string
}

export interface CreateSlipsheetPartialState {
  readonly [CREATE_SLIPSHEET_FEATURE_KEY]: CreateSlipsheetState
}

export const createSlipsheetAdapter: EntityAdapter<CreateSlipsheetEntity> =
  createEntityAdapter<CreateSlipsheetEntity>()

export const initialCreateSlipsheetState: CreateSlipsheetState =
  createSlipsheetAdapter.getInitialState({
    // set initial required properties
    loaded: false,
    summary: null,
    templates: null,
    slipsheetFields: null,
    slipsheetPayload: {
      previousTIFFCount: 0,
      totalSlipSheetDocument: 0,
      slipsheetTemplate: {
        slipsheetTemplateId: -1,
        slipsheetTemplateName: '',
        slipSheetType: 0,
        placeHolderText: '',
        placeHolderTextFont: 'Tahoma, 12pt',
        placeHolderPosition: StampLocation.Center,
        placeHolderFile: null,
        createdOn: '',
        updatedOn: '',
        replaceFulltext: false,
        replaceTiffofExistingFiles: false,
      },
    },
    imagePreview: null,
  })

const reducer = createReducer(
  initialCreateSlipsheetState,
  on(CreateSlipSheetAction.setSlipSheetSummary, (state, { payload }) => ({
    ...state,
    summary: payload.summary,
  })),
  on(
    CreateSlipSheetAction.setSlipsheetTemplateForConversion,
    (state, { payload }) => ({
      ...state,
      templates: payload.templates,
    })
  ),
  on(
    CreateSlipSheetAction.setSlipSheetFieldForConversion,
    (state, { payload }) => ({
      ...state,
      slipsheetFields: payload.slipSheetFields,
    })
  ),
  on(CreateSlipSheetAction.createSlipsheetSuccess, (state, { payload }) => ({
    ...state,
    message: payload.message,
  })),
  on(CreateSlipSheetAction.setSlipsheetOptions, (state, { payload }) => ({
    ...state,
    slipsheetPayload: {
      ...state.slipsheetPayload,
      slipsheetTemplate: payload.slipsheetTemplateOptions,
    },
  })),
  on(CreateSlipSheetAction.setSlipSheetDocumentCount, (state, { payload }) => ({
    ...state,
    slipsheetPayload: {
      ...state.slipsheetPayload,
      previousTIFFCount: payload.previousTIFFCount,
      totalSlipSheetDocument: payload.totalSlipSheetDocument,
    },
  })),
  on(CreateSlipSheetAction.setImagePreviewData, (state, { payload }) => ({
    ...state,
    imagePreview: payload.base64Image,
  })),
  on(CreateSlipSheetAction.fetchImagePreviewFailed, (state) => ({
    ...state,
    imagePreview: null,
    error: state.error,
  })),
  on(CreateSlipSheetAction.removeImagePreviewData, (state) => ({
    ...state,
    imagePreview: null,
  }))
)

export function createSlipsheetReducer(
  state: CreateSlipsheetState | undefined,
  action: Action
): CreateSlipsheetState {
  return reducer(state, action)
}
