import { ResponseModel } from '@venio/shared/models/interfaces'
import { Action, createReducer, on } from '@ngrx/store'
import * as fromAudioTranscribeInitialStateAction from './audio-transcribe.actions'
import { resetStateProperty } from '@venio/util/utilities'

export const AUDIO_TRANSCRIBE_FEATURE_KEY = 'venioAudioTranscribe'

export interface AudioTranscribeState {
  isGettingFilesForAudioTranscribe: boolean | null
  getFilesForAudioTranscribeSuccessResponse: ResponseModel | null
  getFilesForAudioTranscribeFailureResponse: ResponseModel | null

  isQueuingFilesForAudioTranscribe: boolean | null
  queueFilesForAudioTranscribeSuccessResponse: ResponseModel | null
  queueFilesForAudioTranscribeFailureResponse: ResponseModel | null

  isGettingJobDetailsOfAudioTranscribe: boolean | null
  getJobDetailsOfAudioTranscribeSuccessResponse: ResponseModel | null
  getJobDetailsOfAudioTranscribeFailureResponse: ResponseModel | null
}

export const audioTranscribeInitialState: AudioTranscribeState = {
  isGettingFilesForAudioTranscribe: false,
  getFilesForAudioTranscribeSuccessResponse: null,
  getFilesForAudioTranscribeFailureResponse: null,

  isQueuingFilesForAudioTranscribe: false,
  queueFilesForAudioTranscribeSuccessResponse: null,
  queueFilesForAudioTranscribeFailureResponse: null,

  isGettingJobDetailsOfAudioTranscribe: false,
  getJobDetailsOfAudioTranscribeSuccessResponse: null,
  getJobDetailsOfAudioTranscribeFailureResponse: null,
}

const _reducer = createReducer(
  audioTranscribeInitialState,
  on(
    fromAudioTranscribeInitialStateAction.resetAudioTranscribeState,
    (state, { stateKey }) =>
      resetStateProperty<AudioTranscribeState>(
        state,
        audioTranscribeInitialState,
        stateKey
      )
  ),
  on(
    fromAudioTranscribeInitialStateAction.getFilesForAudioTranscribe,
    (state) => ({
      ...state,
      isGettingFilesForAudioTranscribe: true,
    })
  ),
  on(
    fromAudioTranscribeInitialStateAction.getFilesForAudioTranscribeSuccess,
    (state, { responseModel }) => ({
      ...state,
      isGettingFilesForAudioTranscribe: false,
      getFilesForAudioTranscribeSuccessResponse: responseModel,
    })
  ),
  on(
    fromAudioTranscribeInitialStateAction.getFilesForAudioTranscribeFailure,
    (state, { responseModel }) => ({
      ...state,
      isGettingFilesForAudioTranscribe: false,
      getFilesForAudioTranscribeFailureResponse: responseModel,
    })
  ),
  on(
    fromAudioTranscribeInitialStateAction.queueFilesForAudioTranscribe,
    (state) => ({
      ...state,
      isQueuingFilesForAudioTranscribe: true,
    })
  ),
  on(
    fromAudioTranscribeInitialStateAction.queueFilesForAudioTranscribeSuccess,
    (state, { responseModel }) => ({
      ...state,
      isQueuingFilesForAudioTranscribe: false,
      queueFilesForAudioTranscribeSuccessResponse: responseModel,
    })
  ),
  on(
    fromAudioTranscribeInitialStateAction.queueFilesForAudioTranscribeFailure,
    (state, { responseModel }) => ({
      ...state,
      isQueuingFilesForAudioTranscribe: false,
      queueFilesForAudioTranscribeFailureResponse: responseModel,
    })
  ),
  on(
    fromAudioTranscribeInitialStateAction.getJobDetailsOfAudioTranscribe,
    (state) => ({
      ...state,
      isGettingJobDetailsOfAudioTranscribe: true,
    })
  ),
  on(
    fromAudioTranscribeInitialStateAction.getJobDetailsOfAudioTranscribeSuccess,
    (state, { responseModel }) => ({
      ...state,
      isGettingJobDetailsOfAudioTranscribe: false,
      getJobDetailsOfAudioTranscribeSuccessResponse: responseModel,
    })
  ),
  on(
    fromAudioTranscribeInitialStateAction.getJobDetailsOfAudioTranscribeFailure,
    (state, { responseModel }) => ({
      ...state,
      isGettingJobDetailsOfAudioTranscribe: false,
      getJobDetailsOfAudioTranscribeFailureResponse: responseModel,
    })
  )
)

export function audioTranscribeReducer(
  state: AudioTranscribeState,
  action: Action
): AudioTranscribeState {
  return _reducer(state, action)
}
