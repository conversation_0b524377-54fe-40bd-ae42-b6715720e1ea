import { createReducer, on } from '@ngrx/store'
import * as CaseInfoActions from './case-info.actions'
import { CaseModel, Project } from '../../models/interfaces'

export const CASE_INFO_FEATURE_KEY = 'caseInfo'

export interface CaseInfoState {
  projectList: Project[] | null
  caseInfo: CaseModel | null
}

export const initialCaseInfoState: CaseInfoState = {
  projectList: [],
  caseInfo: null,
}

export const CaseInfoReducer = createReducer(
  initialCaseInfoState,
  on(CaseInfoActions.fetchProjectListSuccess, (state, { projects }) => ({
    ...state,
    projectList: projects,
  })),
  on(CaseInfoActions.fetchCaseInfoByIdSuccess, (state, { caseInfo }) => ({
    ...state,
    caseInfo,
  }))
)
