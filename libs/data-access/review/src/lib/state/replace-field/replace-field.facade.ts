import { Injectable } from '@angular/core'
import { Store, select } from '@ngrx/store'
import * as ReplaceFieldActions from './replace-field.actions'
import * as ReplaceFieldSelectors from './replace-field.selectors'
import {
  FindReplacePayloadModel,
  OverlayCustomFieldsMergePayloadModel,
} from '../../models/interfaces/overlay-fields.model'

@Injectable()
export class ReplaceFieldFacade {
  public getCustomFields$ = this.store.pipe(
    select(ReplaceFieldSelectors.getStateOfReplaceFieldState('customFields'))
  )

  public getComponentFields$ = this.store.pipe(
    select(ReplaceFieldSelectors.getStateOfReplaceFieldState('componentFields'))
  )

  public getDelimiters$ = this.store.pipe(
    select(ReplaceFieldSelectors.getStateOfReplaceFieldState('delimiters'))
  )

  public isCustomFieldsLoading$ = this.store.pipe(
    select(
      ReplaceFieldSelectors.getStateOfReplaceFieldState('isCustomFieldsLoading')
    )
  )

  public isFieldsLoading$ = this.store.pipe(
    select(ReplaceFieldSelectors.getStateOfReplaceFieldState('isFieldsLoading'))
  )

  public isReplaceInProgress$ = this.store.pipe(
    select(
      ReplaceFieldSelectors.getStateOfReplaceFieldState('isReplaceInProgress')
    )
  )

  public selectReplacesResponse$ = this.store.pipe(
    select(
      ReplaceFieldSelectors.getStateOfReplaceFieldState('replacesResponse')
    )
  )

  constructor(private readonly store: Store) {}

  public fetchCustomFields(projectId: number): void {
    this.store.dispatch(
      ReplaceFieldActions.FetchCustomFieldsAction({ projectId })
    )
  }

  public fetchComponentFields(projectId: number): void {
    this.store.dispatch(
      ReplaceFieldActions.FetchComponentFieldsAction({ projectId })
    )
  }

  public fetchDelimiters(projectId: number): void {
    this.store.dispatch(
      ReplaceFieldActions.FetchDelimitersAction({ projectId })
    )
  }

  public applyMergeFields(
    projectId: number,
    payload: OverlayCustomFieldsMergePayloadModel
  ): void {
    this.store.dispatch(
      ReplaceFieldActions.MergeOverlayFieldsAction({ projectId, payload })
    )
  }

  public applyPriorityOrderFields(
    projectId: number,
    payload: OverlayCustomFieldsMergePayloadModel
  ): void {
    this.store.dispatch(
      ReplaceFieldActions.PriorityOrderOverlayFieldsAction({
        projectId,
        payload,
      })
    )
  }

  public findReplaceField(
    projectId: number,
    payload: FindReplacePayloadModel[]
  ): void {
    this.store.dispatch(
      ReplaceFieldActions.FindReplaceFieldAction({ projectId, payload })
    )
  }

  public clearReplaceReponse(): void {
    this.store.dispatch(
      ReplaceFieldActions.ClearReplaceResponseAction({
        payload: {
          error: '',
        },
      })
    )
  }

  public resetReplaceState(): void {
    this.store.dispatch(
      ReplaceFieldActions.ReplaceFieldStateResetAction({
        keys: null,
      })
    )
  }
}
