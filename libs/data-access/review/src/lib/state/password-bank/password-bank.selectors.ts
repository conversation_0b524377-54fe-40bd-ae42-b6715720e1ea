import {
  createFeatureSelector,
  createSelector,
  MemoizedSelector,
} from '@ngrx/store'
import {
  PASSWORD_BANK_FEATURE_KEY,
  PasswordBankState,
} from './password-bank.reducer'

/**
 * Function to get the feature state for ConvertDocument.
 *
 * @returns {MemoizedSelector<object, PasswordBankState>} - A memoized selector function that returns the PasswordBankState.
 */
export const selectPasswordBankState = createFeatureSelector<PasswordBankState>(
  PASSWORD_BANK_FEATURE_KEY
)

/**
 * Function to get the state of a specific key within the PasswordBankState.
 *
 * @template T - A type that extends the keys of PasswordBankState.
 * @param {T} stateKey - The key for which the state is to be retrieved.
 *
 * @returns {MemoizedSelector} - A memoized selector function that returns the state for the given key.
 */
export const getStateOfPasswordBank = <T extends keyof PasswordBankState>(
  stateKey: T
): MemoizedSelector<object, PasswordBankState[T], unknown> => {
  /**
   * createSelector is a function that takes one or more selectors and a result function as its parameters.
   *
   * @param {Function} getAuthState - A selector function that returns the PasswordBankState.
   * @param {Function} resultFunc - A result function that takes the PasswordBankState and returns the state for the given key.
   *
   * @returns {MemoizedSelector<object, PasswordBankState[T], unknown>} - A memoized version of the result function.
   */
  return createSelector(
    selectPasswordBankState,
    (state: PasswordBankState) => state[stateKey]
  )
}
