import { createAction, props } from '@ngrx/store'
import { PasswordBankModel } from '../../models/interfaces/password-bank.model'
import { ResponseModel } from '@venio/shared/models/interfaces'

export const fetchPasswordBanks = createAction(
  '[PasswordBank] Fetch PasswordBanks',
  props<{
    projectId: number
  }>()
)

export const fetchPasswordBanksSuccess = createAction(
  '[PasswordBank] Fetch PasswordBanks Success',
  props<{
    passwordBanks: PasswordBankModel[]
  }>()
)

export const addPassword = createAction(
  '[PasswordBank] Add Password',
  props<{
    projectId: number
    passwordBankModel: PasswordBankModel
    file: File
  }>()
)

export const setAddPasswordResponse = createAction(
  '[PasswordBank] Set Add Password Response',
  props<{
    response: ResponseModel
  }>()
)

export const importPasswords = createAction(
  '[PasswordBank] Import Passwords',
  props<{
    projectId: number
    passwordBankModels: PasswordBankModel[]
  }>()
)

export const setImportPasswordsResponse = createAction(
  '[PasswordBank] Set Import Passwords Response',
  props<{
    response: ResponseModel
  }>()
)

export const clearImportPasswordsResponse = createAction(
  '[PasswordBank] Clear Import Passwords Response'
)

export const deletePassword = createAction(
  '[PasswordBank] Delete Password Response',
  props<{
    projectId: number
    passwordIds: number[]
  }>()
)

export const setDeletePasswordResponse = createAction(
  '[PasswordBank] Set Delete Password Response',
  props<{
    response: ResponseModel
  }>()
)

export const clearResponseMessage = createAction(
  '[PasswordBank] Clear Response Message'
)
