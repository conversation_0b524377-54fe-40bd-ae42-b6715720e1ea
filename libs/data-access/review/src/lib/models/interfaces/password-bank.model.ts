export interface PasswordBankModel {
  passwordBankId?: number | null

  originalUserIdFilePath?: string

  password?: string

  isUserIdFile: boolean | null

  nsfUserIdFilePath?: string
}

export interface PasswordBankImportModel extends PasswordBankModel {
  errorMessage?: string

  nsfFileSize?: number | null

  // when sending import request, entries with isAdded set to false (indicates validation error) will be excluded and only entries with isAdded set to null will be sent.
  // server returns this flag is true or false depending on whether the entry was imported or not.
  isAdded?: boolean | null

  seq?: number | null
}
