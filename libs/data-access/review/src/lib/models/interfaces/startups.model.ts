import { GlobalRoleModel, ProjectGroups, RightModel } from './global.model'
import { SearchRequestModel } from './search.model'

export interface StartupsModel {
  /**
   * Global roles
   */
  globalRoleGroups?: GlobalRoleModel[]

  /**
   * When an user selects a project from anywhere from a list, we then share that selected to reflect to all.
   */
  selectedProjectId?: number

  /**
   * Global project groups. Might get mutate when inherited a slice from other components.
   */
  projectGroups?: ProjectGroups[]

  //UN-COMMENT users?,  userRights, userLocalStorageInfo IF NEEDED IN FUTURE
  // users?: UsersModel[]

  // userRights: RightModel

  // /** assigning logged in user local storage information */
  // userLocalStorageInfo: UserLocalStorageModel

  /**
   * list of selected media ids.
   */
  selectedMediaScope: number[]

  /**
   * List of selected folder scope
   */
  selectedFolderScope: number[]

  acceptanceStatus: boolean
  /**
   * Shared search request object across components.
   */
  searchParams: Partial<SearchRequestModel>

  showDuplicateWarningMessage: boolean

  userRights: RightModel

  isFullFeatureLicense: boolean

  // TODO: add more application startup data property
}

export const startupsInitialState: StartupsModel = {
  globalRoleGroups: [],
  selectedProjectId: null,
  projectGroups: [],
  //users: [],
  //userRights: null,
  //userLocalStorageInfo: null,
  selectedMediaScope: [],
  selectedFolderScope: [],
  acceptanceStatus: null,
  searchParams: {},
  showDuplicateWarningMessage: null,
  userRights: null,
  isFullFeatureLicense: null,
}
