export interface Project {
  projectName: string

  projectId: number

  enableNativeAutoPrefetch?: boolean

  allowTiff?: boolean

  isServiceTypeCase?: boolean
}

export interface CaseModel {
  projectId?: number
  projectName?: string
  projectVersion?: string
  custodianCount?: number
  mediaCount?: number
  totalDocumentCount?: number
  totalFileSize?: string
  createdDate?: string
  createdByUser?: string
  isFilteringServiceCase?: boolean
  serviceType?: string
  caseName?: string
  clientMatterNumber?: number
  dsid?: number
  fsid?: number
  workingMachineName?: string
  createdMachineName?: string
  databaseInstanceName?: string
  allowTiff?: boolean
  maxpagelimit?: number
  ignoreAutoTiffJobForMediaProcessingStatus?: boolean
  allowEmailAnalysis?: boolean
  clientName?: string
  hasOndemandClientAssociated?: boolean
  displayReprocessingLink?: boolean
  displayUploadLink?: boolean
  displayAnalyzeLink?: boolean
  displayReviewLink?: boolean
  displayProductionLink?: boolean
  displayUploadInviteLink?: boolean
  matterNumber?: string
  canDeleteProject?: boolean
  canEditProject?: boolean
  isImageTypePdf?: boolean
  enableTranscriptViewer?: boolean
  isExportServiceCase?: boolean
}
