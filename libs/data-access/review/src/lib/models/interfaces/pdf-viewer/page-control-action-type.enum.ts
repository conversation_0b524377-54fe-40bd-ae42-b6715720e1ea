export enum PageControlActionType {
  FIRST_PAGE,
  <PERSON>XT_PAGE,
  <PERSON><PERSON><PERSON>_PAGE,
  LAST_PAGE,
  PAN,
  REDACTIONS,
  HIGHLIGHTS,
  HIGHLIGHT_FIRST,
  HIGHLIGHT_PREVIOUS,
  HIG<PERSON><PERSON>HT_NEXT,
  HIGHL<PERSON>HT_LAST,
  <PERSON><PERSON><PERSON>,
  CREATE_REDACTION_SET,
  REDACT_WHOLE_PAGE,
  COPY_REDACTION,
  ENABLE_REDACTION,
  HIDE_REDACTION,
  REDACTED_PAGES_ONLY,
  ROTATE_RIGHT,
  ROTATE_LEFT,
  FIT_TO_HEIGHT,
  FIT_TO_WIDTH,
  ACTUAL_SIZE,
  ZOOM_IN,
  ZOOM_OUT,
  <PERSON><PERSON><PERSON>,
  H<PERSON><PERSON><PERSON>HT_GROUP,
  R<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
}
