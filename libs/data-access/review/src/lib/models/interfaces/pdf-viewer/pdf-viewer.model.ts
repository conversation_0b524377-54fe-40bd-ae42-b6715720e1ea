import { UserRights } from '../../constants'
import { ImageExportFormat } from '../viewer.model'
import { PageControlActionType } from './page-control-action-type.enum'

export interface RedactionSet {
  name: string
  id: number
  type: string
  foreColor: string
  backColor: string
  caption: string
  fontSize: string
}

export interface PdfSaveAnnotationPayload {
  xfdfString: string
}
export interface PDFContent {
  content
  annotationData: PdfAnnotationProperties
}

export interface PdfAnnotationProperties {
  xfdf: string
  redactionLoadedDate: Date
  annotationList: Array<AnnotationDetail>
}
export interface AnnotationDetail {
  name: string
  isLocked: boolean
}
export interface PdfPageInfo {
  height: number
  width: number
}

export interface WholePageRedactionPayload {
  xfdf: string
  redactionSet: RedactionSet
  pagesInfo: { [key: number]: PdfPageInfo }
}

export enum PageSelectionOption {
  CURRENT_PAGE = 'CURRENT_PAGE',
  ALL_PAGES = 'ALL_PAGES',
  PAGE_RANGE = 'PAGE_RANGE',
  PAGE_EXCEPT = 'PAGE_EXCEPT',
}

export interface WholePageRedactionOptions {
  pageSelection: PageSelectionOption
  pageFrom: number
  pageTo: number
  exceptPage: number
  redactionSet: number
}

export interface CopyRedactionPayload {
  sourcePageNumberList: string
  isForCurrentDocOnly: boolean
  redactionSetIds: number[]
  searchTempTable: string
  destinationPageNumberList: string
}
export interface IconModel {
  actionType: PageControlActionType
  iconPath: string
  title: string
  userRight?: UserRights | UserRights[]
}

export interface ReasonModel {
  id: number
  reason: string
  isInUse: boolean
}

export interface RedactionType {
  id: number
  redactionTypeName: string
}

export interface RedactionProjGroupAssoc {
  projectGroupId: number
  projectGroupName: string
  permission: string
}

export interface AddUpdateRedactionSetModel {
  name: string
  caption: string
  redactionTypeId: number
  reasonId: number
  foreColor: string
  backColor: string
  fontSize: number
  associatedProjectGroups?: Array<RedactionProjGroupAssoc>
}

export interface WholePageRedaction {
  solidRectangles: RedactionSet[]

  highlights: RedactionSet[]

  currentPageNumber: number

  pageCount: number

  pageList: number[]
}

export interface BulkCopyRedaction {
  redactionSets: RedactionSet[]
  fileId: number
  projectId: number
}

export interface CreateRedactionSet {
  projectId: number
}

export interface PDFPageRotationModel {
  rotationAngle: number
}

export interface ExportDetail {
  exportId: number
  exportName: string
  exportType: string
  pageNumbers: Array<number>
  imageFormat: ImageExportFormat
}
