import { ReviewViewType, SearchFieldViewerType } from '../constants'

export interface ViewSession {
  searchResultTempTable: string

  computedSearchTempTable?: string

  viewTypeRecordsTable?: string

  viewTypePagingTable?: string
}

export interface SearchResultRequestData {
  pageNumber: number

  pageSize: number

  viewSession: ViewSession

  venioFieldIds?: number[]

  customFieldIds?: number[]

  tagIds?: number[]

  overrideGroupSecurity?: boolean

  searchFieldViewerType?: SearchFieldViewerType

  //reviewSetId is optional field because, it is only required for document table data.
  reviewSetId?: number

  isExternalUser?: boolean

  viewType?: ReviewViewType

  reviewLayoutPanelId?: number

  documentShareToken?: string

  showOnlyInclusiveEmailsThreads?: boolean
}

export interface SearchDocumentMetadata {
  key: string

  value: any
}

export interface SearchResponseData {
  fileId: number

  metadata: SearchDocumentMetadata[]
}

export interface DocumentLookupBySeqNoResult {
  fileId: number
  pageNumber: number
  seqNo: number
  paginatedDocumentIndex: number
}

export interface SeqNoInfo {
  fileId: number
  index: number
}

export interface DocumentSearchScopeModel {
  currentfileId: number
  documentNo: number
  isDocumentExistsInSearchScope: boolean
  primaryFileId?: number
}
