export const VIEWER_PANELS: Array<string> = [
  'TextViewer',
  'NativeViewer',
  'ImageViewer',
]

export interface LayoutCreateRequestModel {
  projectUserGroups: Array<LayoutProjectUserGroups>
  layoutModel: Layout
  sourceLayoutId: number
  //isPrivate: boolean
}

export interface LayoutDeleteRequestModel {
  layoutIds: Array<number>
}

export interface LayoutProjectUserGroups {
  projectId: number
  userGroupIds: Array<number>
}
export interface Layout {
  layoutId?: number
  layoutName: string
  //createdBy?: number
  //createdOn?: Date
  isPrivate: boolean
  layoutPanels: Array<LayoutPanel>
}

export interface LayoutPanel {
  isSelected: boolean
  hasField: boolean
  panelId: number
  panelName: string
  fields: Array<LayoutField>
}

export interface LayoutField {
  isSelected: boolean
  fieldId: number
  fieldName: string
  fieldOrder: number
  isCustomField: boolean
}

export interface LayoutResponseModel {
  layoutId: number
  layoutName: string
  createdBy?: string
  createdDate?: string
  createdTime?: string
  isPrivate: boolean
  isFavorite: boolean
  allowManageBasedOnClient: boolean
}

export interface ClientProjectRequestModel {
  clientIds: number[]
}
export interface ClientModel {
  clientId: number
  clientName: string
  address?: string
  contactPerson?: string
  phoneNumber?: string
  mobileNumber?: string
  email?: string
}

export interface ProjectInfo {
  projectName: string
  projectId: number
  enableNativeAutoPrefetch?: boolean
  allowTiff?: boolean
}

export interface GroupInfo {
  groupId: number
  groupName: string
  projectId: number
}

export interface LayoutClientProjectUserGroups {
  clientIds: Array<number>
  projects: Array<LayoutProjectUserGroups>
}

export interface MarkAsFavoriteRequestModel {
  isFavorite: boolean
}
