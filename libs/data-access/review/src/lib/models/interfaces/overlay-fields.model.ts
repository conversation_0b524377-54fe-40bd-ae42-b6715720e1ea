import {
  OverlayCustomFieldsCodingOptions,
  ReplaceActionType,
  UpdateWithType,
} from '../constants/overlay-fields.enums'

interface CommonProps {
  indexDelimiter: number
  fieldName: string
  fieldType: string
  selectedFileIds: string
  searchId: number
  searchResultTempTable: string
}

export interface OverlayCustomFieldsMergePayloadModel extends CommonProps {
  customFieldId: number
  unSelectedFileIds: string
  mergeFieldItems?: string[]
  priorityFields?: string[]
  selectedFileIds: string
  isBatchSelected: boolean
}

export interface OverlayCustomFieldsCodingPayloadModel extends CommonProps {
  codingValue: string
  multiValuedCodingOptions: OverlayCustomFieldsCodingOptions
  codingType: string
  isSequentialValue: boolean
  prefix: string
  startNumber: number
  paddingValue: number
}

export interface FieldValueMappingModel {
  mappedSourceFields: string
  mappedDestinationFields: string
}

export interface OverlayCustomFieldsFieldValuePayloadModel {
  searchResultTempTable: string
  selectedFileIds: string
  isBatchSelected: boolean
  mappedFields: FieldValueMappingModel[]
}

export interface FindReplaceCodingModel {
  customFieldId: number
  fieldName: string
  fieldType: string
  findValue: string
  replaceValue: string
  searchResultTempTable: string
  selectedFileIds: string
  unSelectedFileIds: string
  isBatchSelected: boolean
  searchID: number
}

export enum OverlayCustomFieldsUiType {
  MERGE = 'merge-fields',
  PRIORITY = 'priority-fields',
}

export enum OverlayCustomFieldsHeaderLabel {
  MERGE_TITLE = 'Overlay the field with merged value of the selected fields',
  PRIORITY_TITLE = 'Overlay the field with one of selected field in priority order',
}

export interface OverlayCustomFieldsDelimiterModel {
  indexDelimiter: number
  displayText: string
}

export interface CodingOverlaySaveModel {
  displayFieldName: string
  message: string
  isSuccess: boolean
  updatedCount: number
}

export interface FindReplacePayloadModel {
  customFieldId: number
  displayFieldName: string
  replaceValueType: ReplaceActionType
  findValue: string
  indexDelimeter: number
  replaceValueWith: UpdateWithType
  replaceValue: string
  replaceFieldId: number
  replaceFieldIsCustomField: boolean
  searchResultTempTable: string
  selectedFileIds: string
  unSelectedFileIds: string
  isBatchSelected: boolean
  searchId: number
}
