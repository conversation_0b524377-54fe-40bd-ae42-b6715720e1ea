export interface DocumentShareUserListModel {
  shareExtUserInfoList: DocumentShareUserModel[]
  shareUserInfoList: DocumentShareUserModel[]
}

export interface DocumentShareUserModel {
  email: string

  fullName: string

  userId: number

  userRole: string

  userInfoString: string
}

export interface SharedDocumentUserModel {
  userId: number
  userName: string
  fullName: string
  userRole: string
}

export interface DocumentShareModel {
  allowToAddDocumentNotes: boolean

  documentSelectionType: number

  projectId: number

  searchGlobalTempTableName: string

  sharePermission: string

  sharedBy: number

  sharedDocumentIds: string

  sharedExtUserInfo: string[]

  shareName: string

  sharedInstructionMsg: string

  sharedUserInfo: string[]

  allowToTagUntag: boolean

  allowToApplyRedaction: boolean

  remainingDaysUntilExpiry: number

  externalUsers: DocumentShareUserModel[]

  internalUsers: DocumentShareUserModel[]

  sharedExpiryDate: string

  isWrite: boolean

  sharedOn: string

  documentShareID: number

  carbonCopySender: boolean
}

export interface SharedDocumentDetailModel {
  externalUsers: SharedDocumentUserModel[]

  internalUsers: SharedDocumentUserModel[]

  documentShareID: number

  shareName: string

  shareInstruction: string

  shareLinkExpirationPeriod: string

  sharedOn: string

  sharedExpiryDate: string

  expiredStatus: string

  remainingDaysUntilExpiry: number

  sharedBy: number

  sharedByFullName: string

  projectId: number

  dsid: number

  databaseInstanceName: string | null

  projectName: string | null

  sharedFolderLineage: string

  loggedInUserRole: string | null

  fileCountInFolder: number

  sharedType: string | null

  fsUsername: string | null

  fsPassword: string | null

  token: string | null

  isWrite: boolean

  allowToAddDocumentNotes: boolean

  allowToTagUntag: boolean

  allowToApplyRedaction: boolean

  carbonCopySender: boolean
}

export interface DocumentShareUserResponseModel {
  shareExtUserInfoList: DocumentShareUserModel[]

  shareUserInfoList: DocumentShareUserModel[]
}

export interface SharedDocumentDetailRequestInfo {
  // Only for the client side as it won't be sent to the server
  totalSharedDocCount?: number

  // For server side
  /**
   * Page number to fetch the data
   */
  pageNumber: number
  /**
   * Number of items to fetch in a page
   */
  pageSize: number
  /**
   * Sort field to sort the data
   */
  sortField?: string
  /**
   * Sort order to sort the data
   */
  sortOrder?: string
  /**
   * Search text to filter the data
   */
  searchText?: string
}

export enum SharedDocRequestType {
  SharedByMe = 'SHARED_BY_ME',
  SharedToMe = 'SHARED_TO_ME',
  All = 'All',
}
