import { CommonBase } from './convert-document.models'

/**
 * Interface for the 'OcrImage' data
 */
export interface OcrImageEntity {
  id: string | number // Primary ID
  name: string
}

export interface FileTypesResponseModel {
  fileType: string
  total: number
  fileTypeID: number
  fileTypeDescription: string
  fileTypeGroupID: number
  fileTypeGroup: string
  groupTotal: number
  extension: string
  children?: FileTypesResponseModel[]
  selected: boolean
}

export interface OcrCommandPayloadModel extends CommonBase {
  ocrOption: number
  selectedFileIds: number[]
  selectedRedactionObject: string[]
  extensionList: string[]
  fileTypeList: string[]
  unSelectedFileIds: number[]
  isBatchSelected: boolean
}

export interface OcrImageResponseModel {
  totalSearchHitCount: number
  previousOCRCount: number
  remainOCRCount: number
  totalcountOfOCR: number
}
