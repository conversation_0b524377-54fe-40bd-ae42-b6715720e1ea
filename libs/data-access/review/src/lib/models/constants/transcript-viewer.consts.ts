import {
  AnnotationType,
  AnnotationSymbols,
  TranscriptReport,
  TranscriptReportType,
  TranscriptReportMenu,
} from '../interfaces'

export const ANNOTATION_SYMBOLS: Record<AnnotationType, AnnotationSymbols> = {
  [AnnotationType.DocumentLink]: {
    start: '--D^--',
    end: '--D$--',
    name: 'Document',
  },
  [AnnotationType.Highlight]: {
    start: '--H^--',
    end: '--H$--',
    name: 'Highlight',
  },
  [AnnotationType.Note]: { start: '--N^--', end: '--N$--', name: 'Note' },
}

export const TRANSCRIPT_REPORTS: TranscriptReport[] = [
  {
    id: TranscriptReportType.HIGHLIGHT,
    label: 'Highlight',
    description: 'Highlight',
  },
  {
    id: TranscriptReportType.NOTES,
    label: 'Notes',
    description: 'Notes',
  },
  {
    id: TranscriptReportType.DOCUMENTLINK,
    label: 'Document Link',
    description: 'Document Link',
  },
]

export const TRANSCRIPT_REPORT_MENU: TranscriptReportMenu[] = [
  {
    id: TranscriptReportType.ALL,
    label: 'All',
  },
  {
    id: TranscriptReportType.HIGHLIGHT,
    label: 'Highlight',
  },
  {
    id: TranscriptReportType.NOTES,
    label: 'Notes',
  },
  {
    id: TranscriptReportType.DOCUMENTLINK,
    label: 'Document Link',
  },
]

export const TRANSCRIPT_REPORT = {
  TITLE: (ReportType: string): string => `${ReportType.toUpperCase()} REPORT`,
}
