export enum ReviewViewType {
  Search,
  EmailThread,
}

export enum EmailThreadVisibleType {
  All,
  InclusiveEmailOnly,
}

export enum ReviewDataSourceType {
  Search,
  Review,
}

export enum SearchFieldViewerType {
  List_View = 0,
  Meta_Detail = 1,
  Detail_View = 2,
  Search_Filter = 3,
  View_Similar_Docs = 4,
  Fulltext_Viewer = 5,
  Thread_View = 6,
  Field_Search = 7,
  Tiff_QC = 8,
  Venio_Views = 9,
}

export enum ReviewPanelType {
  Table = 'Table',
  Tag = 'Tag',
  Metadata = 'Metadata',
  ParentChild = 'ParentChild',
  EmailThread = 'EmailThread',
  Duplicates = 'Duplicates',
  Notes = 'Notes',
  TextViewer = 'TextViewer',
  NearNativeViewer = 'NearNativeViewer',
  ImageViewer = 'ImageViewer',
  Coding = 'Coding',
  CodingHistory = 'CodingHistory',
  Transcript = 'Transcript',
  SocialMedia = 'SocialMedia',
  SpreadDocument = 'Spreadsheet',
  SyncfusionViewer = 'SyncfusionViewer',
  Folder = 'Folder',
  SimilarDocument = 'SimilarDocument',
  NearDuplicate = 'NearDuplicate',
  EdaiRelevance = 'EdaiRelevance',
  EdaiPrivilege = 'EdaiPrivilege',
  DocumentHistory = 'DocumentHistory',
}

export enum ReviewBreadcrumbType {
  Main,
  Condition,
  Filter,
}

export enum IndexedDBViewerType {
  Text,
  Html,
  Spreadsheet,
}

export enum SearchScope {
  SAME_ORIGINAL = 'SAME_ORIGINAL',
  CURRENT = 'CURRENT',
}

export enum ReviewLayoutAssociationType {
  User = 'User',
  Client = 'Client',
}
