import { Injectable, WritableSignal, signal } from '@angular/core'
import {
  Layout,
  LayoutResponseModel,
} from '../../models/interfaces/composite-layout/composite-layout.model'
import { Field } from '../../models/interfaces'
import { keyBy, orderBy } from 'lodash'
@Injectable({
  providedIn: 'root',
})
export class CompositeLayoutState {
  public layoutPanelFieldMapping: WritableSignal<{
    [key: number]: Array<Field>
  }> = signal([])

  public userSelectedLayout: WritableSignal<Layout> = signal(null)

  public userLayouts: WritableSignal<Array<LayoutResponseModel>> = signal([])

  public showLayoutListing: WritableSignal<boolean> = signal(false)

  constructor() {}

  public getFieldsSortedByLayoutOrder<T>(
    datasource: T[],
    key: string,
    panelName: string
  ): T[] {
    const panelFields = this.userSelectedLayout().layoutPanels.find(
      (p) => p.panelName === panelName
    )?.fields
    const fieldOrderMap = keyBy(panelFields, 'fieldName')

    return orderBy(
      datasource,
      [
        (item): number =>
          fieldOrderMap[item[key]]?.fieldOrder ?? Number.MAX_SAFE_INTEGER,
      ],
      ['asc']
    )
  }

  public getStringFieldsSortedByLayoutOrder(
    datasource: string[],
    key: string,
    panelName: string
  ): string[] {
    const panelFields = this.userSelectedLayout().layoutPanels.find(
      (p) => p.panelName === panelName
    )?.fields
    const fieldOrderMap = keyBy(panelFields, 'fieldName')

    //if(datasource.every((field)=>typeof field ==='string'))
    if (key === '') {
      return orderBy(
        datasource,
        [
          (item): number =>
            fieldOrderMap[item]?.fieldOrder ?? Number.MAX_SAFE_INTEGER,
        ],
        ['asc']
      )
    }
  }
}
