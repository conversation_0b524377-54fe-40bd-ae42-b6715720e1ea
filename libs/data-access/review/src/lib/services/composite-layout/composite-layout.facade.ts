import { Injectable } from '@angular/core'
import { CompositeLayoutService } from './composite-layout.service'
import { map, Observable, Subject } from 'rxjs'
import {
  ClientProjectRequestModel,
  LayoutCreateRequestModel,
  LayoutDeleteRequestModel,
  MarkAsFavoriteRequestModel,
} from '../../models/interfaces/composite-layout/composite-layout.model'

@Injectable({
  providedIn: 'root',
})
export class CompositeLayoutFacade {
  public notifyLoadLayout: Subject<void> = new Subject<void>()

  constructor(private layoutService: CompositeLayoutService) {}

  public fetchLayouts$<T>(projectId: number): Observable<T> {
    return this.layoutService.fetchLayouts$<T>(projectId)
  }

  public fetchLayoutPanels$<T>(layoutId: number): Observable<T> {
    return this.layoutService.fetchLayoutPanels$<T>(layoutId)
  }

  public fetchLayoutPanelFields$<T>(
    layoutId: number,
    panelId: number
  ): Observable<T> {
    return this.layoutService.fetchLayoutPanelFields$<T>(layoutId, panelId)
  }

  public createLayout$<T>(payload: LayoutCreateRequestModel): Observable<T> {
    return this.layoutService.createLayout$<T>(payload)
  }

  public cloneLayout$<T>(payload: LayoutCreateRequestModel): Observable<T> {
    return this.layoutService.cloneLayout$<T>(payload)
  }

  public updateLayout$<T>(
    layoutId: number,
    payload: LayoutCreateRequestModel
  ): Observable<T> {
    return this.layoutService.updateLayout$<T>(layoutId, payload)
  }

  public deleteLayout$<T>(payload: LayoutDeleteRequestModel): Observable<T> {
    return this.layoutService.deleteLayout$<T>(payload)
  }

  public fetchDefaultLayout$<T>(projectId: number): Observable<T> {
    return this.layoutService.fetchDefaultLayout$<T>(projectId)
  }

  public fetchLayout$<T>(layoutId: number): Observable<T> {
    return this.layoutService.fetchLayout$<T>(layoutId)
  }

  public fetchClients$<T>(): Observable<T> {
    return this.layoutService.fetchClients$<T>()
  }

  public fetchCasesByClientIds<T>(
    payload: ClientProjectRequestModel
  ): Observable<T> {
    return this.layoutService.fetchCasesByClientIds$<T>(payload)
  }

  public fetchCases$<T>(): Observable<T[]> {
    return this.layoutService.fetchCases$<T[]>().pipe(
      map(
        (projects: any[]) =>
          projects.map((p: any) => ({
            projectId: p.ProjectId,
            projectName: p.ProjectName,
          })) as T[]
      )
    )
  }

  public fetchuserGroups$<T>(projectId: number): Observable<T> {
    return this.layoutService.fetchUserGroups$<T>(projectId)
  }

  public fetchProjectUserGroups$<T>(projectIds: number[]): Observable<T> {
    return this.layoutService.fetchProjectUserGroups$<T>(projectIds)
  }

  public fetchLayoutClientProjectUsergroups<T>(
    layoutId: number
  ): Observable<T> {
    return this.layoutService.fetchLayoutClientProjectUsergroups$<T>(layoutId)
  }

  public isLayoutNameTaken$<T>(
    layoutId: number,
    layoutName: string
  ): Observable<T> {
    return this.layoutService.isLayoutNameTaken$<T>(layoutId, layoutName)
  }

  public markLayoutAsFavorite$<T>(
    projectId: number,
    layoutId: number,
    model: MarkAsFavoriteRequestModel
  ): Observable<T> {
    return this.layoutService.markLayoutasFavorite$<T>(
      projectId,
      layoutId,
      model
    )
  }
}
