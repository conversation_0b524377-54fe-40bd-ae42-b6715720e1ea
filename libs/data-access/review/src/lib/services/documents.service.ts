import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from '@venio/shared/environments'
import { Observable, Subject } from 'rxjs'
import {
  DocumentViewerLogModel,
  ImageTypeExportDetail,
  PDFLoadingArgs,
  SearchResultRequestData,
  SearchResultRequestModel,
  TagSummaryRequestModel,
  Viewer,
} from '../models/interfaces'
import { PageControlActionType } from '@venio/shared/models/constants'
import { TiffViewerPayload } from '../models/interfaces/tiff-viewer/tiff-viewer.model'

@Injectable({
  providedIn: 'root',
})
export class DocumentsService {
  private get _apiUrl(): string {
    return environment.apiUrl
  }

  public updateDocumentSelection$: Subject<void> = new Subject<void>()

  public loadViewerContainer$: Subject<number> = new Subject<number>()

  public viewerContainerLoaded$: Subject<boolean> = new Subject<boolean>()

  public viewerComponentReady$: Subject<Viewer> = new Subject<Viewer>()

  public loadPDF$: Subject<PDFLoadingArgs> = new Subject<PDFLoadingArgs>()

  public updateDocumentSelectionToReviewPanel$: Subject<void> =
    new Subject<void>()

  public loadProducedPDF$: Subject<PDFLoadingArgs> =
    new Subject<PDFLoadingArgs>()

  public loadExportDetails$: Subject<ImageTypeExportDetail> =
    new Subject<ImageTypeExportDetail>()

  public loadNearNative$: Subject<number> = new Subject<number>()

  public loadFulltextViewer$: Subject<number> = new Subject<number>()

  public loadTiffViewer$: Subject<TiffViewerPayload> =
    new Subject<TiffViewerPayload>()

  public documentNavigation$: Subject<PageControlActionType> =
    new Subject<PageControlActionType>()

  public resetDocumentSelection$: Subject<void> = new Subject<void>()

  constructor(private http: HttpClient) {}

  public exportToFile(
    searchResultRequestModel: SearchResultRequestModel
  ): Observable<any> {
    return this.http.post(
      this._apiUrl +
        'search/project/' +
        searchResultRequestModel?.projectId +
        '/results/downloadCSV',
      searchResultRequestModel,
      {
        responseType: 'blob',
      }
    )
  }

  public fetchTagSummary(
    projectId: number,
    requestModel: TagSummaryRequestModel
  ): Observable<any> {
    return this.http.post(
      this._apiUrl + `/tags/project/${projectId}/tag-folder-summary`,
      requestModel
    )
  }

  public fetchTagHistory(projectId: number, fileId: number): Observable<any> {
    return this.http.get(
      this._apiUrl +
        `tags/project/${projectId}/file/${fileId}/tag-folder-history`
    )
  }

  public saveDocumentViewerLog$<T>(
    projectId: number,
    payload: DocumentViewerLogModel
  ): Observable<T> {
    return this.http.post<T>(
      this._apiUrl + `/project/${projectId}/document-viewer-log`,
      payload
    )
  }

  public lookupDocumentIdAndPageBySeqNo$<T>(
    projectId: number,
    seqNo: number,
    searchResultRequestModel: SearchResultRequestData
  ): Observable<T> {
    return this.http.post<T>(
      this._apiUrl + `/project/${projectId}/SeqNo/${seqNo}/LookupDocument`,
      searchResultRequestModel
    )
  }
}
