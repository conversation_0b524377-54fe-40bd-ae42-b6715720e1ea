import { HttpClient } from '@angular/common/http'
import { inject, Injectable } from '@angular/core'
import { environment } from '@venio/shared/environments'
import { Observable } from 'rxjs'
import { PasswordBankModel } from '../models/interfaces'
import { ResponseModel } from '@venio/shared/models/interfaces'

@Injectable({
  providedIn: 'root',
})
export class PasswordBankService {
  private get _apiUrl(): string {
    return environment.apiUrl
  }

  private readonly httpClient = inject(HttpClient)

  public fetchPasswordBanks$(projectId: number): Observable<ResponseModel> {
    return this.httpClient.get<ResponseModel>(
      `${this._apiUrl}/project/${projectId}/passwordBank`
    )
  }

  public addPassword$(
    projectId: number,
    payload: PasswordBankModel,
    file: File
  ): Observable<ResponseModel> {
    const fd = new FormData()
    fd.append(file?.name ? 'file' : file?.name, file)
    fd.append('passwwordModel', JSON.stringify(payload))

    return this.httpClient.post<ResponseModel>(
      `${this._apiUrl}/project/${projectId}/passwordBank`,
      fd
    )
  }

  public importPasswords$(
    projectId: number,
    payload: PasswordBankModel[]
  ): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this._apiUrl}/project/${projectId}/passwordBanks`,
      payload
    )
  }

  public deletePassword$(
    projectId: number,
    passwordIds: number[]
  ): Observable<ResponseModel> {
    return this.httpClient.delete<ResponseModel>(
      `${this._apiUrl}/project/${projectId}/passwordBank`,
      {
        body: passwordIds,
      }
    )
  }
}
