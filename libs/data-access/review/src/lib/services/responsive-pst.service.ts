import { HttpClient, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from '@venio/shared/environments'
import {
  CreatePSTRequest,
  ListFilesForPstRequest,
} from '../models/interfaces/responsive-pst.model'
import { Observable } from 'rxjs'

@Injectable({
  providedIn: 'root',
})
export class ResponsivePstService {
  private get _apiUrl(): string {
    return environment.apiUrl
  }

  constructor(private http: HttpClient) {}

  public readonly fetchFilesForResponsivePstInfo$ = <T>(
    responsivePstRequest: ListFilesForPstRequest,
    projectId: number
  ): Observable<T> =>
    this.http.post<T>(
      `${this._apiUrl}project/${projectId}/ResponsivePst/FileList`,
      responsivePstRequest
    )

  public readonly fetchErrorFilesCSVInfo$ = <Blob>(
    responsivePstRequest: ListFilesForPstRequest,
    projectId: number
  ): Observable<Blob> =>
    this.http.post<Blob>(
      `${this._apiUrl}project/${projectId}/ResponsivePst/ErrorFileCsv`,
      responsivePstRequest,
      {
        responseType: 'blob' as 'json',
      }
    )

  public readonly createPSTActionInfo$ = <T>(
    createPSTRequest: CreatePSTRequest,
    projectId: number
  ): Observable<T> =>
    this.http.post<T>(
      `${this._apiUrl}project/${projectId}/ResponsivePst`,
      createPSTRequest
    )

  public readonly deletePSTActionInfo$ = <T>(
    pstJobId: number,
    projectId: number
  ): Observable<T> =>
    this.http.delete<T>(
      `${this._apiUrl}project/${projectId}/ResponsivePst/${pstJobId}`
    )

  public readonly downloadPSTInfo$ = <Blob>(
    pstJobId: number,
    projectId: number,
    filetype: string
  ): Observable<Blob> =>
    this.http.get<Blob>(
      `${this._apiUrl}project/${projectId}/ResponsivePst/${pstJobId}/Download`,
      {
        params: new HttpParams().set('filetype', String(filetype)),
        responseType: 'blob' as 'json',
      }
    )

  public readonly getFilesForPSTStatusInfo$ = <T>(
    projectId: number
  ): Observable<T> =>
    this.http.get<T>(`${this._apiUrl}project/${projectId}/ResponsivePst`)
}
