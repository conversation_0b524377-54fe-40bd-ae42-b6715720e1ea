import { HttpClient, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from '@venio/shared/environments'
import { Observable, firstValueFrom } from 'rxjs'
import {
  BreadCrumb,
  DelSearchHistoryRequestModel,
  ReviewBreadCrumb,
  SaveSearchRequestModel,
  SearchHistoryRequestModel,
  SearchInputParams,
  SearchRequestModel,
  SearchStatusModel,
} from '../models/interfaces/search.model'
import { FieldFacade } from '../state/field'
import { ResponseModel } from '@venio/shared/models/interfaces'

@Injectable({
  providedIn: 'root',
})
export class SearchService {
  constructor(private http: HttpClient, private fieldFacade: FieldFacade) {}

  private get _apiUrl(): string {
    return environment.apiUrl
  }

  public search$(
    searchRequestModel: SearchRequestModel
  ): Observable<ResponseModel> {
    if (searchRequestModel?.reviewSetId > 0) {
      const docUrl =
        this._apiUrl +
        'project/' +
        searchRequestModel.projectId +
        '/reviewset/' +
        searchRequestModel.reviewSetId +
        '/checkout'
      return this.http.post<ResponseModel>(docUrl, searchRequestModel)
    }
    return this.http.post<ResponseModel>(
      this._apiUrl + 'search/project/' + searchRequestModel.projectId + '/go',
      searchRequestModel,
      {
        params: new HttpParams().set(
          'documentShareToken',
          searchRequestModel.documentShareToken
            ? String(searchRequestModel.documentShareToken)
            : 'NA'
        ),
      }
    )
  }

  public constructFilterQuery_old(
    breadcrumbs: BreadCrumb[],
    payload: SearchInputParams
  ): SearchInputParams {
    const truncated = breadcrumbs.slice(
      0,
      payload.isBreadCrumbClicked ? payload.breadCrumbIndex : undefined
    )

    const joinedExpression = truncated
      .filter((x) => x.filterText?.toLocaleLowerCase() !== 'home')
      .reduce((accum, el, xIndex) => {
        //If breadcrumb queries already has multiple search term, then break those search terms and append the current expression with And operator
        if (this.isMultipleTermExpression(accum)) {
          const multiTermQueries: string[] = accum.split('\n')
          let finalQuery = ''
          multiTermQueries.forEach((query) => {
            finalQuery +=
              '((' + query + ')' + ' AND ' + '(' + el.query + '))' + '\n'
          })
          return (accum = finalQuery.trim())
        }
        //If current search expression is multiple search term expression, then break those search terms and append the breadcrumb query expressions with And operator
        else if (this.isMultipleTermExpression(el.query)) {
          const multiTermQueries: string[] = el.query.split('\n')
          let finalQuery = ''
          multiTermQueries.forEach((query) => {
            if (accum.length > 0)
              finalQuery +=
                '((' + accum + ')' + ' AND ' + '(' + query + '))' + '\n'
            else finalQuery += query + '\n'
          })
          return (accum = finalQuery.trim())
        }
        return (accum += `${xIndex > 0 ? ' AND ' : ''}(${el.query})`)
      }, '')

    const finalized: SearchInputParams = {
      ...payload,
      searchExpression: !joinedExpression
        ? payload.searchExpression
        : this.getFinalQuery_old(joinedExpression, payload.searchExpression),
    }
    return finalized
  }

  private getFinalQuery_old(
    joinedExpression: string,
    currentExpression: string
  ): string {
    let finalQuery = ''
    //If breadcrumb queries already has multiple search term, then break those search terms and append the current expression with And operator
    if (this.isMultipleTermExpression(joinedExpression)) {
      const multiTermQueries: string[] = joinedExpression.split('\n')
      multiTermQueries.forEach((query) => {
        finalQuery +=
          '((' + query + ')' + ' AND ' + '(' + currentExpression + '))' + '\n'
      })
    }
    //If current search expression is multiple search term expression, then break those search terms and append the breadcrumb query expressions with And operator
    else if (this.isMultipleTermExpression(currentExpression)) {
      const multiTermQueries: string[] = currentExpression.split('\n')
      multiTermQueries.forEach((query) => {
        if (joinedExpression.length > 0)
          finalQuery +=
            '((' + joinedExpression + ')' + ' AND ' + '(' + query + '))' + '\n'
        else finalQuery += query + '\n'
      })
    }
    // once all queries are joined with `AND` the incoming should be in last.
    else finalQuery = `(${joinedExpression}) AND (${currentExpression})`
    return finalQuery.trim()
  }

  private isMultipleTermExpression(expression: string): boolean {
    return expression?.indexOf('\n') > 0
  }

  public async constructFilterQuery(filterParam): Promise<string> {
    let finalQuery = ''
    const selectedField = await firstValueFrom(
      this.fieldFacade.getInternalFieldName$(filterParam.field)
    )

    const query = filterParam.value
    switch (filterParam.operator.toUpperCase()) {
      case 'CONTAINS':
        finalQuery = selectedField + ' LIKE ' + '"*' + query + '*"'
        break
      case 'HAS':
        finalQuery = selectedField + ' HAS ' + '"' + query + '"'
        break
      case 'EQUALS':
        finalQuery = selectedField + ' = ' + '"' + query + '"'
        break
      case 'BEGINS_WITH':
        finalQuery = selectedField + ' LIKE ' + '"' + query + '*"'
        break
      case 'ENDS_WITH':
        finalQuery = selectedField + ' LIKE ' + '"*' + query + '"'
        break
      case 'LESS_THAN':
        finalQuery = selectedField + '<' + '"' + query + '"'
        break
      case 'LESS_THAN_EQUALS':
        finalQuery = selectedField + '<=' + '"' + query + '"'
        break
      case 'GREATER_THAN':
        finalQuery = selectedField + '>' + '"' + query + '"'
        break
      case 'GREATER_THAN_EQUALS':
        finalQuery = selectedField + '>=' + '"' + query + '"'
        break
      case 'BETWEEN':
        finalQuery = selectedField + ' BETWEEN ' + query
        break
      case 'IS_NOT_NULL':
        finalQuery = selectedField + ' IS NOT NULL '
        break
      case 'IS_NULL':
        finalQuery = selectedField + ' IS NULL '
        break
      default:
        break
    }
    return finalQuery
  }

  public generateQueryFromBreadcrumbs(breadcrumbs: ReviewBreadCrumb[]): string {
    if (!breadcrumbs || breadcrumbs.length === 0) {
      return ''
    }

    let query = ''

    for (let i = 0; i < breadcrumbs.length; i++) {
      const crumb = breadcrumbs[i]

      if (crumb.isLogicalGroup && crumb.children?.length > 0) {
        // let groupQuery = this.generateQueryFromBreadcrumbs(crumb.children)
        // if (groupQuery.trim().length > 0) {
        //   query += i > 0 ? ` ${crumb.operator} ` : ' ' + '(' + this.generateQueryFromBreadcrumbs(crumb.children) + ')';
        // }
        query += i > 0 ? ` ${crumb.operator} ` : ' '

        query += '(' + this.generateQueryFromBreadcrumbs(crumb.children) + ')'
      } else {
        if (crumb?.expression) {
          query += i > 0 ? ` ${crumb.operator} ` : ' '
          query += crumb?.expression
        }
      }
    }
    return query
  }

  public readonly fetchSearchHistoryLog$ = <T>(
    projectId: number,
    reqModel: SearchHistoryRequestModel
  ): Observable<T> =>
    this.http.post<T>(
      `${this._apiUrl}project/${projectId}/search/history`,
      reqModel
    )

  public readonly deleteSearchHistory$ = <T>(
    projectId: number,
    reqModel: DelSearchHistoryRequestModel
  ): Observable<T> =>
    this.http.post<T>(
      `${this._apiUrl}project/${projectId}/search/deletesearchhistory`,
      reqModel
    )

  public readonly fetchDSJobStatus$ = <T>(
    searchStatusModel: SearchStatusModel
  ): Observable<T> =>
    this.http.post<T>(
      `${this._apiUrl}Search/${searchStatusModel.projectId}/SearchJobStatus`,
      searchStatusModel
    )

  public readonly fetchDSJobStatusCount$ = <T>(
    searchStatusModel: SearchStatusModel
  ): Observable<T> =>
    this.http.post<T>(
      `${this._apiUrl}Search/${searchStatusModel.projectId}/SearchJobCount`,
      searchStatusModel
    )

  public readonly getSavedSearchWithTags$ = <T>(
    projectId: number
  ): Observable<T> =>
    this.http.get<T>(
      `${this._apiUrl}saved-search/project/${projectId}/tag-groups`
    )

  public readonly saveSearch$ = <T>(
    projectId: number,
    saveSearchRequest: SaveSearchRequestModel
  ): Observable<T> =>
    this.http.post<T>(
      `${this._apiUrl}search/project/${projectId}/save`,
      saveSearchRequest
    )
}
