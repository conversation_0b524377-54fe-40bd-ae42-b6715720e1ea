import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from '@venio/shared/environments'
import { Observable } from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'

@Injectable({
  providedIn: 'root',
})
export class ViewService {
  constructor(private http: HttpClient) {}

  private get _apiUrl(): string {
    return environment.apiUrl
  }

  public readonly fetchSelectedView$ = <T>(
    projectId: number,
    viewId: number
  ): Observable<T> =>
    this.http.get<T>(`${this._apiUrl}project/${projectId}/view/${viewId}`)

  public saveUserDefaultView(
    projectId: number,
    viewId: number
  ): Observable<ResponseModel> {
    return this.http.post<ResponseModel>(
      `${this._apiUrl}project/${projectId}/View/${viewId}/UserDefaultView`,
      {}
    )
  }

  public fetchUserDefaultView(projectId: number): Observable<ResponseModel> {
    return this.http.get<ResponseModel>(
      `${this._apiUrl}project/${projectId}/UserDefaultView`
    )
  }
}
