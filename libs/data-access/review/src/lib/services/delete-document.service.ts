import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from '@venio/shared/environments'
import { DeleteDocumentOptions } from '../models/interfaces/delete-document.model'
import { Observable } from 'rxjs'

@Injectable({
  providedIn: 'root',
})
export class DeleteDocumentService {
  constructor(private http: HttpClient) {}

  private get _apiUrl(): string {
    return environment.apiUrl
  }

  public fetchDeleteDocumentSummary$<T>(
    projectId: number,
    deleteDocumentOptions: DeleteDocumentOptions
  ): Observable<T> {
    return this.http.post<T>(
      `${this._apiUrl}project/${projectId}/v2/documents/DeleteSummary`,
      deleteDocumentOptions
    )
  }

  public deleteDocument$<T>(
    projectId: number,
    deleteDocumentOptions: DeleteDocumentOptions
  ): Observable<T> {
    const options = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      }),
      body: deleteDocumentOptions,
    }

    return this.http.delete<T>(
      `${this._apiUrl}project/${projectId}/v2/documents`,
      options
    )
  }

  public getDeleteDocumentStatus$<T>(
    projectId: number,
    sessionId: string
  ): Observable<T> {
    return this.http.get<T>(
      `${this._apiUrl}project/${projectId}/documents/delete/status?sessionId=${sessionId}`
    )
  }

  public checkIfFilesToBeDeletedInFolder$<T>(
    projectId: number,
    deleteDocumentOptions: DeleteDocumentOptions
  ): Observable<T> {
    return this.http.post<T>(
      `${this._apiUrl}project/${projectId}/v2/documents/folderassociation`,
      deleteDocumentOptions
    )
  }
}
