import { HttpClient, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from '@venio/shared/environments'
import { Observable } from 'rxjs'
import { EditDocumentNoteRequestModel } from '../../models/interfaces'

@Injectable({
  providedIn: 'root',
})
export class DocumentNotesService {
  constructor(private http: HttpClient) {}

  private get _apiUrl(): string {
    return environment.apiUrl
  }

  public fetchDocumentNotes$ = <T>(
    fileId: number,
    projectId: number
  ): Observable<T> =>
    this.http.get<T>(`${this._apiUrl}file/notes`, {
      params: new HttpParams()
        .set('fileId', fileId.toString())
        .set('projectId', projectId.toString()),
    })

  public addDocumentNote$<T>(
    editDocumentNoteRequestModel: EditDocumentNoteRequestModel,
    fileId: number,
    isReply: boolean,
    projectId: number,
    docShareToken?: string
  ): Observable<T> {
    return this.http.post<T>(
      this._apiUrl + '/DocumentNote',
      editDocumentNoteRequestModel,
      {
        params: new HttpParams()
          .set('FileId', String(fileId))
          .set('IsReply', String(isReply))
          .set('ProjectId', String(projectId))
          .set('docShareToken', docShareToken ? String(docShareToken) : 'NA'),
      }
    )
  }

  public editDocumentNote$<T>(
    editDocumentNoteRequestModel: EditDocumentNoteRequestModel,
    projectId: number
  ): Observable<T> {
    return this.http.put<T>(
      this._apiUrl + '/DocumentNote',
      editDocumentNoteRequestModel,
      {
        params: new HttpParams().set('ProjectId', String(projectId)),
      }
    )
  }

  public deleteDocumentNote$<T>(
    commentId: number,
    projectId: number
  ): Observable<T> {
    return this.http.delete<T>(this._apiUrl + '/DocumentNote', {
      params: new HttpParams()
        .set('CommentId', String(commentId))
        .set('ProjectId', String(projectId)),
    })
  }
}
