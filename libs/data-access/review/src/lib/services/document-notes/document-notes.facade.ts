import { Injectable } from '@angular/core'
import { Observable, Subject } from 'rxjs'

import { DocumentNotesService } from './document-notes.service'
import { EditDocumentNoteRequestModel } from '../../models/interfaces'
import { Store } from '@ngrx/store'
import { hasGroupRight } from '../../state/startups'
import { UserRights } from '../../models/constants'

@Injectable({
  providedIn: 'root',
})
export class DocumentNotesFacade {
  public fetchDocumentNotesAction: Subject<{ fileId; projectId }> =
    new Subject<{ fileId; projectId }>()

  constructor(
    private documentNotesService: DocumentNotesService,
    private store: Store
  ) {}

  public fetchDocumentNotes<T>(
    projectId: number,
    fileId: number
  ): Observable<T> {
    return this.documentNotesService.fetchDocumentNotes$(fileId, projectId)
  }

  public addDocumentNote<T>(
    editDocumentNoteRequestModel: EditDocumentNoteRequestModel,
    fileId: number,
    isReply: boolean,
    projectId: number,
    docShareToken?: string
  ): Observable<T> {
    return this.documentNotesService.addDocumentNote$<T>(
      editDocumentNoteRequestModel,
      fileId,
      isReply,
      projectId,
      docShareToken
    )
  }

  public editDocumentNote<T>(
    editDocumentNoteRequestModel: EditDocumentNoteRequestModel,
    projectId: number
  ): Observable<T> {
    return this.documentNotesService.editDocumentNote$<T>(
      editDocumentNoteRequestModel,
      projectId
    )
  }

  public deleteDocumentNote<T>(
    commentId: number,
    projectId: number
  ): Observable<T> {
    return this.documentNotesService.deleteDocumentNote$<T>(
      commentId,
      projectId
    )
  }

  public allowToAddNotesForCreatorOnly(): Observable<boolean> {
    return this.store.select(
      hasGroupRight(
        UserRights.ALLOW_TO_ADD_DOCUMENT_NOTES_VISIBLE_TO_CREATOR_ONLY
      )
    )
  }

  public allowToAddNotesForCreatorGroupOnly(): Observable<boolean> {
    return this.store.select(
      hasGroupRight(
        UserRights.ALLOW_TO_ADD_DOCUMENT_NOTES_VISIBLE_TO_CREATORS_GROUP_ONLY
      )
    )
  }

  public allowToAddNotesForAllUsers(): Observable<boolean> {
    return this.store.select(
      hasGroupRight(UserRights.ALLOW_TO_ADD_DOCUMENT_NOTES_VISIBLE_TO_ALL_USERS)
    )
  }
}
