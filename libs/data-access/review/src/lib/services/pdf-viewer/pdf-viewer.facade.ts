import { inject, Injectable, signal, WritableSignal } from '@angular/core'
import {
  BehaviorSubject,
  Observable,
  Subject,
  map,
  switchMap,
  take,
} from 'rxjs'
import {
  AddUpdateRedactionSetModel,
  CopyRedactionPayload,
  PDFPageRotationModel,
  PdfSaveAnnotationPayload,
  ReasonModel,
  WholePageRedactionPayload,
} from '../../models/interfaces/pdf-viewer/pdf-viewer.model'
import { ActionModel } from '../../models/interfaces'
import { PdfViewerService } from './pdf-viewer.service'
import { SearchFacade } from '../../state/search'

@Injectable({
  providedIn: 'root',
})
export class PdfViewerFacade {
  public highlightGroupActionHandler: BehaviorSubject<ActionModel> =
    new BehaviorSubject<ActionModel>(null)

  public saveAnnotationAction: Subject<void> = new Subject<void>()

  public saveActionCompleteAction: Subject<void> = new Subject<void>()

  public isAnnotationChanged: WritableSignal<boolean> = signal(false)

  private pdfViewerService: PdfViewerService = inject(PdfViewerService)

  private searchFacade: SearchFacade = inject(SearchFacade)

  constructor() {}

  public fetchImageStatus<T>(projectId: number, fileId: number): Observable<T> {
    return this.pdfViewerService.fetchImageStatus<T>(projectId, fileId)
  }

  public fetchPdfDocument(projectId: number, fileId: number): Observable<Blob> {
    return this.pdfViewerService.fetchPdfDocument(projectId, fileId)
  }

  public fetchRedactionSets<T>(projectId: number): Observable<T> {
    return this.pdfViewerService.fetchRedactionSets<T>(projectId)
  }

  public fetchPdfAnnotations<T>(
    projectId: number,
    fileId: number
  ): Observable<T> {
    return this.pdfViewerService.fetchPdfAnnotations<T>(projectId, fileId)
  }

  public savePdfAnnotation<T>(
    projectId: number,
    fileId: number,
    payload: PdfSaveAnnotationPayload
  ): Observable<T> {
    return this.pdfViewerService.savePdfAnnotation<T>(
      projectId,
      fileId,
      payload
    )
  }

  public addWholePageRedaction<T>(
    projectId: number,
    fileId: number,
    payload: WholePageRedactionPayload
  ): Observable<T> {
    return this.pdfViewerService.addWholePageRedaction<T>(
      projectId,
      fileId,
      payload
    )
  }

  public bulkCopyRedaction<T>(
    projectId: number,
    fileId: number,
    payload: Partial<CopyRedactionPayload>
  ): Observable<T> {
    return this.searchFacade.getSearchTempTables$.pipe(
      map((tempTables) => ({
        ...payload,
        searchTempTable: tempTables.searchResultTempTable,
      })),
      switchMap((copyPayload: CopyRedactionPayload) => {
        return this.pdfViewerService.bulkCopyRedaction<T>(
          projectId,
          fileId,
          copyPayload
        )
      })
    )
  }

  public fetchRedactionTypes<T>(projectId: number): Observable<T> {
    return this.pdfViewerService.fetchRedactionTypes<T>(projectId)
  }

  public fetchRedactionReasons<T>(projectId: number): Observable<T> {
    return this.pdfViewerService.fetchRedactionReasons<T>(projectId)
  }

  public setRedactionReasons(reasons: Array<ReasonModel>): void {
    this.pdfViewerService.redactionReasons$.next(reasons)
  }

  public getRedactionReasons(): Observable<Array<ReasonModel>> {
    return this.pdfViewerService.redactionReasons$
  }

  public fetchProjectGroups<T>(projectId: number): Observable<T> {
    return this.pdfViewerService.fetchProjectGroups<T>(projectId)
  }

  public addRedactionSet<T>(
    projectId: number,
    payload: AddUpdateRedactionSetModel
  ): Observable<T> {
    return this.pdfViewerService.addUpdateRedactionSet<T>(projectId, payload)
  }

  public lookupRedactionSet<T>(
    projectId: number,
    name: string,
    currentRedactionSetId: number
  ): Observable<T> {
    return this.pdfViewerService.lookupRedactionSetNameExists(
      projectId,
      name,
      currentRedactionSetId
    )
  }

  public addReason<T>(projectId: number, payload: ReasonModel): Observable<T> {
    return this.pdfViewerService.addReason(projectId, payload)
  }

  public updateReason<T>(
    projectId: number,
    reasonId: number,
    payload: ReasonModel
  ): Observable<T> {
    return this.pdfViewerService.updateReason(projectId, reasonId, payload)
  }

  public lookupRedactionReasonExists<T>(
    projectId: number,
    reason: string
  ): Observable<T> {
    return this.pdfViewerService.lookupRedactionReasonExists(projectId, reason)
  }

  public deleteReason<T>(projectId: number, reasonId: number): Observable<T> {
    return this.pdfViewerService.deleteReason(projectId, reasonId)
  }

  public getRedactedPages<T>(projectId: number, fileId: number): Observable<T> {
    return this.pdfViewerService.fetchRedactedPages(projectId, fileId)
  }

  public rotatePage<T>(
    projectId: number,
    fileId: number,
    pageNumber: number,
    payload: PDFPageRotationModel
  ): Observable<T> {
    return this.pdfViewerService.rotatePage(
      projectId,
      fileId,
      pageNumber,
      payload
    )
  }

  public fetchExportDetails<T>(
    projectId: number,
    fileId: number
  ): Observable<T> {
    return this.pdfViewerService.fetchExportDetails<T>(projectId, fileId)
  }

  public fetchProducedPdfImage(
    projectId: number,
    exportId: number,
    fileId: number
  ): Observable<Blob> {
    return this.pdfViewerService.fetchProducedPdfImage(
      projectId,
      exportId,
      fileId
    )
  }

  public fetchProducedTiffImage(
    projectId: number,
    exportId: number,
    fileId: number,
    pageNumber: number
  ): Observable<Blob> {
    return this.pdfViewerService.fetchProducedTiffImage(
      projectId,
      exportId,
      fileId,
      pageNumber
    )
  }

  public executeWithAnnotationCheck(
    handler: (...args: any[]) => void,
    ...args: any[]
  ): void {
    if (this.isAnnotationChanged()) {
      this.saveAnnotationAction.next()
      this.saveActionCompleteAction.pipe(take(1)).subscribe(() => {
        handler(...args)
      })
    } else {
      handler(...args)
    }
  }
}
