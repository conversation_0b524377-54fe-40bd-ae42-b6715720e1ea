import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from '@venio/shared/environments'
import { Observable } from 'rxjs'
import {
  HtmlRtfCommandPayloadModel,
  OcrCommandPayloadModel,
} from '../../models/interfaces/convert-document'

@Injectable({
  providedIn: 'root',
})
export class OcrImageService {
  constructor(private http: HttpClient) {}

  private get _apiUrl(): string {
    return environment.apiUrl
  }

  /*
   * Fetches either the summary of OCR
   * @param payload requested payload to fetch data based on criteria
   */
  public readonly fetchOcrSummary = <T>(
    payload: Partial<OcrCommandPayloadModel>
  ): Observable<T> =>
    this.http.post<T>(
      `${this._apiUrl}OCR/project/${payload.projectId}/Summary`,
      payload
    )

  /*
   * Fetches either the file types of OCR
   * @param payload requested payload to fetch data based on criteria
   */
  public readonly fetchOcrFileTypes = <T>(
    payload: Partial<OcrCommandPayloadModel>
  ): Observable<T> =>
    this.http.post<T>(
      `${this._apiUrl}OCR/project/${payload.projectId}/FileType`,
      payload
    )

  /*
   * Fetches redaction sets by project ID
   * @param projectId  project ID to predicate and get values.
   */
  public readonly fetchRedactions = <T>(projectId: number): Observable<T> =>
    this.http.get<T>(`${this._apiUrl}OCR/project/${projectId}`)

  /*
   * Starts a new or re-lunch job for html/rtf conversion
   * @param payload requested payload
   */
  public readonly startDocConversion = <T>(
    payload: Partial<HtmlRtfCommandPayloadModel>
  ): Observable<T> =>
    this.http.post<T>(
      `${this._apiUrl}OCR/project/${payload.projectId}/Queue`,
      payload
    )

  /*
   * Starts a new or re-lunch job for OCR conversion
   * @param payload requested payload
   */
  public readonly startOCRConversion = <T>(
    payload: Partial<OcrCommandPayloadModel>
  ): Observable<T> =>
    this.http.post<T>(
      `${this._apiUrl}OCR/project/${payload.projectId}/Queue`,
      payload
    )
}
