import { DestroyRef, Injectable, computed, inject, signal } from '@angular/core'
import { BatchModel, ReviewSetBasicInfo } from '../../models/interfaces'
import { ActivatedRoute } from '@angular/router'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'

@Injectable({
  providedIn: 'root',
})
export class ReviewSetStateService {
  private activatedRoute = inject(ActivatedRoute)
  private destroyRef = inject(DestroyRef)
  private queryParams = signal(this.activatedRoute.snapshot.queryParams)

  constructor() {
    this.activatedRoute.queryParams
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((params) => {
        this.queryParams.set(params)
      })
  }

  public reviewSetId = signal(this.queryParams().reviewSetId)
  public reviewSetBasicInfo = signal<ReviewSetBasicInfo>(undefined)
  public isBatchReview = computed(() => Boolean(this.reviewSetId()))
  public batchId = signal(undefined)
  public isBatchInfoLoading = signal(undefined)
  public reviewsetBatchInfo = signal<BatchModel>(undefined)

  public reset(): void {
    this.reviewSetId.set(this.queryParams().reviewSetId)
    this.reviewSetBasicInfo.set(undefined)
    this.batchId.set(undefined)
    this.isBatchInfoLoading.set(undefined)
    this.reviewsetBatchInfo.set(undefined)
  }
}
