import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from '@venio/shared/environments'
import { Observable } from 'rxjs'
import { ChunkDocumentModel } from '../models/interfaces'
import { cloneDeep } from 'lodash'

@Injectable({
  providedIn: 'root',
})
export class ViewerService {
  constructor(private http: HttpClient) {}

  private get _apiUrl(): string {
    return environment.apiUrl
  }

  public fetchFullText$(
    chunkDocumentInfo: ChunkDocumentModel
  ): Observable<any> {
    // incorrect isExternalUser value with external user so must reset here
    const _chunkDocumentInfo = cloneDeep(chunkDocumentInfo)
    if (!chunkDocumentInfo.fileId) {
      chunkDocumentInfo.fileId = 0
    }
    if (localStorage.getItem('DocShareUserRole') !== null) {
      _chunkDocumentInfo.isExternalUser =
        localStorage.getItem('DocShareUserRole').toString().toLowerCase() !==
        'external'
          ? false
          : true
    }
    return this.http.post(
      `${this._apiUrl} 
        viewer/project/
        ${chunkDocumentInfo.projectId}
        /file/
        ${chunkDocumentInfo.fileId} 
        /text`,
      chunkDocumentInfo
    )
  }

  public redactionStatus<T>(projectId: number): Observable<T> {
    const docUrl =
      this._apiUrl + 'viewer/project/' + projectId + '/bulk-redaction/status'
    return this.http.get<T>(docUrl)
  }
}
