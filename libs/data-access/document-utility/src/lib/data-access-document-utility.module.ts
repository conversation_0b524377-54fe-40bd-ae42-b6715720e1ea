import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { EffectsModule } from '@ngrx/effects'
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http'
import { StoreModule } from '@ngrx/store'
import {
  DocumentTagEffects,
  DOCUMENT_TAG_FEATURE_KEY,
  documentTagReducer,
  DocumentTagFacade,
} from './+state/document-tag'
import { NotificationModule } from '@progress/kendo-angular-notification'
import {
  DOCUMENT_CODING_FEATURE_KEY,
  DocumentCodingFacade,
  DocumentCodingEffects,
  documentCodingReducer,
} from './+state/document-coding'
import {
  RequestProcessorUtilityService,
  ResponseProcessorUtilityService,
} from './utilities'
import {
  REVIEW_PANEL_FEATURE_KEY,
  ReviewPanelEffects,
  ReviewPanelFacade,
  reviewPanelReducer,
} from './+state/review-panel'
import {
  UTILITY_PANEL_FEATURE_KEY,
  UtilityPanelFacade,
  utilityPanelReducer,
} from './+state/utility-panel'

@NgModule({
  imports: [
    CommonModule,
    NotificationModule,
    EffectsModule.forFeature([
      DocumentTagEffects,
      DocumentCodingEffects,
      ReviewPanelEffects,
    ]),
    StoreModule.forFeature(DOCUMENT_TAG_FEATURE_KEY, documentTagReducer),
    StoreModule.forFeature(DOCUMENT_CODING_FEATURE_KEY, documentCodingReducer),
    StoreModule.forFeature(REVIEW_PANEL_FEATURE_KEY, reviewPanelReducer),
    StoreModule.forFeature(UTILITY_PANEL_FEATURE_KEY, utilityPanelReducer),
  ],
  providers: [
    DocumentTagFacade,
    DocumentCodingFacade,
    ReviewPanelFacade,
    UtilityPanelFacade,
    ResponseProcessorUtilityService,
    RequestProcessorUtilityService,
    provideHttpClient(withInterceptorsFromDi()),
  ],
})
export class DataAccessDocumentUtilityModule {}
