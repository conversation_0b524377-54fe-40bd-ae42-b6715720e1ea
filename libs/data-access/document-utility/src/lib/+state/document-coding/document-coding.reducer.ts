import { Action, createReducer, on } from '@ngrx/store'
import * as documentCodingActions from './document-coding.actions'
import {
  DocumentCodingModel,
  ResponseModel,
  SelectedMultiCodingValueModel,
} from '@venio/shared/models/interfaces'
import { resetStateProperty } from '@venio/util/utilities'
import { DocumentCodingControlActionType } from '@venio/shared/models/constants'
import { difference, union } from 'lodash'

export const DOCUMENT_CODING_FEATURE_KEY = 'venioDocumentCoding'

export interface DocumentCodingState {
  documentCodingFields: DocumentCodingModel[] | undefined
  fieldCodingModel: DocumentCodingModel[] | undefined
  visibleCodingFields: DocumentCodingModel[] | undefined
  updatedCodingFieldInfoIds: number[]
  selectedMultiCodingValue: SelectedMultiCodingValueModel | undefined
  codingActionEventType: DocumentCodingControlActionType | undefined
  searchDocumentCoding: string
  isDocumentCodeLoading: boolean | undefined
  isCodingDataModified: boolean | undefined
  isCodingDataValid: boolean | undefined
  codingFailureResponse: ResponseModel | undefined
  bulkCodingValues: ResponseModel | undefined
  isBulkCodingValuesLoading: boolean | undefined
  bulkCodingFailureResponse: ResponseModel | undefined
  isDocumentCodingUpdated: boolean | undefined
}

export const documentCodingInitialState: DocumentCodingState = {
  documentCodingFields: [],
  fieldCodingModel: [],
  visibleCodingFields: undefined,
  updatedCodingFieldInfoIds: [],
  selectedMultiCodingValue: undefined,
  codingActionEventType: undefined,
  searchDocumentCoding: '',
  isDocumentCodeLoading: false,
  isCodingDataModified: false,
  isCodingDataValid: undefined,
  codingFailureResponse: undefined,
  bulkCodingValues: undefined,
  isBulkCodingValuesLoading: false,
  bulkCodingFailureResponse: undefined,
  isDocumentCodingUpdated: undefined,
}

export interface DocumentCodingPartialState {
  readonly [DOCUMENT_CODING_FEATURE_KEY]: DocumentCodingState
}

const _reducer = createReducer(
  documentCodingInitialState,
  on(documentCodingActions.resetDocumentCodingState, (state, { stateKey }) =>
    resetStateProperty<DocumentCodingState>(
      state,
      documentCodingInitialState,
      stateKey
    )
  ),
  on(
    documentCodingActions.isDocumentCodeLoading,
    (state, { isDocumentCodeLoading }) => ({
      ...state,
      isDocumentCodeLoading,
    })
  ),
  on(
    documentCodingActions.setCodingFields,
    (state, { payload: { responseModel } }) => ({
      ...state,
      documentCodingFields: responseModel,
      isDocumentCodeLoading: false,
    })
  ),
  on(
    documentCodingActions.updateCodingFields,
    (
      state,
      { payload: { fieldCodingModel, customFieldInfoId, isChecked } }
    ) => {
      const updatedCodingFieldInfoIds = isChecked
        ? union(state.updatedCodingFieldInfoIds, [customFieldInfoId])
        : difference(state.updatedCodingFieldInfoIds, [customFieldInfoId])

      return {
        ...state,
        fieldCodingModel,
        updatedCodingFieldInfoIds,
        isCodingDataModified:
          updatedCodingFieldInfoIds.length > 0 ? true : false,
      }
    }
  ),
  on(
    documentCodingActions.updateVisibleCodingFields,
    (state, { payload: { visibleCodingFields } }) => ({
      ...state,
      visibleCodingFields,
    })
  ),
  on(
    documentCodingActions.setMultiCodingValue,
    (state, { codingActionEventType, selectedMultiCodingValue }) => ({
      ...state,
      codingActionEventType,
      selectedMultiCodingValue,
    })
  ),
  on(
    documentCodingActions.searchDocumentCoding,
    (state, { payload: { searchDocumentCoding } }) => ({
      ...state,
      searchDocumentCoding,
    })
  ),
  on(documentCodingActions.getCodingFields, (state) => ({
    ...state,
    isDocumentCodeLoading: true,
  })),
  on(
    documentCodingActions.documentCodingFailure,
    (state, { codingFailureResponse }) => ({
      ...state,
      isDocumentCodeLoading: false,
      codingFailureResponse,
    })
  ),
  on(
    documentCodingActions.setBulkCodingValues,
    (state, { payload: { bulkCodingValues } }) => ({
      ...state,
      bulkCodingValues,
      isBulkCodingValuesLoading: false,
    })
  ),
  on(documentCodingActions.getBulkCodingValues, (state) => ({
    ...state,
    isBulkCodingValuesLoading: true,
  })),
  on(
    documentCodingActions.isBulkCodingValuesLoading,
    (state, { isBulkCodingValuesLoading }) => ({
      ...state,
      isBulkCodingValuesLoading,
    })
  ),
  on(
    documentCodingActions.bulkCodingValuesFailure,
    (state, { bulkCodingFailureResponse }) => ({
      ...state,
      isBulkCodingValuesLoading: false,
      bulkCodingFailureResponse,
    })
  ),
  on(
    documentCodingActions.setCodingDataValidStatus,
    (state, { isCodingDataValid }) => ({
      ...state,
      isCodingDataValid,
    })
  ),
  on(
    documentCodingActions.setDocumentCodingUpdated,
    (state, { isDocumentCodingUpdated }) => ({
      ...state,
      isDocumentCodingUpdated,
    })
  )
)

export function documentCodingReducer(
  state: DocumentCodingState,
  action: Action
): DocumentCodingState {
  return _reducer(state, action)
}
