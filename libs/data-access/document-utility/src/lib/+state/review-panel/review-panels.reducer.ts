import * as reviewPanelActions from './review-panels.actions'
import { ResponseModel } from '@venio/shared/models/interfaces'
import {
  DocumentHistory,
  DocumentMetadata,
  DuplicateDocument,
  NearDuplicateModel,
  ParentChild,
  ReviewPanelSelectedDocumentModel,
  SimilarDocumentModel,
  SimilarDocumentResponseModel,
} from '../../models/interfaces'
import { Action, createReducer, on } from '@ngrx/store'
import { resetStateProperty } from '@venio/util/utilities'
import {
  SimilaritySearchScopeType,
  UpdateDocumentHistoryPage,
} from '../../models/constants'

export const REVIEW_PANEL_FEATURE_KEY = 'venioParentChild'

export interface ReviewPanelState {
  parentChild: ParentChild[] | undefined
  isParentChildLoading: boolean | undefined
  selectedReviewPanelDocument: ReviewPanelSelectedDocumentModel | undefined
  parentChildFailureResponse: ResponseModel | undefined
  metaData: DocumentMetadata[] | undefined
  isMetadataLoading: boolean | undefined
  metadataFailureResponse: ResponseModel | undefined
  duplicateDocument: DuplicateDocument[] | undefined
  isDuplicateLoading: boolean | undefined
  duplicateFailureResponse: ResponseModel | undefined
  documentHistory: DocumentHistory[] | undefined
  isDocumentHistoryLoading: boolean | undefined
  documentHistoryFailureResponseModel: ResponseModel | undefined
  similarityScore: number | undefined
  similaritySearchScopeType: SimilaritySearchScopeType | undefined
  similarDocuments: SimilarDocumentModel[] | undefined
  similarDocumentResponse: SimilarDocumentResponseModel | undefined
  isSimilarDocumentLoading: boolean | undefined
  similarDocumentFailureResponse: ResponseModel | undefined
  similarFailureResponse: ResponseModel | undefined
  adjustSimilarDocumentFailureResponse: ResponseModel | undefined
  selectedDocumentSeqNo: number | undefined
  updateDocumentHistoryPage: UpdateDocumentHistoryPage | undefined
  isEmailThreadLoading: boolean | undefined
  emailThreadResponse: any[] | undefined
  emailThreadFailureResponse: ResponseModel | undefined
  nearDuplicateDocument: NearDuplicateModel | undefined
  isNearDuplicateLoading: boolean | undefined
  nearDuplicateFailureResponse: ResponseModel | undefined
}

export const reviewPanelInitialState: ReviewPanelState = {
  parentChild: [],
  isParentChildLoading: false,
  selectedReviewPanelDocument: undefined,
  parentChildFailureResponse: undefined,
  metaData: [],
  isMetadataLoading: false,
  metadataFailureResponse: undefined,
  duplicateDocument: [],
  isDuplicateLoading: false,
  duplicateFailureResponse: undefined,
  documentHistory: undefined,
  isDocumentHistoryLoading: false,
  documentHistoryFailureResponseModel: undefined,
  similarityScore: 50,
  similaritySearchScopeType: SimilaritySearchScopeType.ALL_DOCUMENTS,
  similarDocuments: [],
  similarDocumentResponse: undefined,
  isSimilarDocumentLoading: false,
  similarDocumentFailureResponse: undefined,
  similarFailureResponse: undefined,
  adjustSimilarDocumentFailureResponse: undefined,
  selectedDocumentSeqNo: undefined,
  updateDocumentHistoryPage: undefined,
  isEmailThreadLoading: false,
  emailThreadResponse: undefined,
  emailThreadFailureResponse: undefined,
  nearDuplicateDocument: undefined,
  isNearDuplicateLoading: false,
  nearDuplicateFailureResponse: undefined,
}

export interface ReviewPanelPartialState {
  readonly [REVIEW_PANEL_FEATURE_KEY]: ReviewPanelState
}

const _reducer = createReducer(
  reviewPanelInitialState,
  on(reviewPanelActions.resetReviewPanelState, (state, { stateKey }) =>
    resetStateProperty<ReviewPanelState>(
      state,
      reviewPanelInitialState,
      stateKey
    )
  ),
  on(reviewPanelActions.fetchDocumentParentChild, (state) => ({
    ...state,
    isParentChildLoading: true,
  })),
  on(
    reviewPanelActions.setDocumentParentChild,
    (state, { payload: { parentChild } }) => ({
      ...state,
      isParentChildLoading: false,
      parentChild,
    })
  ),
  on(
    reviewPanelActions.setSelectedReviewPanelDocument,
    (state, { selectedReviewPanelDocument }) => ({
      ...state,
      selectedReviewPanelDocument,
    })
  ),
  on(
    reviewPanelActions.parentChidFailureFailure,
    (state, { parentChildFailureResponse }) => ({
      ...state,
      isParentChildLoading: false,
      parentChildFailureResponse,
    })
  ),
  on(reviewPanelActions.fetchDocumentMetadata, (state) => ({
    ...state,
    isMetadataLoading: true,
  })),
  on(
    reviewPanelActions.setDocumentMetadata,
    (state, { payload: { metaData } }) => ({
      ...state,
      isMetadataLoading: false,
      metaData,
    })
  ),
  on(
    reviewPanelActions.metadataFailure,
    (state, { metadataFailureResponse }) => ({
      ...state,
      isMetadataLoading: false,
      metadataFailureResponse,
    })
  ),
  on(reviewPanelActions.fetchDuplicateDocument, (state) => ({
    ...state,
    isDuplicateLoading: true,
  })),
  on(
    reviewPanelActions.setDuplicateDocument,
    (state, { payload: { duplicateDocument } }) => ({
      ...state,
      isDuplicateLoading: false,
      duplicateDocument,
    })
  ),
  on(
    reviewPanelActions.duplicateFailure,
    (state, { duplicateFailureResponse }) => ({
      ...state,
      isDuplicateLoading: false,
      duplicateFailureResponse,
    })
  ),
  on(reviewPanelActions.fetchDocumentHistory, (state) => ({
    ...state,
    isDocumentHistoryLoading: true,
  })),
  on(
    reviewPanelActions.fetchDocumentHistorySuccess,
    (state, { documentHistorySuccessResponseModel }) => ({
      ...state,
      documentHistory: documentHistorySuccessResponseModel.data,
      isDocumentHistoryLoading: false,
    })
  ),
  on(
    reviewPanelActions.fetchDocumentHistoryFailure,
    (state, { documentHistoryFailureResponseModel }) => ({
      ...state,
      documentHistoryFailureResponseModel,
      isDocumentHistoryLoading: false,
    })
  ),
  on(reviewPanelActions.populateSimilarDocument, (state) => ({
    ...state,
    isSimilarDocumentLoading: true,
  })),
  on(reviewPanelActions.fetchSimilarDocument, (state) => ({
    ...state,
    isSimilarDocumentLoading: true,
  })),
  on(
    reviewPanelActions.setSimilarityScore,
    (state, { payload: { similarityScore } }) => ({
      ...state,
      similarityScore,
    })
  ),
  on(
    reviewPanelActions.setSimilaritySearchScopeType,
    (state, { payload: { similaritySearchScopeType } }) => ({
      ...state,
      similaritySearchScopeType,
    })
  ),
  on(
    reviewPanelActions.setSimilarDocumentResponse,
    (state, { payload: { similarDocumentResponse } }) => ({
      ...state,
      isSimilarDocumentLoading: false,
      similarDocumentResponse,
    })
  ),
  on(
    reviewPanelActions.setSimilarDocument,
    (state, { payload: { similarDocuments } }) => ({
      ...state,
      isSimilarDocumentLoading: false,
      similarDocuments,
    })
  ),
  on(
    reviewPanelActions.similarDocumentResponseFailure,
    (state, { similarDocumentFailureResponse }) => ({
      ...state,
      isSimilarDocumentLoading: false,
      similarDocumentFailureResponse,
    })
  ),
  on(
    reviewPanelActions.similarDocumentFailure,
    (state, { similarFailureResponse }) => ({
      ...state,
      isSimilarDocumentLoading: false,
      similarFailureResponse,
    })
  ),
  on(
    reviewPanelActions.adjustSimilarDocumentResponseFailure,
    (state, { ajustSimilarDocumentFailureResponse }) => ({
      ...state,
      isSimilarDocumentLoading: false,
      ajustSimilarDocumentFailureResponse,
    })
  ),
  on(reviewPanelActions.fetchSeqNoByFileId, (state) => ({
    ...state,
    isSimilarDocumentLoading: true,
  })),
  on(
    reviewPanelActions.setSelectedDocumentSeqNo,
    (state, { payload: { selectedDocumentSeqNo } }) => ({
      ...state,
      isSimilarDocumentLoading: false,
      selectedDocumentSeqNo,
    })
  ),
  on(
    reviewPanelActions.refrehDocumentHistory,
    (state, { payload: { updateDocumentHistoryPage } }) => ({
      ...state,
      updateDocumentHistoryPage,
    })
  ),
  on(reviewPanelActions.fetchEmailThreadDocument, (state) => ({
    ...state,
    isEmailThreadLoading: true,
  })),
  on(
    reviewPanelActions.setEmailThreadResponse,
    (state, { payload: { emailThreadResponse } }) => ({
      ...state,
      isEmailThreadLoading: false,
      emailThreadResponse: emailThreadResponse,
    })
  ),
  on(
    reviewPanelActions.emailThreadResponseFailure,
    (state, { emailThreadFailureResponse }) => ({
      ...state,
      isEmailThreadLoading: false,
      emailThreadFailureResponse: emailThreadFailureResponse,
    })
  ),
  on(reviewPanelActions.fetchNearDuplicateDocument, (state) => ({
    ...state,
    isNearDuplicateLoading: true,
  })),
  on(
    reviewPanelActions.setNearDuplicateDocument,
    (state, { payload: { nearDuplicateDocument } }) => ({
      ...state,
      isNearDuplicateLoading: false,
      nearDuplicateDocument,
    })
  ),
  on(
    reviewPanelActions.nearDuplicateFailure,
    (state, { nearDuplicateFailureResponse }) => ({
      ...state,
      isNearDuplicateLoading: false,
      nearDuplicateFailureResponse,
    })
  )
)

export function reviewPanelReducer(
  state: ReviewPanelState,
  action: Action
): ReviewPanelState {
  return _reducer(state, action)
}
