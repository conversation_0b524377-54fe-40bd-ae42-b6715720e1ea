import {
  createFeatureSelector,
  createSelector,
  MemoizedSelector,
} from '@ngrx/store'
import {
  UtilityPanelState,
  UTILITY_PANEL_FEATURE_KEY,
} from './utility-panel.reducer'

export const getUtilityPanelState = createFeatureSelector<UtilityPanelState>(
  UTILITY_PANEL_FEATURE_KEY
)

export const getStateOfUtilityPanel = <T extends keyof UtilityPanelState>(
  stateKey: T
): MemoizedSelector<object, UtilityPanelState[T], unknown> =>
  createSelector(getUtilityPanelState, (state: UtilityPanelState | undefined) =>
    state ? state[stateKey] : undefined
  )

export const selectUtilityPanelData = createSelector(
  getStateOfUtilityPanel('expandedStatus'),
  getStateOfUtilityPanel('visibilityStatus'),
  getStateOfUtilityPanel('panelSortOrder'),
  getStateOfUtilityPanel('visibleTags'),
  getStateOfUtilityPanel('expandedTagIds'),
  getStateOfUtilityPanel('filterDocumentTags'),
  getStateOfUtilityPanel('visibleCodingFields'),
  getStateOfUtilityPanel('isViewerInFullScreen'),
  getStateOfUtilityPanel('panelFilterEvent'),
  getStateOfUtilityPanel('visibleMetaDataFields'),
  (
    expandedStatus,
    visibilityStatus,
    panelSortOrder,
    visibleTags,
    expandedTagIds,
    filterDocumentTags,
    visibleCodingFields,
    isViewerInFullScreen,
    panelFilterEvent,
    visibleMetaDataFields
  ) => {
    const utilityPanelUIState = {
      expandedStatus,
      visibilityStatus,
      panelSortOrder,
      visibleTags,
      expandedTagIds,
      filterDocumentTags,
      visibleCodingFields,
      isViewerInFullScreen,
      panelFilterEvent,
      visibleMetaDataFields,
    }

    return utilityPanelUIState
  }
)

export const selectVisibilityStatusByKey = (
  key: string
): MemoizedSelector<object, boolean | undefined, unknown> =>
  createSelector(
    getStateOfUtilityPanel('visibilityStatus'),
    (visibilityStatus) =>
      visibilityStatus && visibilityStatus[key] !== undefined
        ? visibilityStatus[key]
        : undefined
  )
