import { createAction, props } from '@ngrx/store'
import { UtilityPanelState } from './utility-panel.reducer'
import { ProjectTag } from '../../models/interfaces'
import { DocumentCodingModel } from '@venio/shared/models/interfaces'
import { UtilityPanelType } from '@venio/shared/models/constants'

export enum UtilityPanelActionTypes {
  ResetUtilityPanelState = '[Utility Panel] Reset Utility Panel State',
  FetchUtilityPanelState = '[Utility Panel] Fetch Document Utility Panel',
  SetUtilityPanelExpanedItems = '[Utility Panel] Set Utility Panel Expanded Items',
  SetUtilityPanelVisibilityItems = '[Utility Panel] Set Utility Panel Visibility Items',
  SetPanelSortOrder = '[Utility Panel] Set Panel Sort Order',
  SetVisibleTags = '[Utility Panel] Set Visible Tags',
  SetExpandedTagId = '[Utility Panel] Set Expanded Tag Id',
  SetFilterDocumentTags = '[Utility Panel] Set Filter Document Tags',
  SetVisibleCodingFields = '[Utility Panel] Set Visible Coding Fields',
  SetIsUtilityPanelInFullScreen = '[Utility Panel] Set Is Utility Panel In Full Screen',
  SetIsViewerInFullScreen = '[Utility Panel] Set Is Viewer In Full Screen',
  PanelFilterActionEvent = '[Utility Panel] Panel Filter Action Event',
  SetVisibleMetaDataFields = '[Utility Panel] Set Visible MetaData Fields',
}

export const resetUtilityPanelState = createAction(
  UtilityPanelActionTypes.ResetUtilityPanelState,
  props<{
    stateKey: keyof UtilityPanelState | Array<keyof UtilityPanelState>
  }>()
)

export const setUtilityPanelExpandedItems = createAction(
  UtilityPanelActionTypes.SetUtilityPanelExpanedItems,
  props<{ expandedStatus: { [key: string]: boolean } }>()
)

export const setUtilityPanelVisibilityItems = createAction(
  UtilityPanelActionTypes.SetUtilityPanelVisibilityItems,
  props<{ visibilityStatus: { [key: string]: boolean } }>()
)

export const setPanelSortOrder = createAction(
  UtilityPanelActionTypes.SetPanelSortOrder,
  props<{ panelSortOrder: string[] }>()
)

export const setVisibleTags = createAction(
  UtilityPanelActionTypes.SetVisibleTags,
  props<{ visibleTags: ProjectTag[] }>()
)

export const setExpandedTagId = createAction(
  UtilityPanelActionTypes.SetExpandedTagId,
  props<{ expandedTagIds: number[] }>()
)

export const setFilterDocumentTags = createAction(
  UtilityPanelActionTypes.SetFilterDocumentTags,
  props<{ filterDocumentTags: string }>()
)

export const setVisibleCodingFields = createAction(
  UtilityPanelActionTypes.SetVisibleCodingFields,
  props<{ visibleCodingFields: DocumentCodingModel[] }>()
)

export const setIsUtilityPanelInFullScreen = createAction(
  UtilityPanelActionTypes.SetIsUtilityPanelInFullScreen,
  props<{ isUtilityPanelInFullScreen: boolean }>()
)

export const setIsViewerInFullScreen = createAction(
  UtilityPanelActionTypes.SetIsViewerInFullScreen,
  props<{ isViewerInFullScreen: boolean }>()
)

export const panelFilterActionEvent = createAction(
  UtilityPanelActionTypes.PanelFilterActionEvent,
  props<{ panelFilterEvent: UtilityPanelType }>()
)

export const setVisibleMetaDataFields = createAction(
  UtilityPanelActionTypes.SetVisibleMetaDataFields,
  props<{ visibleMetaDataFields: string[] }>()
)
