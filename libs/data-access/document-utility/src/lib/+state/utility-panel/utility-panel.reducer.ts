import * as utilityPanelActions from './utility-panel.actions'
import { Action, createReducer, on } from '@ngrx/store'
import { resetStateProperty } from '@venio/util/utilities'
import { ProjectTag } from '../../models/interfaces'
import { DocumentCodingModel } from '@venio/shared/models/interfaces'
import { UtilityPanelType } from '@venio/shared/models/constants'

export const UTILITY_PANEL_FEATURE_KEY = 'venioUtilityPanel'

export interface UtilityPanelState {
  expandedStatus: { [key: string]: boolean } | undefined
  visibilityStatus: { [key: string]: boolean } | undefined
  panelSortOrder: string[] | undefined
  visibleTags: ProjectTag[] | undefined
  expandedTagIds: number[] | undefined
  filterDocumentTags: string | undefined
  visibleCodingFields: DocumentCodingModel[] | undefined
  isUtilityPanelInFullScreen: boolean | undefined
  isViewerInFullScreen: boolean | undefined
  panelFilterEvent: UtilityPanelType | undefined
  visibleMetaDataFields: string[] | undefined
}

export const utilityPanelInitialState: UtilityPanelState = {
  expandedStatus: undefined,
  visibilityStatus: undefined,
  panelSortOrder: undefined,
  visibleTags: undefined,
  expandedTagIds: undefined,
  filterDocumentTags: undefined,
  visibleCodingFields: undefined,
  isUtilityPanelInFullScreen: false,
  isViewerInFullScreen: false,
  panelFilterEvent: undefined,
  visibleMetaDataFields: undefined,
}

export interface UtilityPanelPartialState {
  readonly [UTILITY_PANEL_FEATURE_KEY]: UtilityPanelState
}

const _reducer = createReducer(
  utilityPanelInitialState,
  on(utilityPanelActions.resetUtilityPanelState, (state, { stateKey }) =>
    resetStateProperty<UtilityPanelState>(
      state,
      utilityPanelInitialState,
      stateKey
    )
  ),
  on(
    utilityPanelActions.setUtilityPanelExpandedItems,
    (state, { expandedStatus }) => ({
      ...state,
      expandedStatus,
    })
  ),
  on(
    utilityPanelActions.setUtilityPanelVisibilityItems,
    (state, { visibilityStatus }) => ({
      ...state,
      visibilityStatus,
    })
  ),
  on(utilityPanelActions.setPanelSortOrder, (state, { panelSortOrder }) => ({
    ...state,
    panelSortOrder,
  })),
  on(utilityPanelActions.setVisibleTags, (state, { visibleTags }) => ({
    ...state,
    visibleTags,
  })),
  on(utilityPanelActions.setExpandedTagId, (state, { expandedTagIds }) => ({
    ...state,
    expandedTagIds,
  })),
  on(
    utilityPanelActions.setFilterDocumentTags,
    (state, { filterDocumentTags }) => ({
      ...state,
      filterDocumentTags,
    })
  ),
  on(
    utilityPanelActions.setVisibleCodingFields,
    (state, { visibleCodingFields }) => ({
      ...state,
      visibleCodingFields,
    })
  ),
  on(
    utilityPanelActions.setIsUtilityPanelInFullScreen,
    (state, { isUtilityPanelInFullScreen }) => ({
      ...state,
      isUtilityPanelInFullScreen,
    })
  ),
  on(
    utilityPanelActions.setIsViewerInFullScreen,
    (state, { isViewerInFullScreen }) => ({
      ...state,
      isViewerInFullScreen,
    })
  ),
  on(
    utilityPanelActions.panelFilterActionEvent,
    (state, { panelFilterEvent }) => ({
      ...state,
      panelFilterEvent,
    })
  ),
  on(
    utilityPanelActions.setVisibleMetaDataFields,
    (state, { visibleMetaDataFields }) => ({
      ...state,
      visibleMetaDataFields,
    })
  )
)

export function utilityPanelReducer(
  state: UtilityPanelState,
  action: Action
): UtilityPanelState {
  return _reducer(state, action)
}
