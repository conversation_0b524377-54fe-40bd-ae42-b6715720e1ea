import { createAction, props } from '@ngrx/store'
import {
  DocumentProjectTagGroupModel,
  DocumentProjectTagRequestModel,
  DocumentTag,
  DocumentTagPayloadModel,
  FileTaggedRequestModel,
  ProjectTag,
  SaveTagPayloadModel,
  TagRule,
  TagRuleInfo,
  TagSettings,
} from '../../models/interfaces'
import { DocumentTagState } from './document-tag.reducer'
import {
  HttpErrorResponseModel,
  ResponseModel,
} from '@venio/shared/models/interfaces'
import { PageControlActionType } from '@venio/shared/models/constants'

export enum DocumentTagActionTypes {
  ResetDocumentTagState = '[Document Tag] Reset Document Tag State',
  FetchProjectTags = '[Document Tag] Fetch Project Tags',
  SetProjectTags = '[Document Tag] Set Project Tags',
  UpdatVisibleTagFields = '[Document Tag] Update Visible Tag Fields',
  FetchDocumentTags = '[Document Tag] Fetch Document Tags',
  SetDocumentTags = '[Document] Set Document Tags',
  setExpandedTagIds = '[Document Tag] Set Collapased Tag Ids',
  FetchTagsFromProfileCategory = '[Document Tag] Get Tags From Profile Category',
  SetTagsFromProfileCategory = '[Document Tag] Set Tags From Profile Category',
  FetchTagSettings = '[Document Tag] Fetch Tag Settings',
  UpdateUserSelectedDocumentTag = '[Document Tag] User Selected Document Tags',
  SetTagSettings = '[Document Tag] Set Tag Settings',
  SearchDocumentTags = '[Document Tag] Search Document Tags',
  MoveToNextDocument = '[Document Tag] Move To Next Document',
  MoveBackDocument = '[Document Tag] Move Back Previous Document',
  SaveTag = '[Document Tag] Save Tag',
  IsDocumentTagLoading = '[Document Tag] Is Document Tag Loading',
  ValidateTagComments = '[Document Tag] Required Tags Missing Comments',
  ShowNotification = '[Document Tag] Display Success Message',
  FilterDocumentTags = '[Document Tag] Filter Document Tags',
  ApplyDocumentTagSuccess = '[Document Tag] Apply Document Tag Success',
  ApplyDocumentTagFailure = '[Document Tag] Apply Document Tag  Failure',
  FetchProjectTagsFailure = '[Document Tag] Fetch Project Tags Failure',
  FetchDocumentTagsFailure = '[Document Tag] Fetch Document Tags Failure',
  FetchProfileCategoryFailure = '[Document Tag] Fetch Tags From Profile Category Failure',
  FetchTagSettingsFailure = '[Document Tag] Fetch Tag Settings Failure',
  DocumentPageActionEvent = '[Document Tag] Document Action Event',
  UpdateFileStatus = '[Document Tag] Update File Status',
  UndoTagCodingChanges = '[Document Tag] Undo Tag Coding Changes',
  SaveTagCodingChanges = '[Document Tag] Save Tag Coding Changes',
  FetchTagRuleList = '[Document Tag] Fetch Tag Rule List',
  FetchTagRuleListSuccess = '[Document Tag] Fetch Tag Rule List Success',
  FetchTagRuleListFailure = '[Document Tag] Fetch Tag Rule List Failure',
  FetchTagRuleDescription = '[Document Tag] Fetch Tag Rule Description',
  FetchTagRuleDescriptionSuccess = '[Document Tag] Fetch Tag Rule Description Success',
  FetchTagRuleDescriptionFailure = '[Document Tag] Fetch Tag Rule Description Failure',
  FilterTagRule = '[Document Tag] Filter Tag Rule',
  SetTagRuleViolationList = '[Document Tag] Set Tag Rule Violation List',
  ShowHideTagRuleHeader = '[Document Tag] Show Hide Tag Rule Header',
  FetchDocumentProjectTagGroups = '[Document Tag] Fetch Document Project Tag Groups',
  SetDocumentProjectTagGroups = '[Document Tag] Set Document Project Tag Groups',
  FetchDocumentProjectTagGroupsFailure = '[Document Tag] Fetch Document Project Tag Groups Failure',
  SetDocumentTagUpdated = '[Document Tag] Set Document Tag Updated',
  ReloadTagGroup = '[Document Tag] Reload Tag Group',
}

export const resetDocumentTagState = createAction(
  DocumentTagActionTypes.ResetDocumentTagState,
  props<{ stateKey: keyof DocumentTagState | Array<keyof DocumentTagState> }>()
)

export const fetchProjectTags = createAction(
  DocumentTagActionTypes.FetchProjectTags,
  props<{
    payload: {
      projectId: number
      isExternalUser: boolean
      isGetAllParentTags: boolean
    }
  }>()
)

export const setProjectTags = createAction(
  DocumentTagActionTypes.SetProjectTags,
  props<{ payload: { projectTags: ProjectTag[] } }>()
)

export const setExpandedTagIds = createAction(
  DocumentTagActionTypes.setExpandedTagIds,
  props<{ payload: { expandedTagIds: number[] } }>()
)

export const updatVisibleTagFields = createAction(
  DocumentTagActionTypes.UpdatVisibleTagFields,
  props<{ payload: { visibleTags: ProjectTag[] } }>()
)

export const fetchDocumentTags = createAction(
  DocumentTagActionTypes.FetchDocumentTags,
  props<{
    payload: {
      documentTagPayload: DocumentTagPayloadModel
    }
  }>()
)

export const setDocumentTags = createAction(
  DocumentTagActionTypes.SetDocumentTags,
  props<{ payload: { documentTags: DocumentTag[] } }>()
)

export const fetchTagsFromProfileCategory = createAction(
  DocumentTagActionTypes.FetchTagsFromProfileCategory,
  props<{ projectId: number; tagGroupId: number }>()
)

export const setTagsFromProfileCategory = createAction(
  DocumentTagActionTypes.SetTagsFromProfileCategory,
  props<{ tagsProfileCategory: number[] }>()
)

export const fetchTagSettings = createAction(
  DocumentTagActionTypes.FetchTagSettings,
  props<{
    payload: {
      projectId: number
    }
  }>()
)

export const udpateUserSelectedDocumentTag = createAction(
  DocumentTagActionTypes.UpdateUserSelectedDocumentTag,
  props<{
    payload: {
      userSelectedDocumentTag: DocumentTag[]
      isTagDataModified: boolean
    }
  }>()
)

export const setTagSettings = createAction(
  DocumentTagActionTypes.SetTagSettings,
  props<{ payload: { projectTagSettings: TagSettings } }>()
)

export const searchDocumentTags = createAction(
  DocumentTagActionTypes.SearchDocumentTags,
  props<{ payload: { searchDocumentTags: string } }>()
)

export const moveToNextDocument = createAction(
  DocumentTagActionTypes.MoveToNextDocument
)

export const moveBackDocument = createAction(
  DocumentTagActionTypes.MoveBackDocument
)

export const saveTag = createAction(
  DocumentTagActionTypes.SaveTag,
  props<{ payload: { tagPayload: SaveTagPayloadModel } }>()
)

export const isDocumentTagLoading = createAction(
  DocumentTagActionTypes.IsDocumentTagLoading,
  props<{
    isDocumentTagLoading: boolean
  }>()
)

export const validateTagComments = createAction(
  DocumentTagActionTypes.ValidateTagComments,
  props<{
    areTagCommentsMissing: boolean
  }>()
)

export const applyDocumentTagSuccess = createAction(
  DocumentTagActionTypes.ApplyDocumentTagSuccess,
  props<{ applyDocumentTagSuccessResponse: ResponseModel }>()
)

export const applyDocumentTagFailure = createAction(
  DocumentTagActionTypes.ApplyDocumentTagFailure,
  props<{ applyDocumentTagErrorResponse: ResponseModel }>()
)

export const fetchProjectTagsFailure = createAction(
  DocumentTagActionTypes.FetchProjectTagsFailure,
  props<{ tagProjectTagsErrorResponse: ResponseModel }>()
)

export const fetchDocumentTagsFailure = createAction(
  DocumentTagActionTypes.FetchDocumentTagsFailure,
  props<{ tagDocumentTagsErrorResponse: ResponseModel }>()
)

export const fetchProfileCategoryFailure = createAction(
  DocumentTagActionTypes.FetchProfileCategoryFailure,
  props<{ tagProfileCategoryErrorResponse: HttpErrorResponseModel }>()
)

export const fetchTagSettingsFailure = createAction(
  DocumentTagActionTypes.FetchTagSettingsFailure,
  props<{ tagTagSettingErrorResponse: HttpErrorResponseModel }>()
)

export const documentPageActionEvent = createAction(
  DocumentTagActionTypes.DocumentPageActionEvent,
  props<{ pageActionEventType: PageControlActionType }>()
)

export const updateFileStatus = createAction(
  DocumentTagActionTypes.UpdateFileStatus,
  props<{ fileTaggedRequestModel: FileTaggedRequestModel; projectId: number }>()
)

export const filterDocumentTags = createAction(
  DocumentTagActionTypes.FilterDocumentTags,
  props<{ payload: { filterDocumentTags: string } }>()
)

export const undoTagCodingChanges = createAction(
  DocumentTagActionTypes.UndoTagCodingChanges,
  props<{
    isUndoTagCodingChanges: boolean
  }>()
)

export const saveTagCodingChanges = createAction(
  DocumentTagActionTypes.SaveTagCodingChanges,
  props<{
    isSaveTagCodingChanges: boolean
  }>()
)

export const fetchTagRuleList = createAction(
  DocumentTagActionTypes.FetchTagRuleList,
  props<{
    payload: {
      projectId: number
    }
  }>()
)

export const fetchTagRuleListSuccess = createAction(
  DocumentTagActionTypes.FetchTagRuleListSuccess,
  props<{ tagRuleList: TagRuleInfo[] }>()
)

export const fetchTagRuleListFailure = createAction(
  DocumentTagActionTypes.FetchTagRuleListFailure,
  props<{ tagRuleListErrorResponse: ResponseModel }>()
)

export const fetchTagRuleDescription = createAction(
  DocumentTagActionTypes.FetchTagRuleDescription,
  props<{
    payload: {
      projectId: number
    }
  }>()
)

export const fetchTagRuleDescriptionSuccess = createAction(
  DocumentTagActionTypes.FetchTagRuleDescriptionSuccess,
  props<{ tagRuleDescription: TagRule[] }>()
)

export const fetchTagRuleDescriptionFailure = createAction(
  DocumentTagActionTypes.FetchTagRuleDescriptionFailure,
  props<{ tagRuleDescriptionErrorResponse: ResponseModel }>()
)

export const filterTagRule = createAction(
  DocumentTagActionTypes.FilterTagRule,
  props<{ filterTagRuleId: number; isTagHeaderRequired: boolean }>()
)

export const setTagRuleViolationList = createAction(
  DocumentTagActionTypes.SetTagRuleViolationList,
  props<{ tagRuleViolationList: TagRule[] }>()
)

export const showHideTagRuleHeader = createAction(
  DocumentTagActionTypes.ShowHideTagRuleHeader,
  props<{ isTagHeaderRequired: boolean }>()
)

export const fetchDocumentProjectTagGroups = createAction(
  DocumentTagActionTypes.FetchDocumentProjectTagGroups,
  props<{
    payload: {
      documentProjectTagPayload: DocumentProjectTagRequestModel
    }
  }>()
)

export const setDocumentProjectTagGroups = createAction(
  DocumentTagActionTypes.SetDocumentProjectTagGroups,
  props<{
    payload: { documentProjectTagGroup: DocumentProjectTagGroupModel[] }
  }>()
)

export const fetchDocumentProjectTagGroupsFailure = createAction(
  DocumentTagActionTypes.FetchDocumentProjectTagGroupsFailure,
  props<{ documentProjectTagGroupErrorResponse: ResponseModel }>()
)

export const setDocumentTagUpdated = createAction(
  DocumentTagActionTypes.SetDocumentTagUpdated,
  props<{ isDocumentTagUpdated: boolean }>()
)

export const reloadTagGroup = createAction(
  DocumentTagActionTypes.ReloadTagGroup,
  props<{ reloadTagGroup: boolean }>()
)
