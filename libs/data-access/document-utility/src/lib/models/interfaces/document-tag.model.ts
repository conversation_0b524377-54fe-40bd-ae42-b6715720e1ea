import {
  PageControlActionType,
  TagGroupActionTitle,
  TagGroupActionType,
} from '@venio/shared/models/constants'
import {
  DocumentCodingModel,
  SamplingModel,
} from '@venio/shared/models/interfaces'

export interface ProjectTag {
  Description: string
  IsExclusive: boolean
  IsProfileManualTag: boolean
  IsReviewTag: boolean
  IsTagOperation: boolean
  IsVARTag: boolean
  ParentTagId: string
  SelectFlag: boolean
  TagGroupId: number
  TagGroupName: string
  TagId: number
  TagName: string
  TagCommentRequirement: string
  CommentLabel: string
  Comments: string
  TagColor: string
  TagSortOrder: number
  TempTagId: string
  TagIdLineage?: string
  WritePermission: boolean
  expanded: boolean
  selected: boolean
  TagSecurityLevel: string
  TreeKeyId: string
  TreeParentId: string
  IsInUse: boolean
  children?: ProjectTag[]
}

export interface DocumentTagRequestModel {
  fileIds: number[] | null
  isNotReviewedOnly: boolean | null
  markAsReviewed: boolean | null
  projectId: number | null
  searchTempTableName: string | null
  tagSettings: TagSettings | null
  tags: ProjectTag[] | null
  unSelectedFileIds: number[] | null
  isBatchSelected: boolean | null
}

export interface TagSettings {
  IncludeEmailThread: boolean | null
  PropagatePCSet: boolean | null
  dupTagOption: number | null
  nddTagOption: number | null
}

export interface DocumentTag {
  tagId: number
  comments?: string | null
  tagState: boolean | null
  reviewTagTriState?: boolean | null
}

export interface SaveTagRequestModel {
  FileIds: number[]
  IsNotReviewedOnly: boolean
  MarkAsReviewed: boolean
  ProjectId: number
  SearchTempTableName: string | null
  TagSettings: TagSettings | null
  Tags: ProjectTag[]
  UnSelectedFileIds: number[]
  isBatchSelected: boolean
  MediaList: number[] | null
  ReviewSetBatchId: number
  DocShareToken: string
  TagEventComment: string
  samplingInfo: SamplingModel | null
  isRandom: boolean
  ModuleName: string
  FieldCodingModel: DocumentCodingModel[] | null
  isBulkDocument?: boolean
  isEmailThreadTagging?: boolean
}

export interface SaveTagResponseModel {
  AppliedTagRule: TagSettings
  IncludeEmailThread: boolean
  PropagatePCSet: boolean
  dupTagOption: number
  nddTagOption: number
  DuplicateDocCount: number
  EmailThreadCount: number
  IsTagOperation: boolean
  IsTagOperationSuccessful: boolean
  NddFileCount: number
  ParentFileCount: number
  SelectedDocCount: number
  TotalCount: number
  UntaggedCount: number
  batchid: number
  tagName: string
  tagId: number
  IsExclusive: boolean
  TagGroupName: string
}

export interface FileTaggedRequestModel {
  isFileTagged: boolean

  reviewSetId: number

  currentPageFileIds: number[]
}

export interface FileTaggedResponseModel {
  reviewedFileIds: number[]
}

/**
 * Tag Saved Model use for Tag copy in review
 */
export interface TagSavedModel {
  fileId: number
  fileName: string
  tagSavedChange: TagSavedChangeModel[]
}

/**
 * Tag Save Change use for TagSavedModel
 */
export interface TagSavedChangeModel {
  tagId: number
  tagName: string
  isTagOperation: boolean
  isExclusive?: boolean
  tagGroupName?: string
}

export interface UpdatedFileDocNotes {
  fileId: number
  documentNotes: string
}

export interface UpdatedFileTagColors {
  fileId: number
  tagNameColors: string
}

export interface UpdatedFileTags {
  fileId: number
  tags: string
}

/**
 * Tag Saved Model use for Tag copy in review
 */
export interface TagSavedModel {
  fileId: number
  fileName: string
  tagSavedChange: TagSavedChangeModel[]
}

export interface TagSavedChangeModel {
  tagId: number
  tagName: string
  isTagOperation: boolean
  isExclusive?: boolean
  tagGroupName?: string
}

export interface ProjectTagViewModel extends ProjectTag {
  parentTreeKeyId?: string
}

export interface DocumentTagPayloadModel {
  selectedDocuments: number[]
  searchResultTempTable: string
  projectId: number
  isBatchSelected: boolean
}

export interface SaveDocumentPayloadModel {
  selectedDocuments: number[]
  unselectedDocuments: number[]
  isBatchSelected: boolean
  isBulkDocument: boolean
}

export interface SaveDocumentCodingPayloadModel {
  fieldCoding: DocumentCodingModel[]
  updatedCodingFieldInfoIds: number[]
}

export interface SaveDocumentTagPayloadModel {
  projectTags: ProjectTag[]
  tagSettings: TagSettings
  selectChangedTags: any
  isEmailThreadTagging?: boolean
}

export interface SaveTagPayloadModel {
  documentPayload: SaveDocumentPayloadModel
  documentTagPayload: SaveDocumentTagPayloadModel
  documentCodingTagPayload: SaveDocumentCodingPayloadModel
  currentPageFileIds: number[]
  projectId: number
  navigationType: PageControlActionType
  searchResultTempTable: string
  documentShareToken?: string
}

export interface DocumentProjectTagRequestModel {
  fileIds: number[] | null
  projectId: number | null
}

export interface DocumentProjectTagGroupModel {
  isExclusive: boolean
  tagGroupId: number
  name: string
}

export interface TagSelectionModel {
  comments: string
  isChecked: boolean
  isExclusive: boolean
  tagId: number
}

export interface TagGroupInfo {
  title: TagGroupActionTitle
  actionType: TagGroupActionType
}

export enum TagActionType {
  TagAllInclusiveEmails,
  TagWholeThreadOfSelectedDocuments,
  SelectedDocuments,
}
