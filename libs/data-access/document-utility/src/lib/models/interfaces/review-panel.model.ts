import { TempTableResponseModel } from '@venio/data-access/review'
import { SimilaritySearchScopeType } from '../constants'

export interface DocumentMetadata {
  key: string
  value: string
}

export interface ParentChild {
  fileId: number
  fileName: string
  seqNo: number
  isFileExistsSearchScope: boolean
  metadata: DocumentMetadata[]
  children: ParentChild[]
}

export interface ParentChildPayloadModel {
  fileId: number
  projectId: number
  tempTable: TempTableResponseModel
  venioFieldIds: number[]
  customFieldIds: number[]
}

export interface ReviewPanelSelectedDocumentModel {
  currentfileId: number
  documentNo: number
  isDocumentExistsInSearchScope: boolean
}

export interface MetadataPayloadModel {
  fileId: number
  projectId: number
  tempTable: TempTableResponseModel
  venioFieldIds: number[]
  customFieldIds: number[]
}

export interface DuplicateDocument {
  fileId: number
  orderId: number
  isFileExistsSearchScope: boolean
  metadata: DocumentMetadata[]
}

export interface DuplicatePayloadModel {
  fileId: number
  projectId: number
  tempTable: TempTableResponseModel
  venioFieldIds: number[]
  customFieldIds: number[]
}

export interface DocumentHistoryEntry {
  actionDate: Date
  actionYear: number
  actionMonth: string
  actionDay: number
  actionTime: string
  actionPerformedBy: string
  action: string
  actionDescription: string
  tagName: string
  tagColor: string
}

export interface DocumentHistory {
  year: number
  documentHistoryEntries: DocumentHistoryEntry[]
}

export interface SimilarDocumentFetchModel {
  similarityScore?: number
  tempTables?: SimilarDocumentTempTables
  searchTempTable: string
  isSearchScope: boolean
}

export interface SimilarDocumentRequestModel extends SimilarDocumentFetchModel {
  fileId: number
  searchId: number
  mediaList?: number[]
  folderList?: number[]
}

export interface SimilarDocumentTempTables {
  similarHitTableName: string
  similarResultTableName: string
}

export interface SimilarDocumentResponseModel {
  totalHitCount: number
  tempTables?: SimilarDocumentTempTables
  searchTerms: string[]
}

export interface SimilarDocumentModel {
  fileId: number
  metadata: DocumentMetadata[]
}

export interface SimilarDocumentPayloadModel {
  fileId: number
  projectId: number
  pageNumber: number
  pageSize: number
  similarDocumentResponse: SimilarDocumentResponseModel
  venioFieldIds: number[]
  customFieldIds: number[]
}

export interface ReviewPanelModel {
  similarityScore: number | undefined
  similaritySearchScopeType: SimilaritySearchScopeType | undefined
}

export interface SeqNoByFileIdPayloadModel {
  fileId: number
  tempTable: TempTableResponseModel
}

export interface PopulateSimilarPayloadModel {
  projectId: number
  fileId: number
  searchId: number
  similarityScore: number
  mediaScopeType: SimilaritySearchScopeType
  searchTempTable: string
  isSearchScope: boolean
  mediaList: number[]
  folderList: number[]
  similarDocumentResponse: SimilarDocumentResponseModel
}

export interface DocumentSimilarViewModel {
  similarDocuments: SimilarDocumentModel[]
  headers: string[]
}

export interface AdjustSimilarDocumentPayloadModel
  extends SimilarDocumentFetchModel {
  projectId: number
}

export interface NearDuplicateDocument {
  fileId: number
  orderId: number
  isFileExistsSearchScope: boolean
  metadata: DocumentMetadata[]
}

export interface NearDuplicatePayloadModel {
  fileId: number
  projectId: number
  tempTable: TempTableResponseModel
  venioFieldIds: number[]
}

export interface NearDuplicateModel {
  isNDDFormed: boolean
  nearDuplicates: NearDuplicateDocument[]
}

export interface CustomHeaders {
  [key: string]: string
}
