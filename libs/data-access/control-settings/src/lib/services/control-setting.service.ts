import { Injectable, signal } from '@angular/core'
import { HttpClient } from '@angular/common/http'
import { environment } from '@venio/shared/environments'
import { Observable } from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { ControlSettingModel } from '../models/interfaces/control-setting.mdel'

@Injectable({ providedIn: 'root' })
export class ControlSettingService {
  /**
   * Private state signal for control settings.
   */
  private controlSettingState = signal<ControlSettingModel>(undefined)

  /**
   * Access the current control settings.
   * The state is a signal and is immutable and can only be set via the setControlSetting method.
   * @returns {ControlSettingModel} The current control settings.
   */
  public get getControlSetting(): ControlSettingModel {
    return this.controlSettingState()
  }

  /**
   * Private getter for the API URL from the environment settings.
   * @returns {string} The API URL.
   */
  private get _apiUrl(): string {
    return environment.apiUrl
  }

  constructor(private http: HttpClient) {}

  /**
   * Fetches control settings from the server.
   * @returns {Observable<ResponseModel>} - An observable of the response model.
   */
  public fetchControlSetting(): Observable<ResponseModel> {
    return this.http.get<ResponseModel>(
      `${this._apiUrl}/config/control_settings`
    )
  }

  /**
   * Sets the control setting.
   * @param {ControlSettingModel} controlSetting - The new control setting to be set.
   * @returns {void}
   */
  public setControlSetting(controlSetting: ControlSettingModel): void {
    this.controlSettingState.set(controlSetting)
  }
}
