import { Injectable } from '@angular/core'
import { HttpClient, HttpParams } from '@angular/common/http'
import { environment } from '@venio/shared/environments'
import { Observable } from 'rxjs'

@Injectable({ providedIn: 'root' })
export class ConfigService {
  /**
   * Private getter for the API URL from the environment settings.
   * @returns {string} The API URL.
   */
  private get _apiUrl(): string {
    return environment.apiUrl
  }

  constructor(private http: HttpClient) {}

  /**
   * Service endpoint for fetching license status.
   * @param component component
   * @param subComponent sub component
   */
  public fetchLicenseStatus$(
    component: string,
    subComponent: string
  ): Observable<boolean> {
    return this.http.get<boolean>(this._apiUrl + '/licenses/status', {
      params: new HttpParams()
        .set('component', component)
        .set('subComponent', subComponent),
    })
  }
}
