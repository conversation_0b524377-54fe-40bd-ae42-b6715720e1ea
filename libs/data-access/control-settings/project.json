{"name": "control-settings", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/data-access/control-settings/src", "prefix": "lib", "projectType": "library", "tags": ["control-settings"], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/data-access/control-settings/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/data-access/control-settings/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/data-access/control-settings/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/data-access/control-settings/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}