import { inject, Injectable } from '@angular/core'
import { Store } from '@ngrx/store'
import * as ReportActions from './reports.actions'
import * as ReportSelectors from './reports.selectors'
import { ReportsState } from './reports.reducer'
import { ReportTypes } from '@venio/shared/models/constants'
import { SelectionRange } from '@progress/kendo-angular-dateinputs'
import { ResponseModel } from '@venio/shared/models/interfaces'

@Injectable({ providedIn: 'root' })
export class ReportsFacade {
  private readonly store = inject(Store)

  /**
   * Selects the selectedReportType from the Reports State.
   * @see ReportsTypes
   */
  public readonly selectedSelectedReportType$ = this.store.select(
    ReportSelectors.getStateFromReportStore('selectedReportType')
  )

  public readonly selectSelectedDateRange$ = this.store.select(
    ReportSelectors.getStateFromReportStore('dateRange')
  )

  public readonly selectIsReportLoading$ = this.store.select(
    ReportSelectors.getStateFromReportStore('isReportLoading')
  )

  public readonly selectIsReportExporting$ = this.store.select(
    ReportSelectors.getStateFromReportStore('isReportExporting')
  )

  public readonly selectFetchReportSuccessResponse$ = this.store.select(
    ReportSelectors.getStateFromReportStore('fetchReportSuccessResponse')
  )

  public readonly selectFetchReportErrorResponse$ = this.store.select(
    ReportSelectors.getStateFromReportStore('fetchReportErrorResponse')
  )

  public readonly selectFetchExportableReportSuccess$ = this.store.select(
    ReportSelectors.getStateFromReportStore('fetchExportDataSuccess')
  )

  public readonly selectFetchExportableReportError$ = this.store.select(
    ReportSelectors.getStateFromReportStore('fetchExportDataError')
  )

  public readonly selectTotalReportCount$ = this.store.select(
    ReportSelectors.totalReportCount
  )

  public readonly selectPaging$ = this.store.select(
    ReportSelectors.getStateFromReportStore('paging')
  )

  public readonly selectExportReportFormatType$ = this.store.select(
    ReportSelectors.getStateFromReportStore('exportEvent')
  )

  public readonly selectFetchDBServerDateTimeInfoSuccess$ = this.store.select(
    ReportSelectors.selectFetchDBServerDateTimeInfoSuccess
  )

  public readonly selectFetchDBServerDateTimeInfoFailure$ = this.store.select(
    ReportSelectors.selectFetchDBServerDateTimeInfoFailure
  )

  /**
   * Resetting a specific property of the Reports State.
   * You can pass a single property or an array of properties to reset.
   * @example
   * // Reset a single property
   * this.ReportsFacade.resetCommandState('X')
   *
   * // Reset multiple properties
   * this.ReportsFacade.resetReportState(['Y', 'RemoveX'])
   * @param {ReportsState} stateKey - state keys
   * @returns {void}
   */
  public resetReportState(
    stateKey: keyof ReportsState | Array<keyof ReportsState>
  ): void {
    this.store.dispatch(ReportActions.resetReportsState({ stateKey }))
  }

  public storeReportType(reportType: ReportTypes): void {
    this.store.dispatch(ReportActions.storeReportType({ reportType }))
  }

  public storeSelectedUsers(selectedUsers: number[]): void {
    this.store.dispatch(ReportActions.storeSelectedUsers({ selectedUsers }))
  }

  public storeSelectedLegalHold(selectedLegalHold: number[]): void {
    this.store.dispatch(
      ReportActions.storeSelectedLegalHold({ selectedLegalHold })
    )
  }

  public storeSelectedCustodians(selectedCustodians: number[]): void {
    this.store.dispatch(
      ReportActions.storeSelectedCustodians({ selectedCustodians })
    )
  }

  public storeSelectedStatuses(selectedStatus: number[] | number): void {
    this.store.dispatch(ReportActions.storeSelectedStatuses({ selectedStatus }))
  }

  public storeSelectedActiveTerminatedStatuses(
    selectedActiveTerminatedStatus: boolean | null
  ): void {
    this.store.dispatch(
      ReportActions.storeSelectedActiveTerminatedStatuses({
        selectedActiveTerminatedStatus,
      })
    )
  }

  public storeSelectedActiveTerminatedLegalHold(
    selectedActiveTerminatedLegalHold: number[]
  ): void {
    this.store.dispatch(
      ReportActions.storeSelectedActiveTerminatedLegalHold({
        selectedActiveTerminatedLegalHold,
      })
    )
  }

  public storeSelectedProjects(selectedProjects: number[]): void {
    this.store.dispatch(
      ReportActions.storeSelectedProjects({ selectedProjects })
    )
  }

  public storeSelectedDeleteMode(selectedDeletedMode: number[]): void {
    this.store.dispatch(
      ReportActions.storeSelectedDeleteMode({ selectedDeletedMode })
    )
  }

  public storeDateRange(dateRange: SelectionRange): void {
    this.store.dispatch(ReportActions.storeDateRange({ dateRange }))
  }

  public storePaging(paging: { pageSize: number; pageNumber: number }): void {
    this.store.dispatch(ReportActions.storePaging({ paging }))
  }

  public fetchReports(): void {
    this.store.dispatch(ReportActions.fetchReports())
  }

  public fetchExportableReports(): void {
    this.store.dispatch(ReportActions.fetchExportableReport())
  }

  public notifyExportReportToGivenFormat(exportEvent: object): void {
    this.store.dispatch(
      ReportActions.notifyExportReportToGivenFormat({ exportEvent })
    )
  }

  public setIsReportLoading(isReportLoading: boolean): void {
    this.store.dispatch(ReportActions.setIsReportLoading({ isReportLoading }))
  }

  public fetchDBServerDateTimeInfo(): void {
    this.store.dispatch(ReportActions.fetchDBServerDateTimeInfo())
  }

  public fetchDBServerDateTimeInfoSuccess(response: ResponseModel): void {
    this.store.dispatch(
      ReportActions.fetchDBServerDateTimeInfoSuccess({
        fetchDBServerDateTimeInfoSuccess: response,
      })
    )
  }

  public fetchDBServerDateTimeInfoFailure(error: ResponseModel): void {
    this.store.dispatch(
      ReportActions.fetchDBServerDateTimeInfoFailure({
        fetchDBServerDateTimeInfoFailure: error,
      })
    )
  }
}
