import { Action, createReducer, on } from '@ngrx/store'
import * as ReportActions from './reports.actions'
import { resetStateProperty } from '@venio/util/utilities'
import { ReportTypes } from '@venio/shared/models/constants'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { SelectionRange } from '@progress/kendo-angular-dateinputs'

export const REPORTS_FEATURE_KEY = 'reportsStore'
const DEFAULT_PAGING = {
  pageSize: 25,
  pageNumber: 1,
}

export interface ReportsState {
  selectedReportType: ReportTypes | undefined
  selectedUsers: number[] | undefined
  selectedLegalHold: number[] | undefined
  selectedCustodians: number[] | undefined
  selectedStatus: number | number[] | undefined
  selectedActiveTerminatedStatus: boolean | null
  selectedActiveTerminatedLegalHold: number[] | undefined
  selectedProjects: number[] | undefined
  selectedDeleteMode: number[] | undefined
  dateRange: SelectionRange | undefined
  isReportLoading: boolean | undefined
  isReportExporting: boolean | undefined
  exportEvent: object | undefined
  fetchReportSuccessResponse: ResponseModel | undefined
  fetchReportErrorResponse: ResponseModel | undefined
  fetchExportDataSuccess: ResponseModel | undefined
  fetchExportDataError: ResponseModel | undefined
  fetchDBServerDateTimeInfoSuccess: ResponseModel | undefined
  fetchDBServerDateTimeInfoFailure: ResponseModel | undefined
  paging: {
    pageSize: number
    pageNumber: number
  }
}

export const reportsInitialState: ReportsState = {
  selectedReportType: undefined,
  selectedUsers: undefined,
  selectedLegalHold: undefined,
  selectedCustodians: undefined,
  selectedStatus: undefined,
  selectedProjects: undefined,
  selectedDeleteMode: undefined,
  dateRange: undefined,
  isReportLoading: undefined,
  isReportExporting: undefined,
  fetchReportSuccessResponse: undefined,
  fetchReportErrorResponse: undefined,
  exportEvent: undefined,
  fetchExportDataSuccess: undefined,
  fetchExportDataError: undefined,
  fetchDBServerDateTimeInfoSuccess: undefined,
  fetchDBServerDateTimeInfoFailure: undefined,
  selectedActiveTerminatedStatus: null,
  selectedActiveTerminatedLegalHold: undefined,
  // Default paging values
  paging: {
    ...DEFAULT_PAGING,
  },
}

const reducer = createReducer<ReportsState>(
  reportsInitialState,
  on(ReportActions.resetReportsState, (state, { stateKey }) =>
    resetStateProperty<ReportsState>(state, reportsInitialState, stateKey)
  ),
  on(ReportActions.storeReportType, (state, { reportType }) => ({
    ...state,
    selectedReportType: reportType,
  })),
  on(ReportActions.storeSelectedUsers, (state, { selectedUsers }) => ({
    ...state,
    selectedUsers,
  })),
  on(ReportActions.storeSelectedLegalHold, (state, { selectedLegalHold }) => ({
    ...state,
    selectedLegalHold,
  })),
  on(
    ReportActions.storeSelectedCustodians,
    (state, { selectedCustodians }) => ({
      ...state,
      selectedCustodians,
    })
  ),
  on(ReportActions.storeSelectedStatuses, (state, { selectedStatus }) => ({
    ...state,
    selectedStatus,
  })),
  on(
    ReportActions.storeSelectedActiveTerminatedLegalHold,
    (state, { selectedActiveTerminatedLegalHold }) => ({
      ...state,
      selectedActiveTerminatedLegalHold,
    })
  ),
  on(
    ReportActions.storeSelectedActiveTerminatedStatuses,
    (state, { selectedActiveTerminatedStatus }) => ({
      ...state,
      selectedActiveTerminatedStatus,
    })
  ),
  on(ReportActions.storeSelectedProjects, (state, { selectedProjects }) => ({
    ...state,
    selectedProjects,
  })),
  on(
    ReportActions.storeSelectedDeleteMode,
    (state, { selectedDeletedMode }) => ({
      ...state,
      selectedDeleteMode: selectedDeletedMode,
    })
  ),
  on(ReportActions.storeDateRange, (state, { dateRange }) => ({
    ...state,
    dateRange,
  })),
  on(ReportActions.fetchReports, (state) => ({
    ...state,
    isReportLoading: true,
  })),
  on(
    ReportActions.fetchReportsSuccess,
    (state, { fetchReportSuccessResponse }) => ({
      ...state,
      isReportLoading: false,
      fetchReportErrorResponse: undefined,
      fetchReportSuccessResponse,
    })
  ),
  on(
    ReportActions.fetchReportsFailure,
    (state, { fetchReportErrorResponse }) => ({
      ...state,
      isReportLoading: false,
      fetchReportSuccessResponse: undefined,
      fetchReportErrorResponse,
    })
  ),
  on(
    ReportActions.notifyExportReportToGivenFormat,
    (state, { exportEvent }) => ({
      ...state,
      isReportExporting: true,
      exportEvent,
    })
  ),
  on(
    ReportActions.fetchExportReportDataSuccess,
    (state, { fetchExportDataSuccess }) => ({
      ...state,
      isReportExporting: false,
      fetchExportDataError: undefined,
      fetchExportDataSuccess,
    })
  ),
  on(
    ReportActions.fetchExportReportDataFailure,
    (state, { fetchExportDataError }) => ({
      ...state,
      isReportExporting: false,
      fetchExportDataSuccess: undefined,
      fetchExportDataError,
    })
  ),
  on(ReportActions.storePaging, (state, { paging }) => ({
    ...state,
    paging,
  })),
  on(ReportActions.setIsReportLoading, (state, { isReportLoading }) => ({
    ...state,
    isReportLoading,
  })),
  on(ReportActions.fetchDBServerDateTimeInfo, (state) => ({
    ...state,
    isReportLoading: true,
  })),
  on(
    ReportActions.fetchDBServerDateTimeInfoSuccess,
    (state, { fetchDBServerDateTimeInfoSuccess }) => ({
      ...state,
      isReportLoading: false,
      fetchDBServerDateTimeInfoFailure: undefined,
      fetchDBServerDateTimeInfoSuccess,
    })
  ),
  on(
    ReportActions.fetchDBServerDateTimeInfoFailure,
    (state, { fetchDBServerDateTimeInfoFailure }) => ({
      ...state,
      isReportLoading: false,
      fetchDBServerDateTimeInfoSuccess: undefined,
      fetchDBServerDateTimeInfoFailure,
    })
  )
)

export function reportsReducer(
  state: ReportsState | undefined,
  action: Action
): ReportsState {
  return reducer(state, action)
}
