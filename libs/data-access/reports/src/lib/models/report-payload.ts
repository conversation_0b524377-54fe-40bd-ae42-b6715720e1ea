import { ReportTypes } from '@venio/shared/models/constants'
export interface ReportBody {
  userIds?: number[]
  projectIds?: number[]
  startDate?: string
  endDate?: string
  pageSize?: number
  pageNumber?: number
  deleteModes?: number[]
  // Only for client side use case.
  totalCount?: number
  legalHoldIds?: number[]
  legalHoldCutodianIds?: number[]
  CustodianStatus?: number[]
  activeTerminatedStatus?: boolean
  activeTerminatedHoldIds?: number[]
}

export interface ReportPayloadEvent {
  selectedReportType: ReportTypes
  body: ReportBody
}
