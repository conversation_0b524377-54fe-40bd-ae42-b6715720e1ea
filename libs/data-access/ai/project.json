{"name": "ai", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/data-access/ai/src", "prefix": "venio", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "libs/data-access/ai/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/data-access/ai/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/data-access/ai/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/data-access/ai/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}