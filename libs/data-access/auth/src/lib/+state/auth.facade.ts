import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import { LogoutEventPayloadModel } from '../models/interfaces/logout-event-payload.model'
import { UserLoginPayloadModel } from '../models/interfaces/user-login-payload.model'
import * as AuthActions from './auth.actions'
import * as AuthSelectors from './auth.selectors'
import { AuthState } from './auth.reducer'
type AuthStateKeys = keyof AuthState | Array<keyof AuthState>
@Injectable()
export class AuthFacade {
  public selectLoginSuccess$ = this.store.pipe(
    select(AuthSelectors.getStateOf('loginSuccess'))
  )

  public selectLogoutSuccess$ = this.store.pipe(
    select(AuthSelectors.getStateOf('logoutSuccess'))
  )

  public selectLoginError$ = this.store.pipe(
    select(AuthSelectors.getStateOf('error'))
  )

  public selectLoginSettingSuccess$ = this.store.pipe(
    select(AuthSelectors.getStateOf('loginSettings'))
  )

  public selectLoginSettingError$ = this.store.pipe(
    select(AuthSelectors.getStateOf('loginSettingsError'))
  )

  public selectTwoFactorNotificationSent$ = this.store.pipe(
    select(AuthSelectors.getStateOf('isTwoFactorNoticationSent'))
  )

  public selectTwoFactorVerificationSuccess$ = this.store.pipe(
    select(
      AuthSelectors.getStateOf('twoFactorAuthenticationVerificationResponse')
    )
  )

  public selectTwoFactorVerificationFailure$ = this.store.pipe(
    select(
      AuthSelectors.getStateOf(
        'twoFactorAuthenticationVerificationFailureResponse'
      )
    )
  )

  constructor(private readonly store: Store) {}

  public resetAuthState(stateKey: AuthStateKeys): void {
    this.store.dispatch(AuthActions.resetAuthState({ stateKey }))
  }

  public login(payload: UserLoginPayloadModel): void {
    this.store.dispatch(AuthActions.login({ payload }))
  }

  public logout(payload?: LogoutEventPayloadModel): void {
    this.store.dispatch(AuthActions.logout({ payload }))
  }

  public fetchLoginSettings(): void {
    this.store.dispatch(AuthActions.fetchLoginSettings())
  }

  public sendTwoFactorAuthenticationNotification(resend: boolean): void {
    this.store.dispatch(
      AuthActions.sendTwoFactorAuthenticationNotification({ resend })
    )
  }

  public verifyTwoFactorAuthenticationCode(
    verificationCode: string,
    rememberUser: boolean
  ): void {
    this.store.dispatch(
      AuthActions.verifyTwoFactorAuthenticationCode({
        verificationCode,
        rememberUser,
      })
    )
  }
}
