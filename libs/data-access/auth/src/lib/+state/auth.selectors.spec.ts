import { ResponseModel } from '@venio/shared/models/interfaces'
import { LogoutEventPayloadModel } from '../models/interfaces/logout-event-payload.model'
import { TokenResponseModel } from '../models/interfaces/token-response.model'
import { AuthPartialState } from './auth.reducer'
import * as AuthSelectors from './auth.selectors'
import {
  LoginSettingsModel,
  TwoFactorAuthResponseModel,
} from '../models/interfaces/login-settings.model'

describe('Auth Selectors', () => {
  let state: AuthPartialState
  const tokenResponse = { access_token: 'x' } as TokenResponseModel
  const logoutEventPayload = { returnUrl: '' } as LogoutEventPayloadModel
  const errorResponse = { status: 'error' } as ResponseModel
  const loginSettingsError = { status: 'error' } as ResponseModel
  const loginSettings = {} as LoginSettingsModel
  const isTwoFactorNoticationSent = false
  const twoFactorAuthenticationVerificationResponse: TwoFactorAuthResponseModel =
    {
      cookieExpirationHours: 1,
      isVerificationSuccessful: true,
      message: '',
      rememberUserLoginIdentifierName: '',
      rememberUserLoginIdentifierValue: '',
      isUserLocked: false,
    }
  const twoFactorAuthenticationVerificationFailureResponse = {
    status: 'error',
  } as ResponseModel
  beforeEach(() => {
    state = {
      venioAuth: {
        loginSuccess: tokenResponse,
        error: errorResponse,
        logoutSuccess: logoutEventPayload,
        loginSettings: loginSettings,
        loginSettingsError: loginSettingsError,
        isTwoFactorNoticationSent: isTwoFactorNoticationSent,
        twoFactorAuthenticationVerificationFailureResponse,
        twoFactorAuthenticationVerificationResponse,
      },
    }
  })

  describe('Auth Selectors', () => {
    it('getLoginSuccess() should return the login success response object', () => {
      const results = AuthSelectors.getStateOf('loginSuccess')(state)

      expect(results).toStrictEqual(tokenResponse)
    })
    it('getLoginError() should return the login fail error response', () => {
      const results = AuthSelectors.getStateOf('error')(state)

      expect(results).toStrictEqual(errorResponse)
    })
    it('getLogoutSuccess() should return the logged out event payload', () => {
      const results = AuthSelectors.getStateOf('logoutSuccess')(state)

      expect(results).toStrictEqual(logoutEventPayload)
    })
  })
})
