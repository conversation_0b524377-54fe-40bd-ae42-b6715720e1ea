import { createAction, props } from '@ngrx/store'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { LogoutEventPayloadModel } from '../models/interfaces/logout-event-payload.model'
import { TokenResponseModel } from '../models/interfaces/token-response.model'
import { UserLoginPayloadModel } from '../models/interfaces/user-login-payload.model'
import {
  LoginSettingsModel,
  TwoFactorAuthResponseModel,
} from '../models/interfaces/login-settings.model'
import { AuthState } from './auth.reducer'

export const resetAuthState = createAction(
  '[Auth] Reset Auth State',
  props<{
    stateKey: keyof AuthState | Array<keyof AuthState>
  }>()
)

export const login = createAction(
  '[Auth] Login',
  props<{
    payload: UserLoginPayloadModel
  }>()
)

export const loginSuccess = createAction(
  '[Auth] Login Success',
  props<{ loginSuccess: TokenResponseModel }>()
)

export const loginFailure = createAction(
  '[Auth] Login Failure',
  props<{ error: ResponseModel }>()
)

export const logout = createAction(
  '[Auth] Logout',
  props<{
    payload?: LogoutEventPayloadModel
  }>()
)

export const logoutSuccess = createAction(
  '[Auth] Logout Success',
  props<{ logoutSuccess: LogoutEventPayloadModel | undefined }>()
)

//export const ResetAuthState = '[Document View] Reset State',

export const fetchLoginSettings = createAction('[Auth] Fetch Login Settings')

export const fetchLoginSettingsSuccess = createAction(
  '[Auth] Fetch Login Settings Success',
  props<{ loginSettings: LoginSettingsModel }>()
)

export const fetchLoginSettingsFailure = createAction(
  '[Auth] Fetch Login Settings Failure',
  props<{ error: ResponseModel }>()
)

export const sendTwoFactorAuthenticationNotification = createAction(
  '[Auth] Send Two Factor Authentication Notification',
  props<{ readonly resend: boolean }>()
)
export const sendTwoFactorAuthenticationNotificationSuccess = createAction(
  '[Auth] Send Two Factor Authentication Notification Success',
  props<{ readonly isNotificationSent: boolean }>()
)

export const sendTwoFactorAuthenticationNotificationFailure = createAction(
  '[Auth] Send Two Factor Authentication Notification Failure',
  props<{ readonly error: ResponseModel }>()
)

export const verifyTwoFactorAuthenticationCode = createAction(
  '[Auth] Verify Two Factor Authentication Code',
  props<{ verificationCode: string; rememberUser: boolean }>()
)

export const verifyTwoFactorAuthenticationCodeSuccess = createAction(
  '[Auth] Verify Two Factor Authentication Code Success',
  props<{ verificationResponse: TwoFactorAuthResponseModel }>()
)

export const verifyTwoFactorAuthenticationCodeFailure = createAction(
  '[Auth] Verify Two Factor Authentication Code Failure',
  props<{ error: ResponseModel }>()
)
