import {
  HttpClient,
  HttpErrorResponse,
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptorsFromDi,
  withFetch,
} from '@angular/common/http'
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing'
import { fakeAsync, flush, TestBed, tick } from '@angular/core/testing'
import { ActivatedRoute, provideRouter } from '@angular/router'
import { provideMockStore } from '@ngrx/store/testing'
import { AuthFacade } from '../+state/auth.facade'
import { AuthStorageKeys } from '../models/constants/auth-storage-keys'
import { TokenResponseModel } from '../models/interfaces/token-response.model'
import { AuthService } from '../services/auth.service'
import { AuthInterceptor, TOKEN_HEADER_KEY } from './auth.interceptor'
import {
  IframeMessengerFacade,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { of, throwError } from 'rxjs'
import { PLATFORM_ID } from '@angular/core'
import { LocalStorage } from '@venio/shared/storage'

jest.mock('jsencrypt', () => {
  return {
    JSEncrypt: jest.fn().mockImplementation(() => {
      return {
        setPublicKey: jest.fn(),
        encrypt: jest.fn(() => 'mock_encrypted_data'),
        setPrivateKey: jest.fn(),
        decrypt: jest.fn().mockReturnValue('mocked_decrypted_data'),
      }
    }),
  }
})

describe('AuthInterceptor', () => {
  let client: HttpClient
  let controller: HttpTestingController
  let iframeMessengerFacade: IframeMessengerFacade
  let authService: AuthService
  let authFacade: AuthFacade
  let interceptor: AuthInterceptor
  let mockActivatedRoute: Partial<ActivatedRoute>
  const fakeEndPoint = '/demo-endpoint'

  beforeEach(() => {
    LocalStorage.clear()

    mockActivatedRoute = {
      snapshot: {
        queryParams: {},
      },
    } as Partial<ActivatedRoute>

    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
        provideMockStore({}),
        provideRouter([]),
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        {
          provide: AuthService,
          useValue: {
            refreshToken: jest.fn(),
          },
        },
        AuthInterceptor,
        AuthFacade,
        IframeMessengerFacade,
      ],
    })
    client = TestBed.inject(HttpClient)
    controller = TestBed.inject(HttpTestingController)
    iframeMessengerFacade = TestBed.inject(IframeMessengerFacade)
    authService = TestBed.inject(AuthService)
    authFacade = TestBed.inject(AuthFacade)
    interceptor = TestBed.inject(AuthInterceptor)

    // Mock AuthFacade's logout method
    authFacade.logout = jest.fn()
  })

  afterEach(() => {
    controller.verify()
    jest.clearAllTimers()
  })

  it('should be created', () => {
    expect(interceptor).toBeTruthy()
  })
  it('should add access token to the header when http request being intercepted', fakeAsync(() => {
    // GIVEN access token
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(true)
    const authHeader = 'Authorization'
    const accessToken = 'some_token_goes_here'
    const expectedHeader = `Bearer ${accessToken}`
    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)

    // WHEN http request is made
    client.get(fakeEndPoint).subscribe()
    tick(100)

    // THEN should intercept the http request and add authorization header
    const httpRequest = controller.expectOne(fakeEndPoint)
    expect(httpRequest.request.headers.has(authHeader)).toBe(true)
    expect(httpRequest.request.headers.get(authHeader)).toBe(expectedHeader)
    httpRequest.flush(null)
    flush()
  }))
  it('should initialize isLoadedAsMicroApp to false by default', fakeAsync(() => {
    // GIVEN facade set to false
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)

    // WHEN http request is made
    client.get(fakeEndPoint).subscribe()
    tick(100)

    // THEN should add signature header
    const httpRequest = controller.expectOne(fakeEndPoint)
    expect(httpRequest.request.headers.has('Signature')).toBe(true)
    httpRequest.flush(null)
    flush()
  }))
  it('should add signature header to all requests', fakeAsync(() => {
    // GIVEN no token
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)

    // WHEN http request is made
    client.get(fakeEndPoint).subscribe()
    tick(100)

    // THEN should add signature header
    const httpRequest = controller.expectOne(fakeEndPoint)
    expect(httpRequest.request.headers.has('Signature')).toBe(true)
    expect(httpRequest.request.headers.get('Signature')).toBe(
      'mock_encrypted_data'
    )
    httpRequest.flush(null)
    flush()
  }))
  it('should not add auth header when token is null', fakeAsync(() => {
    // GIVEN null token
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    LocalStorage.set(AuthStorageKeys.AccessToken, 'null')

    // WHEN http request is made
    client.get(fakeEndPoint).subscribe()
    tick(100)

    // THEN should not add authorization header
    const httpRequest = controller.expectOne(fakeEndPoint)
    expect(httpRequest.request.headers.has(TOKEN_HEADER_KEY)).toBe(false)
    httpRequest.flush(null)
    flush()
  }))
  it('should not add auth header when token is empty', fakeAsync(() => {
    // GIVEN empty token
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    LocalStorage.set(AuthStorageKeys.AccessToken, '')

    // WHEN http request is made
    client.get(fakeEndPoint).subscribe()
    tick(100)

    // THEN should not add authorization header
    const httpRequest = controller.expectOne(fakeEndPoint)
    expect(httpRequest.request.headers.has(TOKEN_HEADER_KEY)).toBe(false)
    httpRequest.flush(null)
    flush()
  }))
  it('should not add auth header when token is only whitespace', fakeAsync(() => {
    // GIVEN whitespace token
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    LocalStorage.set(AuthStorageKeys.AccessToken, '   ')

    // WHEN http request is made
    client.get(fakeEndPoint).subscribe()
    tick(100)

    // THEN should not add authorization header
    const httpRequest = controller.expectOne(fakeEndPoint)
    expect(httpRequest.request.headers.has(TOKEN_HEADER_KEY)).toBe(false)
    httpRequest.flush(null)
    flush()
  }))
  it('should remove quotes from token value', fakeAsync(() => {
    // GIVEN token with quotes
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    const tokenWithQuotes = '"quoted_token"'
    LocalStorage.set(AuthStorageKeys.AccessToken, tokenWithQuotes)

    // WHEN http request is made
    client.get(fakeEndPoint).subscribe()
    tick(100)

    // THEN should remove quotes from token
    const httpRequest = controller.expectOne(fakeEndPoint)
    expect(httpRequest.request.headers.get(TOKEN_HEADER_KEY)).toBe(
      'Bearer quoted_token'
    )
    httpRequest.flush(null)
    flush()
  }))
  it('should handle non-401 errors normally', fakeAsync(() => {
    // GIVEN normal request that returns 500 error
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    const errorResponse = new HttpErrorResponse({
      status: 500,
      statusText: 'Internal Server Error',
    })
    let actualError: HttpErrorResponse | undefined

    // WHEN http request is made
    client.get(fakeEndPoint).subscribe({
      next: () => {
        throw new Error('should have failed with 500 error')
      },
      error: (error: unknown) => (actualError = error as HttpErrorResponse),
    })
    tick(100)

    // THEN should pass through error normally
    const httpRequest = controller.expectOne(fakeEndPoint)
    httpRequest.flush('Server error', errorResponse)

    expect(actualError?.status).toBe(500)
    flush()
  }))
  it('should refresh token on 401 error when refresh token exists', fakeAsync(() => {
    // GIVEN tokens in storage
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    const accessToken = 'old_access_token'
    const refreshToken = 'refresh_token'
    const newAccessToken = 'new_access_token'
    const newRefreshToken = 'new_refresh_token'

    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)
    LocalStorage.set(AuthStorageKeys.RefreshToken, refreshToken)

    const tokenResponse: TokenResponseModel = {
      access_token: newAccessToken,
      refresh_token: newRefreshToken,
      expires_in: '3600',
      token_type: 'Bearer',
    }

    jest.spyOn(authService, 'refreshToken').mockReturnValue(of(tokenResponse))

    // WHEN http request fails with 401
    let completed = false
    client.get(fakeEndPoint).subscribe({
      next: () => (completed = true),
      error: () => {
        throw new Error('should not have failed')
      },
    })
    tick(100)

    // THEN should attempt refresh
    const firstRequest = controller.expectOne(fakeEndPoint)
    firstRequest.flush('Unauthorized', {
      status: 401,
      statusText: 'Unauthorized',
    })
    tick(100)

    // THEN should retry with new token
    expect(authService.refreshToken).toHaveBeenCalled()

    const retriedRequest = controller.expectOne(fakeEndPoint)
    expect(retriedRequest.request.headers.get(TOKEN_HEADER_KEY)).toBe(
      `Bearer ${newAccessToken}`
    )
    retriedRequest.flush({ success: true })

    tick(100)
    expect(completed).toBe(true)
    expect(LocalStorage.get(AuthStorageKeys.AccessToken)).toBe(newAccessToken)
    expect(LocalStorage.get(AuthStorageKeys.RefreshToken)).toBe(newRefreshToken)

    flush()
  }))
  it('should logout when refresh token fails and not in micro app', fakeAsync(() => {
    // GIVEN tokens and failing refresh
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    const accessToken = 'old_access_token'
    const refreshToken = 'refresh_token'

    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)
    LocalStorage.set(AuthStorageKeys.RefreshToken, refreshToken)

    jest
      .spyOn(authService, 'refreshToken')
      .mockReturnValue(throwError(() => new Error('Refresh failed')))

    // WHEN http request fails with 401
    client.get(fakeEndPoint).subscribe({
      next: () => {
        throw new Error('should have failed')
      },
      error: () => {},
    })
    tick(100)

    // THEN should attempt refresh and fail
    const firstRequest = controller.expectOne(fakeEndPoint)
    firstRequest.flush('Unauthorized', {
      status: 401,
      statusText: 'Unauthorized',
    })
    tick(100)

    // THEN should trigger logout
    expect(authService.refreshToken).toHaveBeenCalled()

    expect(authFacade.logout).toHaveBeenCalledWith({
      isSessionExpired: true,
      returnUrl: '/',
    })

    flush()
  }))
  it('should not logout when refresh token fails and in micro app', fakeAsync(() => {
    // GIVEN tokens, failing refresh, and micro app context
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(true)
    const accessToken = 'old_access_token'
    const refreshToken = 'refresh_token'

    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)
    LocalStorage.set(AuthStorageKeys.RefreshToken, refreshToken)

    jest
      .spyOn(authService, 'refreshToken')
      .mockReturnValue(throwError(() => new Error('Refresh failed')))

    // WHEN http request fails with 401
    client.get(fakeEndPoint).subscribe({
      next: () => {
        throw new Error('should have failed')
      },
      error: () => {},
    })
    tick(100)

    // THEN should attempt refresh and fail
    const firstRequest = controller.expectOne(fakeEndPoint)
    firstRequest.flush('Unauthorized', {
      status: 401,
      statusText: 'Unauthorized',
    })
    tick(100)

    // THEN should not logout in micro app
    expect(authService.refreshToken).toHaveBeenCalled()
    expect(authFacade.logout).not.toHaveBeenCalled()

    flush()
  }))
  it('should logout when 401 occurs and no refresh token exists', fakeAsync(() => {
    // GIVEN access token but no refresh token
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    const accessToken = 'old_access_token'

    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)
    // No refresh token set

    // WHEN http request fails with 401
    client.get(fakeEndPoint).subscribe({
      next: () => {
        throw new Error('should have failed')
      },
      error: () => {}, // expect error
    })
    tick(100)

    // THEN should logout without refresh attempt
    const request = controller.expectOne(fakeEndPoint)
    request.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' })
    tick(100)

    expect(authFacade.logout).toHaveBeenCalledWith({
      isSessionExpired: true,
      returnUrl: '/',
    })

    flush()
  }))
  it('should not logout when 401 occurs, no refresh token exists, and in micro app', fakeAsync(() => {
    // GIVEN access token but no refresh token and micro app context
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(true)
    const accessToken = 'old_access_token'

    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)
    // No refresh token set

    // WHEN http request fails with 401
    client.get(fakeEndPoint).subscribe({
      next: () => {
        throw new Error('should have failed')
      },
      error: () => {}, // expect error
    })
    tick(100)

    // THEN should not logout in micro app
    const request = controller.expectOne(fakeEndPoint)
    request.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' })
    tick(100)

    expect(authFacade.logout).not.toHaveBeenCalled()

    flush()
  }))
  it('should handle multiple 401 errors while refreshing', fakeAsync(() => {
    // GIVEN tokens and successful refresh
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    const accessToken = 'old_access_token'
    const refreshToken = 'refresh_token'
    const newAccessToken = 'new_access_token'
    const newRefreshToken = 'new_refresh_token'

    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)
    LocalStorage.set(AuthStorageKeys.RefreshToken, refreshToken)

    const tokenResponse: TokenResponseModel = {
      access_token: newAccessToken,
      refresh_token: newRefreshToken,
      expires_in: '3600',
      token_type: 'Bearer',
    }

    jest.spyOn(authService, 'refreshToken').mockReturnValue(of(tokenResponse))

    // WHEN two parallel requests fail with 401
    let completed1 = false
    let completed2 = false

    client.get(`${fakeEndPoint}/1`).subscribe({
      next: () => (completed1 = true),
      error: () => {
        throw new Error('first request should not have failed')
      },
    })

    client.get(`${fakeEndPoint}/2`).subscribe({
      next: () => (completed2 = true),
      error: () => {
        throw new Error('second request should not have failed')
      },
    })

    tick(100)

    // THEN both requests should trigger refresh only once
    const firstRequest = controller.expectOne(`${fakeEndPoint}/1`)
    const secondRequest = controller.expectOne(`${fakeEndPoint}/2`)

    firstRequest.flush('Unauthorized', {
      status: 401,
      statusText: 'Unauthorized',
    })
    secondRequest.flush('Unauthorized', {
      status: 401,
      statusText: 'Unauthorized',
    })

    tick(100)

    expect(authService.refreshToken).toHaveBeenCalledTimes(2)

    // THEN both should be retried with new token
    const retriedRequest1 = controller.expectOne(`${fakeEndPoint}/1`)
    const retriedRequest2 = controller.expectOne(`${fakeEndPoint}/2`)

    expect(retriedRequest1.request.headers.get(TOKEN_HEADER_KEY)).toBe(
      `Bearer ${newAccessToken}`
    )
    expect(retriedRequest2.request.headers.get(TOKEN_HEADER_KEY)).toBe(
      `Bearer ${newAccessToken}`
    )

    retriedRequest1.flush({ success: true })
    retriedRequest2.flush({ success: true })

    tick(100)

    expect(completed1).toBe(true)
    expect(completed2).toBe(true)

    flush()
  }))
  it('should use returnUrl from query params when available', fakeAsync(() => {
    // GIVEN query params with returnUrl
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    const returnUrlParam = '/target-page'
    mockActivatedRoute.snapshot.queryParams = { returnUrl: returnUrlParam }

    const accessToken = 'old_access_token'
    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)
    // No refresh token set

    // WHEN http request fails with 401
    client.get(fakeEndPoint).subscribe({
      next: () => {
        throw new Error('should have failed')
      },
      error: () => {},
    })
    tick(100)

    // THEN should logout with returnUrl from params
    const request = controller.expectOne(fakeEndPoint)
    request.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' })
    tick(100)

    expect(authFacade.logout).toHaveBeenCalledWith({
      isSessionExpired: true,
      returnUrl: returnUrlParam,
    })

    flush()
  }))
  it('should return empty string as returnUrl when on login page', fakeAsync(() => {
    // GIVEN on login page
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    // Mock router url to include 'login'
    Object.defineProperty(interceptor['router'], 'url', {
      get: () => '/auth/login',
    })

    const accessToken = 'access_token'
    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)
    // No refresh token

    // WHEN http request fails with 401
    client.get(fakeEndPoint).subscribe({
      next: () => {
        throw new Error('should have failed')
      },
      error: () => {},
    })
    tick(100)

    // THEN should logout with empty returnUrl
    const request = controller.expectOne(fakeEndPoint)
    request.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' })
    tick(100)

    // THEN should use empty string as returnUrl
    expect(authFacade.logout).toHaveBeenCalledWith({
      isSessionExpired: true,
      returnUrl: '',
    })

    flush()
  }))
  it('should use current URL as returnUrl when not on login page', fakeAsync(() => {
    // GIVEN on non-login page
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    // Mock router url to non-login page
    const currentUrl = '/dashboard'
    Object.defineProperty(interceptor['router'], 'url', {
      get: () => currentUrl,
    })

    const accessToken = 'access_token'
    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)
    // No refresh token

    // WHEN http request fails with 401
    client.get(fakeEndPoint).subscribe({
      next: () => {
        throw new Error('should have failed')
      },
      error: () => {},
    })
    tick(100)

    // THEN should logout with current URL as returnUrl
    const request = controller.expectOne(fakeEndPoint)
    request.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' })
    tick(100)

    // THEN should use current URL as returnUrl
    expect(authFacade.logout).toHaveBeenCalledWith({
      isSessionExpired: true,
      returnUrl: currentUrl,
    })

    flush()
  }))
  it('should not add signature when window is unavailable', fakeAsync(() => {
    // GIVEN server-side environment
    TestBed.resetTestingModule()
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(withInterceptorsFromDi(), withFetch()),
        provideHttpClientTesting(),
        provideMockStore({}),
        provideRouter([]),
        { provide: WINDOW, useValue: undefined },
        { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
        {
          provide: IframeMessengerFacade,
          useValue: { selectLoadedAsMicroApp$: of(false) },
        },
        AuthFacade,
      ],
    })

    const ssrClient = TestBed.inject(HttpClient)
    const ssrController = TestBed.inject(HttpTestingController)
    const fakeEndPoint = '/no-window-request'

    // WHEN making request without window
    ssrClient.get(fakeEndPoint).subscribe()
    tick(100)

    // THEN no signature header
    const req = ssrController.expectOne(fakeEndPoint)
    expect(req.request.headers.has('Signature')).toBe(false)
    req.flush(null)
    flush()
  }))
  it('should handle sequential requests after a refresh failure', fakeAsync(() => {
    // GIVEN tokens and failed refresh
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    const accessToken = 'old_access_token'
    const refreshToken = 'refresh_token'

    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)
    LocalStorage.set(AuthStorageKeys.RefreshToken, refreshToken)

    jest
      .spyOn(authService, 'refreshToken')
      .mockReturnValue(throwError(() => new Error('Refresh failed')))

    // WHEN first request fails with 401
    client.get(`${fakeEndPoint}/1`).subscribe({
      next: () => {
        throw new Error('should have failed')
      },
      error: () => {},
    })
    tick(100)

    const firstRequest = controller.expectOne(`${fakeEndPoint}/1`)
    firstRequest.flush('Unauthorized', {
      status: 401,
      statusText: 'Unauthorized',
    })
    tick(100)

    // THEN should trigger logout
    expect(authService.refreshToken).toHaveBeenCalledTimes(1)
    expect(authFacade.logout).toHaveBeenCalledWith({
      isSessionExpired: true,
      returnUrl: '/',
    })

    // WHEN making a subsequent request
    jest.spyOn(authService, 'refreshToken').mockClear()
    client.get(`${fakeEndPoint}/2`).subscribe()
    tick(100)

    // THEN should still process it normally (tokens likely cleared by logout)
    const secondRequest = controller.expectOne(`${fakeEndPoint}/2`)
    secondRequest.flush({ success: true })

    // Verify refresh not attempted again
    expect(authService.refreshToken).not.toHaveBeenCalled()

    flush()
  }))
  it('should handle undefined window.location in signature generation', fakeAsync(() => {
    // GIVEN window exists but window.location is undefined
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    interceptor['windowRef'] = { location: { href: 'test' } } as Window

    // Mock window.location to be undefined
    const originalLocation = interceptor['windowRef'].location
    interceptor['windowRef'].location = undefined

    // WHEN http request is made
    client.get(fakeEndPoint).subscribe()
    tick(100)

    // THEN should still add signature with empty URL
    const httpRequest = controller.expectOne(fakeEndPoint)
    expect(httpRequest.request.headers.has('Signature')).toBe(true)
    httpRequest.flush(null)

    // Restore location
    interceptor['windowRef'].location = originalLocation
    flush()
  }))
  it('should handle token refresh when refresh token exists but is empty', fakeAsync(() => {
    // GIVEN empty refresh token
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    const accessToken = 'old_access_token'

    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)
    LocalStorage.set(AuthStorageKeys.RefreshToken, '') // Empty refresh token

    // WHEN http request fails with 401
    client.get(fakeEndPoint).subscribe({
      next: () => {
        throw new Error('should have failed')
      },
      error: () => {}, // expect error
    })
    tick(100)

    // THEN should logout directly without refresh attempt
    const request = controller.expectOne(fakeEndPoint)
    request.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' })
    tick(100)

    expect(authService.refreshToken).not.toHaveBeenCalled()
    expect(authFacade.logout).toHaveBeenCalledWith({
      isSessionExpired: true,
      returnUrl: '/',
    })

    flush()
  }))
  it('should handle token refresh when refresh token is null', fakeAsync(() => {
    // GIVEN null refresh token
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    const accessToken = 'old_access_token'

    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)
    LocalStorage.set(AuthStorageKeys.RefreshToken, '') // 'null' as string

    // WHEN http request fails with 401
    client.get(fakeEndPoint).subscribe({
      next: () => {
        throw new Error('should have failed')
      },
      error: () => {}, // expect error
    })
    tick(100)

    // THEN should logout directly without refresh attempt
    const request = controller.expectOne(fakeEndPoint)
    request.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' })
    tick(100)

    expect(authService.refreshToken).not.toHaveBeenCalled()
    expect(authFacade.logout).toHaveBeenCalledWith({
      isSessionExpired: true,
      returnUrl: '/',
    })

    flush()
  }))
  it('should handle non-browser environments when adding signature', fakeAsync(() => {
    // GIVEN we're in a non-browser environment
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)

    // Mock JSEncrypt to throw when used (simulating missing window)
    const jsEncrypt = jest.requireMock('jsencrypt').JSEncrypt
    const mockEncryptFn = jsEncrypt.mock.results[0].value.encrypt
    mockEncryptFn.mockImplementationOnce(() => {
      throw new Error('Cannot use JSEncrypt in this environment')
    })

    // WHEN http request is made
    client.get(fakeEndPoint).subscribe()
    tick(100)

    // THEN request should still be made (graceful error handling)
    const httpRequest = controller.expectOne(fakeEndPoint)
    expect(httpRequest.request.headers.has('Authorization')).toBe(false)
    httpRequest.flush(null)
    flush()
  }))
  it('should use empty string as returnUrl when on login page', fakeAsync(() => {
    // GIVEN on login page
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)

    // Mock the router's url to be a login page
    jest
      .spyOn(interceptor['router'], 'url', 'get')
      .mockReturnValue('/auth/login')

    // And have an access token but no refresh token
    const accessToken = 'access_token'
    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)

    // WHEN http request fails with 401
    client.get(fakeEndPoint).subscribe({
      next: () => {
        throw new Error('should have failed')
      },
      error: () => {}, // expect error
    })
    tick(100)

    // THEN should logout with empty returnUrl
    const request = controller.expectOne(fakeEndPoint)
    request.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' })
    tick(100)

    // THEN should use empty string as returnUrl
    expect(authFacade.logout).toHaveBeenCalledWith({
      isSessionExpired: true,
      returnUrl: '',
    })

    flush()
  }))
  it('should handle empty token response from refresh', fakeAsync(() => {
    // GIVEN tokens and refresh that returns empty response
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    const accessToken = 'old_access_token'
    const refreshToken = 'refresh_token'

    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)
    LocalStorage.set(AuthStorageKeys.RefreshToken, refreshToken)

    // Mock refresh to return null (unexpected backend response)
    jest.spyOn(authService, 'refreshToken').mockReturnValue(of(null as any))

    // WHEN http request fails with 401
    client.get(fakeEndPoint).subscribe({
      next: () => {
        throw new Error('should have failed')
      },
      error: () => {}, // expect error
    })
    tick(100)

    // THEN should attempt refresh and fail due to empty response
    const firstRequest = controller.expectOne(fakeEndPoint)
    firstRequest.flush('Unauthorized', {
      status: 401,
      statusText: 'Unauthorized',
    })
    tick(100)

    // THEN should trigger logout
    expect(authService.refreshToken).toHaveBeenCalled()
    expect(authFacade.logout).toHaveBeenCalledWith({
      isSessionExpired: true,
      returnUrl: '/',
    })

    flush()
  }))
  it('should handle refresh with empty tokens', fakeAsync(() => {
    // GIVEN tokens and refresh that returns empty tokens
    iframeMessengerFacade.selectLoadedAsMicroApp$ = of(false)
    const accessToken = 'old_access_token'
    const refreshToken = 'refresh_token'

    LocalStorage.set(AuthStorageKeys.AccessToken, accessToken)
    LocalStorage.set(AuthStorageKeys.RefreshToken, refreshToken)

    // Mock refresh to return response with empty tokens
    jest.spyOn(authService, 'refreshToken').mockReturnValue(
      of({
        access_token: '',
        refresh_token: '',
        expires_in: '3600',
        token_type: 'Bearer',
      })
    )

    // WHEN http request fails with 401
    client.get(fakeEndPoint).subscribe({
      next: () => {
        throw new Error('should have failed')
      },
      error: () => {}, // expect error
    })
    tick(100)

    // THEN should attempt refresh
    const firstRequest = controller.expectOne(fakeEndPoint)
    firstRequest.flush('Unauthorized', {
      status: 401,
      statusText: 'Unauthorized',
    })
    tick(100)

    // THEN should update tokens then retry with empty token (which will fail)
    expect(authService.refreshToken).toHaveBeenCalled()
    expect(LocalStorage.get(AuthStorageKeys.AccessToken)).toBe('')

    // The retry request will be made but should fail again
    const retriedRequest = controller.expectOne(fakeEndPoint)
    retriedRequest.flush('Unauthorized', {
      status: 401,
      statusText: 'Unauthorized',
    })
    tick(100)

    // Then should logout
    expect(authFacade.logout).toHaveBeenCalledWith({
      isSessionExpired: true,
      returnUrl: '/',
    })

    flush()
  }))
})
