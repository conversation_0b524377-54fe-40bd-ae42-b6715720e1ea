import { CommonModule } from '@angular/common'
import {
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http'
import { NgModule } from '@angular/core'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { AuthEffects } from './+state/auth.effects'
import { AuthFacade } from './+state/auth.facade'
import * as fromAuth from './+state/auth.reducer'
import { AuthInterceptor } from './interceptor/auth.interceptor'
import { AuthService } from './services/auth.service'

@NgModule({
  imports: [
    CommonModule,
    EffectsModule.forFeature([AuthEffects]),
    StoreModule.forFeature(fromAuth.AUTH_FEATURE_KEY, fromAuth.authReducer),
  ],
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    AuthFacade,
    AuthService,
    provideHttpClient(withInterceptorsFromDi()),
  ],
})
export class AuthModule {}
