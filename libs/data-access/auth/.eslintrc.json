{"extends": ["../../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "extends": ["plugin:@nx/angular", "plugin:@angular-eslint/template/process-inline-templates"], "parserOptions": {"project": ["libs/data-access/auth/tsconfig.*?.json"]}, "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "venio", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "venio", "style": "kebab-case"}]}}, {"files": ["*.html"], "extends": ["plugin:@nx/angular-template"], "rules": {}}]}