import { Injectable } from '@angular/core'
import { GroupStackType } from '@venio/shared/models/interfaces'
import { BehaviorSubject, Observable } from 'rxjs'

@Injectable({
  providedIn: 'root',
})
export class BreadcrumbService {
  constructor() {}

  // Dictionary mapping each group (enum value) to its BehaviorSubject<boolean>
  private conditionCheckedMap: Partial<
    Record<GroupStackType, BehaviorSubject<boolean>>
  > = {}

  /**
   * Returns an observable for the checkbox state of a given group.
   * If the state doesn't exist, initializes it to true (checked) by default.
   * @param group - The group (enum) for which the checkbox state is required.
   */
  public getConditionChecked$(group: GroupStackType): Observable<boolean> {
    let subject = this.conditionCheckedMap[group]
    if (!subject) {
      subject = new BehaviorSubject<boolean>(true)
      this.conditionCheckedMap[group] = subject
    }
    return subject.asObservable()
  }

  /**
   * Sets the checkbox state for a specific group.
   * @param group - The group (enum) to update.
   * @param value - The new checkbox state.
   */
  public setConditionChecked(group: GroupStackType, value: boolean): void {
    const subject = this.conditionCheckedMap[group]
    if (!subject) {
      this.conditionCheckedMap[group] = new BehaviorSubject<boolean>(value)
    } else {
      subject.next(value)
    }
  }

  /**
   * Updates all groups with the same checkbox state.
   * @param value - The state to set for all groups.
   */
  public setAllConditionChecked(value: boolean): void {
    Object.values(this.conditionCheckedMap).forEach((subject) =>
      subject.next(value)
    )
  }
}
