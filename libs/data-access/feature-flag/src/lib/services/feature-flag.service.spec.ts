import { TestBed } from '@angular/core/testing'
import { hot } from 'jasmine-marbles'
import { FeatureFlagModel } from '../models/interfaces/feature-flag.model'
import { FeatureFlagService } from '../services/feature-flag.service'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('FeatureFlagService', () => {
  let featureFlagService: FeatureFlagService

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        FeatureFlagService,
      ],
    })

    featureFlagService = TestBed.inject(FeatureFlagService)
  })

  it('should be created', () => {
    expect(featureFlagService).toBeTruthy()
  })

  it(`fetchFeatureFlag should return feature flag on success response`, () => {
    const successResponse = {} as FeatureFlagModel
    featureFlagService.fetchFeatureFlag = hot('-a-|', successResponse) as any

    const expected = hot('-a-|', successResponse)

    expect(featureFlagService.fetchFeatureFlag).toBeObservable(expected)
  })
})
