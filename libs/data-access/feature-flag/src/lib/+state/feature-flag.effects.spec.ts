import { TestBed } from '@angular/core/testing'
import { provideMockActions } from '@ngrx/effects/testing'
import { Action } from '@ngrx/store'
import { provideMockStore } from '@ngrx/store/testing'
import { hot } from 'jasmine-marbles'
import { Observable, of } from 'rxjs'
import { FeatureFlagModel } from '../models/interfaces/feature-flag.model'
import { FeatureFlagService } from '../services/feature-flag.service'
import * as FeatureFlagActions from './feature-flag.actions'
import { FeatureFlagEffects } from './feature-flag.effects'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('FeatureFlagEffects', () => {
  let actions: Observable<Action>
  let effects: FeatureFlagEffects
  let featureFlagService: FeatureFlagService
  const featureFlagSuccessResponse = {} as FeatureFlagModel

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        FeatureFlagEffects,
        FeatureFlagService,
        provideMockActions(() => actions),
        provideMockStore(),
      ],
    })

    effects = TestBed.inject(FeatureFlagEffects)
    featureFlagService = TestBed.inject(FeatureFlagService)
  })

  describe('fetchFeatureFlag$', () => {
    it('should load user access token when successfully logged in', () => {
      actions = hot('-a-|', { a: FeatureFlagActions.fetchFeatureFlag() })

      jest
        .spyOn(featureFlagService, 'fetchFeatureFlag')
        .mockReturnValue(of(featureFlagSuccessResponse))

      const expected = hot('-a-|', {
        a: FeatureFlagActions.loadFeatureFlagSuccess({
          featureFlagSuccess: featureFlagSuccessResponse,
        }),
      })

      expect(effects.fetchFeatureFlag$).toBeObservable(expected)
    })
  })
})
