import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import * as FeatureFlagActions from './feature-flag.actions'
import { FeatureFlagState } from './feature-flag.reducer'
import * as FeatureFlagSelectors from './feature-flag.selectors'
import { FeatureFlagModel } from '../models/interfaces/feature-flag.model'
import { toSignal } from '@angular/core/rxjs-interop'
import { skipWhile } from 'rxjs'
import { map } from 'rxjs/operators'

@Injectable()
export class FeatureFlagFacade {
  public selectFeatureFlag$ = this.store.pipe(
    select(FeatureFlagSelectors.getFeatureFlag)
  )

  public selectFeatureFlagFailure$ = this.store.pipe(
    select(FeatureFlagSelectors.getFeatureFlagError)
  )

  public readonly hasElasticAmpEnabled = toSignal(
    this.store.pipe(
      select(FeatureFlagSelectors.getFeatureFlag),
      skipWhile((value) => value === null || value === undefined),
      map((value) => value['elasticFleetApiUrl'] as string)
    ),
    { initialValue: null }
  )

  constructor(private readonly store: Store<FeatureFlagState>) {}

  public fetchFeatureFlag(): void {
    this.store.dispatch(FeatureFlagActions.fetchFeatureFlag())
  }

  public storeFeatureFlag(featureFlagSuccess: FeatureFlagModel): void {
    this.store.dispatch(
      FeatureFlagActions.loadFeatureFlagSuccess({ featureFlagSuccess })
    )
  }
}
