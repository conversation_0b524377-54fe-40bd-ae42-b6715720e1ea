import { ResponseModel } from '@venio/shared/models/interfaces'
import { FeatureFlagModel } from '../models/interfaces/feature-flag.model'
import { FeatureFlagPartialState } from './feature-flag.reducer'
import * as FeatureFlagSelectors from './feature-flag.selectors'

describe('FeatureFlag Selectors', () => {
  let state: FeatureFlagPartialState
  const errorResponse = { status: 'error' } as ResponseModel
  const featureFlagResponse = {} as FeatureFlagModel

  beforeEach(() => {
    state = {
      featureFlag: {
        featureFlagSuccess: featureFlagResponse,
        featureFlagError: errorResponse,
      },
    }
  })

  describe('FeatureFlag Selectors', () => {
    it('getAllFeatureFlag() should return the json data of FeatureFlag', () => {
      const results = FeatureFlagSelectors.getFeatureFlag(state)

      expect(results).toStrictEqual(featureFlagResponse)
    })

    it('getFeatureFlagError() should return the current "error" state', () => {
      const result = FeatureFlagSelectors.getFeatureFlagError(state)

      expect(result).toStrictEqual(errorResponse)
    })
  })
})
