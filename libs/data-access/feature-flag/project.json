{"name": "data-access-feature-flag", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/data-access/feature-flag/src", "prefix": "venio", "tags": ["feature-flag"], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/libs/data-access/feature-flag"], "options": {"tsConfig": "libs/data-access/feature-flag/tsconfig.lib.json", "project": "libs/data-access/feature-flag/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/data-access/feature-flag/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/data-access/feature-flag/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/data-access/feature-flag"], "options": {"jestConfig": "libs/data-access/feature-flag/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}